import { expect } from 'chai';
import 'mocha';
import {BotsService} from "../service/BotsService";
import {Empresa} from "../domain/Empresa";
import {ConvMock} from "./ConvMock";
import {ObjetoPedidoBot} from "../utils/bot/ObjetoPedidoBot";
import {EnderecoBot} from "../utils/bot/EnderecoBot";
import {EmpresaMock} from "./EmpresaMock";
import {FormaDeEntregaBot} from "../utils/bot/FormaDeEntregaBot";
import {MapeadorDeFormaDePagamento} from "../mapeadores/MapeadorDeFormaDePagamento";
import {MapeadorBasico} from "../mapeadores/MapeadorBasico";
import {ComunicadorDialogFlowMock} from "./ComunicadorDialogFlowMock";
import {ComunicadorDialogFlow} from "../service/ComunicadorDialogFlow";

describe('Bot Zona de Entrega', async () => {
  const empresa = EmpresaMock.Nova();

  it('deve pedir o troco', async () => {
    const conv = new ConvMock(BotsService.Intents.PagamentoOutrasOpcoes);
    conv.reqExpress.empresa = empresa;
    ComunicadorDialogFlow._instance = new ComunicadorDialogFlowMock();

    const botsService = new BotsService();

    const mapeador: any = {};

    mapeador.listeAsync = ((query: any) => {
      return new Promise<any>( (resolve, reject) => {
        resolve(empresa.formasDePagamento);
      });
    });
    MapeadorDeFormaDePagamento._instancia = mapeador;

    const intentFormaDeEntrega = botsService.PagamentoOutrasOpcoes();

    return intentFormaDeEntrega.call(this, conv, {opcaoEscolhida: '3'}).then( () => {
      const pedidoFinal: ObjetoPedidoBot = conv.data.pedido;

      const msgEsperada = `E você vai precisar de troco?
*1* - Sim
*2* - Não`;

      expect(conv.obtenhaMensagem()).to.equal(msgEsperada);
      expect(conv.contexts.contexto).to.equal(BotsService.Contextos.PagamentoDinheiroFollowup);
      expect(conv.contexts.lifeSpan).to.equal(1);
    });
  });
});
