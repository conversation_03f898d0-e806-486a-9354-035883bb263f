import {MapeadorBasico} from "./MapeadorBasico";

export class MapeadorDeAtividade extends MapeadorBasico {
  constructor() {
    super('atividade');
  }


  foiUtilizada(query: any): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.desativeMultiCliente();
      this.gerenciadorDeMapeamentos.selecioneUm(this.metodo('foiUtilizada'), query, function(total: number) {
        resolve(total && total > 0)
      });
    });
  }


  removaAtividade (atividade: any) {
    return new Promise<void>((resolve, reject) => {
      this.gerenciadorDeMapeamentos.atualize(this.metodo('remova'), atividade, () => {
        resolve();
      })
    })
  }

  removaAtividadesIntegrada(){
    this.desativeMultiCliente();
    return   this.gerenciadorDeMapeamentos.atualize(this.metodo('removaAtividadesIntegradas'), {});
  }

  atualizeValor(atividade: any){
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeValor'), atividade);
  }

}
