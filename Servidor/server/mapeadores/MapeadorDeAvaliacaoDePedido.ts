import {MapeadorBasico} from './MapeadorBasico';


export class MapeadorDeAvaliacaoDePedido extends MapeadorBasico {
  constructor() {
    super('avaliacaoDePedido', true);
  }

  async obtenhaNotaMedia(dataInicio: Date, dataFim: Date) {
    const query = { dataInicio: dataInicio, dataFim: dataFim};

    return this.gerenciadorDeMapeamentos.selecioneUmAsync(this.metodo('selecioneNotaMedia'), query);
  }
}
