import {MapeadorBasico} from './MapeadorBasico';
import {TemplateDeMensagem} from "../domain/mensagens/TemplateDeMensagem";

export class MapeadorDeTemplateDeMensagem extends MapeadorBasico {
  constructor() {
    super('templateDeMensagem');
  }

  obtenhaQueryEmpresaOuRede(idEmpresa: any, idRede: any, nome: any = null) {
    let query: any = {idEmpresa: idEmpresa, idRede: idRede}

    if(nome)
      query.nome = nome

    return query
  }

  listeEmpresasQueUsam(template: TemplateDeMensagem, filtro: any = null) {
    if(!template || !template.rede)
      return Promise.resolve([])

    let query: any = {
      idTemplate: template.id,
      idRede: template.rede.id,
      contemNomeTemplate: '%' + template.obtenhaNomeCampo() + '%'
    }

    if(filtro)
      Object.assign(query, filtro)


    return this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('selecioneEmpresasQueUsamTemplate'),
      query);
  }


  listeDaEmpresaOuDaRede(idEmpresa: any, idRede: any, nome: any = null): Promise<any> {
    let query = this.obtenhaQueryEmpresaOuRede(idEmpresa, idRede, nome)
    return this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('selecioneDaEmpresaOuDaRede'),
      query);
  }

  selecioneDaEmpresaOuDaRede(idEmpresa: any, idRede: any, nome: any = null): Promise<any> {
    let query = this.obtenhaQueryEmpresaOuRede(idEmpresa, idRede, nome)

    return this.gerenciadorDeMapeamentos.selecioneUmAsync(this.metodo('selecioneDaEmpresaOuDaRede'),
      query);
  }

}
