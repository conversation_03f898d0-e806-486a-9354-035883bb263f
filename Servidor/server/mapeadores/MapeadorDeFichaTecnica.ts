import {MapeadorBasico} from "./MapeadorBasico";


export class MapeadorDeReceita  extends MapeadorBasico {
  constructor() {
    super('receita', false);
  }
}
export class MapeadorDeIngredienteDaReceita extends MapeadorBasico {
  constructor() {
    super('ingredienteDaReceita', false);
  }
}

export class MapeadorDeVinculoIngredienteProdutoOpcao extends MapeadorBasico {
  constructor() {
    super('vinculoIngredienteProduto', false);
  }
}
