import {MapeadorBasico} from './MapeadorBasico';
import {Contato} from "../domain/Contato";

export class MapeadorDeContato extends MapeadorBasico {
  constructor() {
    super('contato');
  }

  async selecionePorTelefone(query: any){
    return this.gerenciadorDeMapeamentos.selecioneUmAsync(this.metodo('selecionePorTelefone'), query);
  }

  async selecioneDoIfood(codigo: any){
    return this.gerenciadorDeMapeamentos.selecioneUmAsync(this.metodo('selecioneDoIfood'), {codigoIfood: codigo});
  }


  async selecioneLogin(query: any){
    return this.gerenciadorDeMapeamentos.selecioneUmAsync(this.metodo('selecioneLogin'), query);
  }

  listeResumo( query: any ) {
    return new Promise( (resolve, reject) => {

      this.gerenciadorDeMapeamentos.selecioneVarios(this.metodo('selecioneResumo'), query,  ( resumo: any ) => {
        resolve(resumo);
      });
    });
  }

  listeSeExisteAlgum(query: any) {
    return new Promise((resolve, reject) => {
      this.gerenciadorDeMapeamentos.selecioneVarios(this.metodo('selecioneSeExisteAlgum'), query,  ( existentes: any ) => {
        resolve(existentes);
      });

    })
  }

  atualizeStatus(contato: Contato ) {
    console.log('alterar status ' + contato.nome + ": " + contato.status)
    return new Promise<void>((resolve, reject) => {
      this.gerenciadorDeMapeamentos.atualize(this.metodo('altereStatus'), contato, () => {
        resolve();
      });
    });
  }

  bloqueie(contato: Contato): Promise<number> {
    console.log('bloqueando contato ' + contato.id)
    return new Promise<number>(async(resolve, reject) => {
      const qtde = await this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('bloqueie'), contato);

      resolve(qtde);
    });
  }

  atualizeUltimaVisita(contato: Contato ) {
    return    this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeUltimaVisita'), contato );
  }

  atualizeCpf(contato: Contato ) {
    return     this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeCpf'), contato );
  }

  atualizeDataNascimento(contato: Contato ) {
    return  this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeDataNascimento'), contato);
  }

  atualizeEmail(contato: Contato ) {
    return    this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeEmail'), contato);
  }

  atualizeCadastro(contato: Contato ) {
    return  this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeCadastro'), contato );
  }

  atualizeDataUltimoPedido(contato: Contato ) {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeDataUltimoPedido'), contato);
  }

  atualizeAtivarMsgMkt(contato: Contato ) {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeAtivarMsgMkt'), contato);
  }

  removaContato (contato: Contato) {
   return   this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('removaContato'), contato);
  }

  atualizeDadosAcesso (contato: Contato) {
   return   this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeDadosAcesso'), contato);
  }

  atualizeCodigoPagarme (contato: Contato) {
   return   this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeCodigoPagarme'), contato);
  }

  removaContatoImportado(contato: Contato) {
    return   this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('removaContatoImportado'), contato);
  }

  async obtenhaRelatorioFidelidade(dataInicio: string, dataFim: string, qtdeVendasRecorrente: number,
                                   qtdeDiasEmRisco: number, qtdeDiasPerdido: number, qtdeVendasVIP: number,
                                   ticketMedioVIP: number, qtdeDiasPeriodo: number,
                                   rede: string = null, redeId: any = null) {
    const dados: any = {
      dataInicio: dataInicio,
      dataFim: dataFim,
      qtdeVendasRecorrente: qtdeVendasRecorrente,
      qtdeDiasEmRisco: qtdeDiasEmRisco,
      qtdeDiasPerdido: qtdeDiasPerdido,
      qtdeVendasVIP: qtdeVendasVIP,
      ticketMedioVIP: ticketMedioVIP,
      qtdeDiasPeriodo: qtdeDiasPeriodo
    };

    if(rede) dados.rede = rede;
    if(redeId) dados.redeId = Number(redeId);

    return await this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('relatorioClientesFidelidade'),
      dados);
  }

  async obtenhaClientesRelatorioFidelidade(categoria: string, dataInicio: string, dataFim: string, qtdeVendasRecorrente: number,
                                           qtdeDiasEmRisco: number, qtdeDiasPerdido: number, qtdeVendasVIP: number,
                                           ticketMedioVIP: number, qtdeDiasPeriodo: number,
                                           rede: string = null, redeId: any = null) {
    const dados: any = {
      categoria: categoria,
      dataInicio: dataInicio,
      dataFim: dataFim,
      qtdeVendasRecorrente: qtdeVendasRecorrente,
      qtdeDiasEmRisco: qtdeDiasEmRisco,
      qtdeDiasPerdido: qtdeDiasPerdido,
      qtdeVendasVIP: qtdeVendasVIP,
      ticketMedioVIP: ticketMedioVIP,
      qtdeDiasPeriodo: qtdeDiasPeriodo
    };

    if(rede) dados.rede = rede;
    if(redeId) dados.redeId = Number(redeId);

    return await this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('clientesFidelidadePorCategoria'),
      dados);
  }

  atualizeCodigoPdv(contato: any) {
    return new Promise<void>((resolve, reject) => {
      this.gerenciadorDeMapeamentos.atualize(this.metodo('atualizeCodigoPdv'), contato, () => {
        resolve();
      })

    })
  }

  insiraTag(contato: any, tag: any){
    return this.gerenciadorDeMapeamentos.insiraAsync( this.metodo('insiraTag'), {contato: contato, tag: tag});
  }

  removaTag(contato: any, tag: any){
    return this.gerenciadorDeMapeamentos.atualizeAsync( this.metodo('removaTag'), {contato: contato, tag: tag});
  }

  async listeStatusContatos(filtro: any = {}): Promise<any> {
    console.log('vai buscar');

    return await this.gerenciadorDeMapeamentos.selecioneVariosAsync(
      this.metodo('listeStatusContatos'), filtro);
  }
}
