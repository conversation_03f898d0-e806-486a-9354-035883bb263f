import {MapeadorBasico} from "./MapeadorBasico";

export class MapeadorDeIntegracaoPedidoFidelidade extends MapeadorBasico {
  constructor() {
    super('integracaoPedidoFidelidade');
  }

  atualizePontuarSoLoja(integracao: any){
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizePontuarSoLoja'), integracao);
  }

  atualizePontuarMesas(integracao: any){
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizePontuarMesas'), integracao);
  }

  atualizeResgatarBrinde(integracao: any){
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeResgatarBrinde'), integracao);
  }

  atualizeOcultarPontos(integracao: any){
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeOcultarPontos'), integracao);
  }
}
