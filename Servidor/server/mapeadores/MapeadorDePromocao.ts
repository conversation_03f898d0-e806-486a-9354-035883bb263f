import {MapeadorBasico} from "./MapeadorBasico";

export class MapeadorDePromocao extends MapeadorBasico {
  constructor() {
    super('promocao');
  }

  atualizeAtiva(promocao: any) {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeStatusAtiva'),
      promocao);
  }

  remova (promocao: any) {
    return new Promise<void>((resolve, reject) => {
      this.gerenciadorDeMapeamentos.atualize(this.metodo('remova'), promocao, () => {
        resolve();
      })
    })
  }

  async removaHorarios(promocao: any) {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('removaHorarios'), promocao);
  }


  async insiraHorarios(promocao: any){

    let dados: any = [];

    promocao.horarios.forEach(
      (horario: any) => dados.push({id: String(`${promocao.id}${horario.dia}`), idPromocao: promocao.id, dia: horario.dia}))

    return this.gerenciadorDeMapeamentos.insiraAsync(this.metodo('insiraHorarios'), { dados: dados});


  }

}
