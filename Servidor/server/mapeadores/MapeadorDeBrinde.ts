import {MapeadorBasico} from './MapeadorBasico';

export class MapeadorDeBrinde extends MapeadorBasico {
  constructor() {
    super('brinde');
  }

  foiUtilizado(query: any): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.desativeMultiCliente();
      this.gerenciadorDeMapeamentos.selecioneUm(this.metodo('foiUtilizado'), query, function(total: number) {
        resolve(total && total > 0)
      });
    });
  }


  removaBrinde (brinde: any) {
    return new Promise<void>((resolve, reject) => {
      this.gerenciadorDeMapeamentos.atualize(this.metodo('remova'), brinde, () => {
        resolve();
      })
    })
  }

}
