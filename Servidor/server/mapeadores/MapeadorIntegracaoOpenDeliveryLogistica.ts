import {MapeadorBasico} from "./MapeadorBasico";

export class MapeadorIntegracaoOpenDeliveryLogistica extends MapeadorBasico {
  constructor() {
    super('opendeliverylogistica');
  }

  atualizeToken(obj: any) {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeToken'),  obj);
  }

  atualizeAtiva(obj: any) {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeAtiva'),
      obj);
  }


}
