import {MapeadorBasico} from "./MapeadorBasico";

export class MapeadorDeIntegracaoIfood  extends MapeadorBasico {
  constructor() {
    super('integracaoIfood');
  }

  atualizeApi(obj: any) {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeApi'),  obj);
  }

  atualizeToken(obj: any) {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeToken'),  obj);
  }

  atualizeAtiva(obj: any) {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeAtiva'), obj);
  }
}
