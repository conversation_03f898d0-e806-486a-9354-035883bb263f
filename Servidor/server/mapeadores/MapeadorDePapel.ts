import {MapeadorBasico} from "./MapeadorBasico";

export class MapeadorDePapel extends MapeadorBasico {
  constructor() {
    super('papel');
  }

  listeOperacoes(){
    this.desativeMultiCliente();
    return    this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('listeOperacoes'), {});
  }

  insiraUsuario(usuario: any, papel: any){
    return    this.gerenciadorDeMapeamentos.insiraAsync(this.metodo('insiraUsuario'),
      {papel: papel.id, usuario: usuario.id});
  }

  removaUsuario(usuario: any, papel: any){
    this.desativeMultiCliente();
    return    this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('removaUsuario'),
      {papel: papel.id, usuario: usuario.id});
  }

  insiraOperacao(papel: any, operacao: any){
    return    this.gerenciadorDeMapeamentos.insiraAsync(this.metodo('insiraOperacao'),
      {papel: papel.id, operacao: operacao.id});
  }


  removaOperacao(papel: any, operacao: any){
    this.desativeMultiCliente();
    return    this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('removaOperacao'),
      {papel: papel.id, operacao: operacao.id});
  }

}
