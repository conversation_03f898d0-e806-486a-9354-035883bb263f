import {MapeadorBasico} from "./MapeadorBasico";
import {<PERSON>tato} from "../domain/Contato";
import {Campanha} from "../domain/Campanha";
import {EnumOrigemContatosCampanha} from "../domain/EnumOrigemContatosCampanha";

export class MapeadorDeCampanha extends MapeadorBasico {
  constructor() {
    super('campanha');
  }

  insiraSync(obj: any) {
    if( obj.menu ) {
      obj.menu = JSON.stringify(obj.menu);
    }

    return this.gerenciadorDeMapeamentos.insiraAsync(this.metodo('insira'), obj);
  }

  atualizeSync(obj: any) {
    if( obj.menu && typeof obj.menu !== 'string') {
      obj.menu = JSON.stringify(obj.menu);
    }

    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualize'), obj);
  }

  async obtenhaQtdeLidas(campanha: any) {
    return this.gerenciadorDeMapeamentos.selecioneUmAsync(this.metodo('selecioneQtdeLidas'), {id: campanha.id});
  }

  async listeAsync(query: any): Promise<any> {
    const campanhas: Array<Campanha> = await this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('selecione'), query);

    for( let campanha of campanhas ) {
      campanha.ajusteMenu();

      if( campanha.campanhaRede && campanha.campanhaRede.id ) {
        campanha.mensagem = campanha.campanhaRede.mensagem;
        campanha.nome = campanha.campanhaRede.nome;
      } else {
        delete campanha.campanhaRede;
      }
    }

    return campanhas;
  }

  async carregueContatosArquivo(campanha: Campanha, envio: boolean = false) {
    const query: any = {};

    if( envio && campanha.naoEnviarMsgParaQuemRecebeuRecente ) {
      query.enviadasNosultimosDias = campanha.qtdeDiasUltimaNotificacao;
    }

    query.id = campanha.id;

    let contatos: Array<Contato> = await this.gerenciadorDeMapeamentos.selecioneVariosAsync(
      this.metodo('carregueContatosCampanhaArquivo'), query);

    return contatos;
  }

  async obtenhaCampanha(dados: any, carregarContatos: boolean = true) {
    const campanha: Campanha = await this.selecioneSync(dados);

    if( campanha.campanhaRede && campanha.campanhaRede.id ) {
      campanha.mensagem = campanha.campanhaRede.mensagem;
      campanha.nome = campanha.campanhaRede.nome;
    } else {
      delete campanha.campanhaRede;
    }

    if( carregarContatos ) {
      await campanha.carregueContatos(null);
    }

    return campanha;
  }

  async obtenhaTodas(tipoDeEnvio: string) {
    const campanhas: Array<Campanha> = await this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('selecioneTodas'),
      {tipoDeEnvio: tipoDeEnvio});

    for( let campanha of campanhas ) {
      campanha.ajusteMenu();

      if( campanha.campanhaRede && campanha.campanhaRede.id ) {
        campanha.mensagem = campanha.campanhaRede.mensagem;
        campanha.nome = campanha.campanhaRede.nome;
      } else {
        delete campanha.campanhaRede;
      }
    }

    return campanhas;
  }

  async atualizeStatus(obj: any) {
    return new Promise<void>((resolve, reject) => {
      this.gerenciadorDeMapeamentos.atualize(this.metodo('atualizeStatus'), obj, () => {
        resolve();
      })
    })
  }

  insiraNaListaContatos(campanha: Campanha){
    this.desativeMultiCliente();

    let contatos = campanha.contatos;

    return new Promise<void>((resolve) => {
      if(!campanha ||  !contatos || !contatos.length) return resolve();

      let dados = contatos.map( (contato: any) => ({idCampanha: campanha.id, idContato: contato.id}));

      this.gerenciadorDeMapeamentos.insiraAsync(this.metodo('insiraListaContatos'), {dados: dados}).then( () => {
        resolve();
      })
    });
  }

  removaListaDeContatos(campanha: Campanha) {
    this.desativeMultiCliente();

    return new Promise<void>((resolve) => {
      this.gerenciadorDeMapeamentos.insiraAsync(this.metodo('removaNaListaContatos'), {campanha: campanha}).then( () => {
        resolve();
      })
    });
  }

  async atualizeTotalEnviadas(obj: any): Promise<Campanha> {
    return new Promise<Campanha>(async (resolve, reject) => {
      await this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeTotalEnviadas'), obj);

      const campanha = await this.selecioneSync({id: obj.id});

      resolve(campanha);
    });
  }

  async atualizeTotalMensagens(obj: any): Promise<Campanha> {
    return new Promise<Campanha>(async (resolve, reject) => {
        await this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeTotalMensagens'), obj);

        const campanha = await this.selecioneSync({id: obj.id});

        resolve(campanha);
    });
  }
}
