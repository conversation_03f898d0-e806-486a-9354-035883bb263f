import {MapeadorBasico} from './MapeadorBasico';
import {Catalogo} from "../domain/catalogo/Catalogo";
import {GerenciadorDeMapeamentosCatalogo} from "./MapeadorDeProduto";


export class MapeadorDeDisponibilidade extends MapeadorBasico {
  constructor(private catalogo: Catalogo) {
    super('disponibilidade', false);
    this.gerenciadorDeMapeamentos = new GerenciadorDeMapeamentosCatalogo(catalogo, this.contexto());
  }

  removaTodas(){
    return  this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('removaTodas'), {idCatalogo: this.catalogo.id} );
  }
}
