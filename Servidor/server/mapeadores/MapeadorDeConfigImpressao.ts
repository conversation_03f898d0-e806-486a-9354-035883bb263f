import {MapeadorBasico} from "./MapeadorBasico";
import {MapeadorDeEmpresa} from "./MapeadorDeEmpresa";
import {MapeadorDeImpressora} from "./MapeadorDeImpressora";
import * as async from "async";
import {Empresa} from "../domain/Empresa";

export class MapeadorDeConfigImpressao extends MapeadorBasico {
  constructor() {
    super('configImpressao', false);
  }

  salveConfigImpressao(empresa: any, configImpressao: any) {
    //removendo configurações anteriores
    return new Promise<any>((resolve) => {
      new MapeadorDeEmpresa().removaDasCaches(empresa);

      this.transacao((conexao: any, commit: any) => {
        if(!empresa.configImpressao) {
          empresa.configImpressao = configImpressao
          this.insiraConfigImpressao(empresa, (erro) => {
            if(!erro){
              commit(() => {
                resolve(null)
              })
            } else {
              conexao.rollback( () => {
                resolve(erro)
              })
            }
          })
        }

        else {
          empresa.configImpressao = configImpressao
          this.atualizeConfigImpressao(empresa, (erro) => {
            if(!erro){
              commit(() => {
                resolve(null)
              })
            } else {
              conexao.rollback( () => {
                resolve(erro)
              })
            }

          })
        }
      })

    })
  }

  private insiraConfigImpressao(empresa: Empresa, resolve: (value?: unknown) => void) {
    if(!empresa.configImpressao.imprimirTXT)
      empresa.configImpressao.multiplasImpressoras = false;

    this.insiraSync(empresa.configImpressao).then(() => {
      new MapeadorDeEmpresa().atualizeConfigImpressao(empresa).then(() => {
        if(!empresa.configImpressao.imprimirTXT) return resolve();
        async.forEachSeries(empresa.configImpressao.impressoras, (impressora: any, cb: any) => {

          this.salveImpressora(impressora, empresa.configImpressao).then ((erro) => {
            cb(erro);
          })
        }, (erro) => {
          if(erro) return    resolve(erro)

          resolve( )
        })
      })
    })
  }

  private atualizeConfigImpressao(empresa: any, resolve: (value?: unknown) => void){
    if(!empresa.configImpressao.imprimirTXT)
      empresa.configImpressao.multiplasImpressoras = false;

    this.atualizeSync(empresa.configImpressao).then( () => {
      if(!empresa.configImpressao.imprimirTXT) return resolve();

      async.forEachSeries(empresa.configImpressao.impressoras, (impressora: any, cb: any) => {
        this.salveImpressora(impressora, empresa.configImpressao).then ((erro) => {
          console.log(erro)
          cb(erro);
        })
      }, (erro) => {
        resolve(erro)
      })

    })


  }

  private salveImpressora(impressora: any, configImpressao: any) {
    return new Promise<string>((resolve) => {

      if(impressora.removida){
        if(!impressora.id) return resolve('');
        (new MapeadorDeImpressora()).removaAsync(impressora).then(() => {
          return resolve('');
        })
      }else {
        if(!impressora.nome) return resolve('Impressora é obrigatorio')
        if(!impressora.tamanhoPapel) return resolve('Tamanho do papel é obrigatorio')

        if(configImpressao.multiplasImpressoras){
          if(!impressora.setor) return resolve('Setor  é obrigatorio')
          if(!impressora.layout) return resolve('Layout  é obrigatorio')
        }

        impressora.configImpressao = {id:  configImpressao.id };

        if(!impressora.id){
          (new MapeadorDeImpressora()).insiraSync(impressora).then(() => {
            return resolve('');
          })
        } else {
          (new MapeadorDeImpressora()).atualizeSync(impressora).then(() => {
            return resolve('');
          })
        }
      }


    })

  }

}
