
body{
  font-family: Arial;
  font-size: 16pt;
  color: #444;
  background-color: #505050;
  padding: 0;
  margin: 0;
}
table{
  margin: 0 auto; border: 0;
}

.texto-left{
  text-align: left;
}

table tr{
  padding:0; border:0;
  text-align: center;
}
table tr td{
  padding: 0;border:0;
}

table tr td p{
  font-size: 18px;
}


table.principal{
  width: 600px;
}

table.black, table.black.china>tr>td{
  background-color: #000000;
}

.bggray{
  background-color: #505050 !important;
}

table.footer, table.footer  tr td{
  background-color: #EFEFEF;
}

table.footer.gendai, table.footer.gendai  tr td{
  background-color:#e4e4e4;
}

.colunafundo, table tr td.colunafundo{
  background-color: #f2ede7 !important;
  vertical-align: top !important;
  width: 29px;
}

.table1{
  width: 100%; height: 100%; background-color: #505050; margin: 0;
}

.table2{
  background: #060606;
  width: 600px;
}

.table3{
  background: #ffff;
  margin-left: 19px;
  width: 523px;
  padding-right: 20px;
  padding-left: 20px;
}
table.linha tr td{
  width:538px; border-top: 2px solid #777777;
}

table.produtos td.titulo{
  font-family: Arial, sans-serif, Helvetica; font-size: 18px;
  line-height: 25px; text-align: left;
  vertical-align: top;
  padding-left: 30px;
  color: #000000;
  height: 66px;
  background: #ffff;
  font-weight: bold;
}

table.produtos td.titulo span{
  text-transform: uppercase;
  font-size: 24px;
  color: #f00000;
}


.gendai3colunas .table3{
  margin-left: 0px;
  width: 502px;

}
.gendai3colunas .table3 p {
  color: #2e2e2b;
}

.gendai3colunas  .titulo-vermelho{
  color: #c13a4e;
  text-transform: none;
}

.gendai3colunas table.produtos td.titulo{
  padding-left: 0px;
  height: 35px;
}


.gendai3colunas  table.linha tr td{
  border-color: #d6d6d6;
}


.font12{
  font-size: 12px !important;
}

a{
  text-decoration: none !important;
  color: #000000;
}
.verlink{
  color:#FFC100
}
.text-blue{
  color: #1235b6;
}

.bgbranco, table tr td.bgbranco{
  background-color: #ffffff !important;
}


.mt0{
  margin-top: 0px !important;
}
.mb0{
  margin-bottom: 0px !important;
}

.lh0{
  line-height: 0px !important;
}

table.produto tr td{
  font-family: Arial, sans-serif, Helvetica;
  font-size: 18px; line-height: 22px;
  text-align: left; vertical-align: top;
  padding-left: 0px;
  color: #000000;
  padding-top: 15px;
  background-color: #ffff;
}
.forma_de_pagamento{
  font-family: Arial, sans-serif, Helvetica; font-size: 20px;
  line-height: 23px; text-align: left; vertical-align: top;
  color: #000000;
}

.tdforma{
  border: solid 2px #000000; border-radius: 15px; font-family: Arial, sans-serif, Helvetica;
  font-size: 22px; line-height: 25px; text-align: left; vertical-align: middle;
  color: #000000; font-weight: bold;
}

.tdforma p ,.tdentrega p , .tdpedido p{
  margin: 0px;
}

.tdentrega p{
  font-size: 20px;
}

.texto-black{
  color: #000000;
}

.margin0{
  margin:0px;
}

.padding-1x{
  padding: 25px;
}

.padding-left1x{
  padding-left: 25px;
}

.padding-top{
  padding-top: 25px;
}

.padding-bottom{
  padding-bottom: 25px;
}

.padding-bottom2x{
  padding-bottom: 50px;
}


.nomeProduto{
  font-size: 16px !important;
}

.capitalize{
  text-transform: capitalize;
}

.tdpedido{
  font-family: Arial, sans-serif, Helvetica;
  font-size: 20px; line-height: 25px; text-align: left;
  vertical-align: top;

}
.titulo-vermelho{
  text-transform: uppercase;
  color: #f00000; font-weight: bold;
  font-size: 26px;
  line-height: 30px;
}

.font-bold{
  font-weight: bold;
}
.footer .texto1{
  color: #000000;
  margin: 0;
  font-family: verdana;
  font-size: 20px;
  text-align: center;
  font-style: normal;
  font-weight: normal;
}

.footer .texto2{
  color: #555555; margin: 15px; font-family: verdana; font-size: 15px; text-align: center; font-style: normal; font-weight: normal;
}
.footer .texto3{
  color: #555555; font-family: verdana; font-size: 11px; text-align: center; font-style: normal; font-weight: normal;
}
.footer .texto4{
  color: gray; margin: 20px; font-family: verdana; font-size: 12px; text-align: center; font-style: normal; font-weight: normal;
}

.footer a{
  text-decoration:underline; color: #DD161A; font-weight: bold;
}

.texto{
  font-family: Arial, sans-serif, Helvetica;
  font-size: 20px;
  line-height: 23px;
  text-align: left;
  vertical-align: top;
  color: #000000;
}

.bloco{
  width: 563px;
  height: 311px;
  background-color: rgb(255, 255, 255);
}

.black{
  color:#000000 !important;
}

.plink{
  font-size: 10px;
  color: #ffffff;
  font-family: verdana;
  font-size: 11px;
  text-align: center;
  font-style: normal;
  font-weight: bold;
  vertical-align: middle;
  padding: 10px 0px;"
}

.plink a{
  text-decoration: none; color:#FFC100
}


@media (max-width:767px){
  table tr td.full{
    width:100% !important;
  }
}
