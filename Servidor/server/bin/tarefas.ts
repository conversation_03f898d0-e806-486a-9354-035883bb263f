import {Tare<PERSON>s} from "../lib/Tarefas";
import {App} from '../App';
import {EnviadorDeCarrinhoAbandonadoService} from "../service/EnviadorDeCarrinhoAbandonadoService";
import {SessaoLinkSaudacao} from "../domain/SessaoLinkSaudacao";
import {Contato} from "../domain/Contato";
import {Empresa} from "../domain/Empresa";
import {TarefasDev} from "../lib/TarefasDev";
process.send = process.send || function () { return false };

const app = new App()
app.configureAmbiente();
app.configureBancoDeDados();

const enviador = EnviadorDeCarrinhoAbandonadoService.Instancia();
const sessao = new SessaoLinkSaudacao();
sessao.empresa = new Empresa();
sessao.empresa.id = 1;
sessao.empresa.nome = '<PERSON><PERSON><PERSON><PERSON>';

sessao.contato = new Contato(21046, '<PERSON><PERSON><PERSON><PERSON>', '62982301144', null, null, null, null, '55');

enviador.execute(sessao, 3);

Tarefas.inicie();

//new TarefasDev().inicie()

process.send('ready');
