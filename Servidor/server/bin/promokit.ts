import * as http from 'http';
import * as https from 'https';
import App from '../App';
import * as cluster from 'cluster';
import * as os from 'os';
import * as fs from 'fs';
import * as util from 'util';
import {Ambiente} from '../service/Ambiente';
import SocketServer from "../service/SocketService";

const serverSocket = SocketServer.getInstance(3000);

// @ts-ignore
process.send = process.send || function () { return false };

// Workers can share any TCP connection
// In this case it is an HTTP server
const port =  3100;
App.set('port', port);

let privateKey: string = fs.readFileSync('certificados/key.pem', 'utf8').toString();
let certificate: string = fs.readFileSync('certificados/server.crt', 'utf8').toString();

process.on("unhandledRejection", (reason: any, promise: any) => {
  const dominio = promise.domain;

  console.log('Promise não tratada');
  console.log(reason);

  if( dominio && dominio.contexto ) {
    dominio.contexto.release();

    const res = dominio.members[1];

    if( res && !res.headersSent ) {
      const error = getErrorObject(reason);
      const errorTrace = getErrorTrace(error);
      res.status(500).json(errorTrace);
    }
/*
    mateCore();

*/
  }
});

const credentials = {key: privateKey, cert: certificate};
const server = http.createServer(App);
const httpsServer = https.createServer(credentials, App);

if( !Ambiente.Instance.producao ) {
  httpsServer.listen(8443);
}

server.listen(port, () => {
  console.log('enviando ready');
  setTimeout( () => {
    process.send('ready');
  }, 30000);
});

console.log(`Worker ${process.pid} started`);
console.log(new Date())


function mateCore() {
  if( !Ambiente.Instance.producao ) {
    return;
  }

  console.log('Server', server.address());

  const killtimer = setTimeout(() => {
    process.exit(1);
  }, 30000);
  // But don't keep the process open just for that!
  killtimer.unref();

  // stop taking new requests.
  server.close();

  // Let the master know we're dead. This will trigger a
  // 'disconnect' in the cluster master, and then it will fork
  // a new worker.
  cluster.worker.disconnect();
}


const getErrorObject = function (e: any) {

  if (e && typeof e.stack === 'string' && typeof e.message === 'string') {
    return e;
  }

  if (e && !(e instanceof Error)) {
    return new Error(typeof e === 'string' ? e : util.inspect(e));
  }

  return e || new Error('Unknown/falsy error, this is a dummy error.');
};

const getErrorTrace = (e: any) => {
  return e && e.stack || util.inspect(e || 'no error trace available');
};

/*
const originalLog = console.log;
console.log = function(...args: any[]) {
  const timestamp = new Date().toISOString();
  const prefix = timestamp + ": ";
  Array.prototype.unshift.call(args, prefix);
  console.trace('chamou');

  return originalLog.apply(this, args);
}*/


App.use(function errorHandler(err: any, req: any, res: any, next: any) {
  console.log('error on request %s %s', req.method, req.url);
  console.log(err);

  // res.send(500, "Something bad happened. :(");
  if( !res.headersSent ) {
    const error = getErrorObject(err);
    const errorTrace = getErrorTrace(error);
    res.status(500).json(errorTrace);
  }

  if ( !Ambiente.Instance.producao ) {
    setTimeout(() => {
      process.exit(1);
    }, 50);
  }

  if ( err.domain ) {
//      mateCore();
  }
});


process.on('uncaughtException', function handleError( error: any ) {
    console.log( 'Uncaught Exception' );
    console.error( error );
});
