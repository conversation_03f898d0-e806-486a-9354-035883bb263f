<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cupom">

  <resultMap id="cupomResultMap" type="Cupom">
    <id property="id" column="cupom_id" />
    <result property="nome" column="cupom_nome" />
    <result property="codigo" column="cupom_codigo" />
    <result property="valor" column="cupom_valor" />
    <result property="percentual" column="cupom_percentual" />
    <result property="tipo" column="cupom_tipo"/>
    <result property="ativo" column="cupom_ativo"/>

    <result property="escopo" column="cupom_escopo"/>
    <result property="formaEntrega" column="cupom_forma_entrega"/>

    <result property="quantidade" column="cupom_quantidade" />
    <result property="qtdeMaxima" column="cupom_qtde_maxima" />
    <result property="valorMinimo" column="cupom_valor_minimo" />
    <result property="qtdeUtilizado" column="cupom_qtde_utilizado" />
    <result property="utilizado" column="cupom_utilizado" />
    <result property="valido" column="cupom_valido" />
    <result property="validade" column="cupom_validade" />
    <result property="restrito" column="cupom_restrito" />
    <result property="naoPontuarFidelidade" column="cupom_nao_pontuar_fidelidade" />
    <result property="primeiraCompra" column="cupom_primeira_compra" />
    <result property="aplicarNaTaxaDeEntrega" column="cupom_aplicar_na_taxa_de_entrega" />
    <result property="percentualMaximoDescontoProduto" column="cupom_percentual_maximo_desconto_produto" />
    <result property="minimoApenasNoValorProduto" column="cupom_aplicar_desconto_valor_produtos" />
    <result property="minimoApenasMesmoTamanho" column="cupom_minimo_apenas_mesmo_tamanho" />
    <result property="produtoNaoComporMinimo" column="cupom_produto_nao_compor_minimo" />
    <result property="restritoContatoPerdido" column="cupom_restrito_contato_perdido" />
    <result property="restritoAniversariantes" column="cupom_restrito_aniversariantes" />
    <result property="permitirComprarComPromocao" column="cupom_permitir_comprar_com_promocao" />
    <result property="aplicarNaFidelidade" column="cupom_aplicar_na_fidelidade" />
    <result property="mensagemMinimo" column="cupom_mensagem_minimo" />

    <result property="selecionavel" column="cupom_selecionavel" />
    <result property="listavel" column="cupom_listavel" />
    <result property="brindeResgate" column="cupom_brinde_resgate" />

    <association property="contato"     resultMap="contato.contatoRM"/>
    <association property="empresa"     resultMap="empresa.empresaSimplesRM"/>
    <collection property="produtos" resultMap="produto.produtoResultMap"/>
    <collection property="categorias" resultMap="categoria.categoriaRM"/>
    <collection property="produtosTemplateTamanho" resultMap="produtoTemplate.produtoTemplateTamanhoRM"/>
    <collection  property="horarios" resultMap="cupom.cupomHorarioRM"/>
    <collection  property="formasDePagamento" resultMap="formaDePagamento.formaDePagamentoResumoRM"/>

    <discriminator javaType="String" column="cupom_tipo" >

      <case value="valor" resultType="vendas.CupomValorFixo"></case>
      <case value="percentual" resultType="vendas.CupomPercentual"></case>

    </discriminator>

  </resultMap>

  <resultMap id="cupomHorarioRM" type="CupomHorario">
    <id property="id" column="cupom_horario_id"/>

    <result property="dia" column="cupom_horario_dia"/>
  </resultMap>

  <resultMap id="cupomBrindeRM" type="CupomHorario">
    <id property="id" column="cupom_brinde_id"/>

    <result property="nome" column="cupom_brinde_nome"/>
    <result property="codigo" column="cupom_brinde_codigo"/>
    <result property="brindeResgate" column="cupom_brinde_resgate"/>
  </resultMap>

  <resultMap id="cupomUsadoResultMap" type="Cupom">
    <id property="id" column="cupom_id" />
    <result property="nome" column="cupom_nome" />
    <result property="codigo" column="cupom_codigo" />
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="cupomResultMap" prefix="true">
    select * from cupom join empresa on empresa.id = cupom.empresa_id
    left join cupom_produto on cupom_produto.cupom_id = cupom.id
    left join produto on produto.id = cupom_produto.produto_id
    left join cupom_produto_template_tamanho on cupom_produto_template_tamanho.cupom_id = cupom.id
    left join produto_template_tamanho on produto_template_tamanho.id = cupom_produto_template_tamanho.produto_template_tamanho_id
    left join cupom_categoria on cupom_categoria.cupom_id = cupom.id
    left join categoria on categoria.id = cupom_categoria.categoria_id and categoria.removida is not true
    left join cupom_horario on cupom_horario.cupom_id = cupom.id
    left join cupom_forma_de_pagamento on cupom_forma_de_pagamento.cupom_id = cupom.id
    left join forma_de_pagamento on forma_de_pagamento.id = forma_de_pagamento_id
    where cupom.removido is not true and

    <choose>
      <when test="redechina">
        empresa.id =  650 and listavel is false
      </when>
      <otherwise>
        empresa.id = #{idEmpresa}  and  cupom.listavel is true
      </otherwise>
    </choose>
    <choose>

      <when test="codigo != null">
        and cupom.codigo = #{codigo}
      </when>

      <when test="selecionavel != null">
        and cupom.selecionavel  is true and brinde_resgate is not true
      </when>

      <when test="id != null">
        and cupom.id = #{id}
      </when>

    </choose>

  </select>

  <select id="existe" parameterType="map" resultType="long">
    select count(1)
    from cupom
    where empresa_id = #{idEmpresa} and  cupom.codigo = #{codigo}  and removido is not true
    <if test="id != null">
      and id != #{id}
    </if>

  </select>



  <select id="listeContatos" parameterType="map" resultMap="contato.contatoRM" prefix="true">
    select contato.*
    from  cupom join cupom_contatos on cupom.id = cupom_id
    join contato on contato.id = cupom_contatos.contato_id
    where  cupom.empresa_id = #{idEmpresa} and  cupom.id = #{id}

    <if test="texto">
      and (contato.nome like #{texto} or contato.telefone like #{texto} )
    </if>


    order by contato.nome limit #{inicio},  #{total}

  </select>

  <select id="listeContatosDisponiveis" parameterType="map" resultMap="contato.contatoRM" prefix="true">
    select contato.*
    from  contato
    where  contato.empresa_id = #{idEmpresa} and
    not exists ( select 1 from  cupom_contatos where cupom_id = #{id} and contato_id = contato.id)

    <if test="texto">
      and (contato.nome like #{texto} or contato.telefone like #{texto} )
    </if>

    order by contato.nome limit #{inicio},  #{total}

  </select>

  <select id="estaNaLista" parameterType="map" resultType="long">
    select count(1)
    from  cupom join cupom_contatos on cupom.id = cupom_id
    join contato on contato.id = cupom_contatos.contato_id
    where  cupom.empresa_id = #{idEmpresa} and  cupom.id = #{id} and
    <choose>
      <when test="telefone">
        contato.telefone =  #{telefone}
      </when>
      <when test="idContato">
        contato.id =  #{idContato}
      </when>

    </choose>

  </select>

  <select id="selecioneJaUsou" parameterType="map" resultType="long">
    select count(1) from cupom
       where empresa_id = #{idEmpresa} and   id = #{id} and exists (select 1 from pedido where cupom_id = #{id})

  </select>

  <select id="obtenhaQtdeUtilizado" parameterType="map" resultType="long">
    select count(1)
    from  cupom join pedido on cupom.id = cupom_id
    join contato on contato.id = pedido.contato_id
    where  cupom.id = #{id} and    4 >= pedido.status and

    <choose>
      <when test="redechina">
        cupom.empresa_id = 650 and listavel is false
      </when>
      <otherwise>
        cupom.empresa_id = #{idEmpresa}
      </otherwise>
    </choose>


    <choose>
      <when test="idContato">
        and contato.id =  #{idContato}

        <if test="ano">
           and YEAR(pedido.horario) = YEAR(CURDATE());
        </if>
      </when>

      <when test="telefone">
        and contato.telefone =  #{telefone}
          <if test="ano">
            and YEAR(pedido.horario) = YEAR(CURDATE());
          </if>
      </when>

    </choose>
  </select>

  <insert id="insira" parameterType="map">
    insert into cupom(nome, codigo, tipo, valor, percentual, primeira_compra, quantidade, qtde_maxima, valor_minimo, validade, empresa_id,
    restrito, utilizado, ativo, selecionavel, aplicar_na_taxa_de_entrega, percentual_maximo_desconto_produto,
    nao_pontuar_fidelidade, aplicar_desconto_valor_produtos, produto_nao_compor_minimo,
    restrito_contato_perdido, restrito_aniversariantes, permitir_comprar_com_promocao, minimo_apenas_mesmo_tamanho, mensagem_minimo, escopo, forma_entrega)
    values(#{nome}, #{codigo}, #{tipo}, #{valor}, #{percentual}, #{primeiraCompra}, #{quantidade}, #{qtdeMaxima},
    #{valorMinimo}, #{validade}, #{empresa.id},
    #{restrito},false, #{ativo} , #{selecionavel}, #{aplicarNaTaxaDeEntrega}, #{percentualMaximoDescontoProduto},
    #{naoPontuarFidelidade}, #{minimoApenasNoValorProduto},
    #{produtoNaoComporMinimo}, #{restritoContatoPerdido}, #{restritoAniversariantes}, #{permitirComprarComPromocao},
    #{minimoApenasMesmoTamanho}, #{mensagemMinimo}, #{escopo}, #{formaEntrega})
  </insert>

  <insert id="insiraProdutos" parameterType="map">
    insert into cupom_produto (cupom_id, produto_id)
    values
    <foreach item="item" collection="dados" open="" separator="," close="">
      (#{item.cupomId}, #{item.produtoId})
    </foreach>
  </insert>

  <update id="removaProdutos">
    delete from cupom_produto where cupom_id = #{id}
  </update>


  <insert id="insiraListaContatos">
    INSERT INTO cupom_contatos
    (cupom_id, contato_id)
    VALUES
    <foreach item="item" collection="dados" open="" separator="," close="">
      ( #{item.idCupom} ,   #{item.idContato}  )
    </foreach>
  </insert>



  <insert id="insiraHorarios" parameterType="map" >
    insert into cupom_horario (id,cupom_id,dia)
    values
    <foreach item="item" collection="dados" open="" separator="," close="">
      ( #{id}, #{item.idCupom} ,   #{item.dia}  )
    </foreach>
  </insert>

  <update id="removaHorarios">
    delete from cupom_horario where cupom_id = #{id}
  </update>

  <insert id="insiraFormasPagamento" parameterType="map" >
    insert into cupom_forma_de_pagamento ( cupom_id,forma_de_pagamento_id)
    values
    <foreach item="item" collection="dados" open="" separator="," close="">
      (  #{item.cupom} ,   #{item.forma}  )
    </foreach>
  </insert>

  <update id="removaFormasPagamento">
    delete from cupom_forma_de_pagamento where cupom_id = #{id}
  </update>

  <update id="atualizeAtivo">
    update cupom set ativo= #{ativo}
    where id = #{id} and   empresa_id = #{empresa.id}
  </update>

  <update id="atualize">
    update cupom set
    nome = #{nome},
    codigo = #{codigo},
    valor = #{valor},
    percentual = #{percentual},
    restrito = #{restrito},
    selecionavel = #{selecionavel},
    nao_pontuar_fidelidade = #{naoPontuarFidelidade},
    minimo_apenas_mesmo_tamanho = #{minimoApenasMesmoTamanho},
    primeira_compra = #{primeiraCompra},
    validade = #{validade},
    valor_minimo = #{valorMinimo},
    quantidade = #{quantidade},
    qtde_maxima = #{qtdeMaxima},
    aplicar_na_taxa_de_entrega = #{aplicarNaTaxaDeEntrega},
    percentual_maximo_desconto_produto = #{percentualMaximoDescontoProduto},
    permitir_comprar_com_promocao = #{permitirComprarComPromocao},
    produto_id = #{produto.id},
    aplicar_na_fidelidade = #{aplicarNaFidelidade},
    aplicar_desconto_valor_produtos = #{minimoApenasNoValorProduto},
    produto_nao_compor_minimo = #{produtoNaoComporMinimo},
    restrito_contato_perdido = #{restritoContatoPerdido},
    restrito_aniversariantes = #{restritoAniversariantes},
    produto_template_tamanho_id = #{produtoTemplateTamanho.id},
    mensagem_minimo = #{mensagemMinimo},
    escopo = #{escopo},
    forma_entrega = #{formaEntrega}
    where id = #{id} and   empresa_id = #{empresa.id}
  </update>


  <update id="foiUtilizado">
    update cupom set utilizado = true,
    qtde_utilizado = qtde_utilizado +1,
    valor_utilizado =  valor_utilizado + #{valor}
    where id = #{id}
  </update>

  <update id="remova">
    update cupom set removido = true
    where id = #{id} and   empresa_id = #{empresa.id}
  </update>


  <update id="removaNaListaContatos">
    delete from cupom_contatos
    where  cupom_id = #{idCupom} and  contato_id = #{idContato}
  </update>

  <update id="atualizeProduto">
    update cupom set produto_id =  #{idProduto}, brinde_resgate = true
      where id = #{id} and   empresa_id = #{empresa.id}
  </update>


  <insert id="insiraContatosPerdidos">
    insert into cupom_contatos(contato_id,cupom_id)
    select id,6250 from contato where empresa_id = 854 and status  = 'Perdido'
  </insert>


  <update id="replicarCupons">
    insert into cupom (empresa_id,nome,codigo,tipo,percentual,ativo,validade,valor_minimo, utilizado)
        select empresa.id,'CLIENTECIB15', 'CLIENTECIB15','percentual',15,true,'2023-10-17 00:00:00',60,false
         from empresa where rede_id = 1 and not exists (select 1 from cupom where empresa_id = empresa.id and codigo  = 'CLIENTECIB15');

    insert into cupom (empresa_id,nome,codigo,percentual,tipo,ativo,validade,valor_minimo,utilizado, percentual_maximo_desconto_produto,permitir_comprar_com_promocao)
    select empresa.id,'Cupom Franquia', 'LUIZAAKEMI', 15,'percentual',true,'2024-02-15 00:00:00',60,false,null,null
    from empresa where rede_id = 2 and not exists (select 1 from cupom where empresa_id = empresa.id and codigo  = 'LUIZAAKEMI');


    insert into cupom (empresa_id,nome,codigo,valor,tipo,ativo,validade,valor_minimo,utilizado)
    select empresa.id,'Cupom franquia', 'GANHEI10', 10,'valor',true,'2022-07-03 00:00:00',50,false
    from empresa where (rede_id = 3 or  nome like 'Kohala%')  and not exists (select 1 from cupom where empresa_id = empresa.id and codigo  = 'GANHEI10');

    insert into cupom(nome,codigo,tipo,percentual,validade,valor_minimo,produto_nao_compor_minimo,ativo,produto_id,empresa_id,utilizado)
    select 'Cupom Franquia', 'SHOWDOCHINA','percentual',100,'2022-09-11 00:00:00',60,null,true ,produto.id,empresa.id,false
    from produto join catalogo on catalogo_id = catalogo.id join empresa on empresa.catalogo_id = catalogo.id and rede_id = 1 where codigo_pdv = '6616' and produto.removido is not true
    and not exists (select 1 from cupom where empresa_id = empresa.id and codigo  = 'SHOWDOCHINA');

    update empresa, notificacao, notificacao notificacao_template set notificacao.mensagem = notificacao_template.mensagem
    where notificacao_template.empresa_id = 650 and notificacao_template.tipo_de_notificacao like 'Mensagem Sauda%'
    and empresa.id = notificacao.empresa_id and rede_id = 2   and notificacao.tipo_de_notificacao like 'Mensagem Sauda%';

    update banner set removida = false,
    link_imagem = 'b8e9e410-8432-11ec-a79c-3b15796b5972.png'
    where link_imagem like '37f84230-7482-11ec-b693-37362e151e42.jpeg';


    insert into cupom_horario(id,cupom_id,dia)  select concat(cupom.id,dia), cupom.id, dia from cupom_horario join cupom on codigo = 'FRETEZERO'
    where cupom_id = 11388 and not exists (select 1 from cupom_horario where cupom.id = cupom_id);


    insert into cupom(empresa_id,nome,codigo,tipo,percentual,valor_minimo,validade,quantidade,percentual_maximo_desconto_produto,listavel,ativo,utilizado)
      select 650,'Biscoito da Sorte - Cupom 30%',codigo,'percentual',30,70,'2023-08-31',1,1,false,true,true from cupom_30 where codigo != '';


    insert into cupom(empresa_id,nome,codigo,tipo,percentual,valor_minimo,validade,quantidade,percentual_maximo_desconto_produto,listavel,ativo,utilizado)
      select 650,'Biscoito da Sorte - Cupom 40%',codigo,'percentual',40,70,'2023-08-31',1,1,false,true,true from cupom_40 where codigo != '';

    insert into cupom(empresa_id,nome,codigo,tipo,percentual,valor_minimo,validade,quantidade,percentual_maximo_desconto_produto,listavel,ativo,utilizado)
     select 650,'Biscoito da Sorte - Cupom 50%',codigo,'percentual',50,80,'2023-08-31',1,1,false,true,true from cupom_50 where codigo != '';

  </update>

  <insert id="insiraCategorias" parameterType="map">
    insert into cupom_categoria (cupom_id, categoria_id)
    values
    <foreach item="item" collection="dados" open="" separator="," close="">
      (#{item.cupomId}, #{item.categoriaId})
    </foreach>
  </insert>

  <update id="removaCategorias">
    delete from cupom_categoria where cupom_id = #{id}
  </update>

  <insert id="insiraProdutosTemplateTamanho" parameterType="map">
    insert into cupom_produto_template_tamanho (cupom_id, produto_template_tamanho_id)
    values
    <foreach item="item" collection="dados" open="" separator="," close="">
      (#{item.cupomId}, #{item.tamanhoId})
    </foreach>
  </insert>


  <update id="removaProdutosTemplateTamanho">
    delete from cupom_produto_template_tamanho where cupom_id = #{id}
  </update>

  <select id="listeProdutosTemplateTamanho" parameterType="map" resultMap="produtoTemplate.produtoTemplateTamanhoRM">
    select produto_template_tamanho.*
    from cupom_produto_template_tamanho
    join produto_template_tamanho on produto_template_tamanho.id = cupom_produto_template_tamanho.produto_template_tamanho_id
    where cupom_produto_template_tamanho.cupom_id = #{id}
  </select>

  <select id="listeProdutos" parameterType="map" resultMap="produto.produtoResultMap">
    select produto.*
    from cupom_produto
    join produto on produto.id = cupom_produto.produto_id
    where cupom_produto.cupom_id = #{id}
    and produto.removido is not true
  </select>
</mapper>
