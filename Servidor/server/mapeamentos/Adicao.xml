<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="adicao">
  <resultMap id="adicaoRM" type="Adicao">
    <id property="id" column="adicao_id"/>
    <result property="numero" column="adicao_numero"/>
    <result property="sequencia" column="adicao_sequencia"/>
    <result property="codigoFabricante" column="adicao_codigo_fabricante"/>
    <result property="descontoDI" column="adicao_desconto_di"/>
    <result property="numeroDrawback" column="adicao_numero_drawback"/>
    <result property="codigoPedido" column="adicao_codigo_pedido"/>
    <result property="numeroDoItemNoPedido" column="adicao_numero_do_item_no_pedido"/>
  </resultMap>

  <create id="crieTabela" parameterType="map">
    create table if not exists adicao (
      id bigint(20) primary key,
      adicao_numero int,
      adicao_sequencia int,
      adicao_codigo_fabricante varchar(10),
      adicao_desconto_di decimal(10,2),
      adicao_numero_drawback varchar(10),
      adicao_codigo_pedido varchar(10),
      adicao_numero_do_item_no_pedido int
    );
  </create>
</mapper>
