<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="contrato">
  <resultMap id="contratoRM" type="faturamento.Contrato">
    <id property="id" column="contrato_id"/>

    <result property="diaVencimento" column="contrato_dia_vencimento"/>
    <result property="dataProximoVencimento" column="contrato_data_proximo_vencimento"/>
    <result property="dataAtivacao"  column="contrato_data_ativacao"/>
    <result property="dataFimTrial"  column="contrato_data_fim_trial"/>
    <result property="diasGratis"    column="contrato_dias_gratis"/>
    <result property="valorNegociado"    column="contrato_valor_negociado"/>
    <result property="taxaAdesao"    column="contrato_taxa_adesao"/>
    <result property="numeroParcelas"    column="contrato_numero_parcelas"/>
    <result property="limiteContatosNegociado"    column="contrato_limite_contatos_negociado"/>
    <result property="qtdeMensagensMes"    column="contrato_qtde_mensagens_mes"/>

    <result property="qtdeOperadores"    column="contrato_qtde_operadores"/>


    <association property="empresa"  resultMap="empresa.empresaRM"/>
    <association property="plano"    resultMap="planoempresarial.planoEmpresarialRM"/>
    <association property="assinatura"    resultMap="assinatura.assinaturaContratoRM"/>

    <collection  property="faturas"  resultMap="fatura.faturaDoContratoRM"/>
    <collection  property="assinaturasDependentes"   resultMap="assinatura.assinaturaDependenteRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="contratoRM"  prefix="true">
      select *
           from contrato join empresa on contrato.empresa_id = empresa.id
                         join plano_empresarial on  contrato.plano_id = plano_empresarial.id
                         left join plano_iugu on plano_iugu.plano_empresarial_id = plano_empresarial.id
                         left join fatura  on fatura.contrato_id = contrato.id and fatura.status != 3
                         left join lancamento on lancamento.fatura_id = fatura.id
                         left join log_fatura   on log_fatura.fatura_id = fatura.id
                         left join servico_cobrado on servico_cobrado.id = servico_cobrado_id
                         left join pagamento on pagamento.fatura_id = fatura.id
                           left join assinatura  on assinatura.id = contrato.assinatura_id
                         left join assinatura  assinatura_dependente on assinatura_dependente.codigo_pai = assinatura.codigo
                         left join cartao_credito on cartao_credito.id = assinatura.cartao_credito_id
            where
                 <choose>
                     <when test="id">
                       contrato.id =  #{id}
                     </when>

                     <when test="ativadosIugu">
                       contrato.assinatura_id is not null and data_ativacao is not null
                     </when>

                     <when test="idAssinatura">
                       contrato.assinatura_id =  #{idAssinatura}
                     </when>

                     <when test="codigo">
                         assinatura.codigo =  #{codigo}
                     </when>

                     <when test="idEmpresa">
                       contrato.empresa_id =  #{idEmpresa}
                     </when>

                 </choose>

              <if test="semVencimento">
                  and contrato.data_proximo_vencimento is  null
              </if>

              order by fatura.referencia desc
  </select>

  <select id="listeQuePagaParcelado" parameterType="map" resultMap="contratoRM"  prefix="true">
    select *
      from   contrato join empresa on empresa.id = contrato.empresa_id
          join plano_empresarial   on plano_id = plano_empresarial.id
          join assinatura on assinatura_id = assinatura.id and assinatura.ativa is true
         where    intervalo > 1 and data_ativacao is not null and empresa.removida is not true
  </select>

  <select id="existe" parameterType="map" resultType="int">
    select count(*) total from contrato  where empresa_id = #{idEmpresa}
  </select>

  <insert id="insira"   parameterType="map" keyProperty="id">
     insert into contrato (plano_id, empresa_id, dia_vencimento, data_ativacao, dias_gratis, valor_negociado,
              limite_contatos_negociado, data_fim_trial, taxa_adesao, numero_parcelas)
        values (#{plano.id}, #{empresa.id},  #{diaVencimento}, #{dataAtivacao}, #{diasGratis}, #{valorNegociado}, #{limiteContatosNegociado},
            #{dataFimTrial}, #{taxaAdesao}, #{numeroParcelas});
  </insert>

  <update id="atualize">
     update contrato
      set plano_id = #{plano.id},  dia_vencimento = #{diaVencimento},
          valor_negociado = #{valorNegociado}, taxa_adesao = #{taxaAdesao},
          limite_contatos_negociado = #{limiteContatosNegociado},  numero_parcelas = #{numeroParcelas}

                where id = #{id};
  </update>

  <update id="atualizePlano">
    update contrato set
     plano_id = #{plano.id}, valor_negociado = #{valorNegociado},
         numero_parcelas = #{numeroParcelas} where id = #{id}
  </update>

  <update id="atualizeDiaVencimento">
    update contrato set  dia_vencimento = #{diaVencimento} where id = #{id}
  </update>
  <update id="atualizeDataVencimento">
    update contrato set  data_proximo_vencimento = #{dataProximoVencimento} where id = #{id}
  </update>

  <update id="atualizeDataAtivacao">
    update contrato set  data_ativacao = #{dataAtivacao} where id = #{id}
  </update>
  <update id="atualizeDataFimTrial">
    update contrato set  data_fim_trial = #{dataFimTrial} where id = #{id}
  </update>

  <update id="atualizeLimiteContatos">
    update contrato set  limite_contatos_negociado = #{limiteContatosNegociado} where id = #{id}
  </update>

  <update id="atualizeValorNegociado">
    update contrato set  valor_negociado = #{valorNegociado} where id = #{id}
  </update>
  <update id="atualizeTaxaAdesao">
    update contrato set  taxa_adesao = #{taxaAdesao} where id = #{id}
  </update>

  <update id="atualizeNumeroParcelas">
    update contrato set  numero_parcelas = #{numeroParcelas} where id = #{id}
  </update>


  <update id="atualizeAssinatura">
    update contrato set  assinatura_id = #{assinatura.id} where id = #{id}
  </update>

  <select id="bloqueios_tempo_abaixo" parameterType="map" resultType="int">
    select empresa.id,data_bloqueio_auto,data_vencimento,intervalo,datediff(data_bloqueio_auto,data_vencimento)
    from empresa join contrato on contrato.empresa_id = empresa.id join
    assinatura on assinatura.id = assinatura_id join plano_empresarial plano on plano.id  = plano_id
    where  empresa.removida is not true   and assinatura.suspensa is not true  and contrato.data_ativacao  is not null and intervalo = 1 and  datediff(data_bloqueio_auto,data_vencimento) &lt; 7
    order by data_bloqueio_auto


    <!-- < 0 significa que mudaou vencimento direto iugu e nao alterou no promokit-->
    <!-- > 0 significa que por algum motivo sistema nao manteve a data sincronizada,
          possivelmente algum erro que nao processa o pagamento corretamente-->
    select empresa.id empresa,contrato.id contrato, assinatura.id assinatura,data_proximo_vencimento,
        data_vencimento,datediff(data_proximo_vencimento,data_vencimento) dias
          from contrato join plano_empresarial plano on plano.id = plano_id
                join empresa on empresa.id = contrato.empresa_id join assinatura on assinatura.id = assinatura_id
               where empresa.removida is not true  and  0 > datediff(data_proximo_vencimento,data_vencimento)    ;


    update contrato join plano_empresarial on plano_id = plano_empresarial.id set numero_parcelas = intervalo  where numero_parcelas != intervalo ;


    <!-- < 0 significa que mudaou vencimento direto iugu e nao alterou no promokit-->
    <!-- > 0 significa que por algum motivo sistema nao manteve a data sincronizada,
          possivelmente algum erro que nao processa o pagamento corretamente-->
    select empresa.id empresa,contrato.id contrato, assinatura.id assinatura,data_proximo_vencimento,
        data_vencimento,datediff(data_proximo_vencimento,data_vencimento) dias
        from contrato join plano_empresarial plano on plano.id = plano_id
              join empresa on empresa.id = contrato.empresa_id join assinatura on assinatura.id = assinatura_id
        where empresa.removida is not true  and 0 > datediff(data_proximo_vencimento,data_vencimento)

  </select>

  <update id="ativarAssinaturasFilhas">
    update contrato  join assinatura on assinatura_id  = assinatura.id join assinatura pai on pai.codigo = assinatura.codigo_pai
      set data_ativacao = pai.data_primeiro_pagamento where assinatura.codigo_pai is not null and contrato.data_ativacao is null;
  </update>


</mapper>
