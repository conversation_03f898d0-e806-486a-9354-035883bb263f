<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="servicoCobrado">
  <resultMap id="servicoCobradoRM" type="faturamento.ServicoCobrado">
    <id property="id" column="servico_cobrado_id" />

    <result property="nome" column="servico_cobrado_nome" />
    <result property="descricao" column="servico_cobrado_descricao" />
    <result property="valor" column="servico_cobrado_valor" />
    <result property="tipo" column="servico_cobrado_tipo" />


  </resultMap>


  <select id="selecione" parameterType="map" resultMap="servicoCobradoRM">
    select sc.id servico_cobrado_id,
            sc.nome servico_cobrado_nome,
            sc.descricao servico_cobrado_descricao,
            sc.valor servico_cobrado_valor,
            sc.tipo servico_cobrado_tipo
    from servico_cobrado sc
    <choose>
      <when test="id">
        where sc.id = #{id}
      </when>

      <when test="idServico">
        where sc.servico_id = #{idServico}

        <if test="tipo != null">
          and sc.tipo = #{tipo}
        </if>
      </when>

      <when test="tipo != null">
        where sc.tipo = #{tipo}

        <if test="valor != null">
          and sc.valor = #{valor}
        </if>
      </when>
    </choose>

  </select>


</mapper>
