<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="trechoDePrompt">
  <resultMap id="trechoDePromptRM" type="TrechoDePrompt">
    <id property="id" column="trecho_de_prompt_id"/>

    <result property="condicao" column="trecho_de_prompt_condicao"/>

    <result property="intent" column="_intent"/>
    <result property="texto" column="_texto"/>
    <result property="ativo" column="_ativo"/>
    <result property="modificado" column="_modificado"/>
    <result property="tipo" column="_tipo"/>
    <result property="posicao" column="_posicao"/>

    <result property="escopo" column="trecho_de_prompt_escopo"/>
    <result property="exemplosJson" column="_exemplos_json"/>

    <association property="trechoOriginal" resultMap="trechoDePromptEmpresaRM"/>
  </resultMap>

  <resultMap id="trechoDePromptEmpresaRM" type="TrechoDePrompt">
    <id property="id" column="trecho_de_prompt_id"/>

    <result property="texto" column="trecho_de_prompt_texto"/>
    <result property="tipo" column="trecho_de_prompt_tipo"/>
    <result property="exemplosJson" column="trecho_de_prompt_exemplos_json"/>
  </resultMap>

  <resultMap id="exemploDePromptRM" type="ExemploPrompt">
    <id property="id" column="exemplo_prompt_id"/>

    <result property="pergunta" column="exemplo_prompt_pergunta"/>
    <result property="resposta" column="exemplo_prompt_resposta"/>
  </resultMap>

    <select id="selecione" parameterType="map" resultMap="trechoDePromptRM" prefix="true">
    SELECT
    *, CASE
      WHEN trecho_de_prompt_empresa.id is NULL THEN trecho_de_prompt.ativo
    ELSE trecho_de_prompt_empresa.ativo
    end as ativo
    , CASE
      WHEN trecho_de_prompt_empresa.texto IS NULL THEN trecho_de_prompt.texto
      ELSE trecho_de_prompt_empresa.texto
      end as texto
    , CASE
      WHEN trecho_de_prompt_empresa.intent IS NULL THEN trecho_de_prompt.intent
      ELSE trecho_de_prompt_empresa.intent
      end as intent
    , CASE
      WHEN trecho_de_prompt_empresa.tipo IS NULL THEN trecho_de_prompt.tipo
      ELSE trecho_de_prompt_empresa.tipo
      end as tipo
    , CASE
      WHEN trecho_de_prompt_empresa.exemplos_json IS NULL THEN trecho_de_prompt.exemplos_json
      ELSE trecho_de_prompt_empresa.exemplos_json
      end as exemplos_json
    , CASE
      WHEN trecho_de_prompt_empresa.posicao IS NULL THEN trecho_de_prompt.posicao
      ELSE trecho_de_prompt_empresa.posicao
      end as posicao
    , CASE
      WHEN trecho_de_prompt_empresa.texto IS NULL THEN false
      ELSE (trecho_de_prompt_empresa.texto &lt;&gt; trecho_de_prompt.texto or trecho_de_prompt_empresa.exemplos_json &lt;&gt; trecho_de_prompt.exemplos_json)
    end as modificado
  FROM
    trecho_de_prompt
    left join (SELECT *
        FROM trecho_de_prompt_empresa
        WHERE empresa_id = #{idEmpresa}) as trecho_de_prompt_empresa ON
      (trecho_de_prompt.id = trecho_de_prompt_empresa.trecho_de_prompt_id)
      LEFT JOIN exemplo_prompt ON trecho_de_prompt.id = exemplo_prompt.trecho_de_prompt_id
    WHERE
      trecho_de_prompt.template_id = #{idTemplate}
      and (trecho_de_prompt.empresa_id = #{idEmpresa} or trecho_de_prompt.escopo = 'global')
      <if test="id != null">
        AND (trecho_de_prompt.id = #{id})
      </if>
      <if test="intent != null">
        AND trecho_de_prompt.intent #{intent}
      </if>
      ORDER BY COALESCE(trecho_de_prompt_empresa.posicao, trecho_de_prompt.posicao) ASC;
    </select>

  <insert id="insira" parameterType="TrechoDePrompt" useGeneratedKeys="true">
  INSERT INTO trecho_de_prompt (
    intent,
    texto,
    escopo,
    template_id,
    tipo,
    empresa_id,
    condicao,
    exemplos_json,
    posicao
  )
  SELECT
    #{intent},
    #{texto},
    #{escopo},
    #{template.id},
    #{tipo},
    #{empresa.id},
    #{condicao},
    #{exemplosJson},
    COALESCE(MAX(posicao), 0) + 1
  FROM trecho_de_prompt
  WHERE template_id = #{template.id};
  </insert>

  <insert id="insiraTrechoEmpresa" parameterType="TrechoDePrompt">
    INSERT INTO trecho_de_prompt_empresa(trecho_de_prompt_id, empresa_id, ativo, texto, intent, tipo, exemplos_json, posicao)
      VALUES (#{id}, #{empresa.id}, #{ativo}, #{texto}, #{intent}, tipo, #{exemplosJson}, #{posicao})
      ON DUPLICATE KEY UPDATE
        ativo = #{ativo},
        texto = #{texto},
        intent = #{intent},
        tipo = #{tipo},
        exemplos_json = #{exemplosJson},
        posicao = #{posicao};
  </insert>

  <update id="atualizeAtivarDesativar" parameterType="map">
    UPDATE trecho_de_prompt
    SET ativo = #{ativo}
    WHERE id = #{id};
  </update>

  <update id="atualize" parameterType="map">
    UPDATE trecho_de_prompt
    SET intent = #{intent},
    texto = #{texto},
    tipo = #{tipo},
    condicao = #{condicao},
    exemplos_json = #{exemplosJson},
    empresa_id = #{empresa.id},
    posicao = #{posicao}
    WHERE id = #{id}
  </update>

  <update id="atualizePosicao" parameterType="map">
    UPDATE trecho_de_prompt
    SET posicao = #{posicao}
    WHERE id = #{id}
  </update>

  <update id="atualizePosicaoEmpresa" parameterType="map">
    UPDATE trecho_de_prompt_empresa
    SET posicao = #{posicao}
    WHERE id = #{id}
  </update>

  <insert id="insiraTrechoGlobalNaEmpresa" parameterType="TrechoDePrompt">
    INSERT INTO trecho_de_prompt_empresa (trecho_de_prompt_id, empresa_id, ativo)
    SELECT tdp.id, e.id, true
    FROM trecho_de_prompt tdp
    CROSS JOIN empresa e
    WHERE tdp.id = #{id} and tdp.escopo = 'global' AND (e.removida = false OR e.removida IS NULL);
  </insert>

  <insert id="insiraExemplos">
    INSERT INTO exemplo_prompt
    (empresa_id, trecho_de_prompt_id, pergunta, resposta)
    VALUES
    <foreach item="item" collection="exemplos" open="" separator="," close="">
      ( #{empresa.id}, #{idTrechoDePrompt}, #{item.pergunta}, #{item.resposta} )
    </foreach>
  </insert>

  <delete id="removaExemplos">
    DELETE FROM exemplo_prompt
    WHERE
    empresa_id = #{empresa.id}
    and trecho_de_prompt_id = #{id};
  </delete>
</mapper>
