<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="opcaoNaEmpresa">

  <resultMap id="opcaoNaEmpresaRM" type="OpcaoNaEmpresa">
    <id property="id" column="opcao_na_empresa_id"/>

    <result property="valor" column="opcao_na_empresa_valor"/>
    <result property="disponivel" column="opcao_na_empresa_disponivel"/>
    <association property="opcao"   resultMap="opcaoDeAdicionalDeProduto.opcaoDeAdicionalDeProdutoRM"/>
    <association property="empresa"   resultMap="empresa.empresaRM"/>
  </resultMap>

  <resultMap id="opcaoNaEmpresaDaOpcaoRM" type="OpcaoNaEmpresa">
    <id property="id" column="opcao_na_empresa_id"/>

    <result property="valor" column="opcao_na_empresa_valor"/>
    <result property="disponivel" column="opcao_na_empresa_disponivel"/>

  </resultMap>

  <resultMap id="opcaoNaEmpresaCampoRM" type="OpcaoNaEmpresa">
    <id property="id" column="naempresa_id"/>

    <result property="valor" column="naempresa_valor"/>
    <result property="disponivel" column="naempresa_disponivel"/>
  </resultMap>


  <insert id="insira" parameterType="map" useGeneratedKeys="true" >

    insert into opcao_na_empresa(opcao_id, empresa_id, valor, disponivel)
    values (#{opcao.id}, #{empresa.id}, #{valor}, #{disponivel})
    ON DUPLICATE KEY     UPDATE valor = #{valor}, disponivel = #{disponivel}
  </insert>

  <insert id="insiraDisponibilidades" parameterType="map" useGeneratedKeys="true" >
    insert into opcao_na_empresa(opcao_id, empresa_id, valor, disponivel)
    values
    <foreach item="dado" collection="dados" open="" separator="," close="">
      (#{dado.opcao.id}, #{dado.empresa.id}, #{dado.valor},  #{dado.disponivel})
    </foreach>
  </insert>


  <update id="atualizeDisponibilidades">
    update opcao_na_empresa
    set
    disponivel = #{disponivel}
    where  id in

    <foreach item="id" collection="ids" open="( " separator="," close=")">
      #{id}
    </foreach>
    and empresa_id = #{empresa.id}
  </update>


</mapper>
