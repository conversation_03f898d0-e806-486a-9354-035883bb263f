<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="promocaoAplicada">
  <resultMap id="promocaoAplicadaRM" type="PromocaoAplicada">
    <id property="id" column="promocao_aplicada_id"/>

    <result property="desconto" column="promocao_aplicada_desconto" />

    <association  property="pedido" resultMap="pedido.pedidoRM"/>
    <association  property="promocao" resultMap="promocao.promocaoRM"/>
  </resultMap>

  <update id="removaTodasDoPedido">
    delete from promocao_aplicada where pedido_id = #{id} and empresa_id = #{empresa.id};
  </update>

</mapper>
