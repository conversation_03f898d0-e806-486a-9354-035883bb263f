<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mensagemWhatsapp">
  <resultMap id="mensagemWhatsappRM" type="MensagemWhatsapp">
    <id property="id" column="mensagem_whatsapp_id"/>

    <result property="nome" column="mensagem_whatsapp_nome"/>
    <result property="mensagem" column="mensagem_whatsapp_mensagem"/>
    <result property="telefone" column="mensagem_whatsapp_telefone"/>
    <result property="resposta" column="mensagem_whatsapp_resposta"/>
    <result property="horario" column="mensagem_whatsapp_horario"/>
    <result property="horarioModificacao" column="mensagem_whatsapp_horario_modificacao"/>
  </resultMap>

  <resultMap id="conversaBotRM" type="ConversaBot">
    <id property="id" column="telefone"/>

    <result property="telefone" column="telefone"/>

    <result property="ultimaMensagem" column="ultima_mensagem"/>

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="mensagemBotRM" prefix="true">
    select *
    from mensagem_bot
    where
    mensagem_bot.empresa_id = #{idEmpresa}
    <if test="id != null">
      and mensagem_bot.id = #{id}
    </if>
    <if test="telefone != null">
      and mensagem_bot.telefone = #{telefone}
    </if>

    <choose>
      <when test="inicio != null">
        order by mensagem_bot.id limit #{inicio},#{total}
      </when>
    </choose>
  </select>

  <select id="ultimasConversas" parameterType="map" resultMap="conversaBotRM">
    select distinct telefone, mensagem ultima_mensagem from mensagem_bot
    where empresa_id = #{idEmpresa}
    limit 50;
  </select>
</mapper>
