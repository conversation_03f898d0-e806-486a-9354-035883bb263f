<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tipoDeTributacaoICMS">

    <resultMap id="tipoDeTributacaoICMSRM" type="TipoDeTributacaoICMS">
      <id property="id" column="tipo_de_tributacao_icms_id"/>
      <result property="codigo" column="tipo_de_tributacao_icms_codigo"/>
      <result property="descricao" column="tipo_de_tributacao_icms_descricao"/>
      <result property="simplesNacional" column="tipo_de_tributacao_icms_simples_nacional"/>
    </resultMap>

    <select id="selecione" parameterType="map" resultMap="tipoDeTributacaoICMSRM" prefix="true">
      select * from tipo_de_tributacao_icms
      where 1 = 1
      <if test="id">
        and id = #{id}
      </if>
      <if test="codigo">
        and codigo = #{codigo}
      </if>
      <if test="simplesNacional">
        and simples_nacional is true
      </if>
      <if test="regimeNormal">
        and simples_nacional is not true
      </if>

    </select>
</mapper>
