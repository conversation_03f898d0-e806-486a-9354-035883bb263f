<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mensagemWhatsappWeb">
  <resultMap id="mensagemWhatsappWebRM" type="MensagemWhatsappWeb">
    <id property="id" column="mensagem_whatsapp_web_id"/>
    <result property="idWhatsapp" column="mensagem_whatsapp_web_id_whatsapp"/>
    <result property="idDestinatario" column="mensagem_whatsapp_web_id_destinatario"/>
    <result property="idRemetente" column="mensagem_whatsapp_web_id_remetente"/>
    <result property="idChat" column="mensagem_whatsapp_web_id_chat"/>
    <result property="nomeChat" column="mensagem_whatsapp_web_nome_chat"/>
    <result property="nomeContato" column="mensagem_whatsapp_web_nome_contato"/>
    <result property="conteudo" column="mensagem_whatsapp_web_conteudo"/>
    <result property="timestamp" column="mensagem_whatsapp_web_timestamp"/>
    <result property="tipo" column="mensagem_whatsapp_web_tipo"/>
  </resultMap>

  <resultMap id="conversaWhatsappWebRM" type="ConversaBot">
    <id property="id" column="id_chat"/>
    <result property="idChat" column="id_chat"/>
    <result property="nome" column="nome_chat"/>
    <result property="telefone" column="id_chat"/>
    <result property="ultimaMensagem" column="ultima_mensagem"/>
    <result property="timestampWpp" column="timestampWpp"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="mensagemWhatsappWebRM" prefix="true">
    SELECT *
    FROM mensagem_whatsapp_web
    WHERE
    mensagem_whatsapp_web.empresa_id = #{idEmpresa}
    <if test="id != null">
      AND mensagem_whatsapp_web.id = #{id}
    </if>
    <if test="id_chat != null">
      AND mensagem_whatsapp_web.id_chat = #{id_chat}
    </if>
    <choose>
      <when test="inicio != null">
        ORDER BY mensagem_whatsapp_web.timestamp DESC LIMIT #{inicio},#{total}
      </when>
    </choose>
  </select>

  <select id="ultimasConversas" parameterType="map" resultMap="conversaWhatsappWebRM">
    SELECT
    DISTINCT mensagem_whatsapp_web.nome_chat, mensagem_whatsapp_web.id_chat,  mensagem_whatsapp_web.conteudo AS ultima_mensagem,
    mensagem_whatsapp_web.timestamp AS timestampWpp
    FROM
    mensagem_whatsapp_web
    INNER JOIN
    (SELECT id_chat, MAX(timestamp) AS ultimo_timestamp FROM mensagem_whatsapp_web
    WHERE
    empresa_id = #{idEmpresa} GROUP BY id_chat) m2
    ON mensagem_whatsapp_web.id_chat = m2.id_chat AND mensagem_whatsapp_web.timestamp = m2.ultimo_timestamp
    WHERE mensagem_whatsapp_web.empresa_id = #{idEmpresa}
    ORDER BY
    mensagem_whatsapp_web.timestamp DESC;
  </select>
</mapper>
