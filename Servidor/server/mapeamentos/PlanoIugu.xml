<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="planoIugu">
  <resultMap id="planoIuguRM" type="PlanoIugu">
    <id property="id" column="plano_iugu_id"/>

    <result property="codigo" column="plano_iugu_codigo"/>
    <result property="token" column="plano_iugu_token"/>
    <result property="identificador" column="plano_iugu_identificador"/>
    <result property="dataCriacao" column="plano_iugu_data_criacao"/>
    <result property="ativo" column="plano_iugu_ativo"/>

  </resultMap>


  <select id="selecione" parameterType="map" resultMap="planoIuguRM" prefix="true">
    select plano_iugu.*
          from  plano_iugu where id = #{id}
  </select>


</mapper>
