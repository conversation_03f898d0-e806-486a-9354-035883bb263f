<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="numeroWhatsapp">
  <resultMap id="numeroWhatsappRM" type="NumeroWhatsapp">
    <id property="id" column="numero_whatsapp_id"/>

    <result property="whatsapp" column="numero_whatsapp_whatsapp"/>
    <result property="principal" column="numero_whatsapp_principal"/>

    <result property="removido" column="numero_whatsapp_removido"/>
    <result property="ocultar" column="numero_whatsapp_ocultar"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="numeroWhatsappRM" prefix="true">
    select *
    from
    numero_whatsapp
    where empresa_id = #{idEmpresa}
    <if test="id != null">
      and id = #{id}
    </if>
    <if test="whatsapp != null">
      and whatsapp = #{whatsapp}
    </if>
    <if test="removidos == null">
      and removido is false
    </if>
  </select>

  <update id="atualize"  parameterType="map">
    update numero_whatsapp
    set whatsapp = #{whatsapp}, principal = #{principal},
    removido = #{removido},
    ocultar = #{ocultar}
    where id = #{id}
    and empresa_id = #{empresa.id};
  </update>

  <update id="marquePrincipal"  parameterType="map">
    update numero_whatsapp set principal = false where empresa_id = #{empresa.id};
    update numero_whatsapp set principal = true where
      id = #{id}
      and empresa_id = #{empresa.id};
  </update>

  <update id="marqueRemovido"  parameterType="map">
    update numero_whatsapp
    set removido = true
    where id = #{id}
    and empresa_id = #{empresa.id};
  </update>
</mapper>
