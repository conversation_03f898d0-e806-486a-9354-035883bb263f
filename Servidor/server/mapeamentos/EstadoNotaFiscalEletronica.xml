<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="estadoNotaFiscalEletronica">
  <resultMap id="estadoNotaFiscalEletronicaRM" type="EstadoNotaFiscalEletronica">
    <id property="id" column="estado_nfe_id"/>
    <result property="estado" column="estado_nfe_estado"/>
    <result property="horario" column="estado_nfe_horario"/>

    <association property="notaFiscalEletronica" column="nota_fiscal_eletronica_id" resultMap="notaFiscalEletronica.notaFiscalEletronicaRM"/>
  </resultMap>

  <create id="crieTabela" parameterType="map">
    create table if not exists estado_nfe (
      id bigint(20) primary key,
      estado varchar(2),
      horario datetime,
      nota_fiscal_eletronica_id bigint(20),

      foreign key (nota_fiscal_eletronica_id) references nota_fiscal_eletronica (id)
    );
  </create>
</mapper>
