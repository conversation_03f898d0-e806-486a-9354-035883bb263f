<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="temaPersonalizado">
  <resultMap id="temaPersonalizadoRM" type="TemaPersonalizado">
    <id property="id" column="tema_personalizado_id"/>

    <result property="nome" column="tema_personalizado_nome"/>
    <result property="corFundo" column="tema_personalizado_cor_fundo"/>
    <result property="corTextoFundo" column="tema_personalizado_cor_texto_fundo"/>
    <result property="corBotao" column="tema_personalizado_cor_botao"/>
    <result property="corTextoBotao" column="tema_personalizado_cor_texto_botao"/>
    <result property="corPrecoAdicional" column="tema_personalizado_cor_preco_adicional"/>
    <result property="corTextoPrecoAdicional" column="tema_personalizado_cor_texto_preco_adicional"/>
    <result property="corTextoPrimaria" column="tema_personalizado_cor_texto_primaria"/>
    <result property="corTextoSecundaria" column="tema_personalizado_cor_texto_secundaria"/>
    <result property="corFundoDoSite" column="tema_personalizado_cor_fundo_site"/>
    <result property="corFundoElementos" column="tema_personalizado_cor_fundo_elementos"/>
    <result property="corBorda" column="tema_personalizado_cor_borda"/>
    <result property="corPreco" column="tema_personalizado_cor_preco"/>
    <result property="corDestaque" column="tema_personalizado_cor_destaque"/>
    <result property="corHover" column="tema_personalizado_cor_hover"/>
    <result property="corTextoTopo" column="tema_personalizado_cor_texto_topo"/>
    <result property="corTextoRodape" column="tema_personalizado_cor_texto_rodape"/>
    <result property="corFundoRodape" column="tema_personalizado_cor_fundo_rodape"/>
    <result property="corBordaRodape" column="tema_personalizado_cor_borda_rodape"/>
    <result property="corItemAtivoRodape" column="tema_personalizado_cor_item_ativo_rodape"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="temaPersonalizadoRM" prefix="true">
      select tema_personalizado.*
         from tema_personalizado
      where tema_personalizado.empresa_id = #{idEmpresa}
       <if test="id"> and tema_personalizado.id = #{id} </if>
       <if test="ativo"> and tema_personalizado.ativo is true </if>
  </select>

  <insert id="insira" parameterType="map" prefix="true">
    insert into tema_personalizado(
      nome, cor_fundo, cor_texto_fundo, cor_botao, cor_texto_botao,
      cor_texto_secundaria, cor_texto_primaria, cor_fundo_site, empresa_id,
      cor_preco_adicional, cor_texto_preco_adicional, cor_fundo_elementos, cor_borda,
      cor_preco, cor_destaque, cor_hover, cor_texto_topo, cor_texto_rodape, cor_fundo_rodape, cor_borda_rodape, cor_item_ativo_rodape)
    values(
      #{nome}, #{corFundo}, #{corTextoFundo}, #{corBotao}, #{corTextoBotao},
      #{corTextoSecundaria}, #{corTextoPrimaria}, #{corFundoDoSite}, #{empresa.id},
      #{corPrecoAdicional}, #{corTextoPrecoAdicional}, #{corFundoElementos}, #{corBorda},
      #{corPreco}, #{corDestaque}, #{corHover}, #{corTextoTopo}, #{corTextoRodape}, #{corFundoRodape}, #{corBordaRodape}, #{corItemAtivoRodape})
  </insert>

  <update id="atualize" parameterType="map" resultMap="temaPersonalizadoRM" prefix="true">
    update tema_personalizado
      set nome = #{nome},
          cor_fundo = #{corFundo},
          cor_texto_fundo = #{corTextoFundo},
          cor_botao = #{corBotao},
          cor_texto_botao = #{corTextoBotao},
          cor_texto_secundaria = #{corTextoSecundaria},
          cor_texto_primaria = #{corTextoPrimaria},
          cor_fundo_site = #{corFundoDoSite},
          cor_preco_adicional = #{corPrecoAdicional},
          cor_texto_preco_adicional = #{corTextoPrecoAdicional},
          cor_fundo_elementos = #{corFundoElementos},
          cor_borda = #{corBorda},
          cor_preco = #{corPreco},
          cor_destaque = #{corDestaque},
          cor_hover = #{corHover},
          cor_texto_topo = #{corTextoTopo},
          cor_texto_rodape = #{corTextoRodape},
          cor_fundo_rodape = #{corFundoRodape},
          cor_borda_rodape = #{corBordaRodape},
          cor_item_ativo_rodape = #{corItemAtivoRodape}
      where empresa_id = #{empresa.id} and id = #{id};
  </update>
</mapper>
