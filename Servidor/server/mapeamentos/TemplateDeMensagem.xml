<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="templateDeMensagem">
  <resultMap id="templateDeMensagemRM" type="TemplateDeMensagem">
    <id property="id" column="template_de_mensagem_id"/>

    <result property="nome" column="template_de_mensagem_nome"/>
    <result property="mensagem" column="template_de_mensagem_mensagem"/>

    <association property="rede" resultMap="rede.redeRM"/>
  </resultMap>

  <select id ="selecioneEmpresasQueUsamTemplate" parameterType="map" resultMap="empresa.dtoEmpresaRM" prefix="true">
    select distinct empresa.* from template_de_mensagem join empresa on template_de_mensagem.rede_id =
    empresa.rede_id join notificacao
      on notificacao.empresa_id = empresa.id
    where empresa.rede_id = #{idRede}
    and template_de_mensagem.id = #{idTemplate}
    and notificacao.mensagem like #{contemNomeTemplate}
    <if test="nome">
      and empresa.nome like #{nome}
    </if>
    order by empresa.nome
  </select>


  <select id="selecione" parameterType="map" resultMap="templateDeMensagemRM" prefix="true">
    select *
    from template_de_mensagem left join rede on rede.id = template_de_mensagem.rede_id
    where
    <if test="idEmpresa">
      template_de_mensagem.empresa_id = #{idEmpresa}
    </if>
    <if test="idRede">
      rede_id = #{idRede}
    </if>

    <if test="id">
      and template_de_mensagem.id = #{id}
    </if>
    <if test="nome">
      and template_de_mensagem.nome = #{nome}
    </if>
  </select>

  <select id="selecioneDaEmpresaOuDaRede" parameterType="map" resultMap="templateDeMensagemRM" prefix="true">
    select *
    from template_de_mensagem left join rede on rede.id = template_de_mensagem.rede_id
    where
    (
      template_de_mensagem.empresa_id = #{idEmpresa}
    or
      rede_id = #{idRede}
    )
    <if test="nome">
      and template_de_mensagem.nome = #{nome}
    </if>

  </select>


  <update id="atualize" parameterType="TemplateDeMensagem" keyProperty="id">
    update template_de_mensagem set nome = #{nome}, mensagem = #{mensagem}
    where    id = #{id}
    <if test="empresa.id">
    and empresa_id = #{empresa.id};
    </if>
  </update>
</mapper>
