<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="comanda">
  <resultMap id="comandaRM" type="Comanda">
    <id property="id" column="comanda_id" />

    <result property="horarioAbertura" column="comanda_horario_abertura" />
    <result property="horarioFechamento" column="comanda_horario_fechamento" />
    <result property="horarioAtualizacao" column="comanda_horario_atualizacao" />
    <result property="valor" column="comanda_valor" />
    <result property="desconto" column="comanda_desconto" />
    <result property="status" column="comanda_status" />
    <result property="referenciaExterna" column="comanda_referencia_externa" />
    <result property="cobrarTaxaServico" column="comanda_cobrar_taxa_servico" />
    <result property="taxaServico" column="comanda_taxa_servico" />
    <result property="totalComTaxa" column="comanda_total_com_taxa"/>
    <result property="pontosGanhos" column="comanda_pontos_ganhos"/>
    <result property="codigoPdv" column="comanda_codigo_pdv"/>

    <association property="empresa" resultMap="empresa.empresaRM"/>

    <association property="mesa" resultMap="mesa.mesaRM"/>
    <association property="cartaoCliente"     resultMap="cartaoCliente.cartaoClienteRM"/>
    <association property="garcom" columnPrefix="comanda_" resultMap="usuario.garcomResultMap"/>

    <association property="contato"  columnPrefix="comanda_"   resultMap="contato.contatoRM"/>

    <collection  property="pagamentos" resultMap="pagamentoComanda.pagamentoComandaRM"/>

    <collection  property="pedidos"   resultMap="pedido.pedidoRM"/>
  </resultMap>

  <resultMap id="mesaComComandaRM" type="Mesa">
    <id property="id" column="mesa_id"/>
    <result property="nome" column="mesa_nome"/>
    <result property="codigoPdv" column="mesa_codigo_pdv"/>
    <result property="disponivel" column="disponivel"/>
    <result property="valorTotal" column="valor_total"/>
    <result property="tempo" column="tempo_mesa"/>
    <result property="naoGerarComanda" column="mesa_nao_gerar_comanda"/>
    <result property="somenteLeitura" column="mesa_somente_leitura"/>

    <collection  property="comandas"  resultMap="comanda.comandaRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="comandaRM" prefix="true">
    select  *  from comanda
      inner join mesa on (comanda.mesa_id = mesa.id)
      left join contato comanda_contato on comanda_contato.id = comanda.contato_id
      left join usuario garcom on garcom.id = comanda.garcom_id
      left join pagamento_comanda   on comanda_id = comanda.id
      left join forma_de_pagamento comanda_forma_de_pagamento on (comanda_forma_de_pagamento.id = forma_de_pagamento_id)
      left join cartao_cliente on cartao_cliente.id = cartao_cliente_id
          where comanda.empresa_id = #{idEmpresa}
    <if test="id != null">
        and comanda.id = #{id}
    </if>
    <if test="idMesa != null">
      and comanda.mesa_id = #{idMesa}
    </if>
    <if test="status != null">
      and comanda.status = #{status}
    </if>

     <if test="naoFechada">
      and comanda.status in ('Aberta', 'EmFechamento')
     </if>

    <if test="idGarcom != null">
      and comanda.garcom_id = #{idGarcom}
    </if>
  </select>

  <select id="selecioneComPedidos"   resultMap="comandaRM" prefix="true">
    select  comanda.*, comanda_contato.*, pedido.*, contato.*, cartao.*, plano.*, tipo_de_pontuacao.*,
            endereco.*, cidade.*, estado.*, forma_de_entrega.*,
            empresa.*, config_impressao.*, impressora.*, resumido_impressora.*, operador.*,
            garcom.*, comanda_garcom.*, comanda_forma_de_pagamento.*, bandeira_cartao_integrada.*,
            brinde_resgatado.*,brinde.*, mesa.*, cupom.*, numero_whatsapp.*, config_meio_pagamento.*,
            pagamento_comanda.*, cartao_cliente.*
    from comanda
                 join empresa on empresa.id = comanda.empresa_id
                 left join integracao_delivery on integracao_delivery.empresa_id  = empresa.id
                 left join contato  comanda_contato on comanda_contato.id = comanda.contato_id
                 left join numero_whatsapp on numero_whatsapp.empresa_id = empresa.id and numero_whatsapp.principal is true
                 left join mesa on mesa.id = comanda.mesa_id
                 left join usuario comanda_garcom on comanda_garcom.id = comanda.garcom_id
                 left join pedido on comanda.id = pedido.comanda_id
                     left join contato on contato.id = pedido.contato_id
                     left join cartao on cartao.contato_id = contato.id
                     left join plano on plano.id = cartao.plano_id
                     left join tipo_de_pontuacao on tipo_de_pontuacao.id = plano.id_tipo_de_pontuacao
                     left join usuario operador on operador.id = pedido.operador_id
                     left join usuario garcom on garcom.id = pedido.garcom_id
                     left join config_impressao on config_impressao.id = empresa.config_impressao_id
                     left join impressora on impressora.id = config_impressao.impressora_id
                     left join impressora resumido_impressora on resumido_impressora.id = config_impressao.impressora_resumido_id
                     left join endereco on endereco.id = pedido.endereco_id
                     left join cidade on cidade.id = cidade_id
                     left join estado on estado.id = estado_id
                      left join pagamento_comanda   on pagamento_comanda.comanda_id = comanda.id and pagamento_comanda.status = 1
                      left join forma_de_pagamento comanda_forma_de_pagamento on (comanda_forma_de_pagamento.id = pagamento_comanda.forma_de_pagamento_id)
                     left join config_meio_pagamento on (config_meio_pagamento.id = comanda_forma_de_pagamento.config_meio_de_pagamento_id)
                     left join bandeira_cartao_integrada_nova as bandeira_cartao_integrada on bandeira_cartao_integrada.forma_de_pagamento_id = comanda_forma_de_pagamento.id
                     left join forma_de_entrega on forma_de_entrega.id = forma_de_entrega_id
                     left join brinde_resgatado on brinde_resgatado.pedido_id = pedido.id and brinde_resgatado.removido is not true
                     left join brinde on brinde.id = id_brinde
                     left join cupom on cupom.id = pedido.cupom_id
                     left join cartao_cliente on cartao_cliente.id = cartao_cliente_id

        where

            <choose>
              <when test="codigoExterno">
                pedido.referencia_externa = #{codigoExterno}
              </when>
              <otherwise>
                empresa.id= #{idEmpresa}

              </otherwise>
            </choose>

            <if test="idComanda != null">
              and pedido.comanda_id = #{idComanda}
            </if>

            <if test="idContato">
                 and contato.id = #{idContato}
            </if>

            <if test="q">
              and (contato.nome like #{q} or contato.telefone like #{q} or pedido.codigo like #{q})
            </if>

            <if test="idMesa">
              and comanda.mesa_id = #{idMesa}
            </if>

            <if test="cartaoId">
              and cartao_cliente.id = #{cartaoId}
            </if>

            <if test="id">
              and comanda.id = #{id}
            </if>

            <if test="referenciaExterna">
                  and pedido.referencia_externa = #{referenciaExterna}
            </if>

            <if test="idUltimo">
              and pedido.id    > #{idUltimo}
            </if>

            <if test="ultimaAtualizacao">
               and pedido.horario_atualizacao    > #{ultimaAtualizacao}
             </if>

            <if test="codigo">
              and pedido.codigo = #{codigo}
            </if>

            <if test="codigoUltimo">
              and pedido.codigo > #{codigoUltimo}
            </if>

            <if test="status">
              and comanda.status = #{status}
            </if>

            <if test="emAberto">
              and (pedido.status &lt;= 3  or (pedido.pago is not true and pedido.status = 4))
            </if>

              <if test="naoFechada">
                and comanda.status in ('Aberta', 'EmFechamento')
              </if>

            <if test="pagos">
              and pedido.pago is   true
            </if>

            <if test="encerrado">
              and (pedido.status > 3 and pedido.pago is   true)
            </if>

             <if test="naoPagos">
              and pago is not true and 4 >= pedido.status
              </if>
             <if test="pagosENaoPagos != null">
              and 4 >= pedido.status
             </if>

            <if test="foiCancelado">

              and pedido.status > 4
            </if>
            <if test="dataInicio">
              and pedido.horario_atualizacao >= #{dataInicio}
            </if>

            <if test="dataFim">
              and pedido.horario_atualizacao  &lt;=  #{dataFim}
            </if>

            <if test="origens">
              and pedido.origem in
                  <foreach item="origem" collection="origens" open="(" separator="," close=")">
                     #{origem}
                  </foreach>
            </if>

            <if test="idsFormasPagamento">
              and forma_de_pagamento.id  in

              <foreach item="idFormaPagamento" collection="idsFormasPagamento" open="(" separator="," close=")">
                #{idFormaPagamento}
              </foreach>

            </if>

          <if test="idsCupons">
            and cupom_id  in
            <foreach item="idCupom" collection="idsCupons" open="(" separator="," close=")">
              #{idCupom}
            </foreach>
          </if>

           <if test="idFormaEntrega ">
              and pedido.forma_de_entrega_id  = #{idFormaEntrega}
           </if>

            <if test="delivery ">
              and pedido.mesa_id is null
           </if>

            <if test="mesas ">
              and pedido.mesa_id is not null
            </if>

            <if test="comandaAberta">
              and comanda.status  in ( 'Aberta', 'EmFechamento')
            </if>

            <if test="filtroPedidos">
              and pedido.comanda_id is null
            </if>

           <if test="orderBy">
             order by pedido.id
           </if>

            <if test="orderByDesc">
              order by pedido.id desc
            </if>

            <if test="inicio != null">
               limit #{inicio}, #{total}
            </if>

            <if test="forUpdate">
              FOR UPDATE
            </if>
  </select>

  <insert id="insira">
      insert into comanda(valor,status,mesa_id,empresa_id,contato_id,horario_abertura,horario_atualizacao,referencia_externa, cobrar_taxa_servico,
                        taxa_servico,total_com_taxa,garcom_id, codigo_pdv, cartao_cliente_id)
        values (#{valor},#{status},#{mesa.id},#{empresa.id},#{contato.id},#{horarioAbertura},#{horarioAtualizacao},#{referenciaExterna},
              #{cobrarTaxaServico},#{taxaServico},#{totalComTaxa},#{garcom.id}, #{codigoPdv}, #{cartaoCliente.id})
  </insert>

  <update id="atualize">
    update comanda set valor = #{valor}, status = #{status}, mesa_id = #{mesa.id}, empresa_id = #{empresa.id},
                      contato_id = #{contato.id}, horario_atualizacao = #{horarioAtualizacao}, referencia_externa = #{referenciaExterna},
                      cobrar_taxa_servico = #{cobrarTaxaServico}, taxa_servico = #{taxaServico}, total_com_taxa = #{totalComTaxa},
                      garcom_id = #{garcom.id}, codigo_pdv = #{codigoPdv}
      where id = #{id} and empresa_id = #{empresa.id}
  </update>

  <update id="atualizeStatus">
    update comanda set status = #{status}
        where id = #{id} and   empresa_id = #{empresa.id}

  </update>


  <update id="atualizeGarcom">
  update comanda set
       garcom_id = #{garcom.id}
    where id = #{id} and   empresa_id = #{empresa.id}

  </update>

  <update id="atualizeCartaoCliente">
    update comanda set
    cartao_cliente_id = #{cartaoCliente.id}
    where id = #{id} and   empresa_id = #{empresa.id}

  </update>

  <update id="atualizeContato">
    update comanda set
         contato_id = #{contato.id}
      where id = #{id} and   empresa_id = #{empresa.id}

  </update>
  <update id="atualizeCodigoPdvComanda">
  update comanda set
       codigo_pdv = #{codigoPdv}
    where id = #{id} and   empresa_id = #{empresa.id}

  </update>

  <update id="atualizeReferenciaExterna">
    update comanda set
    referencia_externa =  #{referenciaExterna}
    where id = #{id} and   empresa_id = #{empresa.id}
  </update>

  <update id="troqueMesa">
    update comanda set mesa_id = #{novaMesa.id}
      where id = #{comanda.id} and   empresa_id = #{empresa.id};
    update pedido set mesa_id = #{novaMesa.id}
      where comanda_id = #{comanda.id} and   empresa_id = #{empresa.id};
  </update>

  <update id="atualizeValorComanda">
    update comanda
        set valor = (select coalesce(sum(valor), 0) from pedido where comanda_id = #{comanda.id} and pedido.status &lt;= 4) - comanda.desconto
       where id = #{comanda.id} and empresa_id = #{empresa.id};


    update comanda join empresa on empresa_id = empresa.id
       set taxa_servico = if(empresa.cobrar_taxa_servico ,  comanda.valor * (empresa.valor_taxa_servico  / 100) , 0)
             where comanda.id = #{comanda.id} and empresa_id = #{empresa.id};

    update comanda
     set total_com_taxa = valor + taxa_servico
        where id = #{comanda.id} and empresa_id = #{empresa.id};
  </update>

  <update id="atualizeValorDesconto">
    update comanda set desconto =  #{desconto}
      where id = #{id} and empresa_id = #{empresa.id};
  </update>

  <update id="atualizeComoLida">
    update pedido set visualizado = true
      where comanda_id = #{id} and empresa_id = #{empresa.id};
  </update>

  <update id="remova">
    update cupom set removido = true
          where id = #{id} and   empresa_id = #{empresa.id}
  </update>


  <update id="removaNaListaContatos">
    delete from cupom_contatos
          where  cupom_id = #{idCupom} and  contato_id = #{idContato}
  </update>

  <select id="obtenhaComandas" resultMap="comandaRM">
    SELECT *
    FROM comanda
    INNER JOIN mesa m ON m.id = c.mesa_id
    WHERE c.mesa_id = #{idMesa}
    AND c.empresa_id = #{idEmpresa}
    AND c.status = #{status}
    ORDER BY c.horario_abertura DESC
  </select>

  <select id="obtenhaMesasEComandas" resultMap="mesaComComandaRM">
    SELECT
      m.id as mesa_id,
      m.nome as mesa_nome,
      m.codigo_pdv as mesa_codigo_pdv,
      m.nao_gerar_comanda as mesa_nao_gerar_comanda,
      m.somente_leitura as mesa_somente_leitura,
      c.id as comanda_id,
      c.valor as comanda_valor,
      c.codigo_pdv as comanda_codigo_pdv,
      c.horario_abertura as comanda_horario_abertura,
      c.status as comanda_status,
      c.garcom_id as comanda_garcom_id,
      CASE
        WHEN COUNT(c.id) OVER (PARTITION BY m.id) > 0 THEN false
        ELSE true
      END as disponivel,
      COALESCE(SUM(c.valor) OVER (PARTITION BY m.id), 0) as valor_total,
      COALESCE(
        TIMESTAMPDIFF(HOUR, MIN(c.horario_abertura) OVER (PARTITION BY m.id), NOW()),
        0
      ) as tempo_mesa
    FROM mesa m
    LEFT JOIN comanda c ON c.mesa_id = m.id
      AND c.status = 'ABERTA'
      AND c.empresa_id = #{idEmpresa}
    WHERE m.empresa_id = #{idEmpresa}
      AND m.removida = false
    ORDER BY m.nome
  </select>
</mapper>
