<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pausaProgramada">
  <resultMap id="pausaProgramadaRM" type="PausaProgramada">
    <id property="id" column="pausa_programada_id"/>

    <result property="descricao" column="pausa_programada_descricao"/>
    <result property="dataInicio" column="pausa_programada_data_inicio"/>
    <result property="dataFim" column="pausa_programada_data_fim"/>
    <result property="mensagem" column="pausa_programada_mensagem"/>
    <result property="dataCancelamento" column="pausa_programada_data_cancelamento"/>
    <result property="cancelada" column="pausa_programada_cancelada"/>

    <association property="operadorCadastrou" columnPrefix="cadastrou_"   resultMap="usuario.operadorResultMap"/>
    <association property="operadorCancelou"   columnPrefix="cancelou_"  resultMap="usuario.operadorResultMap"/>
  </resultMap>



  <select id="selecione" parameterType="map" resultMap="pausaProgramadaRM" prefix="true">
    select * from pausa_programada
            left join usuario cadastrou_operador on cadastrou_operador.id = operador_cadastrou_id
            left join usuario cancelou_operador on cancelou_operador.id = operador_cancelou_id
         where pausa_programada.empresa_id = #{idEmpresa}

            <if test="emConflito">
              and pausa_programada.cancelada is not true and pausa_programada.data_fim is not null
              and (
                /* Verifica se existe qualquer sobreposição de datas */
                (
                  /* Caso 1: Data início da nova pausa está dentro de uma pausa existente */
                  (pausa_programada.data_inicio &lt;= #{dataInicio} and  pausa_programada.data_fim > #{dataInicio})
                  or
                  /* Caso 2: Data fim da nova pausa está dentro de uma pausa existente */
                  (pausa_programada.data_inicio &lt; #{dataFim}  and pausa_programada.data_fim >= #{dataFim})
                  or
                  /* Caso 3: Nova pausa engloba completamente uma pausa existente */
                  (pausa_programada.data_inicio >= #{dataInicio} and pausa_programada.data_fim &lt;= #{dataFim})
                ) )
            </if>

            <if test="idExistente">
               and pausa_programada.id != #{idExistente}
            </if>
            <if test="inicio != null">
              order by pausa_programada.id desc limit #{inicio}, #{total}
            </if>


  </select>



  <select id="selecioneTotal" parameterType="map" resultType="int" prefix="true">
    SELECT COUNT(*) FROM pausa_programada
             WHERE empresa_id = #{idEmpresa}
  </select>


  <update id="atualize">
    update pausa_programada join empresa on empresa.id = empresa_id
        set pausa_programada.descricao = #{descricao},
            pausa_programada.data_inicio = #{dataInicio},
            pausa_programada.data_fim = #{dataFim},
          pausa_programada.mensagem = #{mensagem}

          where pausa_programada.id = #{id}
  </update>

  <update id="remova">
    update pausa_programada
       set cancelada = true, operador_cancelou_id = #{operadorCancelou.id}, data_cancelamento = now()
         where id = #{id} and empresa_id = #{empresa.id}
  </update>

</mapper>
