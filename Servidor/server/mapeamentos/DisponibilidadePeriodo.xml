<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="disponibilidadePeriodo">
  <resultMap id="disponibilidadePeriodoRM" type="DisponibilidadePeriodo">
    <id property="id" column="disponibilidade_periodo_id"/>


    <result property="horaInicio" column="disponibilidade_periodo_hora_inicio"/>
    <result property="horaFim" column="disponibilidade_periodo_hora_fim"/>

    <collection  property="dias" resultMap="disponibilidadePeriodo.diasDaDisponibilidadeRM"/>



  </resultMap>

  <resultMap id="diasDaDisponibilidadeRM" type="DisponibilidadePeriodoDia">
    <id property="id" column="disponibilidade_periodo_dia_id"/>

    <result property="dia" column="disponibilidade_periodo_dia_dia"/>

  </resultMap>


  <select id="selecione" parameterType="map" resultMap="disponibilidadeRM">
    select * from disponibilidade_periodo where empresa_id = #{idEmpresa};
  </select>


  <insert id="insiraDias" parameterType="map" >
    insert into disponibilidade_periodo_dia ( dia ,disponibilidade_periodo_id)
    values
    <foreach item="item" collection="dados" open="" separator="," close="">
      (  #{item.dia} ,   #{item.idDisponibilidadePeriodo}  )
    </foreach>
  </insert>


  <update id="atualize" parameterType="map"  >
    update disponibilidade_periodo
    set       where id = #{id}
  </update>

  <delete id="remova" parameterType="map">
    delete from disponibilidade_periodo_funcionamento where id = #{id};
  </delete>



</mapper>
