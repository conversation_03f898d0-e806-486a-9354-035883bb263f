<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="linkEncurtado">
  <resultMap id="linkEncurtadoRM" type="LinkEncurtado">
    <id property="id" column="link_id"/>
    <result property="url" column="link_url"/>
    <result property="dataCriacao" column="link_data_criacao"/>
    <result property="visitas" column="link_visitas"/>
    <result property="ultimaVisita" column="link_ultima_visita"/>
    <result property="token" column="link_token"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="linkEncurtadoRM">
    select
    le.id link_id,
    le.token link_token,
    le.url link_url,
    le.data_criacao link_data_criacao,
    le.visitas link_visitas,
    le.ultima_visita link_ultima_visita
    from link_encurtado le
    where le.id = #{id}
  </select>


  <insert id="insira" parameterType="map">
    insert into link_encurtado(token, url, visitas, data_criacao, ultima_visita)
    values (#{token}, #{url}, #{visitas}, #{dataCriacao}, #{ultimaVisita})
  </insert>

  <update id="atualize" parameterType="Contato">
    update link_encurtado set visitas = #{visitas},
    ultima_visita = #{ultimaVisita}
    where id = #{id}
  </update>

</mapper>
