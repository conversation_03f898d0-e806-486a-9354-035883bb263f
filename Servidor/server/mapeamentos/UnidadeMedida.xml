<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="unidadeMedida">

  <resultMap id="unidadeMedidaRM" type="UnidadeMedida">
      <id property="id" column="unidade_medida_id"/>

      <result property="nome" column="unidade_medida_nome"/>
      <result property="sigla" column="unidade_medida_sigla"/>
      <result property="valorInicialPadrao" column="unidade_medida_valor_inicial_padrao"/>
      <result property="incrementoPadrao" column="unidade_medida_incremento_padrao"/>

  </resultMap>
  <select id="selecione" parameterType="map" resultMap="unidadeMedidaRM" prefix="true">
    select  * from unidade_medida
      <if test="produto"> where 2 >= id</if>
      <if test="insumo"> where incremento_padrao = 1</if>
  </select>
</mapper>
