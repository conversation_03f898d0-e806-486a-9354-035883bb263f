<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="modulo">

  <resultMap id="moduloRM" type="Modulo">
    <id property="id" column="modulo_id"/>

    <result  property="nome" column="modulo_nome"/>
    <result  property="valorMensalidade" column="modulo_valor_mensalidade"/>
    <result  property="valorAtivacao" column="modulo_valor_ativacao"/>
    <result  property="descricao" column="modulo_descricao"/>

  </resultMap>

  <select id="selecione" parameterType="map"  resultMap="moduloRM" prefix="true">
    SELECT * FROM modulo
        <if test="id"> where modulo.id = #{id}</if>
        <if test="nome"> where modulo.nome = #{nome}</if>
        <if test="pagos"> where  valor_mensalidade > 0 </if>
       ORDER BY id
  </select>



  <update id="atualize" parameterType="map">
    UPDATE modulo SET
      nome = #{nome},
      valor_mensalidade = #{valorMensalidade},
      valor_ativacao = #{valorAtivacao},
      descricao = #{descricao}
    WHERE id = #{id}
  </update>

  <delete id="remova">
    DELETE FROM modulo WHERE id = #{id}
  </delete>

</mapper>
