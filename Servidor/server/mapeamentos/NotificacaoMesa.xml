<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="notificacaomesa">
  <resultMap id="notificacaomesaRM" type="NotificacaoMesa">
    <id property="id" column="notificacao_mesa_id"/>


    <result property="numero" column="notificacao_mesa_numero" />
    <result property="operacao" column="notificacao_mesa_operacao" />
    <result property="horario" column="notificacao_mesa_horario" />
    <result property="horarioNotificado" column="notificacao_mesa_horario_notificado" />
    <result property="origem" column="notificacao_mesa_origem" />
    <result property="dados" column="notificacao_mesa_dados" />
    <result property="executada" column="notificacao_mesa_executada" />
    <result property="erro" column="notificacao_mesa_erro" />
    <result property="ignorar" column="notificacao_mesa_ignorar" />
    <result property="empresaId" column="notificacao_mesa_empresa_id" />

  </resultMap>


  <select id="selecione" parameterType="map" resultMap="notificacaomesaRM" prefix="true">
    select * from notificacao_mesa where

      <if test="id">
          id = #{id}
      </if>
      <if test="comErro">
        comanda_id = #{idComanda}   and erro is not null order by id desc
      </if>



  </select>

  <insert id="insira">
    insert into notificacao_mesa (id,  empresa_id, origem, numero, comanda_id,operacao,comando,horario, horario_notificado, dados, ignorar)
    values (#{id}, #{empresa.id}, #{origem},  #{numero}, #{comanda.id}, #{operacao}, #{comando},#{horario}, #{horarioNotificado}, #{dados}, #{ignorar})
    ON DUPLICATE KEY UPDATE dados = #{dados};
  </insert>

  <update id="atualize">
    update notificacao_mesa
    set executada = #{executada}, erro = #{erro}, ignorar = #{ignorar}
    where id = #{id}

  </update>

</mapper>
