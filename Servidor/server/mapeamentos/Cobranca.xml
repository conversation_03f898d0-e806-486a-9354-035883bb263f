<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cobranca">
  <resultMap id="cobrancaRM" type="Cobranca">
    <id property="id" column="cobranca_id"/>
    <result property="numeroFatura" column="cobranca_numero_fatura"/>
    <result property="valorFatura" column="cobranca_valor_fatura"/>
    <result property="valorDesconto" column="cobranca_valor_desconto"/>
    <result property="valorLiquido" column="cobranca_valor_liquido"/>

    <collection property="faturas" resultMap="fatura.faturaRM"/>
  </resultMap>

  <create id="crieTabela" parameterType="map">
    create table if not exists cobranca (
      id bigint(20) primary key,
      numero_fatura bigint(20),
      valor_fatura decimal(10,2),
      valor_desconto decimal(10,2),
      valor_liquido decimal(10,2)
    );
  </create>
</mapper>
