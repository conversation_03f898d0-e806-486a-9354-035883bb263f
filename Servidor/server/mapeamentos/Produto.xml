
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="produto">
  <resultMap id="produtoResultMap" type="Produto">
    <id property="id" column="produto_id"/>

    <result property="nome" column="produto_nome"/>
    <result property="descricao" column="produto_descricao"/>
    <result property="mensagemPedido" column="produto_mensagem_pedido"/>

    <result property="preco" column="produto_preco"/>
    <result property="novoPreco" column="produto_novo_preco"/>
    <result property="valorResgate" column="produto_valor_resgate"/>
    <result property="exibirNoSite" column="produto_exibir_no_site"/>
    <result property="disponivelParaDelivery" column="produto_disponivel_para_delivery"/>
    <result property="disponivelNaMesa" column="produto_disponivel_na_mesa"/>
    <result property="naoSincronizar" column="produto_nao_sincronizar"/>

    <result property="temEstoque" column="produto_tem_estoque"/>
    <result property="ordem" column="produto_ordem"/>
    <result property="qtdMaxima" column="produto_qtd_maxima"/>
    <result property="exibirPrecoSite" column="produto_exibir_preco_site"/>
    <result property="exibirPrecoNoCardapio" column="produto_exibir_preco_no_cardapio"/>
    <result property="tipoDeVenda" column="produto_tipo_de_venda"/>

    <result property="qtdeMinima" column="produto_qtd_minima"/>

    <result property="pesoMinimo" column="produto_peso_minimo"/>
    <result property="pesoMaximo" column="produto_peso_maximo"/>

    <result property="valorInicial" column="produto_valor_inicial"/>
    <result property="incremento" column="produto_incremento"/>

    <result property="disponibilidade" column="produto_disponibilidade"/>
    <result property="tipo" column="produto_tipo"/>
    <result property="percentualDesconto" column="produto_percentual_desconto"/>
    <result property="destaque" column="produto_destaque"/>
    <result property="codigoPdv" column="produto_codigo_pdv"/>
    <result property="sku" column="produto_sku"/>
    <result property="cashback" column="produto_cashback"/>
    <result property="pontosGanhos" column="produto_pontos_ganhos"/>
    <result property="idIfood" column="produto_id_ifood"/>
    <result property="naoAceitaCupom" column="produto_nao_aceita_cupom"/>
    <result property="ehBrinde" column="produto_eh_brinde"/>
    <result property="origem" column="produto_origem"/>
    <result property="dataCadastro" column="produto_data_cadastro"/>

    <result property="codigoDeBarras" column="produto_codigo_de_barras"/>
    <result property="sincronizarModelos" column="produto_sincronizar_modelos"/>

    <association property="categoria"   resultMap="categoria.categoriaRM"/>
    <association property="template"   resultMap="produtoTemplate.produtoTemplateRM"/>
    <association property="empresa"  columnPrefix="produto_"  resultMap="empresa.empresaReferenciaRM"/>
    <association property="catalogo"  columnPrefix="produto_"  resultMap="catalogo.catalogoRM"/>
    <association property="produtoNaEmpresa"   resultMap="produtoNaEmpresa.produtoNaEmpresaDoPrudutoRM"/>
    <association property="configuracaoFiscal" resultMap="produtoConfigFiscal.produtoConfigFiscalRM"/>

    <association property="unidadeMedida"   resultMap="unidadeMedida.unidadeMedidaRM"/>
    <association property="imagemCodigoDeBarras"  resultMap="imagemCodigoDeBarras.imagemEanRM"/>
    <association property="insumo"  resultMap="insumo.insumoDoEstoqueRM"/>
    <association property="estoque" columnPrefix="produto_"  resultMap="estoque.estoqueRM"/>
    <association property="cupom" resultMap="cupom.cupomBrindeRM"/>

    <collection  property="camposAdicionais" resultMap="adicionalDeProduto.adicionalDeProdutoRM"/>
    <collection  property="imagens" resultMap="imagemDoProduto.imagemDoProdutoRM"/>

    <collection property="disponibilidades" resultMap="disponibilidade.disponibilidadesProdutoRM"  />
    <collection property="tags" resultMap="tagproduto.tagDoprodutoRM"  />

    <discriminator javaType="String" column="produto_tipo" >
      <case value="normal" resultType="Produto"></case>
      <case value="pizza" resultType="ProdutoPizza"></case>
      <case value="brindefidelidade" resultType="ProdutoBrindeFidelidade"></case>
    </discriminator>
  </resultMap>

  <resultMap id="produtoTamanhoRM" type="ProdutoTamanho">
    <id property="id" column="produto_tamanho_id"/>

    <result property="preco" column="produto_tamanho_preco"/>
    <result property="novoPreco" column="produto_tamanho_novo_preco"/>
    <result property="codigoPdv" column="produto_tamanho_codigo_pdv"/>
    <result property="pontosGanhos" column="produto_tamanho_pontos_ganhos"/>
    <result property="cashback" column="produto_tamanho_cashback"/>
    <result property="percentualDesconto" column="produto_tamanho_percentual_desconto"/>
    <result property="disponivel" column="produto_tamanho_disponivel"/>
    <result property="destaque" column="produto_tamanho_destaque"/>

    <result property="descricao" column="produto_template_tamanho_descricao"/>
    <result property="qtdePedacos" column="produto_template_tamanho_qtde_pedacos"/>
    <result property="qtdeSabores" column="produto_template_tamanho_qtde_sabores"/>
    <result property="idProduto" column="produto_tamanho_produto_id"/>

    <association property="template"   resultMap="produtoTemplate.produtoTemplateTamanhoRM"/>
  </resultMap>

  <resultMap id="produtoResumidoRM" type="Produto">

    <id property="id" column="produto_id"/>

    <result property="nome" column="produto_nome"/>
    <result property="descricao" column="produto_descricao"/>
    <result property="preco" column="produto_preco"/>
    <result property="novoPreco" column="produto_novo_preco"/>
  </resultMap>

  <resultMap id="resumoProdutosContatoRM" type="DTOResumoProdutosContato">
    <id property="id" column="resumo_produtos_contato_id"/>

    <result property="nome" column="resumo_produtos_contato_nome" />
    <result property="quantidade" column="resumo_produtos_contato_quantidade" />

    <association property="contato" resultMap="contato.contatoRM"/>
  </resultMap>

  <select id="selecioneResumoProdutosContato" parameterType="map" resultMap="resumoProdutosContatoRM">
    selecT p.id resumo_produtos_contato_id, p.nome resumo_produtos_contato_nome, p.catalogo_id catalogo_id, count(*) resumo_produtos_contato_quantidade, contato_id
    from produto p join item_pedido ip on p.id = ip.produto_id join pedido ped on ped.id = ip.pedido_id
    where contato_id = #{idContato} and p.catalogo_id = #{idCatalogo}
    <if test="horarioInicio != null">
      and horario &gt;= #{horarioInicio}
    </if>
    <if test="horarioFim != null">
      and horario &lt;= #{horarioFim}
    </if>
    group by p.id
    order by resumo_produtos_contato_quantidade desc;

  </select>

  <select id="selecioneResumoVitrine" parameterType="map" resultMap="produtoResultMap" prefix="true">
    select * from produto where   produto.catalogo_id = #{idCatalogo}  and id = -1
  </select>

  <select id="selecioneResumoMercado" parameterType="map" resultMap="produtoResultMap" prefix="true">
    select * from

    (SELECT produto.id   FROM  produto INNER JOIN
    (
    <choose>
      <when test="categoriasPai">
        SELECT  if(categoria3 is null, categoria2, categoria3)  categoria_id,   GROUP_CONCAT(produto.id ORDER BY ordem asc) produtos
           FROM  produto join  produto_categorias on produto_categorias.id = produto.id
                   where produto.catalogo_id = #{idCatalogo}  and produto.disponibilidade != 2  and  removido is not true
                      GROUP BY if(categoria3 is null, categoria2, categoria3)
      </when>
      <otherwise>
        SELECT  categoria_id,  GROUP_CONCAT(id ORDER BY ordem asc) produtos
            FROM  produto where produto.catalogo_id = #{idCatalogo} and produto.disponibilidade != 2   and  removido is not true
                      GROUP BY categoria_id
      </otherwise>
    </choose>  )   group_max ON  produto.catalogo_id = #{idCatalogo} and produto.disponibilidade != 2 and FIND_IN_SET(id, produtos) BETWEEN 1 AND 5

    inner join categoria on categoria.id = produto.categoria_id
    and produto.catalogo_id = #{idCatalogo}
    left join produto_disponibilidade on produto_disponibilidade.produto_id = produto.id
    left join disponibilidade on disponibilidade.id = disponibilidade_id and disponibilidade.removida is not true
    left join adicional_produto on produto.id = adicional_produto.produto_id and adicional_produto.excluido is not true
    left join opcao_adicional_produto on adicional_produto.id = opcao_adicional_produto.adicional_produto_id and
    opcao_adicional_produto.excluido is not true
    where produto.catalogo_id = #{idCatalogo}     and  removido is not true

    <if test="cardapioMesa">
      and produto.disponivel_na_mesa is true
    </if>
    <if test="cardapioDelivery">
      and produto.disponivel_para_delivery is true
    </if>

    <if test="categoria">
      and (categoria.id = #{categoria})
    </if>

    <if test="termo">
      and ( (
      MATCH(produto.nome) AGAINST (#{termo} IN BOOLEAN MODE) or
      MATCH(categoria.nome ) AGAINST (#{termo} IN BOOLEAN MODE) or
      MATCH(produto.descricao) AGAINST (#{termo} IN BOOLEAN MODE)  )
      or
      (
      MATCH(opcao_adicional_produto.nome)  AGAINST (#{termo} IN BOOLEAN MODE)
      )
      )
    </if>
    <if test="preco != null">
      and produto.preco = #{preco}
    </if>
    <if test="integrado != null">
      and produto.codigo_pdv is not null
    </if>
    group by produto.id
    order by categoria.nivel, ISNULL(categoria.posicao), categoria.posicao asc, produto.ordem) produtos_filtrados
    inner join produto on(produto.id = produtos_filtrados.id and produto.catalogo_id = #{idCatalogo})
    left join categoria on categoria.id = produto.categoria_id
    left join imagem_do_produto on imagem_do_produto.produto_id = produto.id
    left join imagem_ean on imagem_ean.codigo_de_barras = produto.codigo_de_barras
    left join adicional_produto on produto.id = adicional_produto.produto_id and adicional_produto.excluido is not true
    left join opcao_adicional_produto on adicional_produto.id = opcao_adicional_produto.adicional_produto_id and
    opcao_adicional_produto.excluido is not true
    left join dependencia_opcao_adicional on dependencia_opcao_adicional.opcao_id = opcao_adicional_produto.id
    left join unidade_medida   on unidade_medida.id = produto.unidade_medida_id
    left join produto_disponibilidade on produto_disponibilidade.produto_id = produto.id
    left join disponibilidade on disponibilidade.id = disponibilidade_id and disponibilidade.removida is not true
    left join produto_template on produto.template_id = produto_template.id
    left join produto_tamanho on produto_tamanho.produto_id = produto.id
    left join produto_template_tamanho on produto_template_tamanho.id = produto_tamanho.produto_template_tamanho_id
    left join produto_template_adicional on produto_template_adicional.id = adicional_produto.produto_template_adicional_id
    left join produto_template_opcao on produto_template_opcao.id = opcao_adicional_produto.produto_template_opcao_id
    left join produto_template_tamanho produto_template_tamanho_opcao on produto_template_tamanho_opcao.id = produto_template_opcao.tamanho_id
    order by categoria.nivel, ISNULL(categoria.posicao), categoria.posicao asc, produto.ordem, imagem_do_produto.ordem, adicional_produto.ordem
  </select>

  <select id="selecione" parameterType="map" resultMap="produtoResultMap" prefix="true">
      select * from
            (  select produto.id  from   produto  left join categoria on categoria.id = produto.categoria_id
                  left join produto_adicional_produto on (produto_adicional_produto.objeto_id = produto.id)
                  left join adicional_produto on (produto_adicional_produto.adicional_produto_id = adicional_produto.id and
                                                    adicional_produto.excluido is not true  )
                  left join opcao_adicional_produto on (adicional_produto.id = opcao_adicional_produto.adicional_produto_id and
                                                           opcao_adicional_produto.excluido is not true)
                  left join configuracao_fiscal_opcao on (configuracao_fiscal_opcao.opcao_adicional_produto_id = opcao_adicional_produto.id)
                <if test="idEmpresa != null">
                  left join produto_na_empresa on (produto_na_empresa.produto_id = produto.id and produto_na_empresa.empresa_id = #{idEmpresa})

                </if>
                  where   produto.removido is not true
                    and produto.catalogo_id = #{idCatalogo}
                    <choose>
                        <when test="ocultos">

                        </when>
                        <otherwise>
                          and produto.disponibilidade != 3
                        </otherwise>
                    </choose>

                    <if test="id != null">
                      and produto.id = #{id}
                    </if>
                    <if test="codigoPdv != null">
                      and produto.codigo_pdv = #{codigoPdv}
                    </if>

                    <if test="idIfood != null">
                      and produto.id_ifood = #{idIfood}
                    </if>

                    <if test="sku != null">
                      and produto.sku = #{sku}
                    </if>
                    <if test="ordem != null">
                      and produto.ordem = #{ordem}
                    </if>
                    <if test="idEmpresa != null">
                      <if test="temEstoque">
                      and ((produto_na_empresa.disponibilidade is not null and produto_na_empresa.disponibilidade &lt; 2)
                                or (produto_na_empresa.disponibilidade is null and produto.tem_estoque is true))
                      </if>
                      <if test="temEstoque != null">
                        <if test="!temEstoque">
                          and ((produto_na_empresa.disponibilidade is not null and (produto_na_empresa.disponibilidade = 2)) or
                          (produto_na_empresa.disponibilidade is null and produto.tem_estoque is false))
                        </if>
                      </if>
                    </if>
                    <if test="idEmpresa == null">
                      <if test="temEstoque">
                        and produto.tem_estoque = #{temEstoque}
                      </if>

                    </if>

                    <if test="alertaEstoque">
                        and exists (select 1 from estoque where estoque.quantidade_minima * 1.1 >= estoque.quantidade and estoque.id = produto.estoque_id )
                    </if>

                    <if test="categoriaDisponivel != null">
                      and (categoria.disponivel is true  or produto.categoria_id is null)
                    </if>

                    <if test="sincronizarModelo">
                        and categoria.sincronizar_modelo is true
                    </if>


                    <if test="cardapioMesa">
                      and produto.disponivel_na_mesa is true
                    </if>
                    <if test="cardapioDelivery">
                      and produto.disponivel_para_delivery is true
                    </if>
                    <if test="exibirSite">
                      and produto.exibir_no_site is true
                    </if>
                    <if test="esconderZerados">
                      and (
                        preco >= 0 or eh_brinde is true
                      )
                    </if>

                    <if test="tipo">
                       and produto.tipo = #{tipo}
                    </if>

                    <if test="idTamanho">
                      and exists (select 1 from produto_tamanho where   produto_id = produto.id and produto_template_tamanho_id = #{idTamanho})
                    </if>

                      <if test="idTemplate">
                        and  produto.template_id = #{idTemplate}
                      </if>

                     <if test="idsNotIn">
                        and produto.id not in
                       <foreach item="id" collection="idsNotIn" open="(" separator="," close=")">
                         #{id}
                       </foreach>

                     </if>

                    <if test="categoria">
                      and (categoria.id = #{categoria})
                    </if>

                    <if test="categoriaPai">
                      and exists (select 1 from produto_categorias where
                         produto_categorias.id = produto.id and ( categoria1 = #{categoriaPai} or categoria2 = #{categoriaPai}  or categoria3 = #{categoriaPai}  ))

                    </if>

                   <if test="vitrines">
                      and exists (select 1 from vitrine join vitrine_produtos on vitrine.id = vitrine_id
                                              where  produto_id = produto.id and vitrine.disponivel is true)
                   </if>

                    <if test="nomeCategoria">
                      and (
                      categoria.nome = #{nomeCategoria}
                      <if test="trazerDestaques">
                        or (produto.destaque is true)

                        <if test="idEmpresa">
                          or (produto_na_empresa.destaque is true)
                        </if>
                      </if>
                      )
                    </if>

    <if test="disponibilidade">
      <if test="daEmpresa == null">
        and  produto.disponibilidade = 1
      </if>
      <if test="daEmpresa != null">
        and  produto_na_empresa.disponibilidade = 1
      </if>

      and  exists (
      select 1 from produto_disponibilidade join disponibilidade on (disponibilidade.id = disponibilidade_id and disponibilidade.removida is not true)
      where produto_id = produto.id and disponibilidade.id
      in (
      <foreach item="item" collection="disponibilidade" open="" separator="," close="">
        #{item}
      </foreach> )
      )

    </if>

    <if test="semCodigoPdv">
      and (    (produto.tipo = 'pizza' and exists (select  1 from produto_tamanho where produto_id = produto.id and  produto_tamanho.codigo_pdv is null))
            or (produto.tipo != 'pizza' and produto.codigo_pdv is null  )
            or (opcao_adicional_produto.id is not null and opcao_adicional_produto.codigo_pdv is null)
           )
    </if>

    <if test="apenasNaoSincronizados">
      and (produto.nao_sincronizar is true)
    </if>

    <if test="termo">
                       and ( (
                              MATCH(produto.nome) AGAINST (#{termo} IN BOOLEAN MODE) or
                              MATCH(categoria.nome ) AGAINST (#{termo} IN BOOLEAN MODE) or
                              MATCH(produto.descricao) AGAINST (#{termo} IN BOOLEAN MODE)
                              <if test="correspondeCategoriaDestaques">
                                or (produto.destaque is true)
                              </if>
                             )
                        or
                        (
                            MATCH(opcao_adicional_produto.nome)  AGAINST (#{termo} IN BOOLEAN MODE)
                            <if test="correspondeCategoriaDestaques">
                              or (produto.destaque is true)
                            </if>
                        )  or  (   produto.codigo_pdv  like #{like}  )
                      )
                    </if>

                    <if test="nomeProduto">
                      and (
                         MATCH(produto.nome) AGAINST (#{nomeProduto} IN BOOLEAN MODE)
                         or     produto.codigo_pdv  like #{like}
                         )

                    </if>

                    <if test="preco != null">
                       and produto.preco = #{preco}
                    </if>
                   <if test="integrado != null">
                       and ( produto.codigo_pdv is not null  or exists (select 1 from produto_tamanho where produto_tamanho.codigo_pdv is not null and produto_tamanho.produto_id = produto.id))
                    </if>

                  <if test=" semImagens">
                      and not exists (select 1 from imagem_do_produto where imagem_do_produto.produto_id = produto.id)
                  </if>

                  group by produto.id
                  order by categoria.nivel, ISNULL(categoria.posicao), categoria.posicao asc, produto.ordem

                  <if test="inicio != null">
                    limit  #{inicio}, #{total}
                  </if>
    ) produtos_filtrados
      inner join produto on(produto.id = produtos_filtrados.id) and produto.catalogo_id = #{idCatalogo}
      inner join catalogo produto_catalogo on produto.catalogo_id = produto_catalogo.id
      left join categoria on categoria.id = produto.categoria_id
      left join imagem_do_produto on imagem_do_produto.produto_id = produto.id
      left join imagem_ean on imagem_ean.codigo_de_barras = produto.codigo_de_barras
      left join unidade_medida   on unidade_medida.id = produto.unidade_medida_id
      left join produto_disponibilidade on produto_disponibilidade.produto_id = produto.id
      left join disponibilidade on disponibilidade.id = disponibilidade_id and disponibilidade.removida is not true
      left join produto_template on produto.template_id = produto_template.id
      left join produto_tags on produto_tags.produto_id= produto.id
      left join produto_config_fiscal on produto_config_fiscal.produto_id = produto.id
      left join origem_produto on produto_config_fiscal.origem_produto_id = origem_produto.id
      left join tipo_de_tributacao_ipi on tipo_de_tributacao_ipi.id = produto_config_fiscal.tipo_de_tributacao_ipi_id
      left join selo_de_controle_ipi on selo_de_controle_ipi.id = produto_config_fiscal.selo_de_controle_ipi_id
      left join insumo_produto on produto.id = insumo_produto.produto_id
      left join insumo on insumo.id = insumo_produto.insumo_id
      left join estoque on estoque.id = insumo.estoque_id
      left join estoque produto_estoque on produto.estoque_id = produto_estoque.id
      <if test="idEmpresa != null">
        left join produto_na_empresa on (produto_na_empresa.produto_id = produto.id and produto_na_empresa.empresa_id = #{idEmpresa})
      </if>
    <choose>
        <when test="!semAdicionais">
         left join produto_adicional_produto on  (produto_adicional_produto.objeto_id = produto.id )
          left join adicional_produto on (produto_adicional_produto.adicional_produto_id = adicional_produto.id
          and  adicional_produto.excluido is not true
          <if test="!ocultos">
            and adicional_produto.oculto is not true
          </if>
          )
          left join opcao_adicional_produto on (adicional_produto.id = opcao_adicional_produto.adicional_produto_id and
          opcao_adicional_produto.excluido is not true
          <if test="!ocultos">
            and opcao_adicional_produto.oculta is not true
          </if>
          )
         left join configuracao_fiscal_opcao on (configuracao_fiscal_opcao.opcao_adicional_produto_id = opcao_adicional_produto.id)

          left join produto_template_opcao on produto_template_opcao.id = opcao_adicional_produto.produto_template_opcao_id
          left join produto_template_tamanho produto_template_tamanho_opcao on produto_template_tamanho_opcao.id = produto_template_opcao.tamanho_id
          left join produto_tamanho opcao_produto_tamanho on opcao_produto_tamanho.id  = opcao_adicional_produto.produto_tamanho_id

         left join dependencia_opcao_adicional on dependencia_opcao_adicional.opcao_id = opcao_adicional_produto.id
         left join produto_template_adicional on produto_template_adicional.id = adicional_produto.produto_template_adicional_id

          left join insumo_opcao on opcao_adicional_produto.id = insumo_opcao.opcao_id
          left join insumo opcao_insumo on opcao_insumo.id = insumo_opcao.insumo_id
          left join estoque opcao_estoque on opcao_estoque.id = opcao_insumo.estoque_id
          left join cupom cupom_brinde on cupom_brinde.id = produto.cupom_id
          <if test="idEmpresa != null">
            left join opcao_na_empresa on (opcao_na_empresa.opcao_id = opcao_adicional_produto.id and opcao_na_empresa.empresa_id = #{idEmpresa})
          </if>

       </when>
    </choose>


    order by categoria.nivel, ISNULL(categoria.posicao), categoria.posicao asc, produto.ordem, imagem_do_produto.ordem

    <if test="!semAdicionais">
      , produto_adicional_produto.ordem, opcao_adicional_produto.disponivel desc
    </if>

  </select>

  <select id="selecioneTamanhos" resultMap="produtoTamanhoRM"   prefix="true">
      select * from produto_tamanho join produto_template_tamanho on produto_template_tamanho.id = produto_tamanho.produto_template_tamanho_id
                                        join produto on produto.id = produto_tamanho.produto_id
    where  produto.id  in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
          #{id}
        </foreach>
  </select>

  <select id="selecioneNovo" parameterType="map" resultMap="produtoResultMap" prefix="true">
    select * from
    (  select produto.id  from   produto  left join categoria on categoria.id = produto.categoria_id
    left join produto_adicional_produto on (produto_adicional_produto.objeto_id = produto.id)
    left join adicional_produto on (produto_adicional_produto.adicional_produto_id = adicional_produto.id and
    adicional_produto.excluido is not true  )
    left join opcao_adicional_produto on (adicional_produto.id = opcao_adicional_produto.adicional_produto_id and
    opcao_adicional_produto.excluido is not true)
    <if test="idEmpresa != null">
      left join produto_na_empresa on (produto_na_empresa.produto_id = produto.id and produto_na_empresa.empresa_id = #{idEmpresa})
    </if>
    where   removido is not true  and produto.catalogo_id = #{idCatalogo}

      <choose>
        <when test="ocultos">  </when>
        <otherwise>
          and produto.disponibilidade != 3
        </otherwise>
      </choose>


    <if test="id != null">
      and produto.id = #{id}
    </if>
    <if test="codigoPdv != null">
      and produto.codigo_pdv = #{codigoPdv}
    </if>
    <if test="ordem != null">
      and produto.ordem = #{ordem}
    </if>
    <if test="temEstoque != null">
      and produto.tem_estoque = #{temEstoque}
    </if>
    <if test="categoriaDisponivel != null">
      and (categoria.disponivel is true  or produto.categoria_id is null)
    </if>
    <if test="cardapioMesa">
      and produto.disponivel_na_mesa is true
    </if>
    <if test="cardapioDelivery">
      and produto.disponivel_para_delivery is true
    </if>
    <if test="exibirSite">
      and produto.exibir_no_site is true
    </if>

    <if test="categoria">
      and (categoria.id = #{categoria})
    </if>

    <if test="categoriaPai">
      and exists (select 1 from produto_categorias where
      produto_categorias.id = produto.id and ( categoria1 = #{categoriaPai} or categoria2 = #{categoriaPai}  or categoria3 = #{categoriaPai}  ))

    </if>

    <if test="nomeCategoria">
      and (
      categoria.nome = #{nomeCategoria}
      <if test="trazerDestaques">
        or (produto.destaque is true)
        <if test="idEmpresa">
          or (produto_na_empresa.destaque is true)
        </if>

      </if>
      )
    </if>

    <if test="disponibilidade">

      and (disponibilidade = 0 or dia in (
      <foreach item="item" collection="disponibilidade" open="" separator="," close="">
        #{item}
      </foreach>


      ))
    </if>

    <if test="semCodigoPdv">
      and (    (produto.tipo = 'pizza' and exists (select  1 from produto_tamanho where produto_id = produto.id and  produto_tamanho.codigo_pdv is null))
      or (produto.tipo != 'pizza' and produto.codigo_pdv is null  )
      or (opcao_adicional_produto.id is not null and opcao_adicional_produto.codigo_pdv is null)
      )
    </if>

    <if test="apenasNaoSincronizados">
      and (produto.nao_sincronizar is true)
    </if>

    <if test="termo">
      and ( (
      MATCH(produto.nome) AGAINST (#{termo} IN BOOLEAN MODE) or
      MATCH(categoria.nome ) AGAINST (#{termo} IN BOOLEAN MODE) or
      MATCH(produto.descricao) AGAINST (#{termo} IN BOOLEAN MODE)
      <if test="correspondeCategoriaDestaques">
        or (produto.destaque is true)
        <if test="idEmpresa">
          or (produto_na_empresa.destaque is true)
        </if>

      </if>
      )
      or
      (
      MATCH(opcao_adicional_produto.nome)  AGAINST (#{termo} IN BOOLEAN MODE)
      <if test="correspondeCategoriaDestaques">
        or (produto.destaque is true)
        <if test="idEmpresa">
          or (produto_na_empresa.destaque is true)
        </if>

      </if>
      )
      )
    </if>
    <if test="preco != null">
      and produto.preco = #{preco}
    </if>
    <if test="integrado != null">
      and ( produto.codigo_pdv is not null  or exists (select 1 from produto_tamanho where produto_tamanho.codigo_pdv is not null and produto_tamanho.produto_id = produto.id))
    </if>

    group by produto.id
    order by categoria.nivel, ISNULL(categoria.posicao), categoria.posicao asc, produto.ordem

    <if test="inicio != null">
      limit  #{inicio}, #{total}
    </if>
    ) produtos_filtrados
    inner join produto on(produto.id = produtos_filtrados.id)
    and produto.catalogo_id = #{idCatalogo}
    left join categoria on categoria.id = produto.categoria_id
    left join imagem_do_produto on imagem_do_produto.produto_id = produto.id
    left join imagem_ean on imagem_ean.codigo_de_barras = produto.codigo_de_barras
    left join produto_adicional_produto on
    (produto_adicional_produto.objeto_id = produto.id )
    left join adicional_produto on (produto_adicional_produto.adicional_produto_id = adicional_produto.id and
    adicional_produto.excluido is not true)
    left join unidade_medida   on unidade_medida.id = produto.unidade_medida_id
    left join produto_disponibilidade on produto_disponibilidade.produto_id = produto.id
    left join disponibilidade on disponibilidade.id = disponibilidade_id and disponibilidade.removida is not true
    left join produto_template on produto.template_id = produto_template.id
    left join produto_tamanho on produto_tamanho.produto_id = produto.id
    left join produto_template_tamanho on produto_template_tamanho.id = produto_tamanho.produto_template_tamanho_id
    left join produto_template_adicional on produto_template_adicional.id = adicional_produto.produto_template_adicional_id
    order by categoria.nivel, ISNULL(categoria.posicao), categoria.posicao asc, produto.ordem, imagem_do_produto.ordem, produto_adicional_produto.ordem;
  </select>

  <select id="selecioneQuantidadePorCategoria" parameterType="map" resultMap="qtdePorCategoriaMap">

  </select>

  <select id="selecioneQuantidade" parameterType="map" resultType="int" prefix="true">
    select count(distinct produto.id)  from   produto  left join categoria on categoria.id = produto.categoria_id
    left join produto_adicional_produto on (produto_adicional_produto.objeto_id = produto.id )
    left join adicional_produto on (adicional_produto.excluido is not true
    and produto_adicional_produto.adicional_produto_id = adicional_produto.id)
    left join opcao_adicional_produto on adicional_produto.id = opcao_adicional_produto.adicional_produto_id and
         opcao_adicional_produto.excluido is not true
      <if test="idEmpresa != null">
        left join produto_na_empresa on (produto_na_empresa.produto_id = produto.id and produto_na_empresa.empresa_id = #{idEmpresa})
      </if>

    where   produto.catalogo_id = #{idCatalogo}  and  removido is not true
    <if test="id != null">
      and produto.id = #{id}
    </if>
    <if test="ordem != null">
      and produto.ordem = #{ordem}
    </if>
    <if test="temEstoque != null">
      and produto.tem_estoque = #{temEstoque}
    </if>
    <if test="categoriaDisponivel != null">
      and (categoria.disponivel = true  or produto.categoria_id is null)
    </if>
    <if test="cardapioMesa">
      and produto.disponivel_na_mesa is true
    </if>
    <if test="cardapioDelivery">
      and produto.disponivel_para_delivery is true
    </if>
    <if test="exibirSite">
      and produto.exibir_no_site is true
    </if>

    <if test="semCodigoPdv">
      and ((produto.tipo = 'pizza' and exists (select  1 from produto_tamanho where produto_id = produto.id and  produto_tamanho.codigo_pdv is null))
            or (produto.tipo != 'pizza' and produto.codigo_pdv is null)
            or (opcao_adicional_produto.id is not null and opcao_adicional_produto.codigo_pdv is null))
    </if>
    <if test="categoria">
      and (categoria.id = #{categoria})
    </if>

    <if test="nomeCategoria">
      and (
      categoria.nome = #{nomeCategoria}
      <if test="trazerDestaques">
        or (produto.destaque is true)
      </if>
      )
    </if>

    <if test="termo">
      and ( (
              MATCH(produto.nome) AGAINST (#{termo} IN BOOLEAN MODE) or
              MATCH(categoria.nome ) AGAINST (#{termo} IN BOOLEAN MODE) or
              MATCH(produto.descricao) AGAINST (#{termo} IN BOOLEAN MODE)
      <if test="correspondeCategoriaDestaques">
        or (produto.destaque is true)
      </if>
      )
      or
      (
          MATCH(opcao_adicional_produto.nome)  AGAINST (#{termo} IN BOOLEAN MODE)
          <if test="correspondeCategoriaDestaques">
            or (produto.destaque is true)
          </if>
      )
      )
    </if>
    <if test="preco != null">
      and produto.preco = #{preco}
    </if>

    <if test="disponibilidade">
      <if test="daEmpresa == null">
        and  produto.disponibilidade = 1
      </if>
      <if test="daEmpresa != null">
        and  produto_na_empresa.disponibilidade = 1
      </if>

      and  exists (
        select 1 from produto_disponibilidade
          where produto_id = produto.id and disponibilidade_id
              in (
              <foreach item="item" collection="disponibilidade" open="" separator="," close="">
                #{item}
              </foreach> )
              )

    </if>
  </select>

  <select id="selecioneProdutoAcima" parameterType="map" resultMap="produtoResultMap" prefix="true">
    select * from produto
    where produto.catalogo_id = #{idCatalogo}
    and produto.categoria_id = #{categoria.id}
    and produto.ordem = (select max(ordem) ordem from produto where
    catalogo_id = #{idCatalogo}
    and categoria_id = #{categoria.id}
    and ordem &lt; #{ordem}
    and removido is not true
    and tem_estoque = true);
  </select>

  <select id="selecioneProdutoAbaixo" parameterType="map" resultMap="produtoResultMap" prefix="true">
    select * from produto
    where produto.catalogo_id = #{idCatalogo}
    and produto.categoria_id = #{categoria.id}
    and produto.ordem = (select min(ordem) ordem from produto where
    catalogo_id = #{idCatalogo}
    and categoria_id = #{categoria.id}
    and ordem &gt; #{ordem}
    and removido is not true
    and tem_estoque = true);
  </select>


  <select id="selecioneIndisponiveisEcletica" parameterType="map" resultMap="produtoResultMap" prefix="true">
    select produto.*, produto_catalogo.*, produto_integracao_delivery.*,
           produto_adicional_produto.*, adicional_produto.*, opcao_adicional_produto.*, produto_template_opcao.*
           from produto join catalogo produto_catalogo on (produto.catalogo_id = produto_catalogo.id and produto.removido is not true
                                                            <if test="idCatalogo">
                                                              and produto_catalogo.id = #{idCatalogo}
                                                            </if>)
                        join empresa produto_empresa on (produto_catalogo.id = produto_empresa.catalogo_id and produto_empresa.removida is not true and data_bloqueio_auto > now()
                                                            <if test="idEmpresa != null">
                                                              and produto_empresa.id = #{idEmpresa}
                                                            </if>)
                          join integracao_delivery produto_integracao_delivery on (produto_integracao_delivery.empresa_id = produto_empresa.id and sistema = 'ecletica' and nao_sincronizar_disponiveis is not true )
                          left join produto_adicional_produto on (produto_adicional_produto.objeto_id = produto.id )
                          left join adicional_produto on (adicional_produto.excluido is not true and
                                produto_adicional_produto.adicional_produto_id = adicional_produto.id)
                          left join opcao_adicional_produto on adicional_produto.id = opcao_adicional_produto.adicional_produto_id and
                                opcao_adicional_produto.excluido is not true
                          left join produto_template_opcao on produto_template_opcao.id = produto_template_opcao_id
                          left join opcao_na_empresa on (opcao_na_empresa.opcao_id = opcao_adicional_produto.id
                                and opcao_na_empresa.empresa_id = produto_empresa.id)
                          left join produto_na_empresa on (produto_na_empresa.produto_id = produto.id and produto_na_empresa.empresa_id = produto_empresa.id)
                          left join produto_tamanho on produto_tamanho.produto_id = produto.id
    where
          (produto.codigo_pdv is not null and COALESCE(produto_na_empresa.disponibilidade, produto.disponibilidade)  = 2 )
       or
          (opcao_adicional_produto.codigo_pdv is not null and COALESCE(produto_template_opcao.disponivel, opcao_na_empresa.disponivel,opcao_adicional_produto.disponivel) is not true )
       or
          (produto_tamanho.codigo_pdv is not null and produto_tamanho.disponivel is not true)




  </select>

  <select id="menorOrdemDaCategoria" resultType="long">
    select min(produto.ordem) ordem from produto join categoria on (produto.categoria_id = categoria.id)
    where
    produto.catalogo_id = #{idCatalogo}
    and produto.removido is not true
    and categoria.id = #{idCategoria};
  </select>

  <select id="maiorOrdemDaCategoria" resultType="long">
    select max(produto.ordem) ordem from produto join categoria on (produto.categoria_id = categoria.id)
    where
    produto.catalogo_id = #{idCatalogo}
    and produto.removido is not true
    and categoria.id = #{idCategoria};
  </select>

  <resultMap id="dtoProdutoMigrarRM" type="DTOGenerico">
    <id property="id" column="id"/>

    <result property="produto" column="produto"/>
    <result property="catalogo" column="catalogo_id"/>
    <result property="dias" column="dias"/>
    <result property="hora_inicio" column="hora_inicio"/>
    <result property="hora_fim" column="hora_fim"/>
  </resultMap>


  <select id="selecioneDisponibilidadeMigrar"  parameterType="map" resultMap="dtoProdutoMigrarRM">
    SELECT uuid() id, produto.id produto, catalogo.id catalogo_id, group_concat(distinct produto_horario.dia) dias,
          produto_turno.hora_inicio, produto_turno.hora_fim
        FROM catalogo join empresa on empresa.catalogo_id = catalogo.id
                      JOIN produto ON catalogo.id = produto.catalogo_id     JOIN produto_horario ON produto.id = produto_horario.produto_id
                      LEFT JOIN produto_turno ON produto.id = produto_turno.produto_id
          WHERE produto.removido is not true  and not exists (select 1 from produto_disponibilidade where produto_id = produto.id)

                    GROUP BY produto.id, hora_inicio,hora_fim  ;

  </select>

  <select id="selecioneReordenarAdicionais" parameterType="map" resultMap="produtoResultMap" prefix="true">
    select * from produto where removido is not true and id in (
        select  distinct(objeto_id) from produto_adicional_produto join adicional_produto on adicional_produto.id = adicional_produto_id and excluido is not true
        group by produto_adicional_produto.catalogo_id,objeto_id, produto_adicional_produto.ordem having count(*) > 1
      )

  </select>



  <insert id="insira" parameterType="map">
    insert into produto(nome,descricao,preco,catalogo_id,exibir_no_site, disponibilidade, destaque, codigo_pdv, sku, id_ifood,
                        tipo_de_venda,qtd_minima, valor_inicial,incremento, novo_preco, percentual_desconto, valor_resgate,
                        disponivel_para_delivery,disponivel_na_mesa,tipo,tem_estoque,  unidade_medida_id, categoria_id,
                        template_id, peso_minimo, peso_maximo, codigo_de_barras, nao_aceita_cupom, exibir_preco_no_cardapio, eh_brinde,
                        origem, data_cadastro, pontos_ganhos, cashback,sincronizar_modelos, cupom_id)
      values(#{nome}, #{descricao}, #{preco}, #{catalogo.id}, #{exibirNoSite}, #{disponibilidade}, #{destaque}, #{codigoPdv}, #{sku}, #{idIfood},
            #{tipoDeVenda},#{qtdeMinima}, #{valorInicial}, #{incremento}, #{novoPreco}, #{percentualDesconto},  #{valorResgate},
            #{disponivelParaDelivery},#{disponivelNaMesa},#{tipo},#{temEstoque}, #{unidadeMedida.id}, #{categoria.id},
            #{template.id}, #{pesoMinimo}, #{pesoMaximo}, #{codigoDeBarras}, #{naoAceitaCupom}, #{exibirPrecoNoCardapio}, #{ehBrinde},
            #{origem}, #{dataCadastro}, #{pontosGanhos}, #{cashback}, #{sincronizarModelos}, #{cupom.id})
  </insert>

  <update id="atualize" parameterType="map" >
    update produto p
      set p.nome = #{nome},
          p.preco = #{preco},
          p.novo_preco = #{novoPreco},
          p.valor_resgate = #{valorResgate},
          p.percentual_desconto = #{percentualDesconto},
          p.descricao = #{descricao},
          p.mensagem_pedido = #{mensagemPedido},
          p.exibir_no_site = #{exibirNoSite},
          p.qtd_minima = #{qtdeMinima},
          p.qtd_maxima = #{qtdMaxima},
          p.peso_minimo = #{pesoMinimo},
          p.peso_maximo = #{pesoMaximo},
          p.categoria_id = #{categoria.id},
          p.exibir_preco_site = #{exibirPrecoSite},
          p.tipo_de_venda = #{tipoDeVenda},
          p.unidade_medida_id = #{unidadeMedida.id},
          p.valor_inicial = #{valorInicial},
          p.incremento = #{incremento},
          p.destaque = #{destaque},
          p.exibir_preco_no_cardapio = #{exibirPrecoNoCardapio},
          p.codigo_pdv = #{codigoPdv},
          p.sku = #{sku},
          p.eh_brinde = #{ehBrinde},
          p.nao_aceita_cupom = #{naoAceitaCupom},
          p.sincronizar_modelos = #{sincronizarModelos},
          p.origem = #{origem},
          p.cupom_id = #{cupom.id}

    <!--,          codigo_de_barras = #{codigoDeBarras}-->
        where p.id = #{id} and p.catalogo_id = #{catalogo.id};

    update opcao_adicional_produto opcao join produto on produto.id = produto_id
                  left join produto_tamanho on produto_tamanho.id = opcao.produto_tamanho_id
                  left join produto_template_tamanho on produto_template_tamanho.id = produto_template_tamanho_id
        set  opcao.nome = if(opcao.produto_tamanho_id is null, produto.nome, concat(produto.nome, ' - ', produto_template_tamanho.descricao)),
             opcao.codigo_pdv =  if(opcao.produto_tamanho_id is null,produto.codigo_pdv,produto_tamanho.codigo_pdv)
               where produto.id =  #{id} and catalogo_id = #{catalogo.id};

  </update>

  <update id="atualizeFoto" parameterType="map" >
    update produto
      set    link_imagem = #{linkImagem}
        where id = #{id} and catalogo_id = #{catalogo.id}
  </update>


  <update id="atualizeUnidadeMedida" parameterType="map" >
    update produto
      set    unidade_medida_id = #{unidadeMedida.id}, tipo_de_venda = #{tipoDeVenda},
             valor_inicial = #{valorInicial}, incremento = #{incremento}
        where id = #{id} and empresa_id = #{empresa.id}
  </update>

  <update id="atualizeULtimaOrdem" parameterType="map" >
    update produto,  (select if(max(ordem) is not null, max(ordem) + 1, 1)ordem from produto p2 where  p2.catalogo_id = #{catalogo.id} ) t
      set   produto.ordem = t.ordem
        where produto.id = #{id}
  </update>

  <update id="atualizeOrdem" parameterType="map" >
    update produto
      set   produto.ordem =  #{ordem}
        where produto.id = #{id} and catalogo_id = #{catalogo.id}
  </update>

  <update id="atualizePreco" parameterType="map" >
    update produto
      set   preco =  #{preco}, novo_preco = #{novoPreco},
            percentual_desconto = #{percentualDesconto}, id_ifood = #{idIfood}
        where produto.id = #{id} and catalogo_id = #{catalogo.id}
  </update>

  <update id="atualizeDescricao" parameterType="map" >
    update produto
      set   descricao =  #{descricao}
        where produto.id = #{id} and catalogo_id = #{catalogo.id}
  </update>

  <update id="atualizeDisponibilidade" parameterType="map" >
    update produto
      set
        tem_estoque = #{temEstoque},
        exibir_preco_site = #{exibirPrecoSite},
        exibir_preco_no_cardapio = #{exibirPrecoNoCardapio},
        exibir_no_site = #{exibirNoSite},
        disponibilidade = #{disponibilidade},
        disponivel_na_mesa = #{disponivelNaMesa},
        disponivel_para_delivery = #{disponivelParaDelivery},
        nao_sincronizar = #{naoSincronizar},
        mensagem_pedido = #{mensagemPedido}
        where id = #{id} and catalogo_id = #{catalogo.id};

    update opcao_adicional_produto opcao join produto on produto.id = produto_id
          set opcao.disponivel = produto.disponibilidade != 2
            where produto.id =  #{id} and catalogo_id = #{catalogo.id};


    update produto_na_empresa pne join produto on produto.id = pne.produto_id
    set pne.disponibilidade = produto.disponibilidade
    where produto.id =  #{id} and catalogo_id = #{catalogo.id} and pne.disponibilidade != 2
    and produto.disponibilidade != 2;
  </update>

  <update id="atualizeDisponibilidadeCatalogo" parameterType="map" >
    update produto
    set

    disponivel_na_mesa = #{disponivelNaMesa},
    disponivel_para_delivery = #{disponivelParaDelivery}

    where id = #{id} and catalogo_id = #{catalogo.id}
  </update>


  <update id="atualizePontosFidelidade">
    update produto
    set  pontos_ganhos =  #{pontosGanhos}, cashback = #{cashback}
        where   catalogo_id = #{catalogo.id} and id = #{id}
  </update>

  <update id="atualizeDisponibilidades">
    update produto
        set
          tem_estoque = #{temEstoque},
          disponibilidade = #{disponibilidade}
     where  catalogo_id = #{catalogo.id} and id in

        <foreach item="id" collection="ids" open="( " separator="," close=")">
          #{id}
        </foreach>
  </update>

  <update id="remova">
    update produto p
    set p.removido = true, ordem = null
    where p.id = #{id}
      and catalogo_id = #{catalogo.id}
  </update>

  <update id="removaTodosProdutos">
    update produto p
        set p.removido = true, ordem = null
             where  catalogo_id = #{catalogo.id}
  </update>

  <insert id="insiraDisponibilidade" parameterType="map" >
      insert into produto_disponibilidade(produto_id, disponibilidade_id)
        values (#{produto.id}, #{disponibilidade.id})
  </insert>

  <insert id="insiraTamanho" parameterType="map" >
    insert into produto_tamanho (produto_id, produto_template_tamanho_id, preco, disponivel, novo_preco, percentual_desconto, destaque, codigo_pdv)
          values   ( #{produto.id}, #{template.id} , #{preco} , #{disponivel}, #{novoPreco}, #{percentualDesconto}, #{destaque}, #{codigoPdv} )
  </insert>

  <update id="atualizeTamanho">
    update produto_tamanho
        set preco =  #{preco},
            novo_preco = #{novoPreco},
            disponivel  = #{disponivel},
            codigo_pdv =  #{codigoPdv},
            percentual_desconto = #{percentualDesconto},
            destaque =  #{destaque}
        where id = #{id};

    update opcao_adicional_produto opcao join produto_tamanho on produto_tamanho.id = opcao.produto_tamanho_id
        set opcao.disponivel = produto_tamanho.disponivel
                where produto_tamanho.id =  #{id};

  </update>

  <update id="atualizePontosFidelidadeTamanho">
    update produto_tamanho
    set
    pontos_ganhos =  #{pontosGanhos},
    cashback =  #{cashback}
    where id = #{id};
  </update>


  <update id="atualizePrecoTamanho">
    update produto_tamanho join produto on  produto.id = produto_id
      set produto_tamanho.preco =  #{preco},
          produto_tamanho.novo_preco = #{novoPreco},
          produto_tamanho.percentual_desconto = #{percentualDesconto}
       where produto_tamanho.id = #{id}  and produto.catalogo_id =  #{catalogo.id}
  </update>

  <update id="atualizeDisponibilidadeTamanho">
    update produto_tamanho
    set disponivel  = #{disponivel}
    where id = #{id};

    update opcao_adicional_produto opcao join produto_tamanho on produto_tamanho.id = opcao.produto_tamanho_id
    set opcao.disponivel = produto_tamanho.disponivel
    where produto_tamanho.id =  #{id};


    update produto_na_empresa pne join produto on produto.id = pne.produto_id
    set pne.disponibilidade = produto.disponibilidade
    where produto.id =  #{id} and catalogo_id = #{catalogo.id} and pne.disponibilidade != 2
    and produto.disponibilidade != 2;
  </update>



  <update id="removaHorariosProduto">
    delete from produto_horario where produto_id = #{id}
  </update>

  <update id="removaDisponibiliadesProduto">
    delete from produto_disponibilidade where produto_id = #{id}
  </update>


  <update id="recalculeOrdens">
    update produto ,
          (select produto.id, produto.nome, produto.preco, ROW_NUMBER() over w as ordem from produto
            join categoria on (produto.categoria_id = categoria.id)
            where produto.removido is not true
            and produto.catalogo_id = #{idCatalogo}
            WINDOW w AS
                  <choose>
                    <when test="porNome">
                       (order by produto.nome)
                    </when>
                    <otherwise>
                      (order by categoria.nivel, categoria.posicao asc, produto.ordem)
                    </otherwise>

                  </choose>
                  ) produto_ordens
    set produto.ordem = produto_ordens.ordem
    where produto.id = produto_ordens.id;
  </update>

  <update id="recalculeOrdens55">
    update produto ,
    (select *, @rownum := @rownum + 1 ordem from
    (select produto.id, produto.nome, produto.preco from produto
              join categoria on (produto.categoria_id = categoria.id)
              where produto.catalogo_id = #{idCatalogo} and produto.removido is not true
                <choose>
                  <when test="porNome">
                    order by produto.nome
                  </when>
                  <otherwise>
                    order by categoria.nivel,categoria.posicao asc, produto.ordem
                  </otherwise>

                </choose>
            ) produtos_ordenados,
              (SELECT @rownum := 0) r) produto_ordens
    set produto.ordem = produto_ordens.ordem
             where produto.id = produto_ordens.id;
  </update>


  <update id="subirTopoCategoria">
    update produto join categoria on (produto.categoria_id = categoria.id) set produto.ordem = produto.ordem + 1
    where categoria.id = #{idCategoria}
    and produto.catalogo_id = #{idCatalogo}
    and produto.ordem &lt; #{ordemProduto};
    update produto set ordem = #{ordemTopo} where id = #{idProduto};
  </update>

  <update id="descerFinalCategoria">
    update produto join categoria on (produto.categoria_id = categoria.id) set produto.ordem = produto.ordem - 1
    where categoria.id = #{idCategoria}
    and produto.catalogo_id = #{idCatalogo}
    and produto.ordem &gt; #{ordemProduto};

    update produto set ordem = #{ordemTopo} where id = #{idProduto};
  </update>

  <update id="removaAdicionaisDuplicados">

    insert into produto_adicional_produto_temp (objeto_id,adicional_produto_id,ordem,catalogo_id,empresa_id)
      select   objeto_id,adicional_produto_id,ordem,catalogo_id,empresa_id
        FROM produto_adicional_produto
        group by objeto_id,adicional_produto_id,catalogo_id having count(*) > 1;


    delete from produto_adicional_produto pap where exists
    (select 1 from produto_adicional_produto_temp t
         where t.objeto_id = pap.objeto_id and t.adicional_produto_id = pap.adicional_produto_id and t.catalogo_id = pap.catalogo_id );


    alter table produto_adicional_produto add unique(objeto_id,adicional_produto_id,catalogo_id);

    insert into produto_adicional_produto(objeto_id,adicional_produto_id,ordem,catalogo_id,empresa_id)
        select objeto_id,adicional_produto_id,ordem,catalogo_id,empresa_id from produto_adicional_produto_temp;

  </update>

  <update id="reordeneAdicionais">
    WITH CTE AS (
    SELECT objeto_id, catalogo_id, adicional_produto_id,
    ROW_NUMBER() OVER (  PARTITION BY objeto_id  ORDER BY ordem, adicional_produto_id) - 1 AS nova_ordem
    FROM  produto_adicional_produto  WHERE  objeto_id =  #{idProduto} and   catalogo_id = #{idCatalogo}
    )
    UPDATE produto_adicional_produto
        JOIN CTE  ON produto_adicional_produto.objeto_id = CTE.objeto_id AND
                     produto_adicional_produto.adicional_produto_id = CTE.adicional_produto_id  and
                     produto_adicional_produto.catalogo_id = CTE.catalogo_id
    SET produto_adicional_produto.ordem = CTE.nova_ordem


  </update>

  <update id="tirarOpcoesSincronizacao">
    update  produto join adicional_produto on produto_id = produto.id join opcao_adicional_produto opcao on adicional_produto.id = adicional_produto_id
      set nao_sincronizar = true, opcao.valor = 9.90
          where produto.codigo_pdv in ('9000000009', '9000000010', '9000000011', '9000000012', '9000000013', '9000000014') and
                 opcao.codigo_pdv in ('2759', '2757', '2755') and produto.removido is not true and opcao.excluido is not true ;


    update  produto join adicional_produto on produto_id = produto.id join opcao_adicional_produto opcao on adicional_produto.id = adicional_produto_id
    set nao_sincronizar = true, opcao.valor = 9.90
    where produto.codigo_pdv in ('9000000484', '9000000485', '9000000483', '9000000487', '9000000488', '9000000489') and
    opcao.codigo_pdv in ('2759', '2757', '2755') and produto.removido is not true and opcao.excluido is not true ;





  </update>

  <update id="view-china">
    create or replace view produtos_china as
    select  empresa.nome empresa,empresa.catalogo_id,
    produto.id produto_id,  produto.nome produto_nome, produto.codigo_pdv,preco,false as opcao, 0 pdv_produto
    from empresa  join  catalogo on empresa.catalogo_id = catalogo.id join produto on produto.catalogo_id = catalogo.id
    where   rede_id = 1 and  removida is not true and produto.removido is not true and disponivel_para_delivery is true and nao_sincronizar is not true
           and disponibilidade = 0
    union
    select empresa.nome empresa,empresa.catalogo_id,
    op.id produto_id, op.nome  COLLATE utf8mb3_unicode_ci produto_nome,  op.codigo_pdv COLLATE utf8mb3_unicode_ci,
    op.valor COLLATE utf8mb3_unicode_ci, true   as opcao, produto.codigo_pdv pdv_produto
    from empresa join produto on produto.catalogo_id = empresa.catalogo_id and removida is not true
    join adicional_produto on produto.id = produto_id join opcao_adicional_produto op on adicional_produto.id = adicional_produto_id
    where produto.removido is not true     and op.excluido is not true and rede_id = 1 and disponivel_para_delivery is true   and disponibilidade = 0;


    create or replace view produtos_template as
    select  catalogo.nome catalogo,catalogo.id,
    produto.id produto_id,  produto.nome produto_nome, produto.codigo_pdv,preco,false as opcao, 0 pdv_produto
    from catalogo  join produto on produto.catalogo_id = catalogo.id
    where    catalogo_id in (1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476)
    union
    select catalogo.nome catalogo,catalogo.id,
    op.id produto_id, op.nome  COLLATE utf8mb3_unicode_ci produto_nome,  op.codigo_pdv COLLATE utf8mb3_unicode_ci,
    op.valor COLLATE utf8mb3_unicode_ci, true   as opcao, produto.codigo_pdv pdv_produto
    from catalogo join produto on produto.catalogo_id = catalogo.id
    join adicional_produto on produto.id = produto_id join opcao_adicional_produto op on adicional_produto.id = adicional_produto_id
    where produto.removido is not true     and op.excluido is not true and  catalogo.id in (1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476)

  </update>

  <update id="atualizarCatalogoPrecosLote">
    UPDATE produto
    SET preco =
    CASE
    WHEN preco * 1.2 - FLOOR(preco * 1.2) > 0 THEN CEIL(preco * 1.2) - 0.01
    ELSE preco * 1.2
    END
    WHERE catalogo_id = 1533;

    UPDATE opcao_adicional_produto join adicional_produto on adicional_produto_id = adicional_produto.id
              join produto on produto.id = adicional_produto.produto_id
        set opcao_adicional_produto.valor = CEIL(opcao_adicional_produto.valor*1.2)
      where valor > 0 and produto.catalogo_id = 1533;

    update produto join produto_tamanho on  produto.id = produto_id set produto_tamanho.preco = 65
      where catalogo_id = 1759 and removido is not true and tipo = 'Pizza' and produto_template_tamanho_id = 1195 and produto_tamanho.preco = 60;


  </update>

  <update id="replicarUmAdicionalEmTodosProdutos">

     insert IGNORE into produto_adicional_produto(objeto_id,adicional_produto_id,ordem,catalogo_id)
     select id,256409,if(objeto_id is null, 0, max(produto_adicional_produto.ordem) + 1) ordem,produto.catalogo_id
     from produto left join produto_adicional_produto on produto.id = objeto_id
     where produto.catalogo_id = 1474 and removido is not true and codigo_pdv not in (20160,20162,20164,20166,39200,39202,39204,39206)
     and not exists (select 1 from produto_adicional_produto where objeto_id = produto.id and adicional_produto_id = 256409)
     group by produto.id ;



    update adicional_produto set compartilhado  = true where id  in (255407,255408, 255605,255718,255854,255925,256367,256369,256371,256396,256409);
    update opcao_adicional_produto set qtde_maxima = 4 where adicional_produto_id  in (255407,255408, 255605,255718,255854,255925,256367,256369,256371,256396,256409);




  </update>

   <update id="replicarAdicionalProdutoModelos">
     <!-- ecleticas
     1465 | CIB Ecletica T1  | 30509 | Bowls Colecionáveis
     1466 | CIB Ecletica T2 | 30510 | Bowls Colecionáveis
     1467 | CIB Ecletica T3 | 30511 | Bowls Colecionáveis
     1468 | CIB Ecletica T5 | 30512 | Bowls Colecionáveis
     1469 | CIB Ecletica T6 | 30513 | Bowls Colecionáveis
     1470 | CIB Ecletica T7 | 30514 | Bowls Colecionáveis
    -->

    insert into

  </update>

  <update id="replicarProdutoModelos">
    <!-- ecleticas
     1465 | CIB Ecletica T1  | 30509 | Bowls Colecionáveis
     1466 | CIB Ecletica T2 | 30510 | Bowls Colecionáveis
     1467 | CIB Ecletica T3 | 30511 | Bowls Colecionáveis
     1468 | CIB Ecletica T5 | 30512 | Bowls Colecionáveis
     1469 | CIB Ecletica T6 | 30513 | Bowls Colecionáveis
     1470 | CIB Ecletica T7 | 30514 | Bowls Colecionáveis
    -->
    insert into produto(catalogo_id,categoria_id,nome,descricao,preco,ordem,tipo_de_venda,incremento,disponivel_na_mesa,disponivel_para_delivery,disponibilidade,qtd_minima,tipo,codigo_pdv,
    exibir_preco_no_cardapio,origem,data_cadastro,sincronizar_modelos)
     select 1470,30514,nome,descricao,preco,ordem,tipo_de_venda,incremento,disponivel_na_mesa,disponivel_para_delivery,disponibilidade,qtd_minima,tipo,codigo_pdv,
          exibir_preco_no_cardapio,origem,data_cadastro,sincronizar_modelos from produto where categoria_id = 30509 and catalogo_id= 1465 ;



    insert IGNORE into imagem_do_produto(produto_id,link_imagem,ordem)
     select id,'ffc126a0-6c61-11ee-8d19-598dfc6a1f7d.png' link_imagem,0 ordem from produto
          where codigo_pdv = 20166 and not exists (select 1 from imagem_do_produto where produto_id = produto.id);

    insert IGNORE into imagem_do_produto(produto_id,link_imagem,ordem)
    select id,'b78d6ab0-6c61-11ee-8d19-598dfc6a1f7d.png' link_imagem,0 ordem from produto
    where codigo_pdv = 20164 and not exists (select 1 from imagem_do_produto where produto_id = produto.id);


    insert IGNORE into imagem_do_produto(produto_id,link_imagem,ordem)
    select id,'6d12f310-6c61-11ee-b913-1702482d565a.png' link_imagem,0 ordem from produto
    where codigo_pdv = 20162 and not exists (select 1 from imagem_do_produto where produto_id = produto.id);


    insert IGNORE into imagem_do_produto(produto_id,link_imagem,ordem)
    select id,'17160290-6c61-11ee-9694-61ddaf0fa6f1.png' link_imagem,0 ordem from produto
    where codigo_pdv = 20160 and not exists (select 1 from imagem_do_produto where produto_id = produto.id);

    <!-- gcoms
      1471 | CIB GCOM T1     |
      1472 | CIB GCOM T2     | 30515 | Bowls Colecionáveis  |
      1473 | CIB GCOM T3     | 30516 | Bowls Colecionáveis
      1474 | CIB GCOM T5     | 30517 | Bowls Colecionáveis
      1475 | CIB GCOM T6     | 30518 | Bowls Colecionáveis
      1476 | CIB GCOM T7     | 30519 | Bowls Colecionáveis

        -->

    insert into produto(catalogo_id,categoria_id,nome,descricao,preco,ordem,tipo_de_venda,incremento,disponivel_na_mesa,disponivel_para_delivery,disponibilidade,qtd_minima,tipo,codigo_pdv,
    exibir_preco_no_cardapio,origem,data_cadastro,sincronizar_modelos)
    select 1476,30519,nome,descricao,preco,ordem,tipo_de_venda,incremento,disponivel_na_mesa,disponivel_para_delivery,disponibilidade,qtd_minima,tipo,codigo_pdv,
    exibir_preco_no_cardapio,origem,data_cadastro,sincronizar_modelos from produto where categoria_id = 30515 and catalogo_id= 1472 and removido is not true;



    insert IGNORE into imagem_do_produto(produto_id,link_imagem,ordem)
    select id,'ffc126a0-6c61-11ee-8d19-598dfc6a1f7d.png' link_imagem,0 ordem from produto
    where codigo_pdv = 39206 and not exists (select 1 from imagem_do_produto where produto_id = produto.id);

    insert IGNORE into imagem_do_produto(produto_id,link_imagem,ordem)
    select id,'b78d6ab0-6c61-11ee-8d19-598dfc6a1f7d.png' link_imagem,0 ordem from produto
    where codigo_pdv = 39204 and not exists (select 1 from imagem_do_produto where produto_id = produto.id);


    insert IGNORE into imagem_do_produto(produto_id,link_imagem,ordem)
    select id,'6d12f310-6c61-11ee-b913-1702482d565a.png' link_imagem,0 ordem from produto
    where codigo_pdv = 39202 and not exists (select 1 from imagem_do_produto where produto_id = produto.id);


    insert IGNORE into imagem_do_produto(produto_id,link_imagem,ordem)
    select id,'17160290-6c61-11ee-9694-61ddaf0fa6f1.png' link_imagem,0 ordem from produto
    where codigo_pdv = 39200 and not exists (select 1 from imagem_do_produto where produto_id = produto.id);



  </update>



</mapper>
