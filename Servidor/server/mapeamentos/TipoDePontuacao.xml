<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tipoDePontuacao">
  <resultMap id="tipoDePontuacaoResultMap" type="TipoDePontuacao">
    <id property="id" column="tipo_de_pontuacao_id"/>

    <result property="valorPorPonto" column="tipo_de_pontuacao_valor_por_ponto"/>
    <result property="pontosPorValor" column="tipo_de_pontuacao_pontos_por_valor"/>
    <result property="selosPorAtividade" column="tipo_de_pontuacao_selos_por_atividade"/>

    <result property="tipo" column="tipo_de_pontuacao_tipo"/>

    <discriminator javaType="String" column="tipo_de_pontuacao_tipo" >
      <case value="cashback" resultType="TipoDePontuacaoCashback"></case>
      <case value="por-valor" resultType="TipoDePontuacaoPorValor"></case>
      <case value="por-pontos" resultType="TipoDePontuacaoPorPontos"></case>
      <case value="qtde-fixa" resultType="TipoDePontuacaoQtdFixa"></case>
      <case value="qtde-fixa-por-atividade" resultType="TipoDePontuacaoQtdFixaPorAtividade"></case>
      <case value="qtde-variavel-por-atividade" resultType="TipoDePontuacaoQtdVariavelPorAtividade"></case>
    </discriminator>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="tipoDePontuacaoResultMap" prefix="true">
    select *
    from tipo_de_pontuacao
    where tipo_de_pontuacao.empresa_id = #{idEmpresa};
  </select>

  <insert id="insira" parameterType="map"    >
    insert into tipo_de_pontuacao(tipo, valor_por_ponto, pontos_por_valor, selos_por_atividade, empresa_id)
      values(#{tipo}, #{valorPorPonto}, #{pontosPorValor}, #{selosPorAtividade}, #{empresa.id});
  </insert>

  <update id="atualize" parameterType="map"  >
    update tipo_de_pontuacao tp  join empresa e
      set tp.tipo = #{tipo},
            tp.valor_por_ponto = #{valorPorPonto},
            tp.selos_por_atividade = #{selosPorAtividade},
            tp.pontos_por_valor = #{pontosPorValor}
        where tp.id = #{id}      and e.id = tp.empresa_id
  </update>
</mapper>
