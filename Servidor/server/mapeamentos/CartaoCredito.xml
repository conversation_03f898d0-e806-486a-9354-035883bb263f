<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cartaocredito">

  <resultMap id="cartaocreditoRM" type="CartaoCredito">
    <id property="id" column="cartao_credito_id"/>

    <result property="numero" column="cartao_credito_numero"/>
    <result property="bandeira" column="cartao_credito_bandeira"/>
    <result property="codigo" column="cartao_credito_codigo"/>
    <result property="validade" column="cartao_credito_validade"/>
    <result property="padrao" column="cartao_credito_padrao"/>


  </resultMap>

  <select id="selecione" parameterType="map" resultMap="cartaocreditoRM"  prefix="true">
        select * from cartao_credito
              where codigo = #{codigo}
  </select>
</mapper>
