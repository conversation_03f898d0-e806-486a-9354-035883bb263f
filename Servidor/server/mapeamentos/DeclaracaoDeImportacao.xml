<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="declaracaoDeImportacao">
  <resultMap id="declaracaoDeImportacaoRM" type="DeclaracaoDeImportacao">
    <id property="id" column="declaracao_de_importacao_id"/>
    <result property="numeroDI" column="declaracao_de_importacao_numero_di"/>
    <result property="dataDI" column="declaracao_de_importacao_data_di"/>
    <result property="localDesembaraco" column="declaracao_de_importacao_local_desembaraco"/>
    <result property="ufDesembaraco" column="declaracao_de_importacao_uf_desembaraco"/>
    <result property="dataDesembaraco" column="declaracao_de_importacao_data_desembaraco"/>
    <result property="viaTransporte" column="declaracao_de_importacao_via_transporte"/>
    <result property="valorAFRMM" column="declaracao_de_importacao_valor_afrmm"/>
    <result property="tipoIntermedio" column="declaracao_de_importacao_tipo_intermedio"/>
    <result property="cnpj" column="declaracao_de_importacao_cnpj"/>
    <result property="ufTerceiro" column="declaracao_de_importacao_uf_terceiro"/>
    <result property="codigoExportador" column="declaracao_de_importacao_codigo_exportador"/>

    <collection property="adicoes" resultMap="adicao.adicaoRM"/>

    <association property="item" column="item_nfe_id" resultMap="itemNFe.itemNFeRM"/>
  </resultMap>


  <create id="crieTabela" parameterType="map">
    create table if not exists declaracao_de_importacao (
      id bigint(20) primary key,
      numero_di varchar(10),
      data_di date,
      local_desembaraco varchar(100),
      uf_desembaraco varchar(2),
      data_desembaraco date,
      via_transporte int,
      valor_afrmm decimal(10,2),
      tipo_intermedio int,
      cnpj varchar(14),
      uf_terceiro varchar(2),
      item_nfe_id bigint(20),
      codigo_exportador varchar(10),
      foreign key (item_nfe_id) references item_nfe (id)
    );
  </create>
</mapper>
