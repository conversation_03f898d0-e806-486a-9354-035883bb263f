<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="empresaDoGrupo">
  <resultMap id="empresaDoGrupoRM" type="EmpresaDoGrupo">
    <id property="id" column="grupo_de_lojas_empresa_id" />

    <result property="listarLoja" column="grupo_de_lojas_empresa_listar_loja" />

    <association  property="empresa"   resultMap="empresa.empresaReferenciaRM"/>
  </resultMap>

  <resultMap id="empresaPrincipalRM" type="DTOObjetoComNome">
    <id property="id" column="grupo_de_lojas_empresa_principal_id"/>
  </resultMap>

  <update id="atualizeListarLoja" parameterType="map">
    update grupo_de_lojas_empresa set listar_loja = #{listarLoja}
    where grupo_de_lojas_empresa.id = #{id};
  </update>
</mapper>
