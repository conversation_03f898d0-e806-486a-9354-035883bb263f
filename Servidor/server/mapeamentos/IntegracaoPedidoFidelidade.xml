<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="integracaoPedidoFidelidade">

  <resultMap id="integracaoPedidoFidelidadeRM" type="IntegracaoPedidoFidelidade">
    <id property="id" column="integracao_pedido_fidelidade_id"/>

    <result property="ativa" column="integracao_pedido_fidelidade_ativa"/>
    <result property="data" column="integracao_pedido_fidelidade_data"/>
    <result property="pontuarSoLoja" column="integracao_pedido_fidelidade_pontuar_so_loja"/>
    <result property="pontuarMesas" column="integracao_pedido_fidelidade_pontuar_mesas"/>
    <result property="resgatarBrinde" column="integracao_pedido_fidelidade_resgatar_brinde"/>
    <result property="ocultarPontos" column="integracao_pedido_fidelidade_ocultar_pontos"/>

    <association  property="plano"   resultMap="plano.planoCartaoRM"/>
    <association  property="atividade"   resultMap="atividade.atividadeDoPlanoResultMap"/>

  </resultMap>

  <update id="remova">
    delete from   integracao_pedido_fidelidade
        where integracao_pedido_fidelidade.id = #{id} and empresa_id = #{idEmpresa};

  </update>

  <update id="atualizePontuarSoLoja">
    update integracao_pedido_fidelidade
            set pontuar_so_loja = #{pontuarSoLoja}
                where id =  #{id} and empresa_id = #{idEmpresa};
  </update>

  <update id="atualizePontuarMesas">
    update integracao_pedido_fidelidade
            set pontuar_mesas = #{pontuarMesas}
                where id =  #{id} and empresa_id = #{idEmpresa};
  </update>

  <update id="atualizeResgatarBrinde">
    update integracao_pedido_fidelidade
    set resgatar_brinde = #{resgatarBrinde}
    where id =  #{id} and empresa_id = #{idEmpresa};
  </update>

  <update id="atualizeOcultarPontos">
    update integracao_pedido_fidelidade
    set ocultar_pontos = #{ocultarPontos}
    where id =  #{id} and empresa_id = #{idEmpresa};
  </update>



</mapper>
