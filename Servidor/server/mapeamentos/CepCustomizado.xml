<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="CepCustomizado">
  <resultMap id="cepCustomizadoRM" type="CepCustomizado">
    <id property="id" column="id"/>
    <result property="cep" column="cep"/>
    <result property="logradouro" column="logradouro"/>
    <result property="bairro" column="bairro"/>
    <result property="cidade" column="cidade"/>
    <result property="estado" column="estado"/>
    <result property="complemento" column="complemento"/>
    <result property="criado_em" column="criado_em"/>
    <result property="atualizado_em" column="atualizado_em"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="cepCustomizadoRM">
    SELECT * FROM cep_customizado
    where 1 = 1
      <if test="id != null">
        AND id = #{id}
      </if>
      <if test="cep != null">
        AND (cep = #{cep} OR REPLACE(cep, '-', '') = REPLACE(#{cep}, '-', ''))
      </if>
      <if test="termoBusca != null">
        AND (
          cep LIKE CONCAT('%', #{termoBusca}, '%')
          OR logradouro LIKE CONCAT('%', #{termoBusca}, '%')
          OR bairro LIKE CONCAT('%', #{termoBusca}, '%')
          OR cidade LIKE CONCAT('%', #{termoBusca}, '%')
          OR estado LIKE CONCAT('%', #{termoBusca}, '%')
        )
      </if>
    <if test="inicio != null">
      LIMIT #{inicio}, #{total};
    </if>
  </select>

  <select id="selecioneTotal" parameterType="map" resultType="int">
    SELECT count(*) FROM cep_customizado
    where 1 = 1
      <if test="id != null">
        AND id = #{id}
      </if>
      <if test="cep != null">
        AND (cep = #{cep} OR REPLACE(cep, '-', '') = REPLACE(#{cep}, '-', ''))
      </if>
      <if test="termoBusca != null">
        AND (
          cep LIKE CONCAT('%', #{termoBusca}, '%')
          OR logradouro LIKE CONCAT('%', #{termoBusca}, '%')
          OR bairro LIKE CONCAT('%', #{termoBusca}, '%')
          OR cidade LIKE CONCAT('%', #{termoBusca}, '%')
          OR estado LIKE CONCAT('%', #{termoBusca}, '%')
        )
      </if>
  </select>

  <insert id="insira" parameterType="CepCustomizado">
    INSERT INTO cep_customizado (
      cep, logradouro, bairro, cidade, estado, complemento
    ) VALUES (
      #{cep}, #{logradouro}, #{bairro}, #{cidade}, #{estado}, #{complemento}
    )
  </insert>

  <update id="atualize" parameterType="CepCustomizado">
    UPDATE cep_customizado SET
      cep = #{cep},
      logradouro = #{logradouro},
      bairro = #{bairro},
      cidade = #{cidade},
      estado = #{estado},
      complemento = #{complemento},
      atualizado_em = now()
    WHERE id = #{id}
  </update>

  <update id="remova" parameterType="map">
    DELETE FROM cep_customizado WHERE id = #{id}
  </update>
</mapper>
