<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="token">
  <resultMap id="tokenRM" type="Token">
    <id property="id" column="token_id" />

    <result property="token" column="token_token" />
    <result property="codigo" column="token_codigo" />
    <result property="validade" column="token_validade" />
    <result property="horario" column="token_horario" />
    <result property="validado" column="token_validado" />


    <association property="contato" resultMap="contato.contatoRM"/>
    <association property="usuario" resultMap="usuario.usuarioResultMap"/>

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="tokenRM" prefix="true">
    select *
      from  token  left join contato on token.contato_id  = contato.id
                   left join usuario on  token.usuario_id = usuario.id
       where
          <choose>
            <when test="token != null">
              token.token = #{token}
            </when>

            <when test="maquinaId != null">
              token.maquina_id = #{maquinaId} and usuario_id = #{idUsuario} and validado is true limit 1
            </when>

            <when test="ultimo">
              contato.id = #{idContato} order by token.id desc limit 1
            </when>
          </choose>
  </select>

  <update id="atualize">
    update token set validado = #{validado} where id = #{id}
  </update>

</mapper>
