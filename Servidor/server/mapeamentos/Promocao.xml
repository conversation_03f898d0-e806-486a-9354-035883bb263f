<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="promocao">
  <resultMap id="promocaoRM" type="Promocao">
    <id property="id" column="promocao_id"/>

    <result property="descricao" column="promocao_descricao" />
    <result property="dataInicio" column="promocao_data_inicio"/>
    <result property="dataFim" column="promocao_data_fim"/>
    <result property="ativa" column="promocao_ativa"/>
    <result property="cumulativa" column="promocao_cumulativa"/>
    <association property="empresa"     resultMap="empresa.empresaSimplesRM"/>
    <collection  property="regras" resultMap="regraDaPromocao.regraDaPromocaoRM"/>
    <collection  property="horarios" resultMap="promocao.promocaoHorarioRM"/>

  </resultMap>

  <resultMap id="promocaoHorarioRM" type="PromocaoHorario">
    <id property="id" column="promocao_horario_id"/>

    <result property="dia" column="promocao_horario_dia"/>
  </resultMap>


  <select id="selecione" parameterType="map" resultMap="promocaoRM" prefix="true">
    select * from promocao  join empresa on empresa.id = promocao.empresa_id
       join regra_da_promocao on regra_da_promocao.promocao_id = promocao.id
       left join  promocao_horario on promocao.id = promocao_horario.promocao_id
    left join produto_template_tamanho produto_template_tamanho_comprar on regra_da_promocao.tamanho_pizza_comprar_id = produto_template_tamanho_comprar.id
    left join produto_template_tamanho produto_template_tamanho_ganhar on regra_da_promocao.tamanho_pizza_ganhar_id = produto_template_tamanho_ganhar.id
       left join produto on regra_da_promocao.produto_id = produto.id
       left join adicional_produto regra_adicional_produto on regra_adicional_produto.id = regra_da_promocao.adicional_id
       left join opcao_adicional_produto regra_opcao_adicional_produto on regra_adicional_produto.id = regra_opcao_adicional_produto.adicional_produto_id
       left join adicional_produto on produto.id = adicional_produto.produto_id and adicional_produto.excluido is not true
       left join opcao_adicional_produto on adicional_produto.id = opcao_adicional_produto.adicional_produto_id and
    opcao_adicional_produto.excluido is not true

    where empresa.id = #{idEmpresa}  and regra_da_promocao.excluido is not true   and promocao.excluido is not true
    <choose>

      <when test="id != null">
        and promocao.id = #{id}
      </when>
      <when test="ativa != null">
        and promocao.ativa = #{ativa}
      </when>

    </choose>


  </select>

  <update id="atualize">
    update promocao
      set descricao = #{descricao},
          data_inicio = #{dataInicio},
          data_fim = #{dataFim},
          ativa = #{ativa},
          cumulativa = #{cumulativa}
    where id = #{id} and empresa_id = #{empresa.id}
  </update>

  <update id="atualizeStatusAtiva">
    update promocao
    set ativa = #{ativa}
    where id = #{id} and empresa_id = #{empresa.id}
  </update>

  <update id="remova">
    update promocao p
    set p.excluido = true
    where p.id = #{id}
    and empresa_id = #{empresa.id}
  </update>

  <insert id="insiraHorarios" parameterType="map" >
    insert into promocao_horario (id,promocao_id,dia)
    values
    <foreach item="item" collection="dados" open="" separator="," close="">
      ( #{id}, #{item.idPromocao} ,   #{item.dia}  )
    </foreach>
  </insert>

  <update id="removaHorarios">
    delete from promocao_horario where promocao_id = #{id}
  </update>

</mapper>

