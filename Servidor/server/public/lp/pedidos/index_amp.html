<!DOCTYPE html>
<html ⚡>
<head>
  <!-- AMP Analytics --><script async custom-element="amp-analytics" src="https://cdn.ampproject.org/v0/amp-analytics-0.1.js"></script>

  <meta charset="utf-8"/>
  <title>Crie seu cardápio digital com qr code e receba pedidos via Whatsapp.</title>
  <link rel="canonical" href="https://meucardapio.ai">
  <meta
    name="viewport"
    content="width=device-width,minimum-scale=1,initial-scale=1"
  />
  <meta name="description" content="Compartilhe seu cardápio, aceite pedidos de seus clientes para retirada ou delivery e fidelize sem sair do WhatsApp" >
  <meta name="robots" content="index, follow">
  <meta property="og:title" content="PromoKit - Compartilhe seu cardápio, receba pedidos, controle seu Delivery e fidelize seus clientes sem sair do WhatsApp.">
  <meta property="og:site_name" content="PromoKit">
  <meta property="og:description" content="Compartilhe seu cardápio, aceite pedidos de seus clientes para retirada ou delivery e fidelize sem sair do WhatsApp">
  <meta property="og:image" content="http://www.promokit.com.br/lp/pedidos/images/pedido01.png">
  <meta property="og:image" content="http://www.promokit.com.br/lp/pedidos/images/pedido02.png">
  <meta property="og:type" content="website">

  <script type="application/ld+json">
    {
      "@context" : "http://schema.org",
      "@type" : "SoftwareApplication",
      "name" : "PromoKit",
      "image" : "/lp/pedidos/images/pedido01.png",
      "applicationCategory" : [ "plataforma", "pedidos", "delivery", "fidelidade", "cardápio" ],
      "screenshot" : "/lp/pedidos/images/Mobile2.png"
    }
  </script>
  <style amp-custom>

    .bg6 .botaoEnviar {
      margin: 0 auto;
      width: 245px;
      display: block
    }


    form.amp-form-submit-success input, .bg6 form.amp-form-submit-success .botaoEnviar, form.amp-form-submit-success .explicacao{
      display: none
    }




    html {
      font-family: sans-serif;
      line-height: 1.15;
      -webkit-text-size-adjust: 100%;
      -webkit-tap-highlight-color: transparent;
      position: relative;
      min-height: 100%;
    }
    .row {
      display: flex;
      flex-wrap: wrap;
      margin-right: -12px;
      margin-left: -12px;
    }

    .text-center {
      text-align: center;
    }

    .barra-lateral {
      display: flex
      flex-basis: auto;
      align-items: center;
    }

    .invalid-feedback {
      display: none;
      width: 100%;
      margin-top: .25rem;
      font-size: .75rem;
      color: #f1556c;
    }

    .form-group {
      margin-bottom: 1rem;
    }

    button, input, optgroup, select, textarea {
      margin: 0;
      font-family: inherit;
    }

    button, input {
      overflow: visible;
    }

    button, select {
      text-transform: none;
    }



    .btn-success {
      background-color: #51ba5b;
      border-color: #4da854;
    }

    .btn {
      color: #fff;
    }

    .btn-success:not(:disabled):not(.disabled), .btn-success:not(:disabled):not(.disabled).active, .btn-success:not(:disabled):not(.disabled):active, .show>.btn-success.dropdown-toggle {
      background-color: #6db31b;
      border-color: #6db31b;
    }

    [type=button], [type=reset], [type=submit], button {
      -webkit-appearance: button;
    }

    .btn {
      display: inline-block;
      font-weight: 400;
      text-align: center;
      vertical-align: middle;
      user-select: none;
      background-color: transparent;
      border: 1px solid transparent;
      padding: .45rem .9rem;
      font-size: .8125rem;
      line-height: 1.5;
    }

    a{
      text-decoration: none;
    }

    [type=button]:not(:disabled), [type=reset]:not(:disabled), [type=submit]:not(:disabled), button:not(:disabled) {
      cursor: pointer;
    }

    .btn-rounded {
      border-radius: 2em;
    }

    .shadow-lg {
      box-shadow: 0 2px 5px 0 rgba(0,0,0,.16),0 2px 10px 0 rgba(0,0,0,.12)
    }

    .btn-group-lg>.btn, .btn-lg {
      padding: .5rem 1rem;
      font-size: 1.25rem;
      line-height: 1.5;
    }


    .col {
      flex-basis: 0;
      flex-grow: 1;
      max-width: 100%;
      position: relative;
      width: 100%;
    }
    dl, ol, p, ul {
      margin-top: 0;
      margin-bottom: 1rem;
    }

    h1, h2, h3, h4, h5, h6 {
      margin: 10px 0;
      font-weight: 500;
      font-family: Poppins,sans-serif;
      color: #343a40;
    }

    .h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
      line-height: 1.1;
    }

    *, ::after, ::before {
      box-sizing: border-box;
    }


    :root {
      --blue: #4a81d4;
      --indigo: #7e57c2;
      --purple: #6658dd;
      --pink: #f672a7;
      --red: #f1556c;
      --orange: #fd7e14;
      --yellow: #f7b84b;
      --green: #1abc9c;
      --teal: #02a8b5;
      --cyan: #4fc6e1;
      --white: #fff;
      --gray: #98a6ad;
      --gray-dark: #343a40;
      --primary: #7e57c2;
      --secondary: #72747b;
      --success: #1abc9c;
      --info: #4fc6e1;
      --warning: #f7b84b;
      --danger: #f1556c;
      --light: #f1f5f7;
      --dark: #323a46;
      --pink: #f672a7;
      --blue: #4a81d4;
      --breakpoint-xs: 0;
      --breakpoint-sm: 576px;
      --breakpoint-md: 768px;
      --breakpoint-lg: 992px;
      --breakpoint-xl: 1200px;
      --font-family-sans-serif: "Poppins",sans-serif;
      --font-family-monospace: SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;
    }

    *, ::after, ::before {
      box-sizing: border-box;
    }


    body {
      margin: 0;
      font-family: Poppins,sans-serif;
      font-size: .8125rem;
      font-weight: 400;
      line-height: 1.5;
      color: #72747b;
      text-align: left;
      background-color: #f7f8f8;
    }

    .loader {
      position: absolute;
      top: 50%;
      left: 50%;
      margin-top: -60px;
      margin-left: -60px;
      border: 16px solid #f3f3f3;
      border-radius: 50%;
      border-top: 16px solid #3498db;
      width: 120px;
      height: 120px;
      -webkit-animation: spin 2s linear infinite; /* Safari */
      animation: spin 2s linear infinite;
    }

    /* Safari */
    @-webkit-keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
      }
      100% {
        -webkit-transform: rotate(360deg);
      }
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    h1 {
      font-weight: 700
    }

    p {
      font-size: 14px
    }

    .escondido {
      display: none;
    }

    .base-logo {
      position: absolute;
      background: url(/lp/pedidos/images/base-logo.png) no-repeat;
      width: 250px;
      height: 250px;
      top: 0;
      left: 0;
      z-index: 100
    }

    .base-logo amp-img {
      width: 150px;
      position: relative;
      top: 35px;
      left: 35px
    }

    .bg1, .bg2, .bg3, .bg4, .bg5, .bg6 {
      background-color: #efefef
    }

    .bg1 > div, .bg2 > div, .bg3 > div, .bg4 > div, .bg5 > div, .bg6 > div {
      margin: 0 auto;
      max-width: 1000px
    }

    .bg1 {
      background: url(/lp/pedidos/images/bg-01.jpg) repeat-x;
      color: #fff;
      height: 700px;
      padding-top: 50px
    }

    .bg2 {
      color: rgba(0, 0, 0, .88);
      height: 430px
    }

    .bg3 {
      background: url(/lp/pedidos/images/bg-03.jpg);
      color: #26502a;
      height: 875px;
      padding-top: 200px;
      font-size: 15px
    }

    .bg4 {
      color: rgba(0, 0, 0, .88);
      height: 510px;
      overflow: hidden
    }

    .bg5 {
      background: url(/lp/pedidos/images/bg-05.jpg) 0 -10px #fff;
      padding-top: 120px;
      height: 720px;
      color: rgba(0, 0, 0, .88)
    }

    .bg6 {
      background-color: #fff ;
      padding-top: 150px;
      padding-bottom: 150px
    }

    .bg1 h1, .bg3 h1, .bg5 h1 {
      color: #fff
    }

    .bg2 h1, .bg4 h1, .bg6 h1, .bg6 h2 {
      color: #2c4499;
    }

    .bg2 .promo-info, .bg4 .promo-info {
      position: relative
    }

    .bg3 amp-img {
      right: 100px;
      position: relative
    }

    .bg1 img {
      max-width: 680px
    }

    .btn-blue {
      background-color: #2c449c;
      border-color: #4a81d4;
      color: #49b8ff;
      font-weight: 700
    }

    .btn-success {
      background-color: #51ba5b;
      border-color: #4da854
    }

    .check-group {
      background: #4d9e3f;
      width: 25px;
      border-radius: 15px;
      height: 25px;
      display: inline-block;
      padding-left: 7px;
      padding-top: 2px;
      margin-right: 10px
    }

    .check {
      --borderWidth: 5px;
      --height: 16px;
      --width: 10px;
      --borderColor: #72c83f;
      display: inline-block;
      transform: rotate(45deg);
      height: var(--height);
      width: var(--width);
      border-bottom: var(--borderWidth) solid var(--borderColor);
      border-right: var(--borderWidth) solid var(--borderColor)
    }

    ul li {
      display: block;
      padding-bottom: 12px
    }

    form {
      width: 600px;
      margin: 0 auto
    }


    .form-control {
      display: block;
      width: 100%;
      height: calc(1.5em + .9rem + 2px);
      padding: .45rem .9rem;
      font-size: .8125rem;
      font-weight: 400;
      line-height: 1.5;
      color: #72747b;
      background-color: #fff;
      background-clip: padding-box;
      border: 1px solid #ced4da;
      border-radius: .2rem;
      transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    }

    .navbar {
      position: relative;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;
      padding: .5rem 1rem;
    }

    .navbar-light .navbar-brand, .navbar-light .navbar-brand:focus, .navbar-light .navbar-brand:hover {
      color: rgba(0,0,0,.9);
    }

    .logo-fibo {
      width: 17px;
      overflow: hidden;
    }

    .btn-rounded {
      border-radius: 2em;
    }

    .border-white {
      border-color: #fff
    }

    .navbar-brand {
      display: inline-block;
      padding-top: .3125rem;
      padding-bottom: .3125rem;
      margin-right: 1rem;
      font-size: 1.25rem;
      line-height: inherit;
      white-space: nowrap;
    }

    .container-fluid {
      width: 100%;
      margin-right: auto;
      margin-left: auto;
    }

    .fixed-top {
      position: fixed;
      top: 0;
      right: 0;
      left: 0;
      z-index: 1030;
    }

    .flex-row {
      flex-direction: row;
    }

    .navbar-nav {
      display: flex;
      padding-left: 0;
      margin-bottom: 0;
      list-style: none;
    }



    .form-control {
      padding: 1.5rem 1rem;
      border-top: 0;
      border-left: 0;
      border-right: 0;
      border-radius: 0;
      color: rgba(0, 0, 0, .88)
    }

    button, input {
      overflow: visible;
    }

    input:focus {
      border-color: rgba(0, 0, 0, .88)
    }

    .row {
      margin: auto;
      max-width: 480px;
    }


      h1 {
        font-size: 24px
      }

      .base-logo {
        width: 150px;
        height: 150px;
        background-size: 150px
      }

      .base-logo amp-img {
        width: 100px;
        top: 20px;
        left: 30px
      }

      form {
        width: 100%
      }


      .bg1 > div, .bg2 > div, .bg3 > div, .bg4 > div, .bg5 > div, .bg6 > div {
        padding-right: 10px;
        padding-left: 10px;
        width: 100%
      }

      .bg1, .bg2, .bg3, .bg4, .bg5, .bg6 {
        position: relative;
        overflow: hidden
      }



      .bg1 amp-img, .bg2 amp-img, .bg3 amp-img, .bg4 amp-img, .bg5 amp-img, .bg6 img {
        width: 280px;
        position: absolute;
        top: 365px
      }

      .bg1 {
        height: 600px;
        padding-top: 30px
      }

      .bg1 .promo-info {
        padding-top: 100px;
      }

      .bg1 amp-img {
        top: 390px;
        width: 60%;
        left: 28%
      }

      .bg2 {
        height: 575px
      }

      .bg2 amp-img {
        top: 285px;
        width: 60%;
        left: 19%
      }

      .bg3 {
        height: 575px;
        background-position-y: -165px;
        padding-top: 0
      }

      .bg3 amp-img {
        width: 60%;
        top: 285px;
        left: 19%;

      }

      .bg3 ul {
        text-align: left;
        padding-left: 10px
      }

      .bg3 .check-group {
        margin-right: 10px
      }

      .bg4 {
        height: 500px
      }

      .bg4 amp-img {
        top: 290px;
        width: 70%;
        left: 11%
      }

      .bg5 {
        padding-top: 0;
        background-position-y: -100px;
        height: 540px
      }

      .bg5 amp-img {
        top: 285px;
        width: 70%;
        left: 10%
      }

      .bg6 {
        padding-top: 15px;
        padding-bottom: 30px
      }

      .bg6 h1 {
        font-size: 18px
      }

      .promo-info {
        width: 100% ;
        float: none ;
        right: initial ;
        text-align: center;
        padding-top: 20px;
      }

  </style>

    <!-- App favicon -->
    <link rel="shortcut icon" href="/lp/pedidos/images/favicon.ico">
    <link href="https://fonts.googleapis.com/css?family=Poppins&amp;display=swap" rel="stylesheet">
  <style amp-boilerplate>
    body {
      -webkit-animation: -amp-start 8s steps(1, end) 0s 1 normal both;
      -moz-animation: -amp-start 8s steps(1, end) 0s 1 normal both;
      -ms-animation: -amp-start 8s steps(1, end) 0s 1 normal both;
      animation: -amp-start 8s steps(1, end) 0s 1 normal both;
    }

    @-webkit-keyframes -amp-start {
      from {
        visibility: hidden;
      }
      to {
        visibility: visible;
      }
    }

    @-moz-keyframes -amp-start {
      from {
        visibility: hidden;
      }
      to {
        visibility: visible;
      }
    }

    @-ms-keyframes -amp-start {
      from {
        visibility: hidden;
      }
      to {
        visibility: visible;
      }
    }

    @-o-keyframes -amp-start {
      from {
        visibility: hidden;
      }
      to {
        visibility: visible;
      }
    }

    @keyframes -amp-start {
      from {
        visibility: hidden;
      }
      to {
        visibility: visible;
      }
    }
  </style>
  <noscript
  >
    <style amp-boilerplate>
      body {
        -webkit-animation: none;
        -moz-animation: none;
        -ms-animation: none;
        animation: none;
      }
    </style>
  </noscript
  >
  <script async src="https://cdn.ampproject.org/v0.js"></script>
  <script async custom-element="amp-form" src="https://cdn.ampproject.org/v0/amp-form-0.1.js"></script>
  <script async custom-template="amp-mustache" src="https://cdn.ampproject.org/v0/amp-mustache-0.2.js"></script>
</head>
<body>
<!-- Google Tag Manager -->
<amp-analytics config="https://www.googletagmanager.com/amp.json?id=GTM-M334FJZ&gtm.url=SOURCE_URL" data-credentials="include"></amp-analytics>

<div class="container-fluid fixed-top" style="padding-left: 0px;padding-right: 0px;padding: 0px;background: #2C375B; height: 42px">
  <div style="max-width: 900px;margin: 0 auto;">
    <nav class="navbar navbar-expand-lg navbar-light">
      <a class="navbar-brand logo-fibo" href="https://solucoesageis.com.br" target="_blank">
        <amp-img src="/lp/pedidos/images/logo-fibo.png" alt="" style="position: relative;top: -2px;" layout="fixed" width="107px" height="26px"></amp-img>
      </a>

      <div class="barra-lateral" id="navbarSupportedContent">
        <ul class="navbar-nav navbar-nav flex-row ml-md-auto d-md-flex">
          <li class="nav-item">
            <a href="https://promokit.promokit.com.br/novo/cadastro" class="btn btn-rounded border-white"  style="margin-top: -9px; color: #E9EAEC" id="navbarDropdown" role="button">
              EXPERIMENTAR
            </a>
          </li>
        </ul>
      </div>
    </nav>
  </div>
</div>
<div  class="container-fluid  " style="margin-top: 42px">
  <div  class="row">
    <div  class="col">
      <div  class="base-logo"><amp-img src="/lp/pedidos/images/logo-promokit.svg" width="100px" height="67px" layout="fixed" ></amp-img>
      </div>
      <div class="bg1">
        <div  class="mt-md-5"><amp-img width="225px" height="182px" layout="fixed"
                                        src="/lp/pedidos/images/Mockup.png" ></amp-img>
          <div  class="promo-info"><h1 >Crie seu cardápio e receba pedidos automaticamente </h1>
            <p>De um jeito simples e integrado ao WhatsApp, você envia seu cardápio, recebe os pedidos,
              acompanha a realização e ainda utiliza funções para alavancar suas vendas</p>
            <a href="https://promokit.promokit.com.br/novo/cadastro"  class="btn btn-blue btn-rounded btn-lg shadow-lg ">EXPERIMENTE AGORA</a>
          </div>
        </div>
      </div>
      <div class="bg2">
        <div ><amp-img src="/lp/pedidos/images/telapedido.png" layout="fixed" width="225px" height="445px"></amp-img>
          <div class="promo-info"><h2>Seu cardápio online no WhatsApp e Instagram</h2>
            <p >Cadastre seu cardápio e ganhe tempo divulgando o link aos seus clientes para que eles mesmos
              montem os pedidos. Após recebidos, acompanhe na nossa plataforma e envie notificações automáticas através do WhatsApp.
            </p>
            <a href="https://promokit.promokit.com.br/novo/cadastro"  class="btn btn-blue btn-rounded btn-lg shadow-lg" on="tap:experimentar.scrollTo(duration=500, position=top)" >EXPERIMENTE AGORA</a>
          </div>
        </div>
      </div>
      <div class="bg3">
        <div><amp-img src="/lp/pedidos/images/Mobile2.png" layout="fixed" width="225px" height="445px"></amp-img>
          <div  class="promo-info"><h1 >Pare de acompanhar vendas no
            papel</h1>
            <p >Implantar uma plataforma de pedidos e delivery agora é simples e barato. Além
              disso, você pode utilizar diversos recursos exclusivos para envolver seus clientes e aumentar suas
              vendas.</p>
            <a href="https://promokit.promokit.com.br/novo/cadastro"  class="btn btn-blue btn-rounded btn-lg shadow-lg" on="tap:experimentar.scrollTo(duration=500, position=top)">EXPERIMENTE AGORA</a>
          </div>
        </div>
      </div>
      <div class="bg4">
        <div><amp-img src="/lp/pedidos/images/img-04.png" width="262px" height="445px" layout="fixed"></amp-img>
          <div class="promo-info"><h1>Acompanhe em tempo real todas informações de seu cliente</h1>
            <p>Com nossa integração com WhatsApp Web, você sabe instantaneamente todos os detalhes sobre o seu cliente, como últimos pedidos, pedidos em aberto, pontuação no seu programa de fidelidade e todo seu histórico</p>
            <a href="https://promokit.promokit.com.br/novo/cadastro" class="btn btn-blue btn-rounded btn-lg shadow-lg" on="tap:experimentar.scrollTo(duration=500, position=top)">EXPERIMENTE AGORA</a>
          </div>
        </div>
      </div>
      <div class="bg5">
        <div><amp-img src="/lp/pedidos/images/img-05.png" width="262px" height="456px" layout="fixed" ></amp-img>
          <div class="promo-info"><h1>Planos flexíveis que cabem no seu
            bolso.</h1>
            <p> Nossos consultores analisam sua empresa e oferecem uma solução personalizada
              para fidelizar seus clientes e gerar mais resultados para sua empresa.</p>
            <a href="https://promokit.promokit.com.br/novo/cadastro" class="btn btn-blue btn-rounded btn-lg shadow-lg" on="tap:experimentar.scrollTo(duration=500, position=top)">EXPERIMENTE AGORA</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</body>
</html>
