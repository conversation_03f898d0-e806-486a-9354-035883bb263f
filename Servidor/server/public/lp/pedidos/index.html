<html lang="pt">
<head>
  <!-- Google Tag Manager -->
  <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
  })(window,document,'script','dataLayer','GTM-TNJ69BG');</script>
  <!-- End Google Tag Manager -->

  <meta charset="utf-8">

  <title>Crie seu cardápio digital com qr code e receba pedidos via Whatsapp.</title>

  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">

  <link rel="apple-touch-icon" sizes="192x192" href="/assets/icons/icon-192x192.png">
  <link rel="amphtml" href="https://meucardapio.ai/index_amp.html">

  <meta name="description" content="Compartilhe seu cardápio, aceite pedidos de seus clientes para retirada ou delivery e fidelize sem sair do WhatsApp" >
  <meta name="robots" content="index, follow">
  <meta property="og:title" content="PromoKit - Compartilhe seu cardápio, receba pedidos, controle seu Delivery e fidelize seus clientes sem sair do WhatsApp.">
  <meta property="og:site_name" content="PromoKit">
  <meta property="og:description" content="Compartilhe seu cardápio, aceite pedidos de seus clientes para retirada ou delivery e fidelize sem sair do WhatsApp">
  <meta property="og:image" content="http://www.promokit.com.br/lp/pedidos/images/pedido01.webp">
  <meta property="og:image" content="http://www.promokit.com.br/lp/pedidos/images/pedido02.webp">
  <meta property="og:type" content="website">


  <style>
    #myModal {
      background: rgba(0,0, 0, 0.6);
      overflow: auto;
    }

    #myModal.exibindo {
      display: block !important;
    }

    .form-check-label {
      margin-bottom: .5rem;
    }

    .loader {
      position: absolute;
      top: 50%;
      left: 50%;
      margin-top: -60px;
      margin-left: -60px;
      border: 16px solid #f3f3f3;
      border-radius: 50%;
      border-top: 16px solid #3498db;
      width: 120px;
      height: 120px;
      -webkit-animation: spin 2s linear infinite; /* Safari */
      animation: spin 2s linear infinite;
    }

    /* Safari */
    @-webkit-keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
      }
      100% {
        -webkit-transform: rotate(360deg);
      }
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }
  </style>

  <!-- App favicon -->
  <link rel="shortcut icon" href="/lp/pedidos/images/favicon.ico">
  <link href="https://fonts.googleapis.com/css?family=Poppins&amp;display=swap" rel="stylesheet">

  <link rel="stylesheet" href="/lp/pedidos/css/styles.css">
  <style></style>
  <style>.container-fluid[_ngcontent-eqt-c1] {
    padding-right: 0 !important;
    padding-left: 0 !important;
    overflow: hidden
  }

  h1, h2 {
    font-weight: 700
  }

  p[_ngcontent-eqt-c1] {
    font-size: 14px
  }

  .escondido {
    display: none;
  }

  .base-logo[_ngcontent-eqt-c1] {
    position: absolute;
    background: url(/lp/pedidos/images/base-logo.webp) no-repeat;
    width: 250px;
    height: 250px;
    top: 0;
    left: 0;
    z-index: 100
  }

  .base-logo[_ngcontent-eqt-c1] img[_ngcontent-eqt-c1] {
    width: 150px;
    position: relative;
    top: 35px;
    left: 35px
  }

  .bg1[_ngcontent-eqt-c1], .bg2[_ngcontent-eqt-c1], .bg3[_ngcontent-eqt-c1], .bg4[_ngcontent-eqt-c1], .bg5[_ngcontent-eqt-c1], .bg6[_ngcontent-eqt-c1] {
    background-color: #efefef !important
  }

  .bg1[_ngcontent-eqt-c1] > div[_ngcontent-eqt-c1], .bg2[_ngcontent-eqt-c1] > div[_ngcontent-eqt-c1], .bg3[_ngcontent-eqt-c1] > div[_ngcontent-eqt-c1], .bg4[_ngcontent-eqt-c1] > div[_ngcontent-eqt-c1], .bg5[_ngcontent-eqt-c1] > div[_ngcontent-eqt-c1], .bg6[_ngcontent-eqt-c1] > div[_ngcontent-eqt-c1] {
    margin: 0 auto;
    max-width: 1000px
  }

  .bg1[_ngcontent-eqt-c1] {
    background: url(lp/pedidos/images/bg-01.jpg) repeat-x;
    color: #fff;
    height: 700px;
    padding-top: 50px
  }

  .bg2[_ngcontent-eqt-c1] {
    color: rgba(0, 0, 0, .88);
    height: 430px
  }

  .bg3[_ngcontent-eqt-c1] {
    background: url(lp/pedidos/images/bg-03.jpg);
    color: #26502a;
    height: 875px;
    padding-top: 200px;
    font-size: 15px
  }

  .bg4[_ngcontent-eqt-c1] {
    color: rgba(0, 0, 0, .88);
    height: 510px;
    overflow: hidden
  }

  .bg5[_ngcontent-eqt-c1] {
    background: url(lp/pedidos/images/bg-05.jpg) 0 -10px #fff;
    padding-top: 120px;
    height: 720px;
    color: rgba(0, 0, 0, .88)
  }

  .bg6[_ngcontent-eqt-c1] {
    background-color: #fff !important;
    padding-top: 150px;
    padding-bottom: 150px
  }

  .bg1[_ngcontent-eqt-c1] h1[_ngcontent-eqt-c1], .bg3[_ngcontent-eqt-c1] h1[_ngcontent-eqt-c1], .bg5[_ngcontent-eqt-c1] h1[_ngcontent-eqt-c1] {
    color: #fff
  }

  .bg2 h1, .bg2 h2, .bg4 h1, .bg6 h1 {
    color: #2c4499;
  }

  .promo-info[_ngcontent-eqt-c1] {
    width: 400px;
    padding-top: 100px
  }

  .bg2[_ngcontent-eqt-c1] .promo-info[_ngcontent-eqt-c1], .bg4[_ngcontent-eqt-c1] .promo-info[_ngcontent-eqt-c1] {
    float: right;
    right: 160px;
    position: relative
  }

  .bg2[_ngcontent-eqt-c1] .promo-info[_ngcontent-eqt-c1] {
    width: 350px
  }

  .bg3[_ngcontent-eqt-c1] .promo-info[_ngcontent-eqt-c1] {
    width: 415px
  }

  .bg4[_ngcontent-eqt-c1] .promo-info[_ngcontent-eqt-c1] {
    right: 80px
  }

  .bg3[_ngcontent-eqt-c1] img[_ngcontent-eqt-c1] {
    right: 100px;
    position: relative
  }

  .bg1[_ngcontent-eqt-c1] img[_ngcontent-eqt-c1] {
    max-width: 650px
  }

  .btn-blue[_ngcontent-eqt-c1] {
    background-color: #2c449c;
    border-color: #4a81d4;
    color: #49b8ff !important;
    font-weight: 700
  }

  .btn-success[_ngcontent-eqt-c1] {
    background-color: #51ba5b;
    border-color: #4da854
  }

  .check-group[_ngcontent-eqt-c1] {
    background: #4d9e3f;
    width: 25px;
    border-radius: 15px;
    height: 25px;
    display: inline-block;
    padding-left: 7px;
    padding-top: 2px;
    margin-right: 10px
  }

  .check[_ngcontent-eqt-c1] {
    --borderWidth: 5px;
    --height: 16px;
    --width: 10px;
    --borderColor: #72c83f;
    display: inline-block;
    transform: rotate(45deg);
    height: var(--height);
    width: var(--width);
    border-bottom: var(--borderWidth) solid var(--borderColor);
    border-right: var(--borderWidth) solid var(--borderColor)
  }

  ul[_ngcontent-eqt-c1] li[_ngcontent-eqt-c1] {
    display: block;
    padding-bottom: 12px
  }

  form[_ngcontent-eqt-c1] {
    width: 600px;
    margin: 0 auto
  }

  .form-control[_ngcontent-eqt-c1] {
    padding: 1.5rem 1rem;
    border-top: 0;
    border-left: 0;
    border-right: 0;
    border-radius: 0;
    color: rgba(0, 0, 0, .88)
  }

  input[_ngcontent-eqt-c1]:focus {
    border-color: rgba(0, 0, 0, .88)
  }

  @media (min-width: 769px) {
    .barra-lateral {
      display: flex!important;
      flex-basis: auto;
      flex-grow: 1;
      align-items: center;
    }
  }


  @media (max-width: 768px) {
    .barra-lateral {
      display: flex!important;
      flex-basis: auto;
      align-items: center;
    }

    .logo-fibo {
      width: 17px;
      overflow: hidden;
    }

    h, h2 {
      font-size: 24px
    }

    .base-logo[_ngcontent-eqt-c1] {
      width: 150px;
      height: 150px;
      background-size: 150px
    }

    .base-logo[_ngcontent-eqt-c1] img[_ngcontent-eqt-c1] {
      width: 100px;
      top: 20px;
      left: 30px
    }

    form[_ngcontent-eqt-c1] {
      width: 100%
    }

    .promo-info[_ngcontent-eqt-c1] {
      width: 100% !important;
      float: none !important;
      right: initial !important;
      text-align: center;
      padding-top: 20px !important
    }

    .bg1[_ngcontent-eqt-c1] > div[_ngcontent-eqt-c1], .bg2[_ngcontent-eqt-c1] > div[_ngcontent-eqt-c1], .bg3[_ngcontent-eqt-c1] > div[_ngcontent-eqt-c1], .bg4[_ngcontent-eqt-c1] > div[_ngcontent-eqt-c1], .bg5[_ngcontent-eqt-c1] > div[_ngcontent-eqt-c1], .bg6[_ngcontent-eqt-c1] > div[_ngcontent-eqt-c1] {
      padding-right: 10px;
      padding-left: 10px;
      width: 100%
    }

    .bg1[_ngcontent-eqt-c1], .bg2[_ngcontent-eqt-c1], .bg3[_ngcontent-eqt-c1], .bg4[_ngcontent-eqt-c1], .bg5[_ngcontent-eqt-c1], .bg6[_ngcontent-eqt-c1] {
      position: relative;
      overflow: hidden
    }

    .bg1[_ngcontent-eqt-c1] img[_ngcontent-eqt-c1], .bg2[_ngcontent-eqt-c1] img[_ngcontent-eqt-c1], .bg3[_ngcontent-eqt-c1] img[_ngcontent-eqt-c1], .bg4[_ngcontent-eqt-c1] img[_ngcontent-eqt-c1], .bg5[_ngcontent-eqt-c1] img[_ngcontent-eqt-c1], .bg6[_ngcontent-eqt-c1] img[_ngcontent-eqt-c1] {
      width: 280px;
      position: absolute;
      top: 365px
    }

    .bg1[_ngcontent-eqt-c1] {
      height: 600px;
      padding-top: 30px
    }

    .bg1[_ngcontent-eqt-c1] .promo-info[_ngcontent-eqt-c1] {
      padding-top: 100px !important
    }

    .bg1[_ngcontent-eqt-c1] img[_ngcontent-eqt-c1] {
      top: 390px;
      width: 60%;
      left: 28%
    }

    .bg2[_ngcontent-eqt-c1] {
      height: 575px
    }

    .bg2[_ngcontent-eqt-c1] img[_ngcontent-eqt-c1] {
      top: 285px;
      width: 60%;
      left: 19%
    }

    .bg3[_ngcontent-eqt-c1] {
      height: 575px;
      background-position-y: -165px;
      padding-top: 0
    }

    .bg3[_ngcontent-eqt-c1] img[_ngcontent-eqt-c1] {
      width: 60%;
      top: 285px;
      left: 19%;
    }

    .bg3[_ngcontent-eqt-c1] ul[_ngcontent-eqt-c1] {
      text-align: left;
      padding-left: 10px
    }

    .bg3[_ngcontent-eqt-c1] .check-group[_ngcontent-eqt-c1] {
      margin-right: 10px
    }

    .bg4[_ngcontent-eqt-c1] {
      height: 500px
    }

    .bg4[_ngcontent-eqt-c1] img[_ngcontent-eqt-c1] {
      top: 290px;
      width: 70%;
      left: 11%
    }

    .bg5[_ngcontent-eqt-c1] {
      padding-top: 0;
      background-position-y: -100px;
      height: 540px
    }

    .bg5[_ngcontent-eqt-c1] img[_ngcontent-eqt-c1] {
      top: 285px;
      width: 70%;
      left: 10%
    }

    .bg6[_ngcontent-eqt-c1] {
      padding-top: 15px;
      padding-bottom: 30px
    }

    .bg6[_ngcontent-eqt-c1] h1[_ngcontent-eqt-c1] {
      font-size: 18px
    }


  }

  .botaoEnviar {
    margin: 0 auto;
    width: 245px;
    display: block
  }

  h2 {
    font-size: 14px;
    color: #fff;
    font-weight: normal;
  }
  </style>
  <script type="application/ld+json">
    {
      "@context" : "http://schema.org",
      "@type" : "SoftwareApplication",
      "name" : "Meu Cardápio",
      "brand": "Meu Cardápio",
      "description": "Crie seu cardápio digital, receba pedidos, controle seu Delivery e fidelize seus clientes direto no WhatsApp. Cardápio via QR Code.",
      "image" : "https://meucardapio.ai/lp/cardapio/images/img02.jpg",
      "applicationCategory" : [ "plataforma", "pedidos", "delivery", "fidelidade", "cardápio", "qrcode" ],
      "screenshot" : "https://promokit.promokit.com.br/lp/cardapio/images/img02.jpg"
    }
  </script>

  <!-- Facebook Pixel Code -->
  <script>
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
      n.callMethod.apply(n,arguments):n.queue.push(arguments)};
      if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
      n.queue=[];t=b.createElement(e);t.async=!0;
      t.src=v;s=b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t,s)}(window, document,'script',
      'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '723487391545650');
    fbq('track', 'PageView');
  </script>
  <noscript><img height="1" width="1" style="display:none"
                 src="https://www.facebook.com/tr?id=723487391545650&ev=PageView&noscript=1"
  /></noscript>
  <!-- End Facebook Pixel Code -->
</head>


<body class="" data-gr-c-s-loaded="true">
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TNJ69BG"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->


<app-root _nghost-eqt-c0="" ng-version="8.2.14">
  <router-outlet _ngcontent-eqt-c0=""></router-outlet>
  <app-landing-page-pedidos _nghost-eqt-c1="">
    <div class="container-fluid fixed-top" style="padding-left: 0px;padding-right: 0px;padding: 0px !important;background: #2C375B; height: 42px">
      <div style="max-width: 900px;margin: 0 auto;">
        <nav class="navbar navbar-expand-lg navbar-light">
          <a class="navbar-brand logo-fibo" href="https://solucoesageis.com.br" target="_blank">
            <img src="/lp/pedidos/images/logo-fibo.webp" height="26" alt="" style="position: relative;top: -2px;">
          </a>

          <div class="barra-lateral" id="navbarSupportedContent">
            <ul class="navbar-nav navbar-nav flex-row ml-md-auto d-md-flex">
              <li class="nav-item">
                <a class="btn btn-rounded border-white"  style="margin-top: -9px; color: #E9EAEC" id="navbarDropdown" role="button" href="#" onclick="solicitarContato();return false;">
                  EXPERIMENTAR
                </a>
              </li>
            </ul>
          </div>
        </nav>
      </div>
    </div>
    <div _ngcontent-eqt-c1="" class="container-fluid  " style="margin-top: 42px">
      <div _ngcontent-eqt-c1="" class="row">
        <div _ngcontent-eqt-c1="" class="col">
          <div _ngcontent-eqt-c1="" class="base-logo"><img _ngcontent-eqt-c1=""
                                                           src="/lp/pedidos/images/logo-promokit.svg"></div>
          <div _ngcontent-eqt-c1="" class="bg1">
            <div _ngcontent-eqt-c1="" class="mt-md-5"><img _ngcontent-eqt-c1="" align="right"
                                                           src="/lp/pedidos/images/Mockup.webp">
              <div _ngcontent-eqt-c1="" class="promo-info"><h1 _ngcontent-eqt-c1="">Crie seu cardápio e receba pedidos automaticamente  </h1>
                <h2 _ngcontent-eqt-c1="">De um jeito simples e integrado ao WhatsApp, você envia seu cardápio, recebe os pedidos,
                  acompanha a realização e ainda utiliza funções para alavancar suas vendas</h2>
                <button _ngcontent-eqt-c1="" class="btn btn-blue btn-rounded btn-lg shadow-lg " onclick="solicitarContato();">EXPERIMENTE AGORA</button>
              </div>
            </div>
          </div>
          <div _ngcontent-eqt-c1="" class="bg2">
            <div _ngcontent-eqt-c1=""><img _ngcontent-eqt-c1="" align="left"
                                           src="/lp/pedidos/images/telapedido.webp">
              <div _ngcontent-eqt-c1="" class="promo-info"><h2 _ngcontent-eqt-c1="">Divulgue Seu cardápio online no WhatsApp e Instagram</h2>
                <p _ngcontent-eqt-c1="">Cadastre seu cardápio e ganhe tempo divulgando o link aos seus clientes para que eles mesmos
                  montem os pedidos. Após recebidos, acompanhe na nossa plataforma e envie notificações automáticas através do WhatsApp.</p>
                <button _ngcontent-eqt-c1="" class="btn btn-blue btn-rounded btn-lg shadow-lg" onclick="solicitarContato();">EXPERIMENTE AGORA</button>
              </div>
            </div>
          </div>
          <div _ngcontent-eqt-c1="" class="bg3">
            <div _ngcontent-eqt-c1=""><img _ngcontent-eqt-c1="" align="right"
                                           src="/lp/pedidos/images/Mobile2.webp">
              <div _ngcontent-eqt-c1="" class="promo-info"><h1 _ngcontent-eqt-c1="">Por que ter seu próprio cardápio online?</h1>
                <h2 style="font-size: 16px;" _ngcontent-eqt-c1="">Implantar uma plataforma de pedidos e delivery agora é simples e barato. Além
                  disso, você pode utilizar diversos recursos exclusivos para envolver seus clientes e aumentar suas
                  vendas.</h2>
                <button _ngcontent-eqt-c1="" class="btn btn-blue btn-rounded btn-lg shadow-lg" onclick="solicitarContato();">EXPERIMENTE AGORA</button>
              </div>
            </div>
          </div>
          <div _ngcontent-eqt-c1="" class="bg4">
            <div _ngcontent-eqt-c1=""><img _ngcontent-eqt-c1="" align="left"
                                           src="/lp/pedidos/images/img-04.webp">
              <div _ngcontent-eqt-c1="" class="promo-info"><h1 _ngcontent-eqt-c1="">Pare de acompanhar vendas no papel</h1>
                <p _ngcontent-eqt-c1="">Com nossa integração com WhatsApp Web, você sabe instantaneamente todos os detalhes sobre o seu cliente, como últimos pedidos, pedidos em aberto, pontuação no seu programa de fidelidade e todo seu histórico</p>
                <button _ngcontent-eqt-c1="" class="btn btn-blue btn-rounded btn-lg shadow-lg" onclick="solicitarContato();">EXPERIMENTE AGORA</button>
              </div>
            </div>
          </div>
          <div _ngcontent-eqt-c1="" class="bg5">
            <div _ngcontent-eqt-c1=""><img _ngcontent-eqt-c1="" align="right"
                                           src="/lp/pedidos/images/img-05.webp">
              <div _ngcontent-eqt-c1="" class="promo-info"><h1 _ngcontent-eqt-c1="">Planos flexíveis que cabem no seu
                bolso.</h1>
                <p _ngcontent-eqt-c1=""> Nossos consultores analisam sua empresa e oferecem uma solução personalizada
                  para fidelizar seus clientes e gerar mais resultados para sua empresa.</p>
                <button _ngcontent-eqt-c1="" class="btn btn-blue btn-rounded btn-lg shadow-lg" onclick="solicitarContato();">EXPERIMENTE AGORA</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </app-landing-page-pedidos>
  <div _ngcontent-eqt-c0="" kendodialogcontainer=""></div>
</app-root>

<!-- The Modal -->
<div class="modal" id="myModal">
  <div class="modal-dialog">
    <div class="modal-content" style="width: 100%;">

      <!-- Modal Header -->
      <div class="modal-header">
        <h4 class="modal-title">Dados da Empresa</h4>
        <button type="button" class="close" data-dismiss="modal" id="btnFechar">&times;</button>
      </div>

      <!-- Modal body -->
      <div class="modal-body" style="padding: 0px;">
        <iframe src="https://promokit.promokit.com.br/novo/cadastro?topo=false" style="width: 100%;height: 600px;
          border: 0px;overflow: hidden;margin-top: -1px;" frameborder="0" data-hj-allow-iframe="">
        </iframe>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
  var versao = '0.21';

  function solicitarContato() {
    window.location.href = "https://promokit.promokit.com.br/novo/cadastro";
  }
</script>

<script type="text/javascript" src="/lp/pedidos/js/script_index.js"></script>

<!-- Hotjar Tracking Code for https://meucardapio.ai -->
<script>
  (function(h,o,t,j,a,r){
    h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
    h._hjSettings={hjid:1932737,hjsv:6};
    a=o.getElementsByTagName('head')[0];
    r=o.createElement('script');r.async=1;
    r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
    a.appendChild(r);
  })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
</script>

<!-- Facebook Pixel Code -->
<script>
  !function(f,b,e,v,n,t,s)
  {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window,document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
  fbq('init', '711113789617663');
  fbq('track', 'PageView');
</script>
<noscript>
  <img height="1" width="1"
       src="https://www.facebook.com/tr?id=711113789617663&ev=PageView
&noscript=1"/>
</noscript>
<!-- End Facebook Pixel Code -->

</body>
</html>
