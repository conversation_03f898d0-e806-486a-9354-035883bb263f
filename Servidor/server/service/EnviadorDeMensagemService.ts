import Queue = require("bull");
import {Empresa} from '../domain/Empresa';
import {MapeadorDeContato} from '../mapeadores/MapeadorDeContato';
import {Contato} from '../domain/Contato';
import {Notificacao} from '../domain/Notificacao';
import {MapeadorDeNotificacao} from '../mapeadores/MapeadorDeNotificacao';
import {NotificacaoService} from './NotificacaoService';
import {Ambiente} from './Ambiente';
import {MapeadorDeEmpresa} from '../mapeadores/MapeadorDeEmpresa';
import {ExecutorAsync} from '../utils/ExecutorAsync';
import {MensagemEnviada} from "../domain/MensagemEnviada";
const async = require('async');
export class EnviadorDeMensagemService {
  private static _instancia: EnviadorDeMensagemService
    = new EnviadorDeMensagemService();
  private filaMensagens: any;
  private filaAniversario: any;
  private constructor() {
    this.crieFilaMensagens();
    if (Ambiente.Instance.producao) {
      this.crieFilaAniversario();
    }
  }

  static Instancia(): EnviadorDeMensagemService {
    return EnviadorDeMensagemService._instancia;
  }

  async execute(mensagemEnviada: MensagemEnviada) {
    if ( !mensagemEnviada ) {
      return null;
    }

    return await this.filaMensagens.add(mensagemEnviada);
  }

  private crieFilaMensagens() {
    this.filaMensagens = new Queue('envio-de-mensagens', { defaultJobOptions: {
        attempts: 90,
        backoff: 2000,
        removeOnComplete: true,
        removeOnFail: true
      }
    });

    this.filaMensagens.process( async (job: any, done: any) => {
      console.log('envio de mensagens: id: ' + new Date());

      console.log(job.data);
      done();
    });

    this.filaMensagens.on('error', function(error: any) {
      // An error occured.
      console.log(error);
    });

    this.filaMensagens.on('completed', function(job: any, result: any) {
      console.log('fila mensagens: terminou');
    });
  }

  private crieFilaAniversario() {
    this.filaAniversario = new Queue('fila-aniversario', { defaultJobOptions: {
        attempts: 90,
        backoff: 2000,
        removeOnComplete: true,
        removeOnFail: true
      }
    });

    this.filaAniversario.process( async (job: any, done: any) => {
      ExecutorAsync.execute( (callback: Function) => {

        const mapeadorDeEmpresa = new MapeadorDeEmpresa();
        mapeadorDeEmpresa.selecioneTodosClientes({aniversariantes: true}).then( async (clientes: Array<Contato>) => {
          for ( const cliente of clientes ) {
            let contexto = require('domain').active.contexto;

            contexto.idEmpresa = cliente.empresa.id;
            contexto.empresa  = cliente.empresa;

            const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(cliente.empresa));

            await notificacaoService.envieNotificacaoAniversario(cliente);
          }

          callback();
          done();
        });
      }, (erro: Error) => {
        done(erro);
      }, 0);

      done();
    });

    this.filaAniversario.add({tipo: 'aniversario'}, {
      repeat: {
        cron: '0 0 13 * * *'
      },
      removeOnComplete: true,
      removeOnFail: true
    });
  }
}
