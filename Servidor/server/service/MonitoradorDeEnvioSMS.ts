import {MapeadorDeMensagemEnviada} from "../mapeadores/MapeadorDeMensagemEnviada";
import {StatusDeMensagem} from "./StatusDeMensagem";
import {ExecutorAsync} from "../utils/ExecutorAsync";
import {MensagemEnviada} from "../domain/MensagemEnviada";
import {Ambiente} from "./Ambiente";
import {Empresa} from "../domain/Empresa";
import {SituacaoDeMensagem} from "./SituacaoDeMensagem";

export class MonitoradorDeEnvioSMS {
  private static _instance: MonitoradorDeEnvioSMS = null;

  private executando = false;

  private constructor() {
  }

  public static get Instance() {
    return this._instance || this.crieInstancia();
  }

  static crieInstancia() {
    this._instance = new MonitoradorDeEnvioSMS();

    return this._instance;
  }

  public async monitore() {
    await this.chequeSMS();

    setInterval( async () => {
      try {
        if( this.executando ) {
          console.log('Monitorador está executando');
          return;
        }

        this.executando = true;

        const inicio = new Date();

        const resposta = await this.chequeSMS();

        console.log('Tempo: ' + (new Date().getTime() - inicio.getTime()) + " ---> " + resposta);
      } finally {
        this.executando = false;
      }
    }, 30000);
  }

  private chequeSMS() {
    return new Promise( (resolve, reject) => {
      ExecutorAsync.execute( async (callback: Function) => {
        const mapeadorDeMensagemEnviada = new MapeadorDeMensagemEnviada(false);

        const q = {status: StatusDeMensagem.Enviando , sms: true };

        mapeadorDeMensagemEnviada.selecioneTodas(q).then( async (mensagensEnviadas: Array<MensagemEnviada>) => {
          for( const mensagemEnviada of mensagensEnviadas ) {
            require('domain').active.contexto.idEmpresa = mensagemEnviada.empresa.id;

            const enviadorDeMensagens = Ambiente.Instance.novoEnviadorDeMensagensSMS(mensagemEnviada.meioDeEnvio);

            const situacao: SituacaoDeMensagem = await enviadorDeMensagens.
              acompanheMensagem(mensagemEnviada.idSMSExterno);

            if( !situacao.sucesso ) {
              continue;
            }

            if( mensagemEnviada.status !== situacao.status ) {
              mensagemEnviada.status = situacao.status;

              await new MapeadorDeMensagemEnviada().atualizeSync(mensagemEnviada);
            }
          }

          callback();
          resolve(null);
        });
      }, (err: any) => {
        console.log(err);

        console.log('falhou');
      }, 1);
    });
  }
}
