import {IEnviadorDeMensagem} from "./IEnviadorDeMensagem";
import {SituacaoDeMensagem} from "./SituacaoDeMensagem";
import {StatusDeMensagem} from "./StatusDeMensagem";
import {MensagemEnviada} from "../domain/MensagemEnviada";
import * as soap from 'soap';
import {EnumMeioDeEnvio} from "../domain/EnumMeioDeEnvio";
import {Empresa} from "../domain/Empresa";

const randtoken = require('rand-token');

export class EnviadorDeMensagemSMSTWW implements IEnviadorDeMensagem {
  acompanheMensagem(idMensagem: string): Promise<SituacaoDeMensagem> {
    return new Promise<SituacaoDeMensagem>((resolve ) => {
      const urlWebService = 'https://webservices2.twwwireless.com.br/reluzcap/wsreluzcap.asmx?wsdl';

      let cliente: any = null;
      soap.createClientAsync(urlWebService).then((client: any) => {
        cliente = client;

        const dados = {
          NumUsu:  'FIBONACCI',
          Senha: 'Fibo@129',
          SeuNum: idMensagem
        };

        cliente.StatusSMSAsync(dados).then((resposta: any) => {
          const situacaoDeMensagem = new SituacaoDeMensagem();

          if( resposta[0] == null || resposta[0].StatusSMSResult == null || resposta[0].StatusSMSResult.diffgram == null
            || resposta[0].StatusSMSResult.diffgram.OutDataSet.StatusSMS == null ) {
            console.log('falhou');
            situacaoDeMensagem.sucesso = false;
            situacaoDeMensagem.mensagem = 'Não veio resposta';

            resolve(situacaoDeMensagem);
            return;
          }

          const status = resposta[0].StatusSMSResult.diffgram.OutDataSet.StatusSMS.status;

          situacaoDeMensagem.sucesso = true;

          switch (status) {
            case 'CL':
              situacaoDeMensagem.status = StatusDeMensagem.Enviada;
              break;
            case 'E1':
              situacaoDeMensagem.status = StatusDeMensagem.BlackList;
              break;
            case 'E0': /*"Numero de celular invalido/sem operadora"*/
              situacaoDeMensagem.status = StatusDeMensagem.NumeroInvalido;
              break;
            case 'E4': /*"SMS recusado pela operadora"*/
              situacaoDeMensagem.status = StatusDeMensagem.NaoEnviada;
              break;
          }

          resolve(situacaoDeMensagem);
        }).catch( (erro: any) => {
          console.log(erro);

          const situacaoDeMensagem = new SituacaoDeMensagem();
          situacaoDeMensagem.sucesso = false;

          resolve(situacaoDeMensagem);
        });
      });
    });
  }

  /*
  Caso "CL"
textoStatus = "SMS entregue ao celular de destino"
Caso "E0"
textoStatus = "Numero de celular invalido/sem operadora"
Caso "E1"
textoStatus = "Blacklist" ( Celular no blacklist )
Caso "E3"
textoStatus = "Duplicada" (Mensagem rejeitada devido a excessivas repetições)
Caso "E4"
textoStatus = "SMS recusado pela operadora"
Caso "E6"
textoStatus = "SMS expirado conforme informe da operadora"
Caso "E7"
textoStatus = "SMS rejeitado por falta de créditos ou conta bloqueada"
Caso "DP"
textoStatus = "SMS com conteúdo de fraude"
   */

  envieSMS(telefone: string, mensagem: string): Promise<SituacaoDeMensagem> {
    return new Promise<SituacaoDeMensagem>((resolve ) => {
      const urlWebService = 'https://webservices2.twwwireless.com.br/reluzcap/wsreluzcap.asmx?wsdl';

      const numero = randtoken.generate(16);

      const mensagemSemAcentos = mensagem.normalize('NFD').replace(/[\u0300-\u036f]/g, "");

      const args = {
        NumUsu:  'FIBONACCI',
        Senha: 'Fibo@129',
        SeuNum: numero,
        Celular: telefone,
        Mensagem: mensagemSemAcentos
      };

      let cliente: any = null;
      soap.createClientAsync(urlWebService).then( (client: any) => {
        console.log(client.EnviaSMSAsync);
        cliente = client;

        const t = client.EnviaSMSAsync(args);

        return t;
      }).then((result: any) => {
        console.log(result);

        const status = {
          status: 200,
          sucesso: true,
          motivo: 0,
          mensagem: 'sms criado com sucesso',
          dados: { id: 51134679 }
        };

        const situacaoDeMensagem = new SituacaoDeMensagem();
        situacaoDeMensagem.sucesso = true;
        situacaoDeMensagem.status = StatusDeMensagem.Enviando;
        situacaoDeMensagem.idSMSExterno = numero;

        resolve(situacaoDeMensagem);
      }).catch( (erro: any) => {
        const s = cliente.lastRequest;
        console.log(erro);
      });
    });
  }

  /*const status = { status: 200,
      sucesso: true,
      motivo: 0,
      mensagem: 'dados retornados com sucesso',
      dados:
      { id: 51134723,
        numero_destino: '62982301144',
        data_criacao: '2019-11-30T13:37:07.000-02:00',
        mensagem: 'Oi Márcio',
        preco: 0.09,
        status_envio: 'enviada',
        data_status: null,
        resposta_usuario: false,
        respostas: [],
        tags: null } };
   */


  envieMensagem(mensagemEnviada: MensagemEnviada, telefone: string, mensagem: string): Promise<SituacaoDeMensagem> {
    if( mensagem.length < 145 ) {
      mensagem = mensagemEnviada.empresa.nome.substr(0, 15) + ": " + mensagem;
    }

    return this.envieSMS(telefone, mensagem);
  }

  requerAtivacaoDoTelefone(): boolean {
    return false;
  }

  obtenhaMeioDeEnvio(): EnumMeioDeEnvio {
    return EnumMeioDeEnvio.SMS_TWW;
  }

  notifiqueAssinantes(mensagemEnviada: MensagemEnviada) {
  }

  notifiqueAssinantesEmpresa(empresa: Empresa) {
  }
}
