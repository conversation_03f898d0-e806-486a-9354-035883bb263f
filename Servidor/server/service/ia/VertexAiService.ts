import {Configuration, OpenAIApi} from "openai";
import {Ambiente} from "../Ambiente";
import {ChamadaIa} from "../../domain/chatbot/ChamadaIa";
import {MapeadorDeChamadaIa} from "../../mapeadores/MapeadorDeChamadaIa";

import axios from "../../utils/AxiosUtils"; 

export class VertexAiService {
  private configuration = new Configuration({
    //apiKey: '***************************************************',
    apiKey: '***************************************************'
  });

  async chameChatgpt2(prompt: string) {
    return new Promise(async (resolve, reject) => {
      const data = { msg: prompt };

      const response = await axios.post('http://localhost:4000/mia/index', data);

      //onFinish(response.data);
      resolve(response.data);
    });
  }

  async chameChatgpt(prompt: string, onProgress: Function, onFinish: Function) {
    const openai = new OpenAIApi(this.configuration);

    console.log(prompt);

    const resposta: any = await openai.createCompletion({
      model: "text-davinci-003",
      prompt: prompt,
      temperature: 0.7,
      presence_penalty: 0.0,
      best_of: 1,
      top_p: 1,
      max_tokens: 250,
      stream: true
    }, { responseType: 'stream' });

    console.log(prompt);
    const result = {
      role: 'assistant',
      text: '',
      usage: {}
    }

    resposta.data.on('data', (data: any) => {
      const lines = data.toString().split('\n').filter( (line: string) => line.trim() !== '');
      for (const line of lines) {
        const message = line.replace(/^data: /, '');
        if (message === '[DONE]') {
          onFinish(result);
          console.log('terminou');
          return; // Stream finished
        }
        try {
          const parsed = JSON.parse(message);
          if( parsed.usage ) {
            result.usage = parsed.usage;
          }
          result.text += parsed.choices[0].text;
          onProgress(result);
        } catch(error) {
          console.error('Could not JSON parse stream message', message, error);
        }
      }
    });
  }

  async chameOpenAI(prompt: string, temperatura: number) {
    return new Promise( async (resolve, reject) => {
      const openai = new OpenAIApi(this.configuration);

      console.log(prompt);

      const resposta: any = await openai.createCompletion({
        model: "text-davinci-003",
        prompt: prompt,
        temperature: temperatura,
        presence_penalty: 0.0,
        best_of: 1,
        top_p: 1,
        max_tokens: 250
      }, { responseType: 'stream' });

      const result = {
        role: 'assistant',
        text: '',
        usage: resposta.data.usage
      };
      console.log(result);
      result.text += resposta.data.choices[0].text;
      resolve(result);
      return;
    });
  }

  async chameOpenAIChat(telefone: string, intent: string, prompt: string, mensagem: string, mensagens: Array<any>, temperatura: number, tagLog: string = '[chatbot]'): Promise<any> {
    return new Promise( async (resolve, reject) => {
      console.log(`[azure] ${tagLog} ${telefone}` + prompt.replace(/\n/g, '|'));

      const listaDeMensagens = [{role: "system", content: prompt}];
      //cria uma lista que une listaDeMensagens com mensagens
      const listaCompleta: any = listaDeMensagens.concat(mensagens);

      console.log(`[azure] ${tagLog} ${telefone}`, JSON.stringify(listaCompleta));

      if( mensagens.length > 0 ) prompt += `\nHistórico da conversa:`;
      for( let mensagem of mensagens ) {
        //concatena cada mensagem com o prompt
        prompt += `\n--${mensagem.role}: ${mensagem.content}`;
      }

      let resposta: any = null;
      let inicio = new Date();

      const chamadaIa = new ChamadaIa();
      chamadaIa.intent = intent;

      const result = {
        role: 'assistant',
        text: '',
        sucesso: true,
        usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
      };

      console.log('Prompt final: \n' + prompt);

      try {
        const url =
          'https://us-central1-aiplatform.googleapis.com/v1/projects/promokitpedidos-ypbowr/locations/us-central1/publishers/google/models/text-bison-32k:predict';

        resposta = await axios.post(url, {
          instances: [{
            content: prompt.trim(),
          }],
          parameters: {
              temperature: 0.2,
              maxOutputTokens: 100,
              stopSequences: [
                  "--user:", "---",
                  "--User:"
              ],
              topK: 40,
              topP: 0.95
          }
        }, {
          timeout: 12000,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ********************************************************************************************************************************************************************************************************************************',
          },
        });

        console.log(resposta.data.predictions);
      } catch (error) {
        if (error.response) {
          console.log(`[azure] ${tagLog} ${telefone} Erro: `, error.response.status);
          console.log(`[azure] ${tagLog} ${telefone} Erro: `, error.response.data);
          chamadaIa.codigo = error.response.status;
        } else {
          console.log(`[azure] ${tagLog} ${telefone} Erro: `, error.message);
        }
      }

      if( resposta ) {
        result.text += resposta.data.predictions[0].content;

        if( result.text.indexOf('--assistant:') !== -1 ) {
          result.text = result.text.split('--assistant:')[1];
        }

        chamadaIa.codigo = 200;
        chamadaIa.resposta = result.text;
        /*
        result.usage = resposta.data.usage;

        chamadaIa.promptTokens = result.usage.prompt_tokens;
        chamadaIa.resposta = result.text;
        chamadaIa.completionTokens = result.usage.completion_tokens;

        chamadaIa.totalTokens = result.usage.total_tokens;
         */
        console.log(`[azure] ${tagLog} ${telefone} Resposta: ` + result.text + ' - uso: ', result.usage);
      } else {
        chamadaIa.resposta = 'Erro ao chamar o serviço';
        result.text = 'Erro ao chamar o serviço';
        result.sucesso = false;
        console.log(`[azure] ${tagLog} ${telefone} Erro ao chamar o serviço Azure.`);
      }

      chamadaIa.mensagem = mensagem;
      chamadaIa.prompt = listaCompleta.map((conversa: any) => {
        return `--${conversa.role}: ${conversa.content}`}).join("\n");
      chamadaIa.api = 'azure-chatgpt';
      chamadaIa.dataCriacao = new Date();
      chamadaIa.horarioCriacao = new Date();

      chamadaIa.tempoChamada = (new Date().getTime() - inicio.getTime()) / 1000;

      console.log(`[azure] ${tagLog} ${telefone} Tempo chamada azure: ${chamadaIa.tempoChamada}`);

      await new MapeadorDeChamadaIa().insiraGraph(chamadaIa);

      resolve(result);
      return;
    });
  }


}
