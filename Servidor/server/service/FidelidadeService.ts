import {RegistroDeOperacaoService} from "./RegistroDeOperacaoService";
import {Ambiente} from "./Ambiente";
import {Plano} from "../domain/Plano";
import {TipoDePontuacao} from "../domain/TipoDePontuacao";
import {TipoDePontuacaoPorValor} from "../domain/TipoDePontuacaoPorValor";
import {TipoDePontuacaoQtdFixa} from "../domain/TipoDePontuacaoQtdFixa";
import {MapeadorDePlano} from "../mapeadores/MapeadorDePlano";
import {MapeadorDeTipoDePontuacao} from "../mapeadores/MapeadorDeTipoDePontuacao";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {Resposta} from "../utils/Resposta";
import {Brinde} from "../domain/obj/Brinde";
import {MapeadorDeBrinde} from "../mapeadores/MapeadorDeBrinde";
import {Atividade} from "../domain/Atividade";
import {MapeadorDeAtividade} from "../mapeadores/MapeadorDeAtividade";


export class FidelidadeService {
  private registroDeOperacaoService: RegistroDeOperacaoService;

  private obtenhaRegistroDeOperacaoService(): RegistroDeOperacaoService {
    if(!this.registroDeOperacaoService) this.registroDeOperacaoService = new RegistroDeOperacaoService(
      Ambiente.Instance.usuarioLogado(), Ambiente.Instance.ip()
    )

    return this.registroDeOperacaoService
  }

  insiraPlano(dados: any) {
    return new Promise((resolve: any, reject: any) => {
      let plano = new Plano();
      Object.assign(plano, dados);

      let tipoDePontuacao: TipoDePontuacao = null;

      if( dados.tipoDePontuacao.tipo === 'por-valor' ) {
        tipoDePontuacao = new TipoDePontuacaoPorValor();
      } else {
        tipoDePontuacao = new TipoDePontuacaoQtdFixa();
      }

      Object.assign(tipoDePontuacao, dados.tipoDePontuacao);

      plano.tipoDePontuacao = tipoDePontuacao;

      let erro = plano.valide();

      if(erro)  return  reject(erro)

      new MapeadorDePlano().transacao(async (conexao: any, commit: any) => {
        await  new MapeadorDeTipoDePontuacao().insiraSync(plano.tipoDePontuacao);
        plano.tipoDePontuacao = tipoDePontuacao;
        await  new MapeadorDePlano().insiraSync(plano);

        await this.obtenhaRegistroDeOperacaoService().adicionouPlano( plano, plano.empresa)

        commit( () => {
           resolve(plano);
        })
      })
    })
  }

  atualizePlano(dados: any){
    return new Promise(async (resolve: any, reject: any) => {
      let tipoDePontuacao = dados.tipoDePontuacao;

      let mapeador =  new MapeadorDePlano();

      mapeador.desativeMultiCliente();

      let plano: Plano = await mapeador.selecioneSync({id: dados.id, idEmpresa: dados.empresa.id});

      tipoDePontuacao.id = plano.tipoDePontuacao.id;

      Object.assign(plano, dados);

      let erro = plano.valide();

      if(erro)   return reject(erro)

      new MapeadorDePlano().transacao(async (conexao: any, commit: any) => {
        await new MapeadorDeTipoDePontuacao().atualizeSync(tipoDePontuacao);
        await  mapeador.atualizeSync(plano);
        await new MapeadorDeEmpresa().removaDasCaches(dados.empresa);
        await this.obtenhaRegistroDeOperacaoService().atualizouPlano( plano, plano.empresa)

        commit(() => {
          resolve()
        })

      })
    })
  }

  ativePlano(plano: any){
    return new Promise(async (resolve: any, reject: any) => {
      let mapeador =  new MapeadorDePlano();

      mapeador.desativeMultiCliente();

      plano.ativo = true  ;

      new MapeadorDePlano().transacao(async (conexao: any, commit: any) => {
        await mapeador.atualizeAtivo(plano);
        await this.obtenhaRegistroDeOperacaoService().ativouPlano( plano, plano.empresa)
        await new MapeadorDeEmpresa().removaDasCaches(plano.empresa);
        commit( () => {
          resolve();
        })
      })


    })
  }

  desativePlano(plano: any){
    return new Promise(async (resolve: any, reject: any) => {
      let mapeador =  new MapeadorDePlano();

      mapeador.desativeMultiCliente();

      plano.ativo = false;

      new MapeadorDePlano().transacao(async (conexao: any, commit: any) => {

        await mapeador.atualizeAtivo(plano);
        await new MapeadorDeEmpresa().removaDasCaches(plano.empresa);
        await this.obtenhaRegistroDeOperacaoService().desativouPlano( plano, plano.empresa)
        commit( () => {
          resolve();
        })
      })

    });
  }

  removaPlano(plano: any) {
    return new Promise( (resolve: any, reject: any) => {
      const mapeadorDePlano = new MapeadorDePlano();

      mapeadorDePlano.foiUtilizado(plano).then( async (foiUtilizado: Boolean) => {
        if(foiUtilizado)
           return reject("Não é possível remover um plano que já foi utilizado para criar cartão.")

        await   mapeadorDePlano.removaPlano(plano );
        await this.obtenhaRegistroDeOperacaoService().removeuPlano( plano, plano.empresa)

        resolve()
      })
    })
  }

  salveRegrasExtras(plano: any) {
    return new Promise(async (resolve: any, reject: any) => {
      let mapeador =  new MapeadorDePlano();

      mapeador.salveRegrasExtras(plano)

      let dados: any = {id: plano.id, regrasExtras: plano.regrasExtras}

      await this.obtenhaRegistroDeOperacaoService().atualizouPlano(dados, plano.empresa)

      resolve();
    })
  }

  salveBrinde(brinde: Brinde): Promise<string>{
    return new Promise<any>( async (resolve, reject) => {
      if(!brinde.plano) return resolve('Plano é obrigatório')
      if(!brinde.empresa) return resolve('Empressa é obrigatório')
      //if(!brinde.linkImagem && !brinde.plano.ehCashback()) return  resolve('Imagem do brinde é obrigatoria')
      if((brinde.valorEmPontos === 0) && !brinde.plano.ehCashback()) return  resolve('Informe um valor maior que 0')

      let mapeador = new MapeadorDeBrinde();

      mapeador.desativeMultiCliente();

      mapeador.transacao(async (conexao: any, commit: any) => {

        if(!brinde.id){
          await mapeador.insiraGraph(brinde) ;
          await this.obtenhaRegistroDeOperacaoService().inseriuBrinde(brinde, brinde.plano.empresa)
        }else{
          await mapeador.atualizeSync(brinde);
          await this.obtenhaRegistroDeOperacaoService().atualizouBrinde(brinde, brinde.plano.empresa)
        }

        commit( () => {
          resolve(null);
        })
      })

    });
  }

  removaBrinde(brinde: any){

    return new Promise<any>( async (resolve, reject) => {
      const mapeador = new MapeadorDeBrinde();

      mapeador.transacao(async (conexao: any, commit: any) => {
        await  mapeador.removaBrinde(brinde);
        await this.obtenhaRegistroDeOperacaoService().removeuBrinde(brinde, brinde.empresa)
        commit( () => {
          resolve(null);
        })
      })

    })
  }

  salveAtividade(atividade: Atividade): Promise<string> {
    return new Promise<string>( (resolve, reject) => {
      if(!atividade.empresa) return resolve('Empresa é obrigatório');
      if(!atividade.plano) return resolve('Plano é obrigatório');
      if(!atividade.nome) return resolve('Nome é obrigatório');
      if(!(atividade.valor >= 0)) return resolve('Valor é obrigatório');

      let mapeador = new MapeadorDeAtividade();

      mapeador.desativeMultiCliente();

      mapeador.transacao(async (conexao: any, commit: any) => {
        if(!atividade.id){
          await mapeador.insiraGraph(atividade);
          await this.obtenhaRegistroDeOperacaoService().inseriuAtividade(atividade, atividade.empresa)
        } else {
          await mapeador.atualizeSync(atividade);
          await this.obtenhaRegistroDeOperacaoService().atualizouAtividade(atividade, atividade.empresa)
        }

        commit( () => {
          resolve('');
        })
      })
    });
  }

  removaAtividade(atividade: any){
    return new Promise<string>( async (resolve, reject) => {
      const mapeadorDeAtividade = new MapeadorDeAtividade();

      const foiUtilizada = await       mapeadorDeAtividade.foiUtilizada(atividade);

      mapeadorDeAtividade.transacao(async (conexao: any, commit: any) => {
        if(foiUtilizada)
          return  resolve('Não é possível remover uma atividade que já possui pontuação registrada.')

        await mapeadorDeAtividade.removaAtividade(atividade );
        await this.obtenhaRegistroDeOperacaoService().removeuAtividade(atividade, atividade.empresa)

        commit( () => {
          resolve(null);
        })
      })
    })
  }

}
