import { Lead } from "../../domain/crm/Lead";
import * as fs from 'fs';
import * as path from 'path';

/**
 * Interface para parâmetros das tools
 */
interface ToolParams {
  [key: string]: any;
}

/**
 * Interface para resposta das tools
 */
interface ToolResponse {
  [key: string]: any;
}

/**
 * Classe de tools para o assistente de vendas WhatsApp
 * Fornece funções auxiliares para enriquecer as sugestões com dados específicos
 */
export class CrmWhatsappAssistantTools {

  /**
   * Busca informações sobre sistemas concorrentes
   */
  async buscar_info_concorrente(params: {
    nome_concorrente: string;
    funcionalidades_especificas?: string[];
  }): Promise<ToolResponse> {
    const concorrentes: { [key: string]: any } = {
      'ifood': {
        taxas: '12% a 27% por pedido',
        taxa_media: '18%',
        suporte: 'Chat e email apenas',
        limitacoes: [
          'Cliente não é seu (base pertence ao iFood)',
          'Sem programa de fidelidade próprio',
          'Não permite contato direto com cliente',
          'Cardápio limitado a formato deles',
          'Demora para receber pagamentos'
        ],
        vantagens_meu_cardapio: [
          'Taxa fixa de R$ 299/mês (não importa quantos pedidos)',
          'Cliente é 100% seu - com telefone e dados',
          'Programa de fidelidade incluso',
          'Suporte humanizado via WhatsApp',
          'Recebe pagamento na hora'
        ]
      },
      'uber eats': {
        taxas: '15% a 30% por pedido',
        taxa_media: '23%',
        suporte: 'Apenas formulário online',
        limitacoes: [
          'Taxa mais alta do mercado',
          'Zero flexibilidade no cardápio',
          'Entregadores podem cancelar pedido',
          'Cliente não é seu'
        ],
        vantagens_meu_cardapio: [
          'Economia de até R$ 20.000/mês em taxas',
          'Controle total sobre entregadores',
          'Personalização completa do cardápio',
          'Dados dos clientes para remarketing'
        ]
      },
      'rappi': {
        taxas: '20% a 25% por pedido',
        taxa_media: '22%',
        suporte: 'Central de ajuda online',
        limitacoes: [
          'Interface complicada para gerenciar',
          'Muitas reclamações não resolvidas',
          'Pagamento demora até 30 dias',
          'Não integra com sistemas'
        ],
        vantagens_meu_cardapio: [
          'Interface simples e intuitiva',
          'Suporte responde em minutos',
          'Pagamento instantâneo via PIX',
          'Integra com qualquer sistema'
        ]
      },
      'aiqfome': {
        taxas: '10% a 12% por pedido',
        taxa_media: '11%',
        suporte: 'WhatsApp business',
        limitacoes: [
          'Cobertura limitada de cidades',
          'Sistema instável',
          'Poucos recursos além do básico',
          'Marketing fraco'
        ],
        vantagens_meu_cardapio: [
          'Funciona em qualquer cidade',
          'Sistema 99.9% online',
          'Recursos avançados de gestão',
          'Marketing automático incluso'
        ]
      },
      'goomer': {
        taxas: 'R$ 199 a R$ 499/mês + taxa por pedido',
        taxa_media: 'R$ 299 + 6%',
        suporte: 'Email e chat',
        limitacoes: [
          'Cobra mensalidade + comissão',
          'Interface antiga',
          'Integração complexa',
          'Relatórios básicos'
        ],
        vantagens_meu_cardapio: [
          'Apenas mensalidade fixa',
          'Interface moderna e responsiva',
          'Integração em 1 dia',
          'Relatórios completos com IA'
        ]
      }
    };

    const nome = params.nome_concorrente.toLowerCase();
    const info = concorrentes[nome] || {
      taxas: 'Informação não disponível',
      limitacoes: ['Sistema genérico com limitações padrão'],
      vantagens_meu_cardapio: ['Meu Cardápio oferece solução completa e personalizada']
    };

    return {
      concorrente: params.nome_concorrente,
      informacoes: info,
      comparativo_resumo: `${params.nome_concorrente} cobra ${info.taxa_media || info.taxas} enquanto Meu Cardápio tem taxa fixa de R$ 299/mês`,
      argumento_principal: info.vantagens_meu_cardapio[0]
    };
  }

  /**
   * Recupera benefícios específicos do Meu Cardápio
   */
  async obter_beneficios_cardapio(params: {
    categoria?: 'delivery' | 'fidelidade' | 'automacao' | 'gestao' | 'marketing' | 'geral';
    segmento_cliente?: string;
  }): Promise<ToolResponse> {
    try {
      // Caminho para o arquivo de benefícios do Meu Cardápio
      const markdownPath = path.join(__dirname, '../../../docs/meu-cardapio-beneficios.md');
      
      // Ler o conteúdo do arquivo markdown
      const conteudoMarkdown = fs.readFileSync(markdownPath, 'utf8');
      
      return {
        fonte: 'arquivo_markdown',
        conteudo_completo: conteudoMarkdown,
        categoria_solicitada: params.categoria || 'geral',
        segmento_cliente: params.segmento_cliente,
        resumo: 'Documento completo com todos os benefícios e funcionalidades do Meu Cardápio'
      };
    } catch (error) {
      console.error('Erro ao ler arquivo de benefícios:', error);
      
      // Fallback com informações básicas em caso de erro
      return {
        fonte: 'fallback',
        erro: 'Não foi possível carregar o arquivo de benefícios',
        categoria_solicitada: params.categoria || 'geral',
        conteudo_basico: 'Meu Cardápio é uma plataforma completa para restaurantes com funcionalidades de pedidos online, programa de fidelidade, marketing por WhatsApp, gestão operacional e muito mais.'
      };
    }
  }

  /**
   * Consulta dados enriquecidos do lead
   */
  async consultar_dados_lead(params: {
    telefone?: string;
    lead?: Lead;
  }): Promise<ToolResponse> {
    // Se o lead foi passado, usar dados dele
    if (params.lead) {
      const lead = params.lead;
      return {
        dados_basicos: {
          nome: lead.nomeResponsavel || 'Não informado',
          empresa: lead.empresa || 'Não informada',
          telefone: lead.telefone,
          email: lead.crmEmpresa?.email || 'Não informado'
        },
        dados_empresa: {
          segmento: lead.segmento || 'Restaurante',
          porte: 'Pequeno', // Campo não existe no Lead, usar valor padrão
          cidade: lead.cidade || 'Não informada',
          sistema_atual: lead.concorrente || 'Nenhum/Manual'
        },
        dados_instagram: {
          handle: lead.instagramHandle || null,
          bio: lead.bioInsta || null,
          seguidores: lead.instagramData?.followers || 0,
          engajamento: 0 // Campo não existe na interface, usar valor padrão
        },
        dados_vendas: {
          etapa_atual: lead.etapa || 'Prospecção',
          score: lead.score || 0,
          origem: lead.origem || 'Instagram',
          ultimo_contato: lead.dataUltimaInteracao || null,
          tentativas_contato: 0 // Campo não existe no Lead, usar valor padrão
        },
        insights: this.gerarInsightsLead(lead)
      };
    }

    // Fallback se não tiver lead
    return {
      dados_basicos: {
        telefone: params.telefone,
        status: 'Lead não encontrado no sistema'
      },
      recomendacao: 'Qualificar lead durante a conversa'
    };
  }

  /**
   * Analisa perfil do Instagram
   */
  async analisar_perfil_instagram(params: {
    instagram_handle: string;
    bio?: string;
    seguidores?: number;
    lead?: Lead;
  }): Promise<ToolResponse> {
    // Simular análise baseada nos dados disponíveis
    const seguidores = params.seguidores || params.lead?.instagramData?.followers || 1000;
    const bio = params.bio || params.lead?.bioInsta || '';

    // Análise de oportunidades baseada em padrões comuns
    const oportunidades = [];
    const insights = [];

    // Verificar se tem link na bio
    if (!bio.includes('link') && !bio.includes('cardapio') && !bio.includes('pedido')) {
      oportunidades.push('Adicionar link do cardápio digital na bio');
      insights.push('Sem link de pedidos na bio = perda de 30% das vendas');
    }

    // Verificar menção a delivery
    if (bio.toLowerCase().includes('delivery') || bio.toLowerCase().includes('entrega')) {
      oportunidades.push('Automatizar pedidos do delivery via link');
      insights.push('Menciona delivery mas não facilita pedidos');
    }

    // Análise por número de seguidores
    if (seguidores > 5000) {
      oportunidades.push('Campanhas segmentadas para base grande');
      insights.push('Base grande mas sem ferramenta de engajamento');
    } else if (seguidores > 1000) {
      oportunidades.push('Programa de indicação para crescer base');
      insights.push('Potencial de crescimento com fidelização');
    }

    // Verificar horário de posts (campo não existe na interface, usar lógica simples)
    if (params.lead?.instagramData) {
      oportunidades.push('Integrar pedidos com stories/posts');
    }

    return {
      perfil: {
        handle: params.instagram_handle,
        seguidores: seguidores,
        categoria_tamanho: seguidores > 10000 ? 'grande' : seguidores > 5000 ? 'médio' : 'pequeno',
        tem_link_bio: bio.includes('link'),
        menciona_delivery: bio.toLowerCase().includes('delivery') || bio.toLowerCase().includes('entrega')
      },
      oportunidades: oportunidades,
      insights_vendas: insights,
      potencial_crescimento: {
        pedidos_mes_estimado: Math.floor(seguidores * 0.05), // 5% conversão média
        receita_adicional_estimada: Math.floor(seguidores * 0.05 * 45) // ticket médio R$ 45
      },
      argumentacao: `Com ${seguidores} seguidores, vocês podem gerar ${Math.floor(seguidores * 0.05)} pedidos extras/mês só otimizando o Instagram!`
    };
  }

  /**
   * Obtém informações sobre integrações
   */
  async obter_info_integracoes(params: {
    sistema_pdv?: string;
    delivery_apps?: string[];
    sistema_pagamento?: string;
  }): Promise<ToolResponse> {
    const integracoes: { [key: string]: any } = {
      // PDVs
      'stone': {
        tipo: 'PDV',
        status: 'Integração nativa',
        tempo_setup: '1 dia',
        beneficios: ['Sincronização automática de vendas', 'Fechamento de caixa integrado', 'Relatório unificado']
      },
      'cielo lio': {
        tipo: 'PDV',
        status: 'Integração disponível',
        tempo_setup: '2 dias',
        beneficios: ['Vendas sincronizadas', 'Comissões automáticas', 'Split de pagamento']
      },
      'pagseguro': {
        tipo: 'PDV/Pagamento',
        status: 'Integração completa',
        tempo_setup: '1 dia',
        beneficios: ['QR Code PIX automático', 'Link de pagamento', 'Conciliação automática']
      },
      'mercado pago': {
        tipo: 'Pagamento',
        status: 'Integração nativa',
        tempo_setup: 'Imediato',
        beneficios: ['QR Code dinâmico', 'Parcelamento sem juros', 'Conta digital gratuita']
      },
      // Delivery
      'ifood': {
        tipo: 'Marketplace',
        status: 'Integração via API',
        tempo_setup: '3 dias',
        beneficios: ['Pedidos centralizados', 'Cardápio sincronizado', 'Status automático']
      },
      'uber eats': {
        tipo: 'Marketplace',
        status: 'Em desenvolvimento',
        tempo_setup: 'Q1 2024',
        beneficios: ['Centralização prevista', 'Gestão unificada']
      },
      // Outros
      'instagram': {
        tipo: 'Rede Social',
        status: 'Integração completa',
        tempo_setup: 'Imediato',
        beneficios: ['Instagram Shopping', 'Stories com link', 'Catálogo sincronizado']
      },
      'whatsapp': {
        tipo: 'Mensageria',
        status: 'Integração oficial Meta',
        tempo_setup: '1 dia',
        beneficios: ['Catálogo no WhatsApp', 'Carrinho nativo', 'Pagamento no chat']
      }
    };

    // Processar sistemas informados
    const sistemas_informados = [
      params.sistema_pdv,
      ...(params.delivery_apps || []),
      params.sistema_pagamento
    ].filter(Boolean);

    const integracoes_disponiveis = sistemas_informados.map(sistema => {
      const nome = sistema!.toLowerCase();
      return {
        sistema: sistema,
        ...integracoes[nome] || {
          status: 'Consultar viabilidade',
          beneficios: ['Integração sob análise']
        }
      };
    });

    return {
      integracoes_disponiveis,
      tempo_total_setup: '2-3 dias para todas integrações',
      processo: [
        'Análise dos sistemas atuais',
        'Configuração das APIs',
        'Testes de integração',
        'Treinamento da equipe'
      ],
      beneficio_principal: 'Todos seus sistemas conversando entre si, sem retrabalho!',
      suporte: 'Equipe especializada acompanha todo o processo'
    };
  }

  /**
   * Busca case de sucesso similar
   */
  async buscar_caso_sucesso(params: {
    segmento: string;
    tamanho_empresa?: string;
    regiao?: string;
    problema_principal?: string;
  }): Promise<ToolResponse> {
    // Base de cases de sucesso
    const cases = [
      {
        nome_empresa: 'Pizzaria Bella Vista',
        segmento: 'pizzaria',
        localizacao: 'São Paulo - Zona Sul',
        tamanho: 'médio',
        problema: 'pedidos WhatsApp desorganizados',
        situacao_anterior: '150 pedidos/dia no WhatsApp, 20% de erros, 3h organizando pedidos',
        solucao_implementada: 'Cardápio digital + automação WhatsApp + programa pontos',
        resultados: {
          aumento_vendas: '35% em 60 dias',
          reducao_erros: 'de 20% para 2%',
          tempo_economizado: '3 horas/dia',
          novos_clientes: '+200 clientes/mês',
          roi_mensal: 'R$ 12.000 de lucro adicional'
        },
        depoimento: 'O Meu Cardápio salvou nosso delivery! Agora consigo dormir sem me preocupar com pedidos perdidos. O melhor: meus clientes adoram o programa de pontos!',
        contato_referencia: 'Carlos Silva - Proprietário',
        tempo_usando: '8 meses'
      },
      {
        nome_empresa: 'Burger & Beer Express',
        segmento: 'hamburgueria',
        localizacao: 'Rio de Janeiro - Barra',
        tamanho: 'pequeno',
        problema: 'concorrência com grandes redes',
        situacao_anterior: '50 pedidos/dia, competindo com grandes franquias, margem apertada',
        solucao_implementada: 'Fidelização agressiva + campanhas segmentadas + cardápio premium',
        resultados: {
          aumento_vendas: '65% em 90 dias',
          ticket_medio: '+45% com combos inteligentes',
          base_fidelizada: '300 clientes VIP',
          frequencia_pedidos: '2x por semana (antes 2x/mês)',
          roi_mensal: 'R$ 8.000 lucro adicional'
        },
        depoimento: 'Conseguimos competir com as grandes redes! O programa de fidelidade criou uma base de fãs que pedem toda semana.',
        contato_referencia: 'Ana Costa - Sócia',
        tempo_usando: '1 ano'
      },
      {
        nome_empresa: 'Açaí da Praia',
        segmento: 'açaiteria',
        localizacao: 'Santos - SP',
        tamanho: 'pequeno',
        problema: 'sazonalidade e gestão de estoque',
        situacao_anterior: 'Vendas caíam 70% no inverno, muito desperdício de frutas',
        solucao_implementada: 'Cardápio sazonal automático + pré-venda + análise preditiva',
        resultados: {
          reducao_sazonalidade: 'Queda de apenas 30% no inverno',
          desperdicio: '-80% de perda de produtos',
          pre_vendas: 'R$ 15.000/mês em pedidos antecipados',
          novos_produtos: '10 receitas de inverno lucrativas',
          roi_mensal: 'R$ 6.000 economizados'
        },
        depoimento: 'A análise preditiva mudou nosso negócio. Agora sei exatamente quanto comprar e quando lançar promoções.',
        contato_referencia: 'Pedro Santana - Proprietário',
        tempo_usando: '6 meses'
      }
    ];

    // Encontrar case mais similar
    let melhor_case = cases[0]; // default
    let maior_score = 0;

    cases.forEach(case_item => {
      let score = 0;
      if (case_item.segmento.includes(params.segmento.toLowerCase())) score += 3;
      if (case_item.tamanho === params.tamanho_empresa) score += 2;
      if (params.regiao && case_item.localizacao.includes(params.regiao)) score += 1;
      if (params.problema_principal && case_item.problema.includes(params.problema_principal)) score += 2;

      if (score > maior_score) {
        maior_score = score;
        melhor_case = case_item;
      }
    });

    return {
      caso_sucesso: melhor_case,
      similaridade: maior_score > 4 ? 'muito alta' : maior_score > 2 ? 'alta' : 'média',
      aplicabilidade: maior_score > 3 ? 'Caso extremamente relevante para o lead' : 'Caso com bons insights aplicáveis',
      principais_aprendizados: [
        `ROI médio de ${melhor_case.resultados.roi_mensal}`,
        `Crescimento de ${melhor_case.resultados.aumento_vendas}`,
        melhor_case.problema === params.problema_principal ? 'Mesmo problema resolvido com sucesso' : 'Solução adaptável ao contexto'
      ]
    };
  }

  /**
   * Analisa e responde objeções
   */
  async analisar_objecao(params: {
    tipo_objecao: 'preco' | 'mudanca' | 'tempo' | 'confianca' | 'funcionalidade' | 'decisao';
    contexto_conversa?: string;
    fase_spin?: string;
  }): Promise<ToolResponse> {
    const respostas_objecao: { [key: string]: any } = {
      preco: {
        compreensao: 'Entendo sua preocupação com o investimento',
        contra_argumentos: [
          {
            tipo: 'comparacao_prejuizo',
            resposta: 'R$ 299 parece um custo, mas você perde quanto por mês com pedidos no WhatsApp? A média é R$ 5.000...',
            impacto: 'alto'
          },
          {
            tipo: 'comparacao_funcionario',
            resposta: 'É menos que 15% do salário de um funcionário, mas trabalha 24h e não falta',
            impacto: 'medio'
          },
          {
            tipo: 'roi_rapido',
            resposta: 'Com 10 pedidos a mais por mês já paga o sistema. Nossos clientes fazem 100 a mais!',
            impacto: 'alto'
          }
        ],
        perguntas_descoberta: [
          'Quanto você acha que perde por mês com erros de pedido?',
          'Se um sistema te trouxesse R$ 5.000 a mais por mês, valeria R$ 299?',
          'Qual valor você considera justo para dobrar suas vendas?'
        ],
        fechamento: 'Que tal testarmos 30 dias? Se não trouxer retorno, devolvemos 100%'
      },
      mudanca: {
        compreensao: 'Sei que mudar sistema parece trabalhoso',
        contra_argumentos: [
          {
            tipo: 'facilidade',
            resposta: 'Nossa equipe faz toda migração em 1 dia. Você nem precisa parar de vender!',
            impacto: 'alto'
          },
          {
            tipo: 'suporte',
            resposta: 'Treinamos sua equipe toda e ficamos no WhatsApp 24h nos primeiros dias',
            impacto: 'medio'
          },
          {
            tipo: 'gradual',
            resposta: 'Podemos implementar aos poucos. Começa só com cardápio digital e vai evoluindo',
            impacto: 'medio'
          }
        ],
        perguntas_descoberta: [
          'O que mais te preocupa na mudança?',
          'Se a migração fosse automática, mudaria sua decisão?',
          'Prefere continuar perdendo dinheiro ou investir 1 dia para mudar?'
        ],
        fechamento: 'Vamos fazer assim: implementamos em 24h e se não gostar em 7 dias, cancelamos tudo'
      },
      tempo: {
        compreensao: 'Entendo que tempo é dinheiro para você',
        contra_argumentos: [
          {
            tipo: 'implementacao_rapida',
            resposta: 'Em 24h está tudo funcionando. Amanhã essa hora você já está vendendo mais!',
            impacto: 'alto'
          },
          {
            tipo: 'custo_espera',
            resposta: 'Cada dia sem automatização = R$ 200 perdidos. Um mês esperando = R$ 6.000!',
            impacto: 'alto'
          },
          {
            tipo: 'concorrencia',
            resposta: 'Seus concorrentes não estão esperando. 3 já automatizaram este mês...',
            impacto: 'medio'
          }
        ],
        perguntas_descoberta: [
          'Quanto tempo você gasta hoje organizando pedidos?',
          'O que acontece se seus concorrentes automatizarem antes?',
          'Quando seria o momento ideal para aumentar suas vendas?'
        ],
        fechamento: 'Posso agendar a implementação para amanhã mesmo. Fechamos?'
      },
      confianca: {
        compreensao: 'É natural querer ter certeza antes de investir',
        contra_argumentos: [
          {
            tipo: 'social_proof',
            resposta: 'Mais de 1.200 restaurantes confiam na gente. Posso te passar contatos para conversar',
            impacto: 'alto'
          },
          {
            tipo: 'garantia',
            resposta: '30 dias de garantia total. Se não gostar, devolvemos 100% sem perguntas',
            impacto: 'alto'
          },
          {
            tipo: 'teste',
            resposta: 'Que tal uma demonstração ao vivo agora? Você vê funcionando antes de decidir',
            impacto: 'medio'
          }
        ],
        perguntas_descoberta: [
          'O que te deixaria mais seguro para tomar a decisão?',
          'Conhece a Pizzaria Bella Vista? Eles usam há 8 meses',
          'Prefere ver uma demo ou conversar com um cliente nosso?'
        ],
        fechamento: 'Vou fazer diferente: teste 7 dias grátis. Se gostar, a gente conversa sobre continuar'
      },
      funcionalidade: {
        compreensao: 'Ótima pergunta sobre essa funcionalidade',
        contra_argumentos: [
          {
            tipo: 'confirmacao',
            resposta: 'Sim, fazemos exatamente isso e ainda melhor! Deixa eu te mostrar...',
            impacto: 'alto'
          },
          {
            tipo: 'alternativa',
            resposta: 'Essa específica não, mas temos algo ainda melhor que resolve o mesmo problema',
            impacto: 'medio'
          },
          {
            tipo: 'customizacao',
            resposta: 'Podemos desenvolver especialmente para vocês. Aliás, adoramos sugestões dos clientes!',
            impacto: 'medio'
          }
        ],
        perguntas_descoberta: [
          'Essa funcionalidade é essencial ou seria um diferencial?',
          'Como vocês resolvem isso hoje?',
          'Se tivéssemos algo similar mas melhor, funcionaria?'
        ],
        fechamento: 'Vamos fazer o teste e se precisar desse ajuste, implementamos em 15 dias'
      },
      decisao: {
        compreensao: 'Entendo que precisa alinhar com os sócios/equipe',
        contra_argumentos: [
          {
            tipo: 'urgencia',
            resposta: 'Claro! Enquanto vocês decidem, preparei uma análise de quanto perdem por dia esperando',
            impacto: 'medio'
          },
          {
            tipo: 'facilitador',
            resposta: 'Posso participar da reunião por vídeo e tirar todas as dúvidas dos sócios',
            impacto: 'alto'
          },
          {
            tipo: 'material',
            resposta: 'Vou te enviar um resumo executivo de 1 página com ROI para facilitar a conversa',
            impacto: 'medio'
          }
        ],
        perguntas_descoberta: [
          'Quem mais precisa estar convencido além de você?',
          'O que seria decisivo para os outros sócios?',
          'Quando vocês costumam se reunir para decidir?'
        ],
        fechamento: 'Envio o material agora e amanhã conversamos após vocês alinharem?'
      }
    };

    const objecao = respostas_objecao[params.tipo_objecao];

    // Selecionar melhor contra-argumento baseado no contexto
    const melhor_argumento = objecao.contra_argumentos.reduce((prev: any, current: any) =>
      current.impacto === 'alto' ? current : prev
    );

    return {
      tipo_objecao: params.tipo_objecao,
      estrategia_resposta: {
        passo1_compreender: objecao.compreensao,
        passo2_questionar: objecao.perguntas_descoberta[0],
        passo3_argumentar: melhor_argumento.resposta,
        passo4_fechar: objecao.fechamento
      },
      argumentos_adicionais: objecao.contra_argumentos.map((arg: any) => arg.resposta),
      perguntas_poderosas: objecao.perguntas_descoberta,
      resposta_sugerida: `${objecao.compreensao}... ${melhor_argumento.resposta} ${objecao.fechamento}`,
      dica_vendas: 'Sempre valide a objeção antes de responder. Às vezes é só necessidade de mais informação!'
    };
  }

  /**
   * Gera instruções para criar rapport personalizado com elogios sinceros baseados em dados reais do lead
   */
  async gerar_rapport_personalizado(params: {
    nome_lead?: string;
    empresa?: string;
    instagram_handle?: string;
    bio_instagram?: string;
    seguidores?: number;
    cidade?: string;
    tentativas_anteriores?: number;
    ultimo_contato?: string;
    lead?: Lead;
  }): Promise<ToolResponse> {

    return {
      instrucoes_rapport: {
        objetivo_principal: "Criar mensagens de rapport usando APENAS informações REAIS e ESPECÍFICAS do lead para fazer elogios SINCEROS",

        regras_fundamentais: [
          "🎯 SEMPRE usar dados concretos do lead (Instagram, empresa, bio, seguidores, etc.)",
          "❤️ Fazer elogios GENUÍNOS baseados em observações reais",
          "🚫 NUNCA inventar informações ou usar templates genéricos",
          "✨ Demonstrar que você realmente pesquisou e conhece o negócio deles",
          "🤝 Focar no que eles fazem BEM, não no que está faltando",
          "📝 A mensagem de rapport final deve vir entre aspas duplas \"assim\""
        ],

        como_usar_dados_do_lead: {
          instagram_handle: "Mencione o perfil específico: 'Vi seu Instagram @{handle}, que trabalho incrível!'",
          seguidores: "Reconheça o engajamento: 'Impressionante como vocês conquistaram {X} seguidores!'",
          bio_instagram: "Cite algo específico da bio: 'Adorei a descrição {trecho da bio}, muito criativo!'",
          empresa: "Elogie o nome/conceito: '{Nome da empresa} é um nome marcante, quem criou teve uma ótima ideia!'",
          cidade: "Conecte localmente: 'Vocês são referência aqui em {cidade}!'",
          fotos_produtos: "Comente sobre as imagens: 'As fotos dos pratos no Instagram estão de dar água na boca!'"
        },

        exemplos_elogios_sinceros: [
          "Se tem 5000+ seguidores: '{Nome}, que trabalho incrível no Instagram! {X} seguidores não é brincadeira, vocês realmente sabem se conectar com o público!'",
          "Se a bio menciona 'artesanal': 'Vi na bio de vocês que é tudo artesanal - isso faz TODA diferença! Dá pra sentir o carinho em cada foto.'",
          "Se tem delivery na bio: '{Nome}, vi que vocês fazem delivery! A organização deve ser impressionante para dar conta de tudo.'",
          "Se tem muitas fotos de pratos: 'Passei 10 minutos vendo as fotos dos pratos de vocês no Instagram... agora estou com fome! 😄'",
          "Se empresa tem nome criativo: '{Nome da empresa} é um nome genial! Quem teve essa ideia criativa?'"
        ],

        estrutura_mensagem_ideal: {
          abertura: "Use o nome da pessoa (sempre mais pessoal)",
          elogio_especifico: "Mencione algo CONCRETO que você viu/observou",
          demonstre_interesse: "Mostre que você realmente prestou atenção",
          pergunta_engajadora: "Termine com curiosidade genuína sobre o negócio",
          formato_final: "IMPORTANTE: Coloque a mensagem final entre aspas duplas \"assim\""
        },

        o_que_evitar: [
          "❌ Elogios genéricos: 'Seu negócio é ótimo'",
          "❌ Informações inventadas: 'Vi que vocês são líderes de mercado'",
          "❌ Templates óbvios: 'Oi, tudo bem? Vi seu Instagram...'",
          "❌ Falar de problemas logo de cara",
          "❌ Mencionar concorrentes ou comparações negativas"
        ],

        dicas_avancadas: [
          "🔍 Analise as últimas 3-5 postagens do Instagram para comentários específicos",
          "📸 Comente sobre detalhes das fotos (ambiente, apresentação, cores)",
          "👥 Se possível, mencione interações que você viu (comentários, curtidas)",
          "🕐 Considere o horário da última postagem para mostrar que está acompanhando",
          "🎨 Elogie aspectos visuais: logo, decoração, apresentação dos pratos"
        ]
      },

      dados_disponiveis: {
        nome: params.nome_lead || params.lead?.nomeResponsavel || 'Não informado',
        empresa: params.empresa || params.lead?.empresa || 'Não informado',
        instagram: params.instagram_handle || params.lead?.instagramHandle || 'Não informado',
        bio: params.bio_instagram || params.lead?.bioInsta || 'Não informado',
        seguidores: params.seguidores || params.lead?.instagramData?.followers || 'Não informado',
        cidade: params.cidade || params.lead?.cidade || 'Não informado'
      },

      proximos_passos: [
        "1. Analise TODOS os dados disponíveis do lead",
        "2. Escolha 1-2 aspectos mais impressionantes para elogiar",
        "3. Crie uma mensagem específica usando as instruções acima",
        "4. Revise se a mensagem soa genuína e não genérica",
        "5. Envie e aguarde resposta antes de fazer follow-up"
      ]
    };
  }

  /**
   * Método auxiliar para gerar insights do lead
   */
  private gerarInsightsLead(lead: Lead): string[] {
    const insights = [];

    // Score
    if (lead.score && lead.score > 80) {
      insights.push('Lead quente - alta probabilidade de conversão');
    } else if (lead.score && lead.score > 50) {
      insights.push('Lead morno - necessita nutrição');
    }

    // Sistema atual
    if (lead.concorrente) {
      insights.push(`Já usa ${lead.concorrente} - focar em diferenciais e migração`);
    } else {
      insights.push('Não usa sistema - educar sobre benefícios');
    }

    // Instagram
    if (lead.instagramData?.followers && lead.instagramData.followers > 5000) {
      insights.push('Grande potencial no Instagram não explorado');
    }

    // Tentativas (campo não existe no Lead, usar lógica baseada em outras informações)
    if (lead.dataUltimaInteracao && lead.dataProximoFollowup) {
      insights.push('Follow-up agendado - manter consistência');
    }

    return insights;
  }
}
