 import { ChatGPTService } from "../ia/ChatGPTService";
import { promises as fs } from 'fs';
import * as path from 'path';
import { Lead } from "../../domain/crm/Lead";
import { CrmWhatsappAssistantTools } from "./CrmWhatsappAssistantTools";
import { CRM_WHATSAPP_FUNCTIONS, FUNCTION_CALL_CONFIG } from "./CrmWhatsappFunctionSchemas";

interface SugestaoParams {
  telefone: string;
  mensagens: any[];
  faseSpin: 'situacao' | 'problema' | 'implicacao' | 'necessidade' | 'auto';
  produto: string;
  tomConversa: 'formal' | 'informal' | 'tecnico';
  contato?: any;
  // Novos parâmetros para melhor contexto
  isNovoLead?: boolean;
  segmento?: string;
  tamanhoEmpresa?: 'micro' | 'pequena' | 'media' | 'grande';
  historicoInteracoes?: number;
  horarioMensagem?: 'manha' | 'tarde' | 'noite';
  motivoContato?: 'inbound' | 'outbound' | 'retorno';
}

interface ConversaAgenteParams {
  telefone: string;
  mensagemSdr: string;
  mensagensLead: any[];
  faseSpin: 'situacao' | 'problema' | 'implicacao' | 'necessidade' | 'auto';
  produto: string;
  tomConversa: 'formal' | 'informal' | 'tecnico';
  contato?: any;
  historicoConversaAgente?: Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp?: Date;
  }>;
}

interface RespostaAgente {
  texto: string;
  confianca: number;
  faseSugerida?: string;
  observacoes?: string;
}

interface ResultadoSugestao {
  sugestoes: Array<{
    texto: string;
    confianca: number;
    faseSpin: string;
    timestamp: Date;
  }>;
  faseSugerida?: string;
  observacoes?: string;
}

export class CrmWhatsappAssistantService {
  private chatGPTService: ChatGPTService;
  private beneficiosMeuCardapio: string | null = null;
  private assistantTools: CrmWhatsappAssistantTools;

  constructor() {
    this.chatGPTService = new ChatGPTService();
    this.assistantTools = new CrmWhatsappAssistantTools();
  }

  /**
   * Carrega o documento de benefícios do Meu Cardápio
   */
  private async carregarBeneficiosMeuCardapio(): Promise<string> {
    try {
      if (!this.beneficiosMeuCardapio) {
        const filePath = path.join(__dirname, '../../../docs/meu-cardapio-beneficios.md');
        console.log('[CrmWhatsappAssistantService] Tentando carregar benefícios de:', filePath);

        const buffer = await fs.readFile(filePath);
        this.beneficiosMeuCardapio = buffer.toString('utf8');

        console.log('[CrmWhatsappAssistantService] Benefícios do Meu Cardápio carregados com sucesso');
        console.log('[CrmWhatsappAssistantService] Tamanho do arquivo:', this.beneficiosMeuCardapio.length, 'caracteres');
        console.log('[CrmWhatsappAssistantService] Preview:', this.beneficiosMeuCardapio.substring(0, 100) + '...');
      }
      return this.beneficiosMeuCardapio;
    } catch (error: any) {
      const filePath = path.join(__dirname, '../../../docs/meu-cardapio-beneficios.md');
      console.error('[CrmWhatsappAssistantService] Erro ao carregar benefícios do Meu Cardápio');
      console.error('[CrmWhatsappAssistantService] Caminho tentado:', filePath);
      console.error('[CrmWhatsappAssistantService] Erro completo:', error.message);

      // Retornar benefícios básicos como fallback
      console.log('[CrmWhatsappAssistantService] Usando benefícios básicos como fallback');
      return this.getBeneficiosBasicos();
    }
  }

  /**
   * Benefícios básicos como fallback
   */
  private getBeneficiosBasicos(): string {
    return `
# Benefícios Básicos do Meu Cardápio

- Sistema completo de gestão para restaurantes
- Pedidos online integrados ao WhatsApp
- Cardápio digital com QR Code
- Sistema de fidelidade com pontos
- Dashboard com métricas em tempo real
- Integrações com sistemas de PDV
- Suporte humanizado via WhatsApp
`;
  }

  /**
   * Gera sugestão de resposta usando IA baseado na metodologia SPIN Selling
   */
  async gerarSugestaoResposta(params: SugestaoParams): Promise<ResultadoSugestao> {
    const { telefone, produto, tomConversa, contato } = params;
    let { faseSpin } = params;

    // Filtrar mensagens sem texto antes de processar
    const mensagensFiltradas = params.mensagens.filter(msg => {
      const textoLimpo = (msg.texto || '').trim();
      return textoLimpo && textoLimpo !== '[Mensagem sem texto]';
    });

    console.log(`[CrmWhatsappAssistantService] Mensagens: ${params.mensagens.length} originais, ${mensagensFiltradas.length} após filtrar`);

    // Verificar se o lead ainda não respondeu (apenas mensagens do vendedor ou nenhuma mensagem)
    const leadAindaNaoRespondeu = mensagensFiltradas.length === 0 ||
      mensagensFiltradas.every(msg => msg.remetente === 'Eu' || msg.fromMe === true);

    console.log('[CrmWhatsappAssistantService] Lead ainda não respondeu?', leadAindaNaoRespondeu);

    // Se o lead ainda não respondeu, forçar fase inicial/rapport
    if (leadAindaNaoRespondeu) {
      faseSpin = 'rapport' as any; // Forçar fase rapport
      console.log('[CrmWhatsappAssistantService] Lead não respondeu - forçando fase rapport/inicial');
    } else if (!faseSpin || faseSpin === 'auto') {
      // Se a fase não foi fornecida, detectar automaticamente
      console.log('[CrmWhatsappAssistantService] Detectando fase SPIN automaticamente...');
      faseSpin = await this.detectarFaseSpin(mensagensFiltradas) as 'situacao' | 'problema' | 'implicacao' | 'necessidade';
      console.log('[CrmWhatsappAssistantService] Fase detectada:', faseSpin);
    }

    console.log('[CrmWhatsappAssistantService] Gerando sugestão com params:', { telefone, faseSpin, produto, tomConversa });

    // Construir contexto da conversa com mensagens filtradas
    const contextoConversa = this.construirContextoConversa(mensagensFiltradas);
    console.log('[CrmWhatsappAssistantService] Contexto construído:', contextoConversa);

    // Determinar contexto adicional
    const hora = new Date().getHours();
    const horarioMensagem = hora < 12 ? 'manha' : hora < 18 ? 'tarde' : 'noite';

    // Construir prompt baseado na fase SPIN com contexto enriquecido
    const prompt = await this.construirPromptSpin({
      faseSpin,
      contextoConversa,
      produto,
      tomConversa,
      nomeContato: contato?.nomeResponsavel || 'Cliente',
      isNovoLead: !contato?.id,
      segmento: this.detectarSegmento(contextoConversa, contato),
      tamanhoEmpresa: contato?.tamanhoEmpresa || 'pequena',
      horarioMensagem,
      contato, // Passar o objeto completo do lead
      leadAindaNaoRespondeu // Indicar se o lead ainda não respondeu
    });
    console.log('[CrmWhatsappAssistantService] Prompt construído para fase:', faseSpin);
    console.log('[CrmWhatsappAssistantService] Tamanho do prompt:', prompt.length, 'caracteres');
    console.log('[CrmWhatsappAssistantService] Prompt inclui benefícios?', prompt.includes('# Benefícios e Funcionalidades do Meu Cardápio'));

    try {
      // Preparar mensagens para o ChatGPT
      // IMPORTANTE: No WhatsApp, "Eu" = SDR (vendedor), então deve ser 'user' no contexto do ChatGPT
      // O cliente (lead) deve ser 'assistant' pois é a resposta esperada
      const mensagensFormatadas = mensagensFiltradas.slice(-10).map(msg => ({
        role: msg.remetente === 'Eu' ? 'user' : 'assistant',
        content: msg.texto
      }));

      // Chamar ChatGPT para gerar sugestão
      console.log('[CrmWhatsappAssistantService] Mensagens formatadas para ChatGPT:', mensagensFormatadas);
      console.log('[CrmWhatsappAssistantService] Chamando ChatGPT com', mensagensFormatadas.length, 'mensagens');
      // Usar o novo método com function loop
      const resposta = await this.chatGPTService.chameOpenAIChatWithFunctionLoop(
        telefone,
        'whatsapp_assistant',
        prompt,
        '', // mensagem vazia pois já está no contexto
        mensagensFormatadas,
        0.7, // temperatura
        '[whatsapp-assistant]',
        { type: 'json_object' }, // Forçar resposta JSON
        CRM_WHATSAPP_FUNCTIONS, // functions
        FUNCTION_CALL_CONFIG.function_call, // function_call
        // Executor de funções
        async (nomeFuncao: string, args: any) => {
          return await this.executarFuncao(nomeFuncao, args, contato);
        },
        10, // max iterações
        'gpt-4o-mini', // modelo
        'pt-BR' // linguagem
      );
      console.log('[CrmWhatsappAssistantService] Resposta do ChatGPT:', resposta);

      // A resposta agora deve vir em formato JSON
      let sugestaoTexto = resposta.text.trim();

      // Tenta parsear como JSON
      if (sugestaoTexto.startsWith('{')) {
        try {
          const resultado = JSON.parse(sugestaoTexto);

          // O processamento de tools agora é automático via function loop

          // Novo formato com array de sugestões (sem tools)
          if (resultado.sugestoes && Array.isArray(resultado.sugestoes)) {
            console.log('[CrmWhatsappAssistantService] Sugestões recebidas:', resultado.sugestoes.length);
            console.log('[CrmWhatsappAssistantService] Fase detectada:', resultado.fase_detectada);
            console.log('[CrmWhatsappAssistantService] Justificativa:', resultado.justificativa_fase);

            return {
              sugestoes: resultado.sugestoes.map((sug: any) => ({
                texto: sug.texto,
                confianca: sug.confianca || 0.85,
                faseSpin: sug.faseSpin || resultado.fase_detectada || faseSpin,
                timestamp: new Date()
              })),
              faseSugerida: resultado.fase_detectada || faseSpin,
              observacoes: resultado.justificativa_fase ? `Fase detectada: ${resultado.fase_detectada}. ${resultado.justificativa_fase}` : ''
            };
          }

          // Formato antigo com mensagem única (retrocompatibilidade)
          if (resultado.mensagem && resultado.fase_detectada) {
            console.log('[CrmWhatsappAssistantService] Formato antigo - convertendo para array');

            return {
              sugestoes: [{
                texto: resultado.mensagem,
                confianca: 0.85,
                faseSpin: resultado.fase_detectada,
                timestamp: new Date()
              }],
              faseSugerida: resultado.fase_detectada,
              observacoes: `Fase detectada: ${resultado.fase_detectada}. ${resultado.justificativa_fase || ''}`
            };
          }
        } catch (e) {
          console.error('[CrmWhatsappAssistantService] Erro ao parsear JSON:', e);
          // Se falhar, usa como texto direto
        }
      }

      // Fallback: retorna a sugestão como texto direto
      console.log('[CrmWhatsappAssistantService] Usando resposta como texto direto (não era JSON)');
      return {
        sugestoes: [{
          texto: sugestaoTexto,
          confianca: 0.85,
          faseSpin: faseSpin,
          timestamp: new Date()
        }],
        faseSugerida: faseSpin,
        observacoes: 'Resposta não estruturada'
      };

    } catch (erro: any) {
      console.error('[CrmWhatsappAssistantService] Erro ao gerar sugestão:', erro);

      // Fallback para sugestões predefinidas
      return this.obterSugestaoFallback(faseSpin, contextoConversa);
    }
  }

  /**
   * Conversa direta entre SDR e Agente IA
   * As mensagens do lead servem apenas como contexto
   */
  async conversarComAgente(params: ConversaAgenteParams): Promise<RespostaAgente> {
    const { telefone, mensagemSdr, mensagensLead, produto, tomConversa, contato, historicoConversaAgente } = params;
    let { faseSpin } = params;

    console.log('[CrmWhatsappAssistantService] Iniciando conversa com agente:', {
      telefone,
      mensagemSdr,
      faseSpin,
      historicoLength: (historicoConversaAgente || []).length,
      contatoNome: contato?.nomeResponsavel || contato?.nome || 'Não informado',
      contatoEmpresa: contato?.empresa || 'Não informada',
      contatoTelefone: contato?.telefone || 'Não informado'
    });

    // Filtrar mensagens do lead sem texto
    const mensagensLeadFiltradas = mensagensLead.filter(msg => {
      const textoLimpo = (msg.texto || '').trim();
      return textoLimpo && textoLimpo !== '[Mensagem sem texto]';
    });

    // Verificar se o lead ainda não respondeu
    const leadAindaNaoRespondeu = mensagensLeadFiltradas.length === 0 ||
      mensagensLeadFiltradas.every(msg => msg.remetente === 'Eu' || msg.fromMe === true);

    // Se o lead ainda não respondeu, forçar fase inicial/rapport
    if (leadAindaNaoRespondeu) {
      faseSpin = 'rapport' as any;
      console.log('[CrmWhatsappAssistantService] Lead não respondeu - forçando fase rapport');
    } else if (!faseSpin || faseSpin === 'auto') {
      // Detectar fase automaticamente
      faseSpin = await this.detectarFaseSpin(mensagensLeadFiltradas) as 'situacao' | 'problema' | 'implicacao' | 'necessidade';
      console.log('[CrmWhatsappAssistantService] Fase detectada automaticamente:', faseSpin);
    }

    // Construir contexto das mensagens do lead para incluir no prompt
    const contextoLead = this.construirContextoConversa(mensagensLeadFiltradas);

    // Construir prompt para conversa SDR-Agente
    const prompt = await this.construirPromptConversaAgente({
      faseSpin,
      contextoLead,
      produto,
      tomConversa,
      nomeContato: contato?.nomeResponsavel || 'Cliente',
      contato,
      leadAindaNaoRespondeu
    });

    try {
      // Preparar histórico da conversa SDR-Agente
      const historicoFormatado = (historicoConversaAgente || [])
        .slice(-10) // Últimas 10 mensagens para não sobrecarregar
        .map(msg => ({
          role: msg.role,
          content: msg.content
        }));

      // Adicionar a mensagem atual do SDR
      historicoFormatado.push({
        role: 'user',
        content: mensagemSdr
      });

      console.log('[CrmWhatsappAssistantService] Chamando ChatGPT para conversa com agente');
      console.log('[CrmWhatsappAssistantService] Histórico da conversa:', historicoFormatado.length, 'mensagens');

      // Usar o novo método com function loop
      const resposta = await this.chatGPTService.chameOpenAIChatWithFunctionLoop(
        telefone,
        'whatsapp_agent_conversation',
        prompt,
        mensagemSdr,
        historicoFormatado.slice(0, -1), // Histórico sem a mensagem atual (que vai no parâmetro mensagem)
        0.7,
        '[whatsapp-agent]',
        null, // formatoResposta
        CRM_WHATSAPP_FUNCTIONS, // functions
        FUNCTION_CALL_CONFIG.function_call, // function_call
        // Executor de funções
        async (nomeFuncao: string, args: any) => {
          return await this.executarFuncao(nomeFuncao, args, contato);
        },
        10, // max iterações
        'o4-mini-2025-04-16', // modelo
        'pt-BR' // linguagem
      );

      console.log('[CrmWhatsappAssistantService] Resposta do agente:', resposta);

      // Processar resposta
      let textoResposta = resposta.text.trim();
      let faseSugerida = faseSpin;
      let observacoes = '';

      // Tentar parsear como JSON se começar com {
      if (textoResposta.startsWith('{')) {
        try {
          const resultado = JSON.parse(textoResposta);
          textoResposta = resultado.resposta || resultado.texto || textoResposta;
          faseSugerida = resultado.fase_sugerida || faseSpin;
          observacoes = resultado.observacoes || '';
        } catch (e) {
          // Se não for JSON válido, usar texto direto
          console.log('[CrmWhatsappAssistantService] Resposta não é JSON válido, usando texto direto');
        }
      }

      return {
        texto: textoResposta,
        confianca: 0.9,
        faseSugerida,
        observacoes
      };

    } catch (erro: any) {
      console.error('[CrmWhatsappAssistantService] Erro na conversa com agente:', erro);

      // Fallback com resposta padrão
      return {
        texto: 'Desculpe, não consegui processar sua mensagem no momento. Pode reformular sua pergunta?',
        confianca: 0.5,
        faseSugerida: faseSpin,
        observacoes: 'Erro na comunicação com IA - resposta fallback'
      };
    }
  }

  /**
   * Detecta automaticamente a fase SPIN baseado nas mensagens usando IA
   */
  async detectarFaseSpin(mensagens: any[]): Promise<string> {
    try {
      // Se houver poucas mensagens, assumir fase inicial
      if (mensagens.length <= 2) {
        return 'situacao';
      }

      // Construir contexto da conversa completa
      const contextoCompleto = mensagens
        .map(m => `${m.remetente}: ${m.texto}`)
        .join('\n');

      const promptDeteccao = `Você é um especialista em vendas SPIN. Analise esta conversa de WhatsApp e identifique em qual fase SPIN ela está.

CONVERSA COMPLETA:
${contextoCompleto}

FASES SPIN:
0. RAPPORT: Lead ainda não respondeu, necessário construir relacionamento primeiro
1. SITUAÇÃO: Exploração inicial, perguntas sobre o negócio atual, como funciona hoje
2. PROBLEMA: Identificação de dificuldades, dores, problemas específicos
3. IMPLICAÇÃO: Discussão sobre consequências, impactos, custos dos problemas
4. NECESSIDADE: Lead demonstra interesse em solução, pergunta sobre preços, implementação

SINAIS PARA IDENTIFICAR CADA FASE:

RAPPORT:
- Lead nunca respondeu ou só tem mensagens do vendedor
- Nenhuma interação real do lead
- Todas as mensagens são tentativas de contato sem resposta
- Lead visualizou mas não respondeu

SITUAÇÃO:
- Primeiras mensagens da conversa COM RESPOSTA DO LEAD
- SDR fazendo perguntas exploratórias
- Lead compartilhando informações básicas do negócio
- Ainda não foram identificados problemas específicos

PROBLEMA:
- Lead mencionou dificuldades específicas
- Discussão sobre falhas ou limitações atuais
- SDR explorando dores do cliente
- Foco em "o que não está funcionando bem"

IMPLICAÇÃO:
- Discussão sobre perdas financeiras
- Menção a impactos negativos dos problemas
- Quantificação de prejuízos
- Lead reconhecendo consequências

NECESSIDADE:
- Lead pergunta sobre a solução
- Interesse em preços ou condições
- Perguntas sobre implementação
- Sinais claros de intenção de compra

IMPORTANTE:
- Se não há nenhuma resposta do lead, SEMPRE retorne "rapport"
- Considere a PROGRESSÃO natural da conversa
- Se o lead voltou atrás (ex: estava em problema mas voltou para situação), identifique a fase ATUAL
- Conversas podem regredir ou pular fases
- Foque na última direção da conversa

Responda APENAS com a fase identificada em lowercase: rapport, situacao, problema, implicacao ou necessidade`;

      // Chamar ChatGPT para análise
      const resposta = await this.chatGPTService.chameOpenAIChat(
        'deteccao_fase_spin',
        'whatsapp_spin_detector',
        promptDeteccao,
        '',
        [],
        0.3, // temperatura baixa para resposta mais determinística
        '[spin-detector]',
        undefined, // response_format
        [], // functions
        undefined, // functionCall
        'gpt-4o-mini', // modelo
        'pt-BR' // linguagem
      );

      // Extrair fase da resposta
      const faseDetectada = resposta.text.trim().toLowerCase();
      const fasesValidas = ['rapport', 'situacao', 'problema', 'implicacao', 'necessidade'];

      if (fasesValidas.includes(faseDetectada)) {
        console.log(`[detectarFaseSpin] Fase detectada via IA: ${faseDetectada}`);
        return faseDetectada;
      }

      // Fallback para detecção por palavras-chave se IA falhar
      console.log('[detectarFaseSpin] IA retornou fase inválida, usando fallback');
      return this.detectarFaseSpinPorPalavrasChave(mensagens);

    } catch (erro) {
      console.error('[detectarFaseSpin] Erro na detecção via IA:', erro);
      // Fallback para método antigo
      return this.detectarFaseSpinPorPalavrasChave(mensagens);
    }
  }

  /**
   * Método fallback de detecção por palavras-chave
   */
  private detectarFaseSpinPorPalavrasChave(mensagens: any[]): string {
    // Se não há mensagens ou apenas mensagens do vendedor, retornar fase rapport
    if (mensagens.length === 0 || mensagens.every(msg => msg.remetente === 'Eu' || msg.fromMe === true)) {
      return 'rapport';
    }
    const palavrasChave = {
      situacao: ['atualmente', 'fazemos', 'processo', 'sistema', 'hoje', 'empresa', 'negócio'],
      problema: ['dificuldade', 'problema', 'demora', 'erro', 'falha', 'ruim', 'complicado'],
      implicacao: ['impacto', 'prejuízo', 'tempo perdido', 'custo', 'afeta', 'consequência'],
      necessidade: ['preciso', 'quero', 'gostaria', 'solução', 'resolver', 'melhorar']
    };

    // Analisar últimas 5 mensagens
    const textoRecente = mensagens.slice(-5)
      .map(m => m.texto.toLowerCase())
      .join(' ');

    const pontuacao = {
      situacao: 0,
      problema: 0,
      implicacao: 0,
      necessidade: 0
    };

    // Contar palavras-chave
    for (const [fase, palavras] of Object.entries(palavrasChave)) {
      for (const palavra of palavras) {
        if (textoRecente.includes(palavra)) {
          pontuacao[fase as keyof typeof pontuacao] += 1;
        }
      }
    }

    // Retornar fase com maior pontuação
    let faseMaiorPontuacao = 'situacao';
    let maiorPontuacao = 0;

    for (const [fase, pontos] of Object.entries(pontuacao)) {
      if (pontos > maiorPontuacao) {
        maiorPontuacao = pontos;
        faseMaiorPontuacao = fase;
      }
    }

    return faseMaiorPontuacao;
  }

  /**
   * Constrói o contexto resumido da conversa
   */
  private construirContextoConversa(mensagens: any[]): string {
    // Pegar últimas 5 mensagens mais relevantes
    const mensagensRelevantes = mensagens.slice(-5);

    return mensagensRelevantes
      .filter(m => m.texto && m.texto.trim()) // Garantir que tem texto
      .map(m => `${m.remetente}: ${m.texto}`)
      .join('\n');
  }

  /**
   * Constrói o prompt específico para cada fase SPIN
   */
  private async construirPromptSpin(params: {
    faseSpin: string;
    contextoConversa: string;
    produto: string;
    tomConversa: string;
    nomeContato: string;
    isNovoLead?: boolean;
    segmento?: string;
    tamanhoEmpresa?: string;
    horarioMensagem?: string;
    contato?: any; // Objeto completo do lead
    leadAindaNaoRespondeu?: boolean; // Indica se o lead ainda não respondeu
  }): Promise<string> {
    const { faseSpin, contextoConversa, produto, tomConversa, nomeContato, isNovoLead, segmento, tamanhoEmpresa,
      horarioMensagem, contato, leadAindaNaoRespondeu } = params;

    // Carregar benefícios do Meu Cardápio
    const beneficios = '';// await this.carregarBeneficiosMeuCardapio();

    // Prompt base comum a todas as fases
    const promptBase = `You are **Meu Cardápio's SDR AI**.
Your mission is to move each lead, via WhatsApp, passo-a-passo pelo método **SPIN Selling** até aceitar uma reunião.

--------------------------------------------------------------------
🎯 1. Como decidir a etapa atual
--------------------------------------------------------------------
Examine todo o histórico de mensagens:

| Etapa | Quando marcar | Sinal típico |
|-------|--------------|-------------|
| **Inicial / Rapport** | Lead ainda não respondeu. | Silêncio total ou respostas automáticas. |
| **Situação** | Lead respondeu, mas ainda não detalhou processos internos. | Respostas curtas; descreve "o que usa hoje" de modo superficial. |
| **Problema** | Já sabemos como o lead opera. | Menciona alguma dificuldade ("muito pedido no pico", "demora no caixa"…). |
| **Implicação** | Lead reconheceu pelo menos 1 dor. | Concorda que isso gera custos, retrabalho ou perda de vendas. |
| **Necessidade / Valor** | Lead quer ideias de solução ou perguntou "como funciona?". | Pede demo, preços ou diz que "precisa resolver isso". |

⚠️ **Avance apenas UMA etapa por vez.**
Silêncio > volte ao Rapport a cada 48 h (com novo gancho).

--------------------------------------------------------------------
📝 2. Como escrever a próxima mensagem
--------------------------------------------------------------------
**Inicial / Rapport**
- Quebre o gelo com elogio, curiosidade ou dado do segmento.
- Nada de pitch; objetivo é resposta.

**Situação**
- Até 2 perguntas factuais (ferramentas, fluxo, metas).
- Tom neutro e investigativo.

**Problema**
- Perguntas que exponham dores ("Isso gera fila?", "Perde pedidos?").
- Mostre empatia, ainda sem vender.

**Implicação**
- Explore impacto: tempo, dinheiro, reputação.
- Dados comparativos ("Restaurantes que automatizam economizam X/h").

**Necessidade / Valor**
- Conecte as dores às funções do **Meu Cardápio**.
- CTA claro: ofereça dois horários ou link de agenda para demo.

--------------------------------------------------------------------
💡 3. Boas práticas rápidas
--------------------------------------------------------------------
1. **Uma pergunta por mensagem** → facilita respostas curtas.
2. **Espelhe o tom do lead** (formal ou informal).
3. **MENSAGENS CURTAS**: Máximo 2-3 linhas (WhatsApp funciona melhor assim).
4. **EVITE TEXTOS LONGOS**: Mensagens longas no WhatsApp são ignoradas.
5. **NÃO ASSINE**: Nunca termine com "Att", "Abraços", nome ou assinatura (WhatsApp é informal).
6. **ELOGIOS SINCEROS**: No rapport, use elogios genuínos baseados em dados reais do lead.
7. Use emojis com moderação (máx. 2 por mensagem).
8. Nos estágios Implicação e Necessidade inclua micro-benefício comprovado (Pix conciliado, WhatsApp oficial, etc.).
9. Sempre encerre as etapas 4-5 com **call-to-action objetivo**.

### REGRA DE OURO - Balanceamento 30/70
⚖️ **SEMPRE siga esta proporção em TODAS as sugestões:**
- **30% máximo**: Acknowledge/resposta ao que o lead disse
- **70% mínimo**: Benefícios do Meu Cardápio relacionados ao contexto
- **100% foco**: SEMPRE termine direcionando para o produto, não para o problema

### IMPORTANTE SOBRE BENEFÍCIOS:
✅ **Você tem acesso automático a funções para buscar informações específicas:**
- obter_beneficios_cardapio - Use quando precisar de funcionalidades específicas do Meu Cardápio
- buscar_info_concorrente - Use quando lead mencionar concorrentes (iFood, Uber Eats, etc.)
- buscar_caso_sucesso - Use para dar exemplos concretos e cases similares
- analisar_perfil_instagram - Use se lead tem Instagram ativo
- consultar_dados_lead - Use para enriquecer informações do lead
- analisar_objecao - Use quando lead fizer objeções específicas
- gerar_rapport_personalizado - Use para estratégias de primeira abordagem
- obter_info_integracoes - Use quando lead perguntar sobre integrações

**As funções serão chamadas automaticamente quando necessário - você não precisa solicitar!**

### CONTEXTO DA CONVERSA:
${contextoConversa}

<dados_lead>
<nome_responsavel>${contato?.nomeResponsavel || nomeContato || 'Não informado'}</nome_responsavel>
<telefone>${contato?.telefone || 'Não informado'}</telefone>
<empresa_lead>${contato?.empresa || 'Não informada'}</empresa_lead>
<empresa_crm>${contato?.crmEmpresa?.nome || 'Não informada'}</empresa_crm>
<email>${contato?.crmEmpresa?.email || contato?.email || 'Não informado'}</email>
<instagram>${contato?.instagramHandle || 'Não informado'}</instagram>
<bio_instagram>${contato?.bioInsta || 'Não informada'}</bio_instagram>
<sistema_concorrente>${contato?.concorrente || contato?.getConcorrente?.() || 'Não informado'}</sistema_concorrente>
<segmento>${segmento || contato?.segmento || 'Não identificado'}</segmento>
<etapa>${contato?.etapa || 'Prospecção'}</etapa>
<score>${contato?.score || 0}</score>
<origem>${contato?.origem || 'WhatsApp'}</origem>
<observacoes>${contato?.observacoes || ''}</observacoes>
<notas>${contato?.notas || ''}</notas>
<endereco>${contato?.endereco || 'Não informado'}</endereco>
<cnpj>${contato?.cnpj || 'Não informado'}</cnpj>
<website>${contato?.website || contato?.instagramData?.website || 'Não informado'}</website>
<seguidores>${contato?.instagramData?.followers || 'Não informado'}</seguidores>
<tipo_negocio>${contato?.rapport?.tipoNegocio || 'Não identificado'}</tipo_negocio>
<pontos_dor>${Array.isArray(contato?.rapport?.pontosDor) ? contato.rapport.pontosDor.join(', ') : 'Não identificados'}</pontos_dor>
<valor_potencial>${contato?.valorPotencial || 'Não definido'}</valor_potencial>
<data_ultima_interacao>${contato?.dataUltimaInteracao || 'Sem interação'}</data_ultima_interacao>
<links_ativos>${contato?.links?.filter((l: any) => l.ativo).map((l: any) => l.tipo + ': ' + l.url).join('; ') || 'Nenhum link'}</links_ativos>
<novo_lead>${isNovoLead}</novo_lead>
<tamanho_empresa>${tamanhoEmpresa || 'pequena'}</tamanho_empresa>
<horario_conversa>${horarioMensagem || 'dia'}</horario_conversa>
</dados_lead>

--------------------------------------------------------------------
🔚 4. Output
--------------------------------------------------------------------
Retorne sua resposta no seguinte formato JSON com EXATAMENTE 2 sugestões diferentes:
{
  "sugestoes": [
    {
      "texto": "primeira sugestão de mensagem",
      "confianca": 0.9,
      "faseSpin": "situacao|problema|implicacao|necessidade"
    },
    {
      "texto": "segunda sugestão de mensagem (abordagem diferente)",
      "confianca": 0.85,
      "faseSpin": "situacao|problema|implicacao|necessidade"
    }
  ],
  "fase_detectada": "situacao|problema|implicacao|necessidade",
  "justificativa_fase": "Breve explicação (1 linha) de por que classificou nesta fase"
}

IMPORTANTE:
- SEMPRE gere EXATAMENTE 2 sugestões com abordagens diferentes
- Retorne APENAS o JSON, sem explicações adicionais
- **MENSAGENS CURTAS**: Máximo 2-3 linhas por mensagem (serão enviadas no WhatsApp)
- **EVITE TEXTOS LONGOS**: WhatsApp funciona melhor com mensagens concisas e diretas
- **NÃO ASSINE MENSAGENS**: Nunca termine com "Att", "Abraços", nome ou assinatura (WhatsApp é informal)
- **FOCO EM RESPOSTA**: Especialmente no rapport, use elogios SINCEROS para fazer o lead responder
`;

    // Se o lead ainda não respondeu, usar prompt específico de rapport
    if (leadAindaNaoRespondeu || faseSpin === 'rapport') {
      return promptBase + `

### FASE ATUAL: RAPPORT/INICIAL - LEAD AINDA NÃO RESPONDEU

🚨 **ATENÇÃO CRÍTICA**: O lead AINDA NÃO RESPONDEU. Seu único objetivo é fazê-lo responder.

**REGRAS ABSOLUTAS:**
1. NÃO mencione produto, sistema, solução ou Meu Cardápio
2. NÃO faça perguntas sobre problemas ou processos
3. NÃO tente vender ou qualificar
4. NÃO assine mensagens (sem "Att", "Abraços", nome ou assinatura)
5. FOQUE apenas em criar conexão e obter resposta
6. USE ELOGIOS SINCEROS baseados em dados reais do lead

**USE OS DADOS DO LEAD PARA PERSONALIZAR:**
- Se tem Instagram: elogie algo específico do perfil ou bio
- Se tem empresa: demonstre que conhece o negócio
- Se tem seguidores: mencione o engajamento
- Se tem website: mostre que visitou
- Se tem endereço: mencione proximidade ou região

**ESTRATÉGIAS QUE FUNCIONAM (SEMPRE COM ELOGIOS SINCEROS):**
1. **Elogio Específico + Curiosidade GENUÍNA**
   - "Vi sua pizzaria no Instagram! Que forno lindo é aquele? Napolitano?"
   - "Adorei as fotos dos pratos! Vocês fazem tudo na casa mesmo?"
   - IMPORTANTE: Só elogie se realmente tiver dados específicos do Instagram/negócio

2. **Conexão Local/Regional REAL**
   - "Oi! Vi que vocês são do [bairro]. Moro pertinho! Como está o movimento?"
   - "Passei na frente ontem e vi a fila! Qual o segredo do sucesso?"
   - IMPORTANTE: Só mencione localização se tiver dados reais do endereço

3. **Pergunta Leve Sobre o Negócio BASEADA EM DADOS**
   - "Oi! Vi que abrem aos domingos. É o dia mais movimentado?"
   - "Que legal o conceito de vocês! Há quanto tempo estão nessa região?"
   - IMPORTANTE: Use apenas informações que realmente tem sobre o negócio

4. **Demonstrar Interesse Genuíno COM BASE EM DADOS REAIS**
   - "Adoro [tipo de comida]. Vi que é especialidade de vocês!"
   - "Fiquei curioso com o nome da empresa. Tem uma história por trás?"
   - IMPORTANTE: Só demonstre interesse se tiver dados específicos do segmento/empresa

**TOM OBRIGATÓRIO:**
- Casual e amigável
- Como se fosse um cliente interessado
- Máximo 2 linhas
- Use no máximo 1 emoji sutil
- SEM assinatura (não termine com nome, "Att", "Abraços", etc.)
- ELOGIOS SINCEROS baseados apenas em dados reais disponíveis

**IMPORTANTE**: A mensagem deve parecer natural, como se você fosse um potencial cliente ou admirador do negócio, NÃO um vendedor. Use apenas elogios que podem ser comprovados pelos dados do lead.`;
    }

    const promptsBase = {
      rapport: promptBase + `

### FASE ATUAL: RAPPORT - LEAD AINDA NÃO RESPONDEU

🚨 **ATENÇÃO**: O lead AINDA NÃO RESPONDEU. Foque em gerar engajamento.

**Estratégias que funcionam:**
1. **Elogio Específico**
   - "Vi o Instagram de vocês e fiquei impressionado com os pratos!"
   - "Passei na frente ontem e vi movimento. Sucesso!"

2. **Pergunta Curiosa**
   - "Vocês fazem tudo na casa mesmo?"
   - "Há quanto tempo estão nesse ponto?"

3. **Conexão Local**
   - "Sou da região e sempre vejo fila aí!"
   - "Meus amigos vivem falando de vocês!"

**TOM**: Casual, como cliente interessado, NÃO como vendedor.
**Máximo**: 2 linhas, 1 emoji sutil.`,

      situacao: promptBase + `

### FASE ATUAL: SITUAÇÃO
O lead já respondeu, mas ainda não detalhou processos internos.

**Objetivo**: Fazer até 2 perguntas factuais sobre ferramentas, fluxo ou metas.
**Tom**: Neutro e investigativo.

**Exemplos de perguntas para Situação:**
- Como vocês gerenciam os pedidos do delivery hoje?
- Quantos pedidos em média vocês recebem por dia?
- Usam algum sistema para controlar o cardápio online?
- Como funciona o processo desde o pedido até a entrega?
- Vocês trabalham com iFood também ou só delivery próprio?

**Lembre-se**: Uma pergunta por mensagem. Sem pitch ainda.`,

      problema: promptBase + `

### FASE ATUAL: PROBLEMA
Já sabemos como o lead opera e agora vamos explorar dificuldades.

**Objetivo**: Fazer perguntas que exponham dores operacionais.
**Tom**: Empático, mas ainda sem vender.

**Exemplos de perguntas para Problema (com balanceamento 30/70):**
- "Entendo que é corrido no fim de semana! Aliás, o Meu Cardápio limita pedidos por hora pra cozinha nunca travar. Vocês conseguem atender todos os pedidos hoje?"
- "Pedido errado é tenso mesmo! Com o sistema automatizado isso cai 70%. Já tiveram muitos problemas assim?"
- "Quando acaba um item, o Meu Cardápio remove do cardápio automaticamente. Como fazem isso hoje durante o serviço?"

**Importante**: Mostre que entende a dor, mas sempre conecte com benefício do Meu Cardápio.`,

      implicacao: promptBase + `

### FASE ATUAL: IMPLICAÇÃO
Lead reconheceu pelo menos uma dor. Hora de explorar o impacto.

**Objetivo**: Quantificar perdas de tempo, dinheiro ou reputação.
**Tom**: Consultivo, com dados do mercado.

**Estratégias para Implicação:**
- "Se perdem 5 pedidos por dia, são R$ 250/dia... R$ 7.500/mês!"
- "3 horas por dia organizando pedidos = 1 funcionário a R$ 2.000"
- "Cliente que espera muito não volta... quantos já perderam assim?"
- "Restaurantes similares que automatizaram cresceram 30% em 6 meses"

**Lembre-se do balanceamento 30/70**:
- 30% acknowledge da dor
- 70% como o Meu Cardápio resolve com números e benefícios concretos`,

      necessidade: promptBase + `

### FASE ATUAL: NECESSIDADE / VALOR
Lead demonstrou interesse em resolver o problema.

**Objetivo**: Conectar as dores às soluções do Meu Cardápio e agendar demo.
**Tom**: Confiante e direcionado para ação.

**Estratégias para fechar:**
- Liste 2-3 funcionalidades que resolvem as dores mencionadas
- Apresente caso de sucesso similar
- Ofereça dois horários específicos para demo
- Mencione garantia ou período de teste
- Crie senso de urgência (mas sem pressionar demais)

**CTA obrigatório**: Termine com pergunta fechada sobre agendamento.`
    };

    return promptsBase[faseSpin as keyof typeof promptsBase] || promptsBase.situacao;
  }

  /**
   * Retorna sugestão fallback caso a IA falhe
   */
  private obterSugestaoFallback(faseSpin: string, contexto: string): ResultadoSugestao {
    const sugestoesFallback = {
      rapport: [
        'Oi! Vi o Instagram de vocês e fiquei impressionado com os pratos! 😍 Vocês fazem tudo na casa mesmo?',
        'Olá! Passei na frente do restaurante ontem e vi movimento. Há quanto tempo vocês estão nesse ponto?'
      ],
      situacao: [
        'Olá! Vi que vocês têm um restaurante incrível! 🍽️ Como vocês gerenciam os pedidos do delivery hoje?',
        'Oi! Notei que vocês trabalham com delivery. Quantos pedidos em média vocês recebem por dia?'
      ],
      problema: [
        'Entendi! Quando o movimento aumenta no fim de semana, vocês conseguem atender todos os pedidos sem problemas?',
        'Interessante! E como vocês lidam quando acaba algum item do cardápio durante o serviço?'
      ],
      implicacao: [
        'Puxa, se perdem 5 pedidos por dia no pico, são uns R$ 7.500/mês deixados na mesa... 😱 Isso impacta bastante o faturamento, né?',
        'Nossa, 3 horas por dia organizando pedidos manualmente... É praticamente um funcionário só pra isso, certo?'
      ],
      necessidade: [
        'Perfeito! O Meu Cardápio resolve exatamente isso com pedidos organizados e cardápio sempre atualizado. Posso te mostrar como funciona amanhã às 10h ou 15h?',
        'Legal! Consigo te mostrar em 15 minutos como automatizar todo esse processo. Prefere uma demonstração hoje ou amanhã?'
      ]
    };

    const sugestoesParaFase = sugestoesFallback[faseSpin as keyof typeof sugestoesFallback] || sugestoesFallback.situacao;

    return {
      sugestoes: sugestoesParaFase.map((texto, index) => ({
        texto,
        confianca: index === 0 ? 0.6 : 0.55,
        faseSpin: faseSpin,
        timestamp: new Date()
      })),
      faseSugerida: faseSpin,
      observacoes: 'Sugestões padrão utilizadas devido a erro na geração via IA'
    };
  }

  /**
   * Constrói prompt para conversa direta entre SDR e Agente IA
   */
  private async construirPromptConversaAgente(params: {
    faseSpin: string;
    contextoLead: string;
    produto: string;
    tomConversa: string;
    nomeContato: string;
    contato?: any;
    leadAindaNaoRespondeu?: boolean;
  }): Promise<string> {
    const { faseSpin, contextoLead, produto, tomConversa, nomeContato, contato, leadAindaNaoRespondeu } = params;

    // Carregar benefícios do Meu Cardápio
    const beneficios = await this.carregarBeneficiosMeuCardapio();

    return `Você é um **Agente de Vendas IA especialista da Meu Cardápio**.

### Meu Cardáppio
O Meu Cardápio é uma plataforma completa para restaurantes, integrada ao WhatsApp, que centraliza pedidos, pagamentos, marketing e fidelização.
Não assuma nenhum conhecimento sobre o meu cardápio, sempre consulte as tools disponíveis.

Sua função é conversar diretamente com o SDR (vendedor) para ajudá-lo a vender melhor para o lead.

## CONTEXTO DO LEAD:
${contextoLead || 'Nenhuma conversa com o lead ainda.'}

## DADOS DO LEAD:
- **Nome**: ${contato?.nomeResponsavel || nomeContato || 'Não informado'}
- **Empresa**: ${contato?.empresa || 'Não informada'}
- **Telefone**: ${contato?.telefone || 'Não informado'}
- **Segmento**: ${contato?.segmento || 'Restaurante'}
- **Instagram**: ${contato?.instagramHandle || 'Não informado'}
- **Bio Instagram**: ${contato?.bioInsta || 'Não informada'}
- **Sistema Atual**: ${contato?.concorrente || 'Não informado'}
- **Fase SPIN Atual**: ${faseSpin}
- **Lead Respondeu**: ${leadAindaNaoRespondeu ? 'NÃO' : 'SIM'}

## COMO RESPONDER AO SDR:

1. **Seja direto e prático** - O SDR precisa de orientações claras
2. **Analise o contexto** - Use as mensagens do lead para dar conselhos específicos
3. **Sugira próximos passos** - Sempre indique qual deve ser a próxima ação
4. **Use metodologia SPIN** - Guie o SDR pela fase correta
5. **Use tools quando necessário** - Para benefícios específicos, use tools disponíveis

## FASES SPIN:
- **Rapport**: Lead não respondeu - foque em quebrar o gelo
- **Situação**: Explore como o lead trabalha hoje
- **Problema**: Identifique dores e dificuldades
- **Implicação**: Quantifique impactos dos problemas
- **Necessidade**: Conecte soluções e agende demo

## INSTRUÇÕES:
- Responda como um consultor experiente
- Seja específico sobre o que o SDR deve fazer
- Para responder sobre o meu cardápio, use as tools disponíveis
- Nunca invente nada, sempre responda baseado no que está informado aqui.
- As mensagens devem usar informacoes desse prompt ou das tools.
- Mantenha tom ${tomConversa === 'formal' ? 'profissional' : tomConversa === 'informal' ? 'descontraído' : 'técnico'}
- **MENSAGENS CURTAS**: Sugira mensagens de máximo 2-3 linhas (serão enviadas no WhatsApp)
- **EVITE TEXTOS LONGOS**: WhatsApp funciona melhor com mensagens concisas e objetivas
- **NÃO ASSINE**: Nunca sugira assinaturas como "Att", "Abraços" ou nome (WhatsApp é informal)
- **ELOGIOS SINCEROS**: Para rapport, oriente elogios genuínos baseados em dados reais do lead

## FORMATO DE RESPOSTA:
- Quando fizer sentido sugerir uma mensagem específica que o SDR deve enviar ao lead, **SEMPRE coloque a mensagem ENTRE ASPAS DUPLAS (")**
- Exemplo: "Oi João! Vi que vocês trabalham com delivery. Como estão gerenciando os pedidos hoje?"
- Se for apenas orientação ou análise, não use aspas
- Use aspas APENAS para mensagens que o SDR deve copiar e enviar

Agora responda à pergunta/situação do SDR:`;
  }

  /**
   * Gera mensagens de rapport/atratividade para abordagem outbound
   */
  async gerarMensagemRapport(params: {
    telefone: string;
    nomeContato?: string;
    empresa?: string;
    tipoAbordagem: 'direta' | 'indireta' | 'consultiva';
    produto: string;
    ultimaMensagem?: string;
    mensagensSDR?: any[]; // Mensagens já enviadas pelo SDR
    contato?: any; // Objeto completo do lead
  }): Promise<ResultadoSugestao> {
    const { telefone, nomeContato, empresa, tipoAbordagem, produto, ultimaMensagem, mensagensSDR, contato } = params;
    console.log('[CrmWhatsappAssistantService] Gerando mensagem de rapport:', { telefone, tipoAbordagem, produto });

    try {
      // Construir prompt específico para rapport
      const prompt = await this.construirPromptRapport({
        nomeContato: nomeContato || 'você',
        empresa,
        tipoAbordagem,
        produto,
        ultimaMensagem,
        mensagensSDR: mensagensSDR || [],
        contato // Passar o objeto completo do lead
      });
      console.log('[CrmWhatsappAssistantService] Prompt de rapport construído para abordagem:', tipoAbordagem);

      // Chamar ChatGPT para gerar mensagem usando function loop
      const resposta = await this.chatGPTService.chameOpenAIChatWithFunctionLoop(
        telefone,
        'whatsapp_rapport',
        prompt,
        '',
        [], // Sem histórico para rapport inicial
        0.8, // temperatura um pouco mais alta para criatividade
        '[whatsapp-rapport]',
        { type: 'json_object' },
        CRM_WHATSAPP_FUNCTIONS, // functions
        FUNCTION_CALL_CONFIG.function_call, // function_call
        // Executor de funções
        async (nomeFuncao: string, args: any) => {
          return await this.executarFuncao(nomeFuncao, args, contato);
        },
        10, // max iterações
        'gpt-4o-mini', // modelo
        'pt-BR' // linguagem
      );
      console.log('[CrmWhatsappAssistantService] Resposta do ChatGPT (rapport):', resposta);

      // Processar resposta
      let resultado: any;
      try {
        resultado = JSON.parse(resposta.text);
      } catch (e) {
        // Se não for JSON, usar resposta como texto direto
        resultado = {
          sugestoes: [{
            texto: resposta.text,
            confianca: 0.7
          }]
        };
      }

      // Garantir formato correto com array de sugestões
      if (resultado.mensagem && !resultado.sugestoes) {
        // Formato antigo - converter para novo formato
        resultado.sugestoes = [{
          texto: resultado.mensagem,
          confianca: resultado.confianca || 0.85
        }];
      }

      // Garantir que sugestoes seja um array
      if (!Array.isArray(resultado.sugestoes)) {
        resultado.sugestoes = [{
          texto: resultado.sugestoes || resposta.text,
          confianca: 0.8
        }];
      }

      return {
        sugestoes: resultado.sugestoes.map((sug: any) => ({
          texto: sug.texto,
          confianca: sug.confianca || 0.85,
          faseSpin: 'rapport',
          timestamp: new Date()
        })),
        faseSugerida: 'rapport',
        observacoes: resultado.observacoes || ''
      };

    } catch (erro: any) {
      console.error('[CrmWhatsappAssistantService] Erro ao gerar mensagem de rapport:', erro);

      // Fallback para mensagens predefinidas
      return this.obterMensagemRapportFallback(params);
    }
  }

  /**
   * Constrói prompt para gerar mensagens de rapport
   */
  private async construirPromptRapport(params: {
    nomeContato: string;
    empresa?: string;
    tipoAbordagem: 'direta' | 'indireta' | 'consultiva';
    produto: string;
    ultimaMensagem?: string;
    mensagensSDR?: any[];
    contato?: Lead; // Objeto completo do lead
  }): Promise<string> {
    const { nomeContato, empresa, tipoAbordagem, produto, ultimaMensagem, mensagensSDR, contato } = params;

    console.log('lead: ', contato);

    // Prompt unificado que usa todos os dados do lead

    // Carregar benefícios do Meu Cardápio
    const beneficios = await this.carregarBeneficiosMeuCardapio();

    const contextoProduto = produto === 'PromoKit - Sistema de Gestão' || produto === 'Sistema de Gestão PromoKit'
      ? 'Meu Cardápio - Sistema que aumenta vendas em 30% com cardápio digital e automação WhatsApp'
      : produto;

    const promptUnificado = `Você é um SDR especialista da Meu Cardápio.ai e precisa criar uma sequência de mensagens de rapport para convencer o lead a responder.

MISSÃO: Criar uma sequência de 2-3 mensagens curtas que combinem rapport + apresentação para convencer o lead a responder.

ESTRUTURA IDEAL:
1ª Mensagem: Gancho personalizado + apresentação sutil da Meu Cardápio.ai
2ª Mensagem: Benefício específico + prova social + dados concretos
3ª Mensagem (opcional): Call to action suave

ESCOLHA DA ABORDAGEM BASEADA NO LEAD:
Analise os dados do lead e escolha a melhor abordagem automaticamente:

• DIRETA: Se o lead tem histórico comercial, sistema concorrente, ou sinais de urgência
  - Mencione Meu Cardápio.ai logo no início
  - Use dados reais: ****** clientes, 30% aumento vendas
  - Seja direto mas não agressivo

• INDIRETA: Se o lead parece mais reservado, pequeno negócio, ou origem orgânica
  - Comece com elogio genuíno baseado nos dados disponíveis
  - Mencione Meu Cardápio.ai naturalmente na conversa
  - Use perguntas abertas que gerem reflexão

• CONSULTIVA: Se o lead tem empresa estruturada, CNPJ, múltiplos links, ou score alto
  - Apresentação profissional + pergunta diagnóstica
  - Use dados e métricas em todas mensagens
  - Demonstre ROI e valor financeiro

PERSONALIZAÇÃO OBRIGATÓRIA:
Use TODOS os dados disponíveis do lead para personalizar:

### IMPORTANTE SOBRE BENEFÍCIOS NO RAPPORT:
❗ **Para rapport, NÃO mencione Meu Cardápio ou benefícios específicos.**
❗ **Use apenas os dados do lead para personalizar (Instagram, empresa, localização, etc.).**

<dados_lead>
<nome_responsavel>${contato?.nomeResponsavel || nomeContato || 'Não informado'}</nome_responsavel>
<telefone>${contato?.telefone || 'Não informado'}</telefone>
<empresa_lead>${contato?.empresa || empresa || 'Não informada'}</empresa_lead>
<empresa_crm>${contato?.crmEmpresa?.nome || 'Não informada'}</empresa_crm>
<email>${contato?.crmEmpresa?.email || 'Não informado'}</email>
<instagram>${contato?.instagramHandle || 'Não informado'}</instagram>
<link_instagram>${contato?.linkInsta || 'Não informado'}</link_instagram>
<bio_instagram>${contato?.bioInsta || 'Não informada'}</bio_instagram>
<sistema_concorrente>${contato?.concorrente || 'Não informado'}</sistema_concorrente>
<segmento>${contato?.segmento || 'Não identificado'}</segmento>
<etapa>${contato?.etapa || 'Prospecção'}</etapa>
<score>${contato?.score || 0}</score>
<origem>${contato?.origem || 'Instagram'}</origem>
<observacoes>${contato?.observacoes || ''}</observacoes>
<notas>${contato?.notas || ''}</notas>
<endereco>${contato?.endereco || 'Não informado'}</endereco>
<cidade>${contato?.cidade || 'Não informada'}</cidade>
<cnpj>${contato?.cnpj || 'Não informado'}</cnpj>
<website>${contato?.website || 'Não informado'}</website>
<avatar_url>${contato?.avatarUrl || 'Não informado'}</avatar_url>
<data_criacao>${contato?.dataCriacao || 'Não informada'}</data_criacao>
<data_ultima_interacao>${contato?.dataUltimaInteracao || 'Não informada'}</data_ultima_interacao>
<data_proximo_followup>${contato?.dataProximoFollowup || 'Não informada'}</data_proximo_followup>
<valor_potencial>${contato?.valorPotencial || 'Não definido'}</valor_potencial>
<vendedor_id>${contato?.vendedorId || 'Não definido'}</vendedor_id>
<motivo_perda>${contato?.motivoPerda || 'Não informado'}</motivo_perda>
<fase_spin_detectada>${contato?.faseSpinDetectada || 'Não detectada'}</fase_spin_detectada>
<confianca_fase_detectada>${contato?.confiancaFaseDetectada || 0}</confianca_fase_detectada>
<data_ultima_deteccao_fase>${contato?.dataUltimaDeteccaoFase || 'Não informada'}</data_ultima_deteccao_fase>
<instagram_data>${contato?.instagramData ? JSON.stringify(contato.instagramData) : 'Não informado'}</instagram_data>
<rapport_gerado>${contato?.rapport ? JSON.stringify(contato.rapport) : 'Não gerado'}</rapport_gerado>
<relatorio_ia_json>${contato?.relatorioIaJson || 'Não disponível'}</relatorio_ia_json>
<links_ativos>${contato?.links?.filter((l: any) => l.ativo).map((l: any) => l.tipo + ': ' + l.url).join('; ') || 'Nenhum link'}</links_ativos>
<telefones_extras>${contato?.telefones?.map((t: any) => t.numero + ' (' + t.tipo + ')').join('; ') || 'Nenhum telefone extra'}</telefones_extras>
<tipo_abordagem_preferida>${tipoAbordagem || 'Não definida'}</tipo_abordagem_preferida>
</dados_lead>

<mensagens_enviadas_sdr>
${mensagensSDR && mensagensSDR.length > 0 ?
  `TOTAL DE TENTATIVAS ANTERIORES: ${mensagensSDR.length}
HISTÓRICO:
` + mensagensSDR.map((msg: any, index: number) =>
    `${index + 1}. [${msg.timestamp || 'Data não informada'}] ${msg.texto || msg.message || msg.content || 'Mensagem sem texto'}`
  ).join('\n') :
  'Nenhuma mensagem enviada pelo SDR ainda (primeira tentativa de rapport)'
}
</mensagens_enviadas_sdr>

REGRAS DE EXECUÇÃO:
1. ANALISE todos os dados do lead para escolher a melhor abordagem
2. PERSONALIZE com informações específicas disponíveis (nome, empresa, Instagram, etc.)
3. SEMPRE mencione que você é da Meu Cardápio.ai
4. Use dados reais: ****** clientes, 30% aumento vendas, ROI 5.000%
5. Crie SEQUÊNCIA de 2-3 mensagens curtas e impactantes
6. Primeira mensagem: gancho personalizado + apresentação sutil
7. Segunda mensagem: benefício específico + prova social + dados concretos
8. Terceira mensagem (opcional): call to action suave
9. **MENSAGENS CURTAS PARA WHATSAPP**: Máximo 2-3 linhas cada (WhatsApp funciona melhor com mensagens concisas)
10. **EVITE TEXTOS LONGOS**: Mensagens longas no WhatsApp são ignoradas ou não lidas
11. **NÃO ASSINE MENSAGENS**: Nunca termine com "Att", "Abraços", nome ou qualquer assinatura (WhatsApp é informal)
12. **ELOGIOS SINCEROS**: Use apenas elogios baseados em dados REAIS do lead (Instagram, empresa, localização, etc.)
13. **FOCO EM RESPOSTA**: O objetivo principal é fazer o lead responder, não vender
14. Use emojis estrategicamente (máximo 2 por mensagem)

REGRAS ANTI-REPETIÇÃO (CRUCIAL):
15. ANALISE as mensagens já enviadas pelo SDR e EVITE:
    - Repetir os mesmos ganchos de abertura
    - Usar abordagens similares ou mesmo tom
    - Mencionar os mesmos benefícios ou dados
    - Usar palavras-chave ou frases já utilizadas
16. Se já existem mensagens anteriores, use ESTRATÉGIA PROGRESSIVA:
    - 1ª tentativa: Abordagem direta com benefício principal
    - 2ª tentativa: Ângulo diferente (problema/urgência)
    - 3ª tentativa: Abordagem social (referências/cases)
    - 4ª+ tentativa: Quebra de padrão (curiosidade/insights)
17. VARIE o gancho de abertura: se já usou empresa, use Instagram; se já usou benefits, use dor; etc.
18. MUDE o tom se necessário: formal→informal, direto→consultivo, etc.
19. Se múltiplas tentativas foram feitas, considere abordagem de "última chance" ou "insights exclusivos"
20. **SEMPRE USE ELOGIOS SINCEROS**: Baseie elogios apenas em dados reais disponíveis do lead}

Responda APENAS em formato JSON:
{
  "sugestoes": [
    {
      "texto": "1ª mensagem: gancho personalizado + apresentação",
      "confianca": 0.95
    },
    {
      "texto": "2ª mensagem: benefício + prova social + dados",
      "confianca": 0.9
    },
    {
      "texto": "3ª mensagem: call to action (opcional)",
      "confianca": 0.85
    }
  ],
  "observacoes": "Abordagem escolhida baseada nos dados do lead e justificativa"
}`;

    return promptUnificado;
  }

  /**
   * Detecta o segmento do cliente baseado no contexto
   */
  private detectarSegmento(contexto: string, contato: any): string {
    const textoAnalise = (contexto + ' ' + (contato?.crmEmpresa?.nome || contato?.empresa || '')).toLowerCase();

    // Palavras-chave por segmento
    const segmentos = {
      'restaurante': ['restaurante', 'pizzaria', 'lanchonete', 'bar', 'café', 'padaria', 'delivery', 'comida', 'alimento', 'refeição', 'cardápio', 'mesa', 'garçom'],
      'varejo': ['loja', 'venda', 'produto', 'estoque', 'vitrine', 'cliente', 'compra', 'mercado', 'supermercado'],
      'servicos': ['serviço', 'consultoria', 'escritório', 'atendimento', 'agenda', 'consulta', 'projeto'],
      'industria': ['fábrica', 'produção', 'manufatura', 'industrial', 'processo', 'linha', 'montagem'],
      'saude': ['clínica', 'hospital', 'médico', 'paciente', 'consulta', 'exame', 'saúde', 'tratamento']
    };

    // Contar ocorrências
    let melhorSegmento = 'geral';
    let maiorPontuacao = 0;

    for (const [segmento, palavras] of Object.entries(segmentos)) {
      let pontuacao = 0;
      for (const palavra of palavras) {
        if (textoAnalise.includes(palavra)) {
          pontuacao++;
        }
      }
      if (pontuacao > maiorPontuacao) {
        maiorPontuacao = pontuacao;
        melhorSegmento = segmento;
      }
    }

    return melhorSegmento;
  }

  /**
   * Retorna mensagens de rapport fallback
   */
  private obterMensagemRapportFallback(params: {
    nomeContato?: string;
    empresa?: string;
    tipoAbordagem: 'direta' | 'indireta' | 'consultiva';
  }): ResultadoSugestao {
    const nome = params.nomeContato || 'você';
    const empresa = params.empresa;

    const mensagensFallback = {
      direta: [
        `Oi ${nome}! 💰 ${empresa ? `A ${empresa}` : 'Sua empresa'} pode faturar R$ 15.000/mês a mais com Meu Cardápio. Investimento: R$ 299. ROI de 5.000%. Quer ver como? 📈`,
        `${nome}, sabia que 73% dos pedidos se perdem no WhatsApp? Com Meu Cardápio você recupera TODOS! ****** restaurantes já faturam 30% a mais. Bora? 🚀`,
        `URGENTE ${nome}! 3 restaurantes ${empresa ? `perto da ${empresa}` : 'da sua região'} já automatizaram com Meu Cardápio. Não fique para trás! Posso mostrar em 5min?`
      ],
      indireta: [
        `Oi ${nome}! Vi que ${empresa || 'vocês'} têm ótimas avaliações! 🌟 Mas imagino que manter isso com WhatsApp pessoal deve ser difícil... Tenho uma dica que pode ajudar!`,
        `${nome}, passando pra parabenizar pelo sucesso! 👏 Notei que ${empresa ? `a ${empresa}` : 'vocês'} tem bastante movimento. Como dão conta de todos os pedidos no WhatsApp?`,
        `Oi! Cliente me indicou ${empresa || 'vocês'}, disse que a comida é TOP! 😋 Mas comentou que demora pra conseguir fazer pedido... Isso procede?`
      ],
      consultiva: [
        `${nome}, você sabe quantos % dos pedidos ${empresa ? `a ${empresa}` : 'vocês'} perdem por demora? A média do mercado é 23%. Com Meu Cardápio cai pra zero. Quer entender como?`,
        `Olá ${nome}! Fazendo uma pesquisa: quanto tempo leva desde o cliente chamar no WhatsApp até pagar? Se for mais de 10min, estão perdendo dinheiro. Posso explicar?`,
        `${nome}, pergunta diagnóstica: se o WhatsApp ${empresa ? `da ${empresa}` : 'de vocês'} cair num sábado à noite, qual o plano B? Meu Cardápio tem backup automático. Vale conhecer?`
      ]
    };

    const mensagensEscolhidas = mensagensFallback[params.tipoAbordagem];

    // Selecionar aleatoriamente 2-3 mensagens diferentes
    const sugestoes = [];
    const indices = [...Array(mensagensEscolhidas.length).keys()];
    const numSugestoes = Math.min(3, mensagensEscolhidas.length);

    for (let i = 0; i < numSugestoes; i++) {
      const randomIndex = Math.floor(Math.random() * indices.length);
      const msgIndex = indices.splice(randomIndex, 1)[0];

      sugestoes.push({
        texto: mensagensEscolhidas[msgIndex],
        confianca: 0.7 - (i * 0.05), // Diminui confiança para cada sugestão adicional
        faseSpin: 'rapport',
        timestamp: new Date()
      });
    }

    return {
      sugestoes,
      faseSugerida: 'rapport',
      observacoes: 'Sugestões de rapport geradas via fallback'
    };
  }

  /**
   * Gera mensagem de apresentação profissional
   */
  async gerarMensagemApresentacao(params: {
    telefone: string;
    nomeContato?: string;
    empresa?: string;
    produto: string;
    lead?: any;
  }): Promise<ResultadoSugestao> {
    const { telefone, nomeContato, empresa, produto, lead } = params;
    console.log('[CrmWhatsappAssistantService] Gerando mensagem de apresentação:', { telefone, nomeContato, empresa });

    try {
      // Não carregar benefícios - usar tools se necessário

      // Construir prompt para apresentação
      const prompt = `Você é um consultor comercial profissional do Meu Cardápio.

Crie 2 mensagens de apresentação profissional para primeiro contato com ${nomeContato || 'o responsável'}${empresa ? ` da ${empresa}` : ''}.

${lead ? `
<dados_lead>
<nome_responsavel>${lead.nomeResponsavel || nomeContato || 'Não informado'}</nome_responsavel>
<telefone>${lead.telefone || 'Não informado'}</telefone>
<empresa_lead>${lead.empresa || empresa || 'Não informada'}</empresa_lead>
<email>${lead.email || 'Não informado'}</email>
<instagram>${lead.instagramHandle || 'Não informado'}</instagram>
<bio_instagram>${lead.bioInsta || 'Não informada'}</bio_instagram>
<sistema_concorrente>${lead.sistemaConcorrente || 'Não informado'}</sistema_concorrente>
<origem>${lead.origem || 'WhatsApp'}</origem>
<observacoes>${lead.observacoes || 'Sem observações'}</observacoes>
</dados_lead>
` : ''}

DIRETRIZES PARA AS MENSAGENS:
1. Tom profissional mas amigável
2. Apresentação clara de quem você é
3. Mencione brevemente o Meu Cardápio
4. Mantenha apresentação genérica (sem benefícios específicos)
5. Inclua uma pergunta ou CTA suave no final
6. Mensagem deve ter entre 3-5 linhas
7. NÃO use emojis em excesso (máximo 1-2 por mensagem)
8. Personalize com o nome se disponível
9. IMPORTANTE: Sempre coloque a mensagem sugerida ENTRE ASPAS DUPLAS (""), para facilitar a cópia pelo SDR

EXEMPLOS DE ESTRUTURA:
- Saudação + apresentação pessoal
- Breve menção da empresa e propósito
- Benefício principal ou estatística
- Pergunta aberta ou oferta de valor

FORMATO DE RESPOSTA JSON:
{
  "sugestoes": [
    {
      "texto": "\"Oi! Sou [Seu Nome] e trabalho com soluções que ajudam restaurantes a impulsionar vendas. Adoraria saber mais sobre seu negócio. Vamos conversar?\"",
      "confianca": 0.9
    },
    {
      "texto": "\"Olá! Me chamo [Seu Nome] e ajudo restaurantes a fidelizar clientes com o Meu Cardápio. Podemos agendar uma conversa rápida?\"",
      "confianca": 0.85
    }
  ],
  "observacoes": "Apresentação profissional para primeiro contato"
}`;

      const resposta = await this.chatGPTService.chameOpenAIChatWithFunctionLoop(
        telefone,
        'whatsapp_apresentacao',
        prompt,
        '',
        [],
        0.7,
        '[whatsapp-apresentacao]',
        { type: 'json_object' },
        CRM_WHATSAPP_FUNCTIONS, // functions
        FUNCTION_CALL_CONFIG.function_call, // function_call
        // Executor de funções
        async (nomeFuncao: string, args: any) => {
          return await this.executarFuncao(nomeFuncao, args, lead);
        },
        10, // max iterações
        'gpt-4o-mini', // modelo
        'pt-BR' // linguagem
      );

      // Processar resposta
      let resultado: any;
      try {
        resultado = JSON.parse(resposta.text);
      } catch (e) {
        resultado = {
          sugestoes: [{
            texto: resposta.text,
            confianca: 0.8
          }]
        };
      }

      // Garantir formato correto
      if (!Array.isArray(resultado.sugestoes)) {
        resultado.sugestoes = [{
          texto: resultado.sugestoes || resposta.text,
          confianca: 0.8
        }];
      }

      return {
        sugestoes: resultado.sugestoes.map((sug: any) => ({
          texto: sug.texto,
          confianca: sug.confianca || 0.9,
          faseSpin: 'apresentacao',
          timestamp: new Date()
        })),
        faseSugerida: 'apresentacao',
        observacoes: resultado.observacoes || 'Apresentação profissional gerada'
      };

    } catch (erro: any) {
      console.error('[CrmWhatsappAssistantService] Erro ao gerar apresentação:', erro);

      // Fallback
      const nome = nomeContato || 'você';
      const emp = empresa ? ` da ${empresa}` : '';

      return {
        sugestoes: [
          {
            texto: `Olá ${nome}! Meu nome é [Seu Nome], consultor do Meu Cardápio. Somos especialistas em soluções digitais para restaurantes, ajudando a aumentar vendas e reduzir custos operacionais. Gostaria de conhecer um pouco mais sobre como vocês gerenciam os pedidos hoje?`,
            confianca: 0.75,
            faseSpin: 'apresentacao',
            timestamp: new Date()
          },
          {
            texto: `Oi ${nome}${emp}! Sou [Seu Nome] do Meu Cardápio, empresa líder em automação para restaurantes. Nossos clientes aumentam em média 30% o faturamento com nossa plataforma. Posso compartilhar como estamos ajudando restaurantes como o seu?`,
            confianca: 0.7,
            faseSpin: 'apresentacao',
            timestamp: new Date()
          }
        ],
        observacoes: 'Mensagens de apresentação padrão (fallback)'
      };
    }
  }

  /**
   * Processa as tools solicitadas pelo ChatGPT
   */
  private async processarTools(toolsNecessarias: any[], contato?: any): Promise<any> {
    const resultados: any = {};

    console.log('[CrmWhatsappAssistantService] Processando', toolsNecessarias.length, 'tools');

    for (const tool of toolsNecessarias) {
      try {
        const { nome, parametros } = tool;
        console.log(`[CrmWhatsappAssistantService] Executando tool: ${nome}`, parametros);

        let resultado;

        // Executar a tool correspondente
        switch (nome) {
          case 'buscar_info_concorrente':
            resultado = await this.assistantTools.buscar_info_concorrente(parametros);
            break;

          case 'obter_beneficios_cardapio':
            resultado = await this.assistantTools.obter_beneficios_cardapio(parametros);
            break;

          case 'consultar_dados_lead':
            resultado = await this.assistantTools.consultar_dados_lead({
              ...parametros,
              lead: contato // Passar o lead se disponível
            });
            break;

          case 'analisar_perfil_instagram':
            resultado = await this.assistantTools.analisar_perfil_instagram({
              ...parametros,
              lead: contato
            });
            break;

          case 'obter_info_integracoes':
            resultado = await this.assistantTools.obter_info_integracoes(parametros);
            break;

          case 'buscar_caso_sucesso':
            resultado = await this.assistantTools.buscar_caso_sucesso(parametros);
            break;

          case 'analisar_objecao':
            resultado = await this.assistantTools.analisar_objecao(parametros);
            break;

          case 'gerar_rapport_personalizado':
            resultado = await this.assistantTools.gerar_rapport_personalizado({
              ...parametros,
              lead: contato
            });
            break;

          default:
            console.warn(`[CrmWhatsappAssistantService] Tool desconhecida: ${nome}`);
            resultado = { erro: `Tool ${nome} não encontrada` };
        }

        resultados[nome] = resultado;
        console.log(`[CrmWhatsappAssistantService] Tool ${nome} executada com sucesso`);

      } catch (error: any) {
        console.error(`[CrmWhatsappAssistantService] Erro ao executar tool ${tool.nome}:`, error);
        resultados[tool.nome] = {
          erro: error.message,
          tool_executada: false
        };
      }
    }

    return resultados;
  }

  /**
   * Executa uma função específica chamada automaticamente pelo ChatGPT
   */
  private async executarFuncao(nomeFuncao: string, argumentos: any, contato?: any): Promise<any> {
    console.log(`[CrmWhatsappAssistantService] Executando função: ${nomeFuncao}`, argumentos);

    try {
      let resultado;

      // Executar a função correspondente
      switch (nomeFuncao) {
        case 'buscar_info_concorrente':
          resultado = await this.assistantTools.buscar_info_concorrente(argumentos);
          break;

        case 'buscar_sobre_meu_cardapio':
          resultado = await this.assistantTools.obter_beneficios_cardapio(argumentos);
          break;

        case 'consultar_dados_lead':
          resultado = await this.assistantTools.consultar_dados_lead({
            ...argumentos,
            lead: contato // Passar o lead se disponível
          });
          break;

        case 'analisar_perfil_instagram':
          resultado = await this.assistantTools.analisar_perfil_instagram({
            ...argumentos,
            lead: contato
          });
          break;

        case 'obter_info_integracoes':
          resultado = await this.assistantTools.obter_info_integracoes(argumentos);
          break;

        case 'buscar_caso_sucesso':
          resultado = await this.assistantTools.buscar_caso_sucesso(argumentos);
          break;

        case 'analisar_objecao':
          resultado = await this.assistantTools.analisar_objecao(argumentos);
          break;

        case 'gerar_rapport_personalizado':
          resultado = await this.assistantTools.gerar_rapport_personalizado({
            ...argumentos,
            lead: contato
          });
          break;

        default:
          console.warn(`[CrmWhatsappAssistantService] Função desconhecida: ${nomeFuncao}`);
          resultado = { erro: `Função ${nomeFuncao} não encontrada` };
      }

      console.log(`[CrmWhatsappAssistantService] Função ${nomeFuncao} executada com sucesso`);
      return resultado;

    } catch (error: any) {
      console.error(`[CrmWhatsappAssistantService] Erro ao executar função ${nomeFuncao}:`, error);
      return {
        erro: error.message,
        funcao_executada: false
      };
    }
  }

  /**
   * Exemplo de como usar diferentes modelos e linguagens
   * Este método demonstra como personalizar o modelo e linguagem nas chamadas
   */
  async exemploUsoModeloLinguagem(params: {
    telefone: string;
    mensagem: string;
    modelo?: string;
    linguagem?: string;
  }): Promise<any> {
    const { telefone, mensagem, modelo = 'gpt-4o-mini', linguagem = 'pt-BR' } = params;

    const prompt = `Você é um assistente de vendas especializado em restaurantes.
Responda de forma profissional e útil.`;

    // Exemplo usando modelo e linguagem personalizados
    const resposta = await this.chatGPTService.chameOpenAIChatWithFunctionLoop(
      telefone,
      'exemplo_personalizado',
      prompt,
      mensagem,
      [],
      0.7,
      '[exemplo]',
      null,
      [], // sem functions
      undefined,
      undefined, // executor
      10,
      modelo, // modelo personalizado
      linguagem // linguagem personalizada
    );

    return resposta;
  }
}
