import {IEnviadorDeMensagem} from "./IEnviadorDeMensagem";
import {SituacaoDeMensagem} from "./SituacaoDeMensagem";
import {StatusDeMensagem} from "./StatusDeMensagem";
import {MensagemEnviada} from "../domain/MensagemEnviada";
import {TipoDeNotificacaoEnum} from "../domain/TipoDeNotificacaoEnum";
import {EnumMeioDeEnvio} from "../domain/EnumMeioDeEnvio";
import {Empresa} from "../domain/Empresa";
// @ts-ignore
import * as FB from "fb";
import {MapeadorDeTelefoneCloudWhatsapp} from "../mapeadores/MapeadorDeDadosCloudWhatsapp";
import {TelefoneCloudWhatsapp} from "../domain/TelefoneCloudWhatsapp";
import {ProdutoService} from "./ProdutoService";
import {CategoriaProdutosUtils} from "../lib/CategoriaProdutosUtils";
import {VariaveisDeRequest} from "./VariaveisDeRequest";
import {MapeadorDeMensagemEnviada} from "../mapeadores/MapeadorDeMensagemEnviada";

export class EnviadorDeMensagemCloudWhatsapp implements IEnviadorDeMensagem {
  tokenApp = 'EAAOQvUReqXUBALfdM8w29v3a5ZCOJ9rMCwvzluZCxKJxkVINzMTEDNR1bZA76VBZAPclZCmBXeiHTekbaJFCAEH9v4kKaMNNEZAjgZBZCShY21PjpjkZAyUiXYgXaDB91ZCtcw5fyPoOgXv0qZBX2UaOOaO7yjjrZBj02O3Q06TKFStzwtKWWAAxS1fs';

  tokenUsuarioSistema_Meucardapio = 'EAAOQvUReqXUBALfdM8w29v3a5ZCOJ9rMCwvzluZCxKJxkVINzMTEDNR1bZA76VBZAPclZCmBXeiHTekbaJFCAEH9v4kKaMNNEZAjgZBZCShY21PjpjkZAyUiXYgXaDB91ZCtcw5fyPoOgXv0qZBX2UaOOaO7yjjrZBj02O3Q06TKFStzwtKWWAAxS1fs';

  phoneNumberId = '111520891793377'; // ID do número de telefone no WhatsApp Business API

  acompanheMensagem(idMensagem: string): Promise<SituacaoDeMensagem> {
    return undefined;
  }

  async envieMensagem(mensagemEnviada: MensagemEnviada, telefone: string, mensagem: string): Promise<SituacaoDeMensagem> {
    return new Promise<SituacaoDeMensagem>( async (resolve, reject) => {
      const mapeadorDeTelefone = new MapeadorDeTelefoneCloudWhatsapp();

      let resposta = null;
      const telefoneCloudWhatsapp: TelefoneCloudWhatsapp = await mapeadorDeTelefone.selecioneSync({});
      if( mensagemEnviada.tipoDeNotificacao === TipoDeNotificacaoEnum.MensagemSaudacaoWhatsappPedido && false ) {
        resposta = await this.envieMensagemMessenger(telefoneCloudWhatsapp.whatsapp_id, this.tokenApp, mensagemEnviada);
      } else {
        resposta = await this.envieMensagemTexto(telefoneCloudWhatsapp.whatsapp_id, this.tokenApp, mensagemEnviada);
      }

      const idWhatsapp = resposta.messages[0].id;

      mensagemEnviada.idWhatsapp = idWhatsapp;

      await new MapeadorDeMensagemEnviada().atualizeSync(mensagemEnviada);

      const situacaoDeMensagem = new SituacaoDeMensagem();

      situacaoDeMensagem.status = StatusDeMensagem.Nova;
      situacaoDeMensagem.sucesso = true;

      resolve(situacaoDeMensagem);
    });
  }

  envieSMS(telefone: string, mensagem: string): Promise<SituacaoDeMensagem> {
    return new Promise<SituacaoDeMensagem>((resolve) => {
      const situacaoDeMensagem = new SituacaoDeMensagem();

      situacaoDeMensagem.status = StatusDeMensagem.Nova;
      situacaoDeMensagem.sucesso = true;

      resolve(situacaoDeMensagem);
    });
  }

  envieMensagemAtivacaoContato(mensagemEnviada: MensagemEnviada, telefoneContato: string):  Promise<SituacaoDeMensagem> {
    return new Promise<SituacaoDeMensagem>((resolve, reject) => {

    });
  }

  requerAtivacaoDoTelefone(): boolean {
    return false;
  }

  obtenhaMeioDeEnvio(): EnumMeioDeEnvio {
    return EnumMeioDeEnvio.CloudWhatsapp;
  }

  notifiqueAssinantes(mensagemEnviada: MensagemEnviada) {
    console.log('notififique assinantes');
  }

  notifiqueAssinantesEmpresa(empresa: Empresa) {

  }

  /**
   * MÉTODO ÚNICO para fazer chamada à API do WhatsApp
   * Centraliza: envio, captura de ID, atualização do banco
   */
  private async envieParaWhatsappAPI(
    idWhatsappAccount: string,
    tokenApp: string,
    body: any,
    mensagemEnviada: MensagemEnviada,
    tipoMensagem: string = 'mensagem'
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let fb = new FB.Facebook({});
      fb.setAccessToken(tokenApp);

      //console.log(`[EnviadorDeMensagemCloudWhatsapp] Enviando ${tipoMensagem}:`, body);

      console.log('contexto: ', new MapeadorDeMensagemEnviada().obtenhaIdEmpresaLogada());
      const empresa = mensagemEnviada.empresa;

      fb.api('/' + idWhatsappAccount + '/messages', 'post', body, async (res: any) => {
        console.log(`[EnviadorDeMensagemCloudWhatsapp] Resposta ${tipoMensagem}:`, res);

        try {
          // Capturar ID da mensagem e atualizar banco
          if (res && res.messages && res.messages.length > 0) {
            const messageId = res.messages[0].id;
            console.log(`[EnviadorDeMensagemCloudWhatsapp] Message ID: ${messageId}`);
          }

          resolve(res);
        } catch (error) {
          console.error(`[EnviadorDeMensagemCloudWhatsapp] Erro ao processar resposta:`, error);
          resolve(res); // Resolve mesmo com erro na atualização
        }
      });
    });
  }

  /**
   * FACTORY METHODS - Cada um cria o body específico para seu tipo
   */

  private crieBodyTextoSimples(mensagemEnviada: MensagemEnviada, telefone: string): any {
    return {
      "messaging_product": "whatsapp",
      "recipient_type": "individual",
      "to": telefone,
      "type": "text",
      "text": {
        "preview_url": true,
        "body": mensagemEnviada.mensagem
      }
    };
  }

  private crieBodyTextoComLink(mensagemEnviada: MensagemEnviada, telefone: string, link: string, mensagemSemLink: string): any {
    return {
      "messaging_product": "whatsapp",
      "recipient_type": "individual",
      "to": telefone,
      "type": "interactive",
      "interactive": {
        "type": "cta_url",
        "body": {
          "text": mensagemSemLink
        },
        "footer": {
          "text": "Clique para acessar nosso cardápio"
        },
        "action": {
          "name": "cta_url",
          "parameters": {
            "display_text": "Fazer Pedido",
            "url": link
          }
        }
      }
    };
  }

     private crieBodyBotoes(mensagemEnviada: MensagemEnviada, telefone: string, botoes: any[], tituloMenu?: string,
                            footerMenu?: string): any {
     const botoesFormatados = botoes.map((botao: any, index: number) => ({
       type: "reply",
       reply: {
         id: `msg_${mensagemEnviada.id || '0'}_${botao.acao || 'opcao'}_${index}`,
         title: botao.title || botao.texto || "Opção"
       }
     }));

     const body: any = {
       "messaging_product": "whatsapp",
       "recipient_type": "individual",
       "to": telefone,
       "type": "interactive",
       "interactive": {
         "type": "button",
         "body": {
           "text": mensagemEnviada.mensagem
         },
         "action": {
           "buttons": botoesFormatados
         }
       }
     };

     // Adicionar header se existir
     if (tituloMenu) {
       body.interactive.header = {
         "type": "text",
         "text": tituloMenu
       };
     }

     // Adicionar footer se existir
     if (footerMenu) {
       body.interactive.footer = {
         text: footerMenu
       };
     }

     return body;
   }

     private crieBodyLista(mensagemEnviada: MensagemEnviada, telefone: string, botoes: any[], tituloMenu?: string,
                           footerMenu?: string): any {
     const rows = botoes.map((botao: any, index: number) => ({
       id: `msg_${mensagemEnviada.id || '0'}_${botao.acao || 'opcao'}_${index}`,
       title: botao.title || botao.texto || "Opção"
     }));

     const body: any = {
       "messaging_product": "whatsapp",
       "recipient_type": "individual",
       "to": telefone,
       "type": "interactive",
       "interactive": {
         "type": "list",
         "body": {
           "text": mensagemEnviada.mensagem
         },
         "action": {
           "button": "Ver opções",
           "sections": [
             {
               "title": tituloMenu || "Selecione uma opção",
               "rows": rows
             }
           ]
         }
       }
     };

     // Adicionar header se existir
     if (tituloMenu) {
       body.interactive.header = {
         "type": "text",
         "text": tituloMenu
       };
     }

     // Adicionar footer se existir
     if (footerMenu) {
       body.interactive.footer = {
         text: footerMenu
       };
     }

     return body;
   }

  private crieBodyTemplate(mensagem: MensagemEnviada, telefone: string): any {
    return {
      "messaging_product": "whatsapp",
      "recipient_type": "individual",
      "to": telefone,
      "type": "template",
      "text": {
        "preview_url": true,
        "body": mensagem.mensagem
      }
    };
  }

  private crieBodyPix(telefone: string, referenceId: string, valor: number, nomeItem: string, retailerId: string): any {
    return {
      "messaging_product": "whatsapp",
      "to": telefone,
      "type": "interactive",
      "interactive": {
        "type": "order_details",
        "body": {
          "text": "Your message content"
        },
        "action": {
          "name": "review_and_pay",
          "parameters": {
            "reference_id": referenceId,
            "type": "physical-goods",
            "payment_type": "br",
            "payment_settings": [
              {
                "type": "payment_link",
                "payment_link": {
                  "uri": "https://fibo.meucardapio.com.br/pix/1234567890"
                }
              }
            ],
            "currency": "BRL",
            "total_amount": {
              "value": valor,
              "offset": 100
            },
            "order": {
              "status": "pending",
              "tax": {
                "value": 0,
                "offset": 100,
                "description": "optional text"
              },
              "items": [
                {
                  "retailer_id": retailerId,
                  "name": nomeItem,
                  "amount": {
                    "value": valor,
                    "offset": 100
                  },
                  "quantity": 1
                }
              ],
              "subtotal": {
                "value": valor,
                "offset": 100
              }
            }
          }
        }
      }
    };
  }

  /**
   * MÉTODOS PÚBLICOS REFATORADOS - Usam factory methods + envio único
   */

  async envieMensagemMessenger(idWhatsappAccount: string, tokenApp: string, mensagem: MensagemEnviada) {
    console.log('envieMensagemMessenger');

    const empresa: Empresa = mensagem.empresa;
    const logo = empresa.obtenhaHostLoja() + "/images/empresa/" + empresa.logo;
    console.log(logo);

    let telefone = mensagem.telefone;
    if( !telefone.startsWith('55') ) {
      telefone = '55' + telefone;
    }

    const body = this.crieBodyTemplate(mensagem, telefone);

    console.log(body);
    return await this.envieParaWhatsappAPI(idWhatsappAccount, tokenApp, body, mensagem, 'template');
  }

  public async envieMensagemTexto(idWhatsappAccount: string, tokenApp: string, mensagemEnviada: MensagemEnviada): Promise<any> {
    console.log('envieMensagemTexto');

    const empresa: Empresa = mensagemEnviada.empresa;
    const logo = empresa.obtenhaHostLoja() + "/images/empresa/" + empresa.logo;
    console.log(logo);

    // Preparar telefone
    let telefone = mensagemEnviada.telefone;
    if (!telefone.startsWith('+55')) {
      telefone = '+55' + telefone;
    }

    // Processar mensagem para links
    let mensagemLimpa = mensagemEnviada.mensagem
      .replace(/Não compartilhe esse link que tem seu telefone/gi, '')
      .replace(/Não compartilhe este link/gi, '')
      .replace(/link contém seu telefone/gi, '')
      .trim();

    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const links = mensagemLimpa.match(urlRegex);
    const linkPrincipal = links && links.length > 0 ? links[0] : null;
    const mensagemSemLink = linkPrincipal ? mensagemLimpa.replace(linkPrincipal, '').trim() : mensagemLimpa;

    let body: any;
    let tipoMensagem: string;

    // Decidir tipo de mensagem e criar body apropriado
    if (mensagemEnviada.temMenu && mensagemEnviada.menu) {
      // Delegar para método de botões/lista
      return await this.envieMensagemComBotoesReply(idWhatsappAccount, tokenApp, mensagemEnviada);
    } else if (linkPrincipal) {
      console.log(`[EnviadorDeMensagemCloudWhatsapp] Link encontrado: ${linkPrincipal}, enviando como botão com mensagem: ${mensagemSemLink}`);
      body = this.crieBodyTextoComLink(mensagemEnviada, telefone, linkPrincipal, mensagemSemLink);
      tipoMensagem = 'texto com link';
    } else {
      console.log(`[EnviadorDeMensagemCloudWhatsapp] Nenhum link encontrado, enviando como texto normal`);
      body = this.crieBodyTextoSimples(mensagemEnviada, telefone);
      tipoMensagem = 'texto simples';
    }

    // Envio único
    return await this.envieParaWhatsappAPI(idWhatsappAccount, tokenApp, body, mensagemEnviada, tipoMensagem);
  }

  /**
   * Envia uma mensagem com botões de resposta rápida (reply buttons) usando a Cloud API do WhatsApp.
   * Utiliza o campo 'menu' do objeto MensagemEnviada, que pode estar em dois formatos:
   *
   * 1. Array de botões: [{ id: 'id_botao', title: 'Texto do Botão' }, ...]
   * 2. Objeto estruturado: { titulo: 'Título', opcoes: [{ texto: 'Texto Botão', valor: 'Valor', acao: 'acao' }] }
   *
   * Se houver mais de 3 botões, a mensagem será enviada como lista (list message).
   *
   * @param idWhatsappAccount ID da conta WhatsApp de origem
   * @param tokenApp Token de autenticação da API
   * @param mensagemEnviada Objeto contendo a mensagem e os botões a serem enviados
   * @returns Promise com o resultado da operação
   */
  public async envieMensagemComBotoesReply(idWhatsappAccount: string, tokenApp: string, mensagemEnviada: MensagemEnviada): Promise<any> {
    try {
      // Preparar telefone
      let telefone = mensagemEnviada.telefone;
      if (!telefone.startsWith('+55')) {
        telefone = '+55' + telefone;
      }

      // Processar o campo menu para extrair os botões
      let botoes = [];
      let tituloMenu = null;
      let footerMenu = null;

      if (typeof mensagemEnviada.menu === 'string') {
        try {
          botoes = JSON.parse(mensagemEnviada.menu);
        } catch (e) {
          console.error('Erro ao processar JSON do menu:', e);
          botoes = [];
        }
      } else if (Array.isArray(mensagemEnviada.menu)) {
        botoes = mensagemEnviada.menu;
      } else if (mensagemEnviada.menu && mensagemEnviada.menu.opcoes) {
        // Novo formato com objeto estruturado { titulo, opcoes: [{texto, valor, acao}] }
        console.log('Detectado menu no formato estruturado com opcoes');
        tituloMenu = mensagemEnviada.menu.titulo || "Menu";

        // Converter as opções para o formato esperado de botões
        botoes = mensagemEnviada.menu.opcoes.map((opcao: any, index: number) => ({
          id: `msg_${mensagemEnviada.id || '0'}_${opcao.acao || 'opcao'}_${index}`,
          title: opcao.texto,
          description: opcao.valor?.substring(0, 72) || "", // Limitar descrição a 72 caracteres
          valor: opcao.valor,
          acao: opcao.acao
        }));

      } else if (mensagemEnviada.menu) {
        // Caso seja um objeto, mas não no formato esperado
        console.log('Menu em formato desconhecido:', mensagemEnviada.menu);
        if (mensagemEnviada.menu.header) {
          tituloMenu = mensagemEnviada.menu.header;
        }
        if (mensagemEnviada.menu.footer) {
          footerMenu = mensagemEnviada.menu.footer;
        }
      }

      // Decidir entre botões ou lista
      const usarLista = botoes.length > 3;
      let body: any;
      let tipoMensagem: string;

      if (usarLista) {
        body = this.crieBodyLista(mensagemEnviada, telefone, botoes, tituloMenu, footerMenu);
        tipoMensagem = 'lista';
      } else {
        body = this.crieBodyBotoes(mensagemEnviada, telefone, botoes, tituloMenu, footerMenu);
        tipoMensagem = 'botões';
      }

      // Envio único
      return await this.envieParaWhatsappAPI(idWhatsappAccount, tokenApp, body, mensagemEnviada, tipoMensagem);
    } catch (error) {
      console.error('[EnviadorDeMensagemCloudWhatsapp] Erro ao enviar mensagem interativa:', error);
      throw error;
    }
  }

  /**
   * Envia uma mensagem PIX no formato order_details com todos os campos avançados.
   */
  async envieMensagemPix(
    tokenApp: string,
    telefone: string,
    referenceId: string,
    pixCode: string,
    nomeBeneficiario: string,
    chavePix: string,
    tipoChavePix: string,
    valor: number,
    nomeItem: string,
    retailerId: string,
    mensagem: string = '',
    quantidade: number = 1,
    taxValue: number = 0,
    taxDescription: string = '',
    currency: string = 'BRL',
    offset: number = 100
  ) {
    console.log('Enviando mensagem PIX com payload');
    console.log('Phone Number ID:', this.phoneNumberId);

    const body = this.crieBodyPix(telefone, referenceId, valor, nomeItem, retailerId);

    // Para PIX, criamos uma MensagemEnviada temporária se necessário
    const mensagemEnviada = new MensagemEnviada();
    mensagemEnviada.telefone = telefone;
    mensagemEnviada.mensagem = mensagem;

    // Envio único usando o phoneNumberId
    return await this.envieParaWhatsappAPI(this.phoneNumberId, tokenApp, body, mensagemEnviada, 'PIX');
  }
}
