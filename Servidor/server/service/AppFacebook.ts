// @ts-ignore
import * as FB from 'fb';
import {Resposta} from "../utils/Resposta";
import {Usuario} from "../domain/Usuario";
import {Promise} from "es6-promise";
import {Ambiente} from "./Ambiente";
import {MapeadorDeCidade} from "../mapeadores/MapeadorDeCidade";
import {Promocao} from "../domain/Promocao";
import {ExecutorAsync} from "../utils/ExecutorAsync";
import * as uuidv4 from 'uuid/v4';
import * as redis from 'redis';
import {URL, URLSearchParams} from "url";
import {I18nConfig} from "../lib/instagram/i18n.config";
let client = redis.createClient();

let config = require('../config.json');


export class AppFacebook {
  static _instance: AppFacebook;

  id: number;
  secret: string;
  adminPermissions: Array<string>;
  userPermissions: Array<string>;
  basicPermissions: Array<string>;
  pagePermissions: Array<string>;
  accessToken: string;
  urlLogin: string;
  accessTokenPagina: string;  

  paginaSorteieme: string;

  accountKitSecret: string;

  private constructor() {
    this.id = Ambiente.Instance.config.aplicativoFacebook.appId;
    this.secret = Ambiente.Instance.config.aplicativoFacebook.appSecret;
    this.paginaSorteieme = Ambiente.Instance.config.aplicativoFacebook.paginaSorteieme;
    this.adminPermissions = config.facebook.adminPermissions;
    this.basicPermissions = config.facebook.basicPermissions;
    this.userPermissions = config.facebook.userPermissions;
    this.pagePermissions = config.facebook.pagePermissions;
    this.accountKitSecret = Ambiente.Instance.config.aplicativoFacebook.accountKitSecret;
    this.urlLogin = config.urlLogin;
  }

  public static get Instance(){
    return this._instance || (this._instance = new AppFacebook());
  }

  public async estenderToken(accessToken: string) {
    if(!accessToken) return null;

    return new Promise( (resolve, reject) => {
      console.log('estender [AppFaceboook] id app: ' + this.id);
      console.log('estender [AppFaceboook] secret: ' + this.secret);

      let fb = new FB.Facebook({'appId': this.id});

      FB.api('oauth/access_token', {
        client_id: this.id,
        client_secret: this.secret,
        grant_type: 'fb_exchange_token',
        fb_exchange_token: accessToken
      }, function (res: any) {
        console.log(res);

        if (!res || res.error) {
          console.log('continuando');

          console.log(!res ? 'error occurred' : res.error);
          reject({
            sucesso: false,
            error: res.error
          });
          return;
        }

        //let accessToken = res.access_token;
        let expires = res.expires_in ? res.expires_in : 0;

        console.log({
          sucesso: true,
          accessToken: res.access_token,
          expires: expires
        });

        resolve({
          sucesso: true,
          accessToken: res.access_token,
          expires: expires
        });
      });
    });
  }

  public async obtenhaPagina(usuario: any, idPagina: number) {
    return new Promise( (resolve, reject) => {
      AppFacebook.Instance.obtenhaPaginasDoUsuario(usuario.accessToken).then(
        (respostaFacebook: any) => {
          let paginasDoUsuario = respostaFacebook.sucesso ? respostaFacebook.objeto : [];

          for (let i = 0; i < paginasDoUsuario.length; i++) {
            const pagina = paginasDoUsuario[i];

            if (pagina.id === idPagina) {
              resolve(pagina);
            }
          }
        }
      );
    });
  }


  public dadosTarefaInstagram(promocao: Promocao, cb: Function) {
    let chave = "tarefa_" + promocao.id;

    client.get(chave, (err, reply) => {
      let tarefa = JSON.parse(reply);

      return cb(tarefa);
    });
  }

  public assineEventosInsta(token: string, idPagina: string) {
    console.log('[AppFacebook] assinando eventos insta: ' + idPagina + ' -> ' + token);

    return new Promise((resolve, reject) => {
      let fb = new FB.Facebook({'appId': this.id});
      fb.setAccessToken(token);

      fb.api('/' + idPagina + '/subscribed_apps?subscribed_fields=feed', 'post', async (fbRes: any) => {
        console.log('[AppFacebook] se inscreveu', fbRes);
        if (!fbRes || fbRes.error) {
          resolve(fbRes.error);
          return
        }

        resolve();
      });
    });

    /*
      let url = new URL(`${config.apiUrl}/${config.pageId}/subscribed_apps`);
      url.search = new URLSearchParams({
        access_token: config.pageAccesToken,
        subscribed_fields: "feed"
      });
      let response = await fetch(url, {
        method: "POST"
      });
      if (response.ok) {
        console.log(`Page subscriptions have been set.`);
      } else {
        console.warn(`Error setting page subscriptions`, response.statusText);
      }
    }

     */
  }

  public obtenhaTokenDePagina(idFanPage: number, accessTokenUsuario: string): Promise<string> {
    return new Promise((resolve, reject) => {
      let fb = new FB.Facebook({'appId': this.id});
      fb.setAccessToken(accessTokenUsuario);

      const urlInicial = '/' + idFanPage + '?fields=access_token';

      console.log(urlInicial);

      fb.api('', 'post', {
        batch: [
          {method: 'get', relative_url: urlInicial}
        ]
      }, async (fbRes: any) => {
        if( !fbRes || fbRes.err ) {
          return resolve(null);
        }

        let resposta: any = JSON.parse(fbRes[0].body);

        console.log(resposta);

        resolve(resposta.access_token);
      });
    });
  }

  public aumenteEObtenhaQtdeComentarios(promocao: Promocao, username: string) {
    return new Promise( (resolve, reject) => {
      client.hincrby('participantes_' + promocao.id, username, 1, (err, reply) => {
        if( err ) {
          //TODO: tratar
        }

        resolve(reply);
      });
    });
  }
  public qtdeMencoes(str: string) {
    const re = /([@][\w_-]+)/g;

    return ((str || '').match(re) || []).length;
  }


  public obtenhaPostagemFace(idPostagem: string, accessTokenUsuario: string) {
    let url = "/" + idPostagem + '?fields=id,thumbnail_url,timestamp,comments_count,caption,is_comment_enabled,permalink';

    console.log(url);

    return new Promise((resolve, reject) => {
      let fb = new FB.Facebook({'appId': this.id});
      fb.setAccessToken(accessTokenUsuario);

      fb.api('', 'post', {
        batch: [
          { method: 'get', relative_url: url}
        ]
      }, function (fbRes: any) {
        console.log(fbRes);

        if (!fbRes || fbRes.error) {
          resolve(new Resposta<Array<any>>(
            {mensagem: !fbRes ? 'Ocorreu um erro ao chamar o Facebook' : fbRes.error.message}
          ));
          return
        }

        let pagina: any = JSON.parse(fbRes[0].body);

        resolve(new Resposta<any>({objeto: pagina}))
      });
    })
  }

  public obtenhaPostagemInsta(idPostagem: string, accessTokenUsuario: string) {
    let url = "/" + idPostagem + '?fields=media_url,media_type,id,thumbnail_url,timestamp,comments_count,caption,is_comment_enabled,permalink';

    console.log(url);

    return new Promise((resolve, reject) => {
      let fb = new FB.Facebook({'appId': this.id});
      fb.setAccessToken(accessTokenUsuario);

      fb.api('', 'post', {
        batch: [
          { method: 'get', relative_url: url}
        ]
      }, function (fbRes: any) {
        console.log(fbRes);

        if (!fbRes || fbRes.error) {
          resolve(new Resposta<Array<any>>(
            {mensagem: !fbRes ? 'Ocorreu um erro ao chamar o Facebook' : fbRes.error.message}
          ));
          return
        }

        let pagina: any = JSON.parse(fbRes[0].body);

        resolve(new Resposta<any>({objeto: pagina}))
      });
    })
  }

  public obtenhaImagensInstagram(idInstagram: string, accessTokenUsuario: string): Promise<any> {
    let url = "/" + idInstagram + '/media?fields=media_url,media_type,id,thumbnail_url,comments_count,caption,is_comment_enabled,permalink';

    console.log('url');

    return new Promise((resolve, reject) => {
      let fb = new FB.Facebook({'appId': this.id});
      fb.setAccessToken(accessTokenUsuario);

      fb.api('', 'post', {
        batch: [
          { method: 'get', relative_url: url}
        ]
      }, function (fbRes: any) {
        console.log(fbRes);

        if (!fbRes || fbRes.error) {
          resolve(new Resposta<Array<any>>(
            {mensagem: !fbRes ? 'Ocorreu um erro ao chamar o Facebook' : fbRes.error.message}
          ));
          return
        }

        let pagina: any = JSON.parse(fbRes[0].body);

        resolve(new Resposta<any>({objeto: pagina}))
      });
    })
  }

  public obtenhaDadosDaPagina(idPagina: string, accessTokenUsuario: String): Promise<any> {
    return new Promise((resolve, reject) => {
      let fb = new FB.Facebook({'appId': this.id});
      fb.setAccessToken(accessTokenUsuario);

      fb.api('', 'post', {
        batch: [
          { method: 'get', relative_url: '/' + idPagina +  '?fields=id,name,username,access_token,instagram_business_account'}
        ]
      }, function (fbRes: any) {
        if (!fbRes || fbRes.error) {
          resolve(new Resposta<Array<any>>(
            {mensagem: !fbRes ? 'Ocorreu um erro ao chamar o Facebook' : fbRes.error.message}
          ));
          return
        }

        let pagina: any = JSON.parse(fbRes[0].body);

        resolve(new Resposta<any>({objeto: pagina}))
      });
    })
  }

  private executePaginacaoFacebook(urlOriginal: string, url: string, funcaoFacebook: Function, cbFim: Function) {
    console.log('Chamando páginação. Url: ' + url);
    funcaoFacebook(url, (paging: any, err: any) => {
      if(err) {
        //console.log('Ocorreu um erro durante a paginação: ' + err);
        return;
      }

      if(!paging || !paging.next) {
        console.log('Paginação encerrada. Executando callback de finalização');
        return cbFim()
      }

      //console.log('Existem mais páginas. Executando novamente.');
      let novaURL = urlOriginal + '&after=' + paging.cursors.after;
      this.executePaginacaoFacebook(urlOriginal, novaURL, funcaoFacebook, cbFim);
    })
  }

  public feed(idPagina: string, accessTokenUsuario: string) {
    return new Promise((resolve, reject) => {
      let fb = new FB.Facebook({'appId': this.id});
      fb.setAccessToken(accessTokenUsuario);

      fb.api('', 'post', {
        batch: [
          { method: 'get', relative_url: '/' + idPagina +  '/feed?fields=id,permalink_url,full_picture'}
        ]
      }, function (fbRes: any) {
        if (!fbRes || fbRes.error) {
          resolve(new Resposta<Array<any>>(
            {mensagem: !fbRes ? 'Ocorreu um erro ao chamar o Facebook' : fbRes.error.message}
          ));
          return
        }

        let pagina: any = JSON.parse(fbRes[0].body);

        resolve(new Resposta<any>({objeto: pagina}))
      });
    })
  }

  public obtenhaPaginasDoUsuario(accessTokenUsuario: String): Promise<Resposta<Array<any>>> {
    return new Promise( (resolve, reject) => {
      let fb = new FB.Facebook({'appId': this.id});
      fb.setAccessToken(accessTokenUsuario);

      let url = '/me/accounts?fields=id,name,username,access_token,category,category_list,instagram_business_account';
      let paginas: Array<any> = [];

      this.executePaginacaoFacebook(url, url, (urlRetorno: string, cb: Function) => {
        //console.log('Preparando para invocar o Facebook');
        fb.api('', 'post', {
          batch: [
            { method: 'get', relative_url: urlRetorno}
          ]
        }, function (fbRes: any) {
          //console.log('Facebook respondeu.')
          if (!fbRes || fbRes.error) {
            console.log( 'Ocorreu um erro na chamada ao Facebook.');
            resolve(new Resposta<Array<any>>(
              {mensagem: !fbRes ? 'Ocorreu um erro ao chamar o Facebook' : fbRes.error.message}
            ));
            return cb(null, 'Ocorreu um erro');
          }
          let respostaFacebook = JSON.parse(fbRes[0].body);
          let listaPaginas  = respostaFacebook.data;
          //console.log(respostaFacebook)

          paginas = paginas.concat(listaPaginas);
          //console.log('Páginas encontradas');
          //console.log(listaPaginas);
          //console.log('Todas as páginas');
          //console.log(paginas);
          cb(respostaFacebook.paging);
        })
      }, function() {
        //console.log('Paginação encerrada. Devolvendo resposta.');
          resolve(new Resposta<Array<any>>({objeto: paginas}))
        });
    });
  }

  public verifiqueSePossuiAbas(paginas: Array<any>, accessTokenUsuario: String): Promise<Resposta<Array<any>>> {
    return new Promise((resolve, reject) => {

      let fb = new FB.Facebook({'appId': this.id});
      fb.setAccessToken(accessTokenUsuario);

      let comandos = [];
      for ( const pagina of paginas) {

        let comando = {
          method: 'get',
          relative_url: '/' + pagina.id + '/tabs?tab=app_' + this.id + '&access_token=' + pagina.access_token,
        };

        comandos.push(comando);
      }

      let lotesComandos = [];
      let tamanhoLote = 50;

      for(let i = 0; i < comandos.length; i = i + tamanhoLote)
        lotesComandos.push(comandos.slice(i, i + tamanhoLote));

      let promises: Array<any> = [];

      lotesComandos.forEach((lote) => {
        promises.push(this.executeChamadaEmLote(fb, lote));
      })



      Promise.all(promises).then((respostas) => {
        console.log('Quantidade de lotes: '  + respostas.length);
        let indicePaginas = 0;

        for(let i = 0; i < respostas.length; i++) {
          console.log('Tratando respostas do lote' + i);
          let resTabs = respostas[i];

          if (!resTabs || resTabs.error) {
            console.log('Houve um erro ao tratar o lote');
            resolve(new Resposta<Array<any>>(
              {mensagem: !resTabs ? 'Ocorreu um erro ao verificar as abas do Usuário' : resTabs.error.message}
            ));
            return;
          }
          console.log('Indice páginas atual: ' + indicePaginas);

          for ( let j = 0; j < resTabs.length; j ++, indicePaginas++) {
            let pagina: any = paginas[indicePaginas];
            let resposta = resTabs[i];

            if( resposta ) {
              let dados = JSON.parse(resposta.body);

              if ( dados.data.length > 0 ) {
                pagina.temAba = true;
              } else {
                pagina.temAba = false;
              }
            } else {
              pagina.temAba = false;
            }

          }
        }
        console.log('Invocando resolve');
        resolve(new Resposta<Array<any>>({objeto: paginas}))
      });
    })
  }

  public executeChamadaEmLote(fb: FB, comandos: Array<any>): Promise<any> {
    return new Promise((resolve, reject) => {
      console.log("Invocando lote");
      console.log(comandos)
      fb.api('', 'post', {
        batch: comandos
      }, function(res: any) {
        resolve(res);
      });
    })
  }

  public envieMensagemMessenger(accessTokenPagina: string, resposta: any) {
    return new Promise( (resolve, reject) => {
      let fb = new FB.Facebook({'appId': this.id});
      fb.setAccessToken(accessTokenPagina);

      fb.api('/me/messages', 'post', resposta, (res: any) => {
        console.log('resposta: ', res);
        resolve(res);
      });
    });
    /*
    let url = new URL(`${config.apiUrl}/me/messages`);
    url.search = new URLSearchParams({
      access_token: config.pageAccesToken
    });
    let response = await fetch(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestBody)
    });
    if (!response.ok) {
      console.warn(`Could not sent message.`, response.statusText);
    }
     */
  }

  debugToken(token: string): Promise<any> {
    return new Promise( (resolve, reject) => {
      let fb = new FB.Facebook({'appId': this.id});

      fb.setAccessToken(token);

      fb.api('debug_token?input_token=' + token, 'get', {}, (fbRes: any) => {
        if (!fbRes || fbRes.error) {
          resolve(Resposta.erro(fbRes.error));
          return;
        }

        resolve(Resposta.sucesso(fbRes.data));
      });
    });
  }

  obtenhaAccessToken(code: string): Promise<any> {
    return new Promise((resolve, reject) => {
      console.log('[AppdFacebook] ================== INÍCIO DA TROCA DE CODE POR TOKEN ==================');
      console.log('[AppFacebook] Trocando code por access token');
      console.log('[AppFacebook] App ID:', this.id);
      console.log('[AppFacebook] Code recebido (primeiros 20 caracteres):', code ? code.substring(0, 20) + '...' : 'vazio');
      console.log('[AppFacebook] Tamanho do code:', code ? code.length : 0);

      // Criar instância do Facebook SDK
      let fb = new FB.Facebook({'appId': this.id});

      // Configurar os parâmetros para a troca de código por token
      const params = {
        client_id: this.id,
        client_secret: this.secret,
        grant_type: "authorization_code",
        code: code
      };

      console.log('[AppFacebook] Parâmetros da requisição:', JSON.stringify(params));
      console.log('[AppFacebook] Iniciando chamada fb.api para trocar código por token');

      // Usar fb.api para fazer a troca
      fb.api('oauth/access_token', 'get', params, (fbRes: any) => {
        console.log('[AppFacebook] Resposta recebida do Facebook:', JSON.stringify(fbRes));

        if (!fbRes || fbRes.error) {
          console.error('[AppFacebook] Erro na resposta do Facebook:', JSON.stringify(fbRes ? fbRes.error : 'Resposta vazia'));
          resolve(Resposta.erro(fbRes ? fbRes.error.message : 'Erro na troca de código por token'));
          console.log('[AppFacebook] ================== FIM DA TROCA DE CODE POR TOKEN (COM ERRO) ==================');
          return;
        }

        // Verificar se a resposta contém o access_token
        if (fbRes.access_token) {
          console.log('[AppFacebook] Access token obtido com sucesso!');
          console.log('[AppFacebook] Access token (primeiros 20 caracteres):', fbRes.access_token.substring(0, 20) + '...');
          console.log('[AppFacebook] Tamanho do token:', fbRes.access_token.length);
          console.log('[AppFacebook] Expires in:', fbRes.expires_in);
          console.log('[AppFacebook] ================== FIM DA TROCA DE CODE POR TOKEN (SUCESSO) ==================');
          resolve(Resposta.sucesso(fbRes));
        } else {
          console.error('[AppFacebook] Resposta sem access_token:', JSON.stringify(fbRes));
          console.log('[AppFacebook] ================== FIM DA TROCA DE CODE POR TOKEN (SEM TOKEN) ==================');
          resolve(Resposta.erro('Resposta sem access_token'));
        }
      });
    });
  }

  listeTelefones(wabas_id: string[], token: string): Promise<any> {
    return new Promise( (resolve, reject) => {
      let fb = new FB.Facebook({'appId': this.id});

      fb.setAccessToken(token);

      const chamadas = wabas_id.map( waba_id => {
        return {method: 'get', relative_url: `${waba_id}/phone_numbers`};
      });

      fb.api('', 'post', {
        batch: chamadas
      }, async (listaRespostasFb: any) => {
        if( !listaRespostasFb || listaRespostasFb.err ) {
          return resolve(null);
        }

        let resps = [];
        for( let i = 0; i < listaRespostasFb.length; i++ ) {
          let fbRes = listaRespostasFb[i];

          let resposta: any = JSON.parse(fbRes.body);

          if( resposta.data && resposta.data.length > 0 ) {
            const dadosTelefone = resposta.data[0];
            dadosTelefone.waba_id = wabas_id[i];
            resps.push({
              id: dadosTelefone.id,
              nome: dadosTelefone.verified_name,
              waba_id: wabas_id[i],
              telefone: dadosTelefone.display_phone_number,
              rating: dadosTelefone.quality_rating,
              status: dadosTelefone.code_verification_status
            });
          }
        }

        console.log('resp: ', resps);

        resolve(resps);
      });
    });
  }

  public assineEventosWhatsapp(waba_id: string, token: string) {
    console.log('[AppFacebook] assinando eventos whatsapp: ' + waba_id + ' -> ' + token);

    return new Promise((resolve, reject) => {
      let fb = new FB.Facebook({'appId': this.id});
      fb.setAccessToken(token);

      fb.api(`/${waba_id}/subscribed_apps`, 'post', async (fbRes: any) => {
        console.log('[AppFacebook] se inscreveu', fbRes);
        if (!fbRes || fbRes.error) {
          resolve(fbRes.error);
          return
        }

        resolve(fbRes.data);
      });
    });
  }

  definaIceBreakers(tokenDePagina: string, idPagina: string): Promise<Resposta<any>> {
    console.log('Definindo icebreakers');

    return new Promise( (resolve, reject) => {
      try {
        const i18n = I18nConfig.Instance.i18n();

        const iceBreakers = [
          {
            question: i18n.__("menu.fazer_pedido"),
            payload: "FAZER_PEDIDO"
          },
          {
            question: i18n.__("menu.cardapio"),
            payload: "CARDAPIO"
          },
          {
            question: i18n.__("menu.horario_atendimento"),
            payload: "HORARIO_ATENDIMENTO"
          }
        ];


        let fb = new FB.Facebook({'appId': this.id});
        fb.setAccessToken(tokenDePagina);

        let objeto = {
          ice_breakers: iceBreakers,
          platform: 'instagram'
        }
        fb.api('/me/messenger_profile', 'post', objeto, (fbRes: any) => {
          if (!fbRes || fbRes.error) {
            resolve(Resposta.erro(fbRes.error));
            return;
          }

          console.log(`Icebreakers have been set.`, fbRes);
          resolve(Resposta.sucesso(true));
        });
      } catch( erro) {
        console.log(erro);
        resolve(Resposta.erro(erro.message));
      }
    });
  }

  desassinarEventos(idObjeto: string, tokenDePagina: string) {
    return new Promise((resolve, reject) => {
      let fb = new FB.Facebook({'appId': this.id});
      fb.setAccessToken(tokenDePagina);

      fb.api('/' + idObjeto + '/subscribed_apps', 'delete', async (fbRes: any) => {
        console.log('[AppFacebook] desinscreveu', fbRes);
        resolve();
      });
    });
  }

  public obtenhaResumoConversas(wabaId: string, accessToken: string, startTime: number, endTime: number): Promise<{ sucesso: boolean, data?: any[], erro?: string }> {
    console.log(`[AppFacebook] Obtendo resumo de conversas para WABA ID: ${wabaId}`);
    console.log(`[AppFacebook] StartTime: ${startTime}, EndTime: ${endTime}`);

    return new Promise((resolve, reject) => {
      let fb = new FB.Facebook({ 'appId': this.id });
      fb.setAccessToken(accessToken);

      const path = `/${wabaId}/conversation_analytics`;
      const params = {
        fields: 'data_points',
        granularity: 'daily',
        start: startTime,
        end: endTime
      };

      fb.api(path, 'get', params, (fbRes: any) => {
        if (!fbRes || fbRes.error) {
          const errorMsg = fbRes && fbRes.error ? fbRes.error.message : 'Erro desconhecido ao buscar resumo de conversas.';
          console.error('[AppFacebook] Erro ao buscar resumo de conversas:', fbRes ? JSON.stringify(fbRes.error) : 'Resposta vazia');
          resolve({ sucesso: false, erro: errorMsg });
          return;
        }

        if (fbRes.data && fbRes.data.length > 0 && fbRes.data[0].data_points) {
          console.log('[AppFacebook] Resumo de conversas obtido com sucesso.');
          resolve({ sucesso: true, data: fbRes.data[0].data_points });
        } else {
          console.warn('[AppFacebook] Resposta da API conversation_analytics não contém data_points esperados:', JSON.stringify(fbRes));
          resolve({ sucesso: true, data: [] }); // Retorna sucesso com dados vazios se a estrutura não for a esperada mas não houver erro explícito.
        }
      });
    });
  }
}


