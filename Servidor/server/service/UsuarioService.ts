 import {Usua<PERSON>} from "../domain/Usuario";
import {MapeadorDeUsuario} from "../mapeadores/MapeadorDeUsuario";
import {Filtro} from "../domain/Filtro";
import {MapeadorDeFiltro} from "../mapeadores/MapeadorDeFiltro";
import * as async from "async";
// @ts-ignore
 import bcrypt = require('bcrypt');
 import {MapeadorDeRegistroDeLogin} from "../mapeadores/MapeadorDeRegistroDeLogin";
 import { promisify } from 'util';
 import {RegistroDeLogin} from "../domain/RegistroDeLogin";
 import {Empresa} from "../domain/Empresa";

 let redis = require("redis");
 let client = redis.createClient();

export class UsuarioService {
  obtenhaUsuarioPeloEmail(email: string): Promise<Usuario> {
    return new Promise((resolve) => {
      if(!email) return resolve(null);

      let mapeador = new MapeadorDeUsuario();

      mapeador.selecioneSync({email: email}).then( (usuario: Usuario) => {
        resolve(usuario)
      })
    })
  }

  obtenhaUsuarioPeloId(id: any): Promise<Usuario> {
    return new Promise(resolve => {
      if(!id) return resolve(null);

      let mapeador = new MapeadorDeUsuario();

      mapeador.selecioneSync({id : id}).then( (usuario: Usuario) => {
        resolve(usuario)
      });
    })
  }

  crieUsuario(dadosUsuario: any): Usuario {
    let usuario: Usuario = new Usuario();

    Object.assign(usuario, dadosUsuario)

    if(dadosUsuario.senha){
      usuario.ultimoLogin = new Date();
      usuario.senha = bcrypt.hashSync(dadosUsuario.senha, 12)
    }

    return usuario;
  }

  insiraUsuario(usuario: Usuario): Promise<boolean> {
    return new Promise((resolve) => {
      if(!usuario) return resolve(false);

      let mapeador = new MapeadorDeUsuario();

      return resolve(mapeador.insiraSync(usuario));
    })

  }

  verifiqueExistencia(email: string): Promise<boolean> {
    return new Promise(resolve => {
      if(!email) return resolve(null);

      let mapeador = new MapeadorDeUsuario();

      mapeador.existe({email: email}, (existe: any) => {
        resolve(existe)
      })
    })

  }

  atualizeFiltro(filtro: Filtro): Promise<string> {
    return new Promise( (resolve, reject) => {
        let erro = filtro.valide()
        if(erro) return resolve(erro)

        const mapeador =  new MapeadorDeFiltro();
        async.series([
          (cb: Function) => {
            mapeador.existeSync({nome: filtro.nome, idUsuario: filtro.usuario.id, id: filtro.id}).then( (existe) => {
              if(!existe) return cb()

              cb('Já existe um filtro com esse nome: ' + filtro.nome);
            })
          },
          async () => {
            return mapeador.atualizeSync(filtro);
          }
        ] , (err: string ) => {
          resolve( err )
        })
    })
  }

  salveFiltro(filtro: Filtro): Promise<string> {
    return new Promise( (resolve, reject) => {
      let erro: string = filtro.valide();
      if(erro) return resolve(erro)

      const mapeador =  new MapeadorDeFiltro();
      async.series([
        (cb: Function) => {
          mapeador.existeSync({nome: filtro.nome, idUsuario: filtro.usuario.id}).then( (existe) => {
            if(!existe) return cb()

            cb('Já existe um filtro com esse nome: ' + filtro.nome);
          })
        },
        async () => {
          return mapeador.insiraSync(filtro);
        }
      ] , (err: string ) => {
        resolve( err )
      })
    })
  }

  atualizeDadosAcesso(empresa: Empresa, usuario: Usuario, senha: any ){
    let mapeador = new MapeadorDeUsuario();

    return new Promise<void>( async (resolve, reject: any) => {
      let erro = usuario.setSenha(senha);
      if(erro) return reject(erro)

      await mapeador.atualizeSenha(usuario);

      await this.deslogarTodasSessoes(empresa, usuario);

      resolve();

    })
  }

  async deslogarTodasSessoes(empresa: Empresa, usuario: Usuario) {
    const mapeador = new MapeadorDeRegistroDeLogin();
    const registrosAtivos: Array<RegistroDeLogin> = await mapeador.listeAsync({idEmpresa: empresa.id, idUsuario: usuario.id, sessaoAtiva: true});

    console.log('registros ativos: ' + registrosAtivos.length);

    const delAsync = promisify(client.del).bind(client);

    for (const registro of registrosAtivos) {
      const resp = await delAsync(`sess:${registro.idSessao}`).catch( (erro: Error) => {
        console.log(erro);
      });
      registro.deslogue();
      await mapeador.atualizeSync(registro);

      console.log(resp);
    }
  }

  async deslogarSessao(registroDeLogin: RegistroDeLogin) {
    const mapeador = new MapeadorDeRegistroDeLogin();

    const delAsync = promisify(client.del).bind(client);

    const resp = await delAsync(`sess:${registroDeLogin.idSessao}`).catch( (erro: Error) => {
      console.log(erro);
    });

    registroDeLogin.deslogue();
    await mapeador.atualizeSync(registroDeLogin);
  }

  async atualizeAssinatura(usuario: Usuario) {
    let mapeador = new MapeadorDeUsuario();

    return await mapeador.atualizeAssinatura(usuario);
  }

  async marqueComoRemovido(usuario: Usuario) {
    let mapeador = new MapeadorDeUsuario();

    return await mapeador.marqueComoRemovido(usuario);
  }
}
