import {MapeadorDePagamentoPedido} from "../mapeadores/MapeadorDePagamentoPedido";
import {NotificacaoService} from "./NotificacaoService";
import {Ambiente} from "./Ambiente";
import {TarefaMensagemPagamentoPendente} from "../domain/TarefaMensagemPagamentoPendente";
import {MensagemEnviada} from "../domain/MensagemEnviada";
import {MapeadorTarefaMensagemPagamentoPendente} from "../mapeadores/MapeadorTarefaMensagemPagamentoPendente";

export class CriadorMensagemPagamentoPendenteService{
   static async criePendentes(){
    const mapeadorTarefa = new MapeadorTarefaMensagemPagamentoPendente();

    let pagamentosNotificar: Array<TarefaMensagemPagamentoPendente> = await mapeadorTarefa.listeAsync({executar: true});


    console.log('total pagamentos notifiar: ' + pagamentosNotificar.length)

     let contexto: any =  require('domain').active.contexto;

     for( let tarefa of pagamentosNotificar ) {
       contexto.idEmpresa = tarefa.empresa.id;
       contexto.empresa = tarefa.empresa;

       const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(tarefa.empresa));

       const resposta =
         await notificacaoService.envieNotificacaoPagamentoPendenteOnline(tarefa.guidPedido, tarefa.pagamento, tarefa.contato);

       if( resposta && resposta.sucesso ) {
         const mensagemCriada: MensagemEnviada = resposta.data;

        // tarefa.mensagemAvaliarPedido = mensagemCriada;

         tarefa.executada = true;
         await mapeadorTarefa.atualizeSync(tarefa);
       }
     }
  }
}
