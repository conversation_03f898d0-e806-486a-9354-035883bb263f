import axios from "axios";
let querystring = require('querystring');

export class GupshupService {
  envieMensagem(contato: {telefone: string}, mensagem: {texto: string}) {
    return new Promise( ((resolve, reject) => {
      let headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        apikey: '4dbf6d4c7c8a49bcc91e08569e6684b1'
      };
      let formData = querystring.stringify({
        "source": '556234325567',
        "destination" : contato.telefone,
        "message" : mensagem.texto,
        "channel": 'whatsapp',
        "src.name": 'PromokitSuporte'
      });

      axios.post(String(`https://api.gupshup.io/sm/api/v1/msg`), formData, {
        headers: headers
      }).then(   (response: any) => {
        console.log('resposta', response.data);
        resolve({});
      }).catch((erro: any) => {
        console.log(erro);
        resolve({});
      });
    }));
  }
}
