import * as bizSdk from 'facebook-nodejs-business-sdk';
import * as cookieParser from "cookie";
import {Ambiente} from "../Ambiente";
import {Pedido} from "../../domain/delivery/Pedido";
import {Content, CustomData} from "facebook-nodejs-business-sdk";
import {Resposta} from "../../utils/Resposta";
import EventResponse from "facebook-nodejs-business-sdk/src/objects/serverside/event-response";
import {Empresa} from "../../domain/Empresa";
import {Contato} from "../../domain/Contato";

export class APIConversoesFacebookService {
  envieEventoIniciarCheckout(req: any) {
    const dadosPedido = req.body;
    const pedido = dadosPedido.pedido;

    const content = (new Content());

    const customData = (new CustomData())
      .setCurrency('brl')
      .setValue(pedido.total);

    const idEvento = dadosPedido.idEvento;

    return this.envieEventoParaFBUsandoReq(req, 'InitiateCheckout', idEvento, customData);
  }

  envieEventoAddCarrinhoFromReq(req: any) {
    const itemPedido = req.body;

    const content = (new Content())
        .setId(itemPedido.produto.id)
        .setQuantity(itemPedido.qtde);

    const customData = (new CustomData())
        .setContents([content])
        .setCurrency('brl')
        .setValue(itemPedido.total / itemPedido.qtde);

    // Usar o ID do evento recebido do front-end, se disponível
    let idEvento = itemPedido.idEvento;


    this.envieEventoParaFBUsandoReq(req, 'AddToCart', idEvento, customData);
  }

  envieEventoFinalizarPedidoFromReq(req: any, dadosPedido: any, idEvento: any) {
    const customData = (new CustomData())
        .setOrderId(dadosPedido.id + '')
        .setCurrency('brl')
        .setValue(dadosPedido.valor);

    console.log(dadosPedido);

    this.envieEventoParaFBUsandoReq(req, 'Purchase', idEvento, customData);
  }

  private envieEventoParaFBUsandoReq(req: any, nomeEvento: string, idEvento: string, customData: bizSdk.CustomData) {
    const fullUrl = req.protocol + '://' + req.get('host') + req.originalUrl;
    const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
    const userAgent = req.headers['user-agent'];
    const contato: any = req.session.contatoLogado;
    const cookies = cookieParser.parse(req.headers.cookie || '');
    const empresa: Empresa = req.empresa;

    return this.envieEventoParaFB(contato, nomeEvento, idEvento, ip, userAgent, fullUrl, cookies._fbp,
      cookies._fbc, customData, empresa.pixelFacebook, empresa. accessTokenAPIConversoes, empresa.codigoTesteAPIConversoes);
  }

  //event_id
  private envieEventoParaFB(contato: any, nomeEvento: string, idEvento: string, ip: string, userAgent: string,
                            url: string, fbp: string, fbc: string, customData: bizSdk.CustomData,
                            pixelFacebook: string, accessToken: string = '', codigoTesteToken: string = ''): Promise<EventResponse> {
    if( !accessToken || !pixelFacebook ) {
      console.log('faltando accessToken ou pixelFacebook -> ', accessToken, pixelFacebook);
      return;
    }

    const DeliveryCategory = bizSdk.DeliveryCategory;
    const EventRequest = bizSdk.EventRequest;
    const UserData = bizSdk.UserData;
    const ServerEvent = bizSdk.ServerEvent;

    //url configuração dos eventos da api de conversões
    //https://business.facebook.com/events_manager2/conversions_api/348592624167355?business_id=102344488856463&global_scope_id=102344488856463

    //url do facebook da configuração do pixel
    //https://business.facebook.com/events_manager2/list/pixel/348592624167355/settings?business_id=102344488856463
    const access_token = accessToken;
    const pixel_id = pixelFacebook;
    const api = bizSdk.FacebookAdsApi.init(access_token);

    let current_timestamp = Math.floor(new Date().getTime() / 1000);

    const userData = (new UserData())
      // It is recommended to send Client IP and User Agent for Conversions API Events.
      .setClientIpAddress(ip)
      .setClientUserAgent(userAgent);

    if( contato ) {
      if( contato instanceof Contato ) {
        try {
          userData.setFirstName(contato.obtenhaPrimeiroNome());
          userData.setLastName(contato.obtenhaLastName());
        } catch (erro) {
          console.log('erro api conversoes: ', erro);
        }
      } else if( (contato as any).primeiroNome && (contato as any).ultimoNome ) {
        userData.setFirstName((contato as any).primeiroNome);
        userData.setLastName((contato as any).ulitmoNome);
      }
    }

    if( contato && contato.telefone ) {
      const telefone = contato.codigoPais + contato.telefone;

      userData.setPhone(telefone);
    }

    if( fbp ) userData.setFbp(fbp);
    if( fbc ) userData.setFbp(fbc);

    const serverEvent = (new ServerEvent())
      .setEventName(nomeEvento)
      .setEventTime(current_timestamp)
      .setEventId(idEvento)
      .setUserData(userData)
      .setCustomData(customData)
      .setEventSourceUrl(url)
      .setActionSource('website');

    const eventsData = [serverEvent];
    const eventRequest = (new EventRequest(access_token, pixel_id))
      .setEvents(eventsData);

    if( codigoTesteToken ) {
      eventRequest.setTestEventCode(codigoTesteToken);
    }

    return eventRequest.execute();
  }
}
