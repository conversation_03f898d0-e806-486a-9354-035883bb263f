import {IBuscadorProdutoEANService} from "../IBuscadorProdutoEANService";
import {DadosProdutoEAN} from "../DadosProdutoEAN";
import {Resposta} from "../../../utils/Resposta";
const axios = require('axios');
const tor_axios = require('tor-axios');
const tor = tor_axios.torSetup({
  ip: 'localhost',
  port: 9050
});

export class BuscadorLiaSupermercadorDeProdutoEANService implements IBuscadorProdutoEANService {
  busque(ean: string): Promise<Resposta<any>> {

    return new Promise<Resposta<any>>( async (resolve, reject) => {
      const url = 'https://liasupermercado.com.br/wp-admin/admin-ajax.php?action=woodmart_ajax_search&number=20' +
        '&post_type=product&query=' + ean;

      await tor.torNewSession(); //change tor ip

      const inst = axios.create({
        httpAgent: tor.httpAgent(),
        httpsagent: tor.httpsAgent(),
      });

      inst.get(url).then((response: any) => {
        const sugestoes = response.data.suggestions;

        if( sugestoes.length === 0 || sugestoes.no_found ) {
          return resolve(Resposta.erro(`Produto ${ean} não encontrado!`));
        }

        const sugestao = sugestoes[0];

        if( sugestao.no_found ) {
          return resolve(Resposta.erro(`Produto ${ean} não encontrado!`));
        }

        const htmlImagem = sugestao.thumbnail;

        const urlImagem = htmlImagem.match(/src="([^"]*)"/g)[0].replace('src="', '').replace('\"', '');

        const dadosProdutoEAN = new DadosProdutoEAN();
        dadosProdutoEAN.nome = sugestao.value;
        dadosProdutoEAN.urlImagemExterna = urlImagem;

        resolve(Resposta.sucesso(dadosProdutoEAN));
      }).catch( (erro: Error) => {
        resolve(Resposta.erro(erro.message));
      });
    });
  }
}
