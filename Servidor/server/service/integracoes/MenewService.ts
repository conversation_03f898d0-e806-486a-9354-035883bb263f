import {IServiceIntegracaoExternaERP} from "../../domain/integracoes/IServiceIntegracaoExternaERP";
import {Pedido} from "../../domain/delivery/Pedido";
import axios from "axios";
import {DTOPedidoMenew} from "../../lib/integracao/menew/DTOPedidoMenew";
import {EnumTipoDeOrigem} from "../../lib/emun/EnumTipoDeOrigem";
import {Comanda} from "../../domain/comandas/Comanda";
import {MapeadorDePedido} from "../../mapeadores/MapeadorDePedido";
import * as moment from "moment";
import {MapStatusPedidoMenew} from "../EnumStatusPedidosIntegrados";
import {RequestParceiro} from "../../domain/integracoes/RequestParceiro";

declare const global: any;
export class MenewService implements IServiceIntegracaoExternaERP {
  private urlApi = 'https://public-api.prod.menew.cloud'
  private id = "3226798"; //id fixo meu cardapio ????
  constructor(public integracao: any, public estabelecimento: any) {
     if(global.desenvolvimento){
       this.estabelecimento = 2895 // estabelicimento teste 2895 e 3434
     }
  }

  static async  obtenhaAlteracoesPedidos(pedidos: any) {
    let listaPedidosAlterados: any = [];

    let promisses = [], ultimoPingMysql = new Date();

    for (let i = 0; i < pedidos.length; i++) {
      promisses.push( new Promise( async (resolve, reject) => {
        let pedido = pedidos[i];
        let empresa = pedido.empresa;
        empresa.setHorariosFuncionamento();

        if(empresa.estaAberta) {
          let serviceInstance: MenewService = empresa.integracaoDelivery.obtenhaService();

          let pedidoIntegrado: any = await serviceInstance.obtenhaPedido(pedido).catch((erroConsultar) => {
            console.log('#erro consulta pedido:' + pedido.id)
            console.log(erroConsultar)
          });

          if(moment().diff(ultimoPingMysql, 's') >= 30){
            console.log('#Fazendo ping no mysql para conexão nao morrer')
            await new MapeadorDePedido().facaPing();
            ultimoPingMysql = new Date();
          }

          if(pedidoIntegrado) {
            console.log(String(`Pedido ${pedido.id} esta no status ${pedidoIntegrado.status_entrega}`))
            let novoStatus = MapStatusPedidoMenew.get( pedidoIntegrado.status_entrega );
            if(novoStatus && novoStatus !== pedido.status){
              console.log('novo status veio do Menew: ' + novoStatus)
              listaPedidosAlterados[pedido.id] = novoStatus;
            } else {
              console.log('monitorar depois, não realizar baixa agora.....')
            }
          }
        } else {
          console.log('monitorar depois, loja fechada: ' + empresa.nome)
        }

        resolve('');
      }))
    }

    await Promise.all(promisses);

    return listaPedidosAlterados;
  }


  async veririqueBaixasNosPedidos(pedidos: any){
    let pedidosAtlerados = await MenewService.obtenhaAlteracoesPedidos(pedidos);


    return pedidosAtlerados;
  }

  listeProdutosConvertidos(ultimaSincronizacaoProdutos: any): Promise<Array<any>> {
       return Promise.resolve([])
  }


  executeRequest(nomeMetodo: string, params: any, tentarDeNovo = true, pedido: any = null){
    return new Promise(async (resolve, reject) => {
      let token = await this.integracao.obtenhaCredencial();

      let dados: any = {
        token: token,
        requests: {
          jsonrpc: "2.0",
          method: nomeMetodo,
          params: params,
          id: this.id
        }
      }
      console.log('executar request para o Menew: ' + nomeMetodo)
      console.log(JSON.stringify(dados));
      axios.post(this.urlApi, dados).then( async (response: any) => {
        if(pedido)
         await new RequestParceiro(pedido, 'menew', dados).saveRetornoHttp(response);
        // sessão expirada
        if(response.data.result && response.data.result.code === 401 && tentarDeNovo){
          console.log('Tentar executamente novamente a request com token renovado..')
          await this.integracao.renoveToken();
          let result = await this.executeRequest(nomeMetodo, params, false, pedido).catch((erro) => { reject(erro)});
          if(result) return resolve(result);
        } else {
          if(response.data.error) return reject(response.data.error)
          if(response.data.result.error) return reject(response.data.result.error)
          resolve(response.data.result)
        }
      }).catch( async (response: any) => {
        let msgErro: any = this.retornoErro(nomeMetodo, response);
        if(pedido)
          await new RequestParceiro(pedido, 'menew', dados).saveRetornoHttp(response, msgErro);

        reject(msgErro)
      })
    })
  }

  obtenhaToken(): Promise<string>{
   return new Promise(async (resolve, reject) => {
     let params: any = {
       token: null,
       requests:
         {
           "jsonrpc": "2.0",
           "method": "Usuario/login",
           "params": {"usuario": "meucardapio", "token": "5aMaahXyg*bsAj0%ec"},
           "id": this.id
         }
     }
     axios.post(this.urlApi, params).then(   (response: any) => {
       if(response.data.error) return reject(response.data.error)
       console.log(response.data.result)
       resolve(response.data.result)
     }).catch( (response: any) => {
       reject(this.retornoErro('obter token', response.message))
     })
   })
  }
  obtenhaDTOPedido(pedido: any, empresa: any){
    return new DTOPedidoMenew(pedido, empresa, this.estabelecimento);
  }

  adicionePedido(pedido: Pedido, empresa: any): Promise<string> {
    return new Promise(async (resolve, reject) => {
      try{
        if(!pedido.pagamentos ||  pedido.pagamentos.length === 0)
          if(!pedido.mesa)
            throw Error(String(`Nenhuma forma de pagamento  associado ao pedido`))

        await pedido.carregueDeParaTamanhoSabores();

        let dadosPedido: DTOPedidoMenew = this.obtenhaDTOPedido(pedido, empresa);

        console.log(dadosPedido)

        dadosPedido.itens.forEach((item: any) => {
          if(item.itens_montagem && item.itens_montagem.length ){
            item.itens_montagem.forEach((itemMontado: any) => {
              console.log(itemMontado)
            })
          }
        })

        let result: any = await this.executeRequest('Pedido/send', dadosPedido, true, pedido).catch( (erro) => {
          reject(this.retornoErro('enviar pedido', erro))
        } )

        if(result){
          console.log(result);
          resolve(result.id.toString())
        }


      } catch (execption){
        console.log('**erro adicionar pedido**')
        console.log(execption)
        reject(this.retornoErro('enviar pedido', execption.message))
      }
    })
  }

  obtenhaPedido(pedido: Pedido){
    return new Promise(async (resolve, reject) => {
      let params: any = { estabelecimento_id: this.estabelecimento, cliente_fone: pedido.contato.telefone, id: pedido.referenciaExterna}

      console.log(params)

      let result: any = await this.executeRequest('Pedido/find',    params).catch( (erro) => {
        reject(this.retornoErro('enviar pedido', erro))
      } )

      if(result)
        resolve(result.length ? result[0] : null)
    })
  }

  fecheComanda(comanda: Comanda, empresa: any){
    return Promise.resolve();
  }

  alterePedido(pedidoNovo: Pedido, pedidoAntigo: Pedido, empresa: any): Promise<string> {
    return Promise.resolve("");
  }

  cancelePedido(pedido: Pedido): Promise<any> {
    return Promise.resolve(undefined);
  }

  listeBandeiras(tipo: string): Promise<Array<any>> {
    return Promise.resolve(undefined);
  }

  listePrecosProdutos(ultimaSincronizacaoPrecos: any): Promise<Array<any>> {
    return Promise.resolve(undefined);
  }
  obtenhaProduto(codigo: string){
    return Promise.resolve(null)
  }

  obtenhaProdutoConvertido(){
    return Promise.resolve(null)
  }

  listeProdutos(): Promise<Array<any>> {
    return new Promise(  async (resolve, reject) => {
      let result: any = await this.executeRequest("Produto/find", { estabelecimento_id: this.estabelecimento} )
        .catch( (erro) => {
          reject(erro)
        } );

      if(result){
        console.log('total: ' + result.length)
      //  result = result.filter( (produto: any) => produto.nome && produto.preco < 10)
       // result = result.filter( (produto: any) => produto.nome && produto.nome.toUpperCase().indexOf('PIZZA') >= 0)
        resolve(result.splice(0, 250)); // retorna quase 2k de produtos, trava
      }
    });
  }

  listeCategorias(){
    return new Promise(  async (resolve, reject) => {
      let result: any = await this.executeRequest("Categoria/find", { estabelecimento_id: this.estabelecimento} )
        .catch( (erro) => {
          reject(this.retornoErro('buscar categorias', erro))
        } );

      if(result){
        console.log('total: ' + result.length)
        resolve(result);
      }

    });
  }

  listeProdutosIndisponiveis(): Promise<any>{
    return Promise.resolve([])
  }

  listeProdutosDisponiblidadeAtualizada(): Promise<Array<any>> {
    return Promise.resolve([])
  }

  valideToken(): Promise<any>{
       return this.listeCategorias();
  }

  veririqueUpdates(data: any): Promise<any> {
    return Promise.resolve(undefined);
  }

  private retornoErro(operacao: string, dados: any) {
    console.log(dados)
    let msgErro = String(`Falha ao ${operacao} (Menew):`);

    if(dados.code) msgErro = String(`${msgErro} ${dados.code} - `)

    if(dados.message){
      msgErro = String(`${msgErro} ${dados.message}`)
    } else if(dados.result && dados.result.error) {
      msgErro = String(`${msgErro} ${dados.result.error}`)
    }  else {
      msgErro = String(`${msgErro} ${dados}`)
    }
    console.log(msgErro)

    return msgErro;
  }

  obtenhaTipoDeOrigem(): EnumTipoDeOrigem {
    return EnumTipoDeOrigem.ImportadoMenew;
  }
}
