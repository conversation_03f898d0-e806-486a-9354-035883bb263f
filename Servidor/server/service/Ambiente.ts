import {IEnviadorDeMensagem} from "./IEnviadorDeMensagem";
import {EnviadorDeMensagemMock} from "./EnviadorDeMensagemMock";
import {Empresa} from "../domain/Empresa";
import {EnumMeioDeEnvio} from "../domain/EnumMeioDeEnvio";
import {EnviadorDeMensagemWhatsapp} from "./EnviadorDeMensagemWhatsapp";

import * as domain from 'domain';
import {EnviadorDeMensagemSMSMulti} from "./EnviadorDeMensagemSMSMulti";
import {EnviadorDeMensagemSMSTotalVoice} from "./EnviadorDeMensagemSMSTotalVoice";
import {EnviadorDeMensagemSMSTWW} from "./EnviadorDeMensagemSMSTWW";
import {EnviadorDeMensagemSMSDev} from "./EnviadorDeMensagemSMSDev";
import {Usuario} from "../domain/Usuario";
import {EnviadorDeMensagemCloudWhatsapp} from "./EnviadorDeMensagemCloudWhatsapp";
import {EnviadorDeMensagemMisto} from "./EnviadorDeMensagemMisto";

const config = require('../config.json');
const redisCache = require('rediscache');

export class Ambiente {
  static _instance: Ambiente;

  private configuracoes: any;
  private _ambiente: string;
  private _producao: boolean;

  private constructor(ambiente: string, configuracoes: any) {
    //console.log('** Carregando configurações do ambiente ' + ambiente  + ' **');
    this._ambiente = ambiente;
    this.configuracoes = configuracoes;
    //console.log(configuracoes);
    this._producao = (process.env.NODE_ENV === 'production');
    console.log('prod: ' + this._producao + " -> " + ambiente);
  }

  static inicialize(ambiente: string): Ambiente {
    switch (ambiente) {
      case 'homologacao':
        this._instance = new Ambiente(ambiente, config.ambiente.homologacao);
        break;
      case 'marcio':
        this._instance = new Ambiente(ambiente, config.ambiente.marcio);
        break;
      case 'novaversao':
        this._instance = new Ambiente(ambiente, config.ambiente.novaversao);
        break;
      case 'PROD':
        this._instance = new Ambiente(ambiente, config.ambiente.producao);
        break;
      case 'DEV':
      default:
        this._instance = new Ambiente('DEV', config.ambiente.desenvolvimento);
    }

    return this._instance;
  }

  public static get Instance() {
    return this._instance || this.inicialize('DEV');
  }

  public get producao(): boolean {
    return this._producao;
  }

  public get ambiente(): string {
    return this._ambiente;
  }

  public get config(): any {
    return this.configuracoes;
  }

  public contexto() {
    // @ts-ignore
    let dominio: any = domain['active'];

    return dominio.contexto;
  }

  public novoEnviadorDeMensagensSMS(meioDeEnvio: EnumMeioDeEnvio): IEnviadorDeMensagem {
    if( meioDeEnvio === EnumMeioDeEnvio.SMS ) {
      return new EnviadorDeMensagemSMSDev();
    } else if( meioDeEnvio === EnumMeioDeEnvio.SMS_TWW ) {
      return new EnviadorDeMensagemSMSTWW();
    } else if( meioDeEnvio === EnumMeioDeEnvio.SMS_TOTAL_VOICE ) {
      return new EnviadorDeMensagemSMSTotalVoice();
    }

    return new EnviadorDeMensagemSMSDev();
  }

  public novoEnviadorDeMensagens(empresa: Empresa, forcarEnvioSMS: boolean = false): IEnviadorDeMensagem {
    if ( empresa.meioDeEnvio === EnumMeioDeEnvio.SMS_TWW ) {
      return new EnviadorDeMensagemSMSTWW();
    }
    else if ( empresa.meioDeEnvio === EnumMeioDeEnvio.SMS_TOTAL_VOICE ) {
      return new EnviadorDeMensagemSMSTotalVoice();
    } else if ( empresa.meioDeEnvio === EnumMeioDeEnvio.SMS ) {
      return new  EnviadorDeMensagemSMSMulti(); // EnviadorDeMensagemSMSDev();
    } else if ( empresa.meioDeEnvio === EnumMeioDeEnvio.Mock ) {
      return new EnviadorDeMensagemMock();
    } else if( empresa.meioDeEnvio === EnumMeioDeEnvio.Whatsapp ) {
      if( forcarEnvioSMS ) {
        return new EnviadorDeMensagemSMSMulti();
      }
      return new EnviadorDeMensagemWhatsapp();
    } else if( empresa.meioDeEnvio === EnumMeioDeEnvio.CloudWhatsapp ) {
      return new EnviadorDeMensagemMisto();
    }
  }

  site(){
      return  { url: this.config.urlSite }
  }

  determineUsuarioLogado(usuario: any) {
    this.contexto().usuario = usuario
    this.contexto().ip = usuario ?  usuario.ip : null
  }

  usuarioLogado(): Usuario {
    return this.contexto().usuario
  }
  empresaContexto(): Empresa{
    return this.contexto().empresa
  }

  ip(): string {
    return this.contexto().ip
  }

  ipV4(): string {
    let ip: string =  this.ip();

    if(ip){
      const regex = /(?:[a-zA-Z]+)?(\d+\.\d+\.\d+\.\d+)/;

      const match = ip.match(regex);

      if (match) {
        const ipv4Address = match[1];
        return ipv4Address;
      } else {
        console.log("Endereço IPv4 não encontrado na string.");
        return ip;
      }
    }

    return ""
  }

}
