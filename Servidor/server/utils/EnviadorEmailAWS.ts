import {GerenciadorEmailTemplates} from "./GerenciadorEmailTemplates";
import {EnvioDeEmail} from "../domain/emails/EnvioDeEmail";
const AWS = require('aws-sdk');
// Set the region
AWS.config.update({region: 'sa-east-1'});
//https://docs.aws.amazon.com/ses/latest/DeveloperGuide/send-email.html
//https://sa-east-1.console.aws.amazon.com/ses/home?region=sa-east-1#

export class EnviadorEmailAWS {
  options = {
    httpOptions: {connectTimeout: 10000, timeout: 0.5 * 10000}, //5s
    apiVersion: '2019-09-27',
    accessKeyId: '********************',
    secretAccessKey: 'LKyivIo7k3F0A1P7HVMm9x2xAwQtVVSJkuHHckb0'
  }

  async envieV2(envioDeEmail: EnvioDeEmail): Promise<string> {
    return new Promise(async (resolve, reject) => {
      let sesv2 = new AWS.SESV2(this.options);
      let erro;
      let resposta = await new GerenciadorEmailTemplates().preview(envioDeEmail)
        .catch( erroRender => { console.error(erroRender); erro = erroRender});

      if(erro) return resolve(erro)
      if(!resposta) return resolve('Email não foi renderizado')

      let dadosEnvio: any = resposta.originalMessage;

      let params = {
        Content: { /* required */

          Simple: {
            Body: { /* required */
              Html: {
                Data: dadosEnvio.html,
                Charset: 'utf8'
              },
              Text: {
                Data: dadosEnvio.text, /* required */
                Charset: 'utf8'
              }
            },
            Subject: { /* required */
              Data: dadosEnvio.subject, /* required */
              Charset: 'utf8'
            }
          },
        },
        Destination: {
          ToAddresses: [
            dadosEnvio.to
          ]
        },
        FromEmailAddress: dadosEnvio.from
      };

      sesv2.sendEmail(params,  async (err: any, data: any) => {
        if (!err){
          await envioDeEmail.atualizeEnviado(data.MessageId)
          return resolve(err)
        }else {
          console.log(err, err.stack); // an error occurred
          await envioDeEmail.atualizeFalha(err.message)
          return resolve(err.message)
        }
      });
    })
  }

}
