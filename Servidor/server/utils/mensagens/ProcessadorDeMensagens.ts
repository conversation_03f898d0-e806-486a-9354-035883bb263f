import {Empresa} from "../../domain/Empresa";
import {Contato} from "../../domain/Contato";
import {RespostaEncurtarLinks} from "../RespostaEncurtarLinks";
import {MapeadorDeCupom} from "../../mapeadores/MapeadorDeCupom";
import {Cupom} from "../../domain/faturamento/Cupom";
import {MapeadorDeEmpresa} from "../../mapeadores/MapeadorDeEmpresa";
import {LinkEncurtado} from "../../domain/LinkEncurtado";
import {SessaoLinkSaudacao} from "../../domain/SessaoLinkSaudacao";
import {MapeadorDeSessaoLinkSaudacao} from "../../mapeadores/MapeadorDeSessaoLinkSaudacao";
import {VariaveisDeRequest} from "../../service/VariaveisDeRequest";
import {ValorDoCampo} from "./ValorDoCampo";
import {MapeadorDeTemplateDeMensagem} from "../../mapeadores/MapeadorDeTemplateDeMensagem";
import {TemplateDeMensagem} from "../../domain/mensagens/TemplateDeMensagem";


export class ProcessadorDeMensagens {
  constructor(private mensagem: string, private encurtarLinks: boolean) {
  }

  public static async crieLinkCardapio(contato: Contato, empresa: Empresa, contexto: any, modoTeste: boolean = false ): Promise<string> {
    if(!contexto.linkCardapio && empresa.temCardapio())
    {
      let sessaoLinkSaudacao

      if(!modoTeste) {
        sessaoLinkSaudacao = await SessaoLinkSaudacao.CrieSessao(contato, contato.telefone, contato.codigoPais);
        await new MapeadorDeSessaoLinkSaudacao().insiraGraph(sessaoLinkSaudacao);
      }

      const variaveisDeRequest = new VariaveisDeRequest();


      const link = variaveisDeRequest.obtenhaUrlCardapio(empresa)

      return link  + (modoTeste ? '' : '/link/' + contato.telefone + '/' + sessaoLinkSaudacao.hash);
    }

    return '';
  }

  public async processe(empresa: Empresa, contato: Contato, contexto: any = {}): Promise<RespostaEncurtarLinks> {
    let msgFinal = this.mensagem;

    contexto.contato = contato;
    contexto.empresa = empresa;

    const reg = /\[(.*?)\]/g;
    let result;
    while ((result = reg.exec(this.mensagem)) !== null) {
      //console.log(result[0]);
      let campo = result[0];

      if( campo.startsWith('[CUPOM_') ) {
        const codigoCupom = campo.replace('[CUPOM_', '').replace(']', '');
        const mapeadorCupom = new MapeadorDeCupom();

        const cupom: Cupom = await mapeadorCupom.selecioneSync({codigo: codigoCupom});

        console.log('processar cupom: ' + codigoCupom);
        if( cupom ) {
          contexto.linkCupom = cupom.obtenhaLinkCupom(empresa);
          if(cupom.restritoContatoPerdido && contexto.notificandoPerdidos){
            let ids: any = [contato.id]
            await mapeadorCupom.insiraNaListaContatos(cupom, ids);
          }

          msgFinal = msgFinal.replace(campo, '[LINK_CUPOM]');
          campo = '[LINK_CUPOM]';
        }
      } else if (campo.startsWith('[Link_Cardapio_')) {
        const dominioEmpresa = campo.replace('[Link_Cardapio_', '').replace(']', '');

        const empresaDoLink = await new MapeadorDeEmpresa().selecioneCachePorDominio(dominioEmpresa);
        const link = await ProcessadorDeMensagens.crieLinkCardapio(contato, empresaDoLink, {}, false);
        msgFinal = msgFinal.replace(campo, link);
      } else if(campo.startsWith('[Template_')) {
        let mapeadorDeTemplate = new MapeadorDeTemplateDeMensagem()

        let template: TemplateDeMensagem
        let nomeDoTemplate = campo.replace("[Template_", "")
          .replace("]", "")
        if(empresa.idRede)
          template = await mapeadorDeTemplate.selecioneDaEmpresaOuDaRede(empresa.id, empresa.idRede, nomeDoTemplate)
        else
          template = await mapeadorDeTemplate.selecioneSync({nome: nomeDoTemplate})

        if(template)
          msgFinal = await template.trate(msgFinal, empresa, contato, contexto)
      }
      /*
      else if( campo.startsWith('[Link_Grupo_Lojas]') ) {
        const empresaDoLink = await new MapeadorDeEmpresa().selecioneCachePorDominio(dominioEmpresa);
        const link = await this.crieLinkCardapio(contato, empresaDoLink, {}, false);
        msgFinal = msgFinal.replace(campo, link);
      }
      */

      let fnExpressao = ValorDoCampo.get(campo);

      let valorCampo = '';

      try {
        if (fnExpressao) {
          valorCampo = await fnExpressao(contexto)
        }
      } catch(erro) {
        //erro ao calcular a expressão
        console.error(erro)
        valorCampo = null;
      }

      //checa se valor do campo é null
      if( valorCampo === null ) {
        return null;
      }

      msgFinal = msgFinal.replace(campo, valorCampo);
    }

    if(contexto.msgConfirmacaoEntrega)
      msgFinal += contexto.msgConfirmacaoEntrega

    if(!this.encurtarLinks)
      return Promise.resolve(RespostaEncurtarLinks.naoEncurtar(msgFinal))

    return LinkEncurtado.encurteLinksDaMensagem(msgFinal);
  }
}

