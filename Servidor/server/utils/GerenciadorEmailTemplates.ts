import * as path from "path";
const Email = require('email-templates');
const ejs = require("ejs");
import {EnvioDeEmail} from "../domain/emails/EnvioDeEmail";

export class GerenciadorEmailTemplates {
  templatesDir: string;

  constructor() {
    this.templatesDir = path.join(__dirname, '../views/emails')
  }

  async renderize(envioDeEmail: EnvioDeEmail): Promise<any> {
    //linha deve vim primeiro
    let localsEmail: any = await envioDeEmail.getLocals();

    const email = new Email({
      message: {
        from: envioDeEmail.rementente
      },
      transport: {
        jsonTransport: true
      },
      juice: true,
      juiceResources: {
        preserveImportant: true,
        webResources: {
          relativeTo: this.templatesDir,
        }
      }
    });

    return email
      .renderAll(path.join(this.templatesDir, envioDeEmail.obtenhaNomeTemplate()), localsEmail)
  }

  async preview(envioDeEmail: EnvioDeEmail): Promise<any> {
    let localsEmail: any = await envioDeEmail.getLocals();

    const email = new Email({
      message: {
        from: envioDeEmail.rementente
      },
      transport: {
        jsonTransport: true
      },
      juice: true,
      juiceResources: {
        preserveImportant: true,
        webResources: {
          relativeTo: this.templatesDir,
        }
      }
    });

    let dadosEmail: any = {
      template: path.join(this.templatesDir, envioDeEmail.obtenhaNomeTemplate()),
      message: {
        to: envioDeEmail.destinatarioEmail
      },
      locals: localsEmail
    }

    //console.log(dadosEmail)
    return email.send(dadosEmail)
  }
}
