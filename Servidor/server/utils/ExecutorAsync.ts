// @ts-ignore
import * as mybatis from 'mybatisnodejs';


const contexto = mybatis.Contexto;

export class ExecutorAsync {
  static execute(fn: any, fnErro: any, t: any = 1000){
    setTimeout(function(){
      const reqDomain = require('domain').create();

      reqDomain.contexto = new contexto();

      reqDomain.on('error', function (er: Error) {
        console.log('==============Erro no execute async==================')
        console.log(er);
        try {
          if ( fnErro ) { fnErro(er); }

          if ( reqDomain.contexto ) {
            reqDomain.contexto.release();
          }
        } catch ( e ) {
          fnErro(er);
        }

        throw er;
      });

      reqDomain.run(function() {
        fn(function() {
          //console.log('liberando conexao executor async');
          reqDomain.contexto.release();
        });
      });

    }, (t));
  }
}
