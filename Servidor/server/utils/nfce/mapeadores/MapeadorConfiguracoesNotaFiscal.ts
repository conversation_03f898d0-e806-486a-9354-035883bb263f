import {MapeadorBasico} from "../../../mapeadores/MapeadorBasico";
import {Empresa} from "../../../domain/Empresa";
import {ConfiguracoesNotaFiscal} from "../../../domain/nfce/configuracoes/ConfiguracoesNotaFiscal";
import {MapeadorTributacaoNaturezaOperacao} from "./MapeadorTributacaoNaturezaOperacao";

export class MapeadorConfiguracoesNotaFiscal extends MapeadorBasico {
    constructor() {
        super('configuracoesNotaFiscal')
    }

     obtenhaPorEmpresa(empresa: Empresa): Promise<ConfiguracoesNotaFiscal> {
      return this.selecioneSync({idEmpresa: empresa.id})
    }

  async salveTribNatProprio(configuracoes: ConfiguracoesNotaFiscal, dadosTributacao: any) {
      configuracoes.tributacaoVendaProducaoPropria = dadosTributacao
    return this.salveTribNat(configuracoes, dadosTributacao, 'atualizeNatTribProprio')
  }

  salveTribNatTerceiros(configuracoes: any, dadosTributacao: any) {
      configuracoes.tributacaoVendaProdutosTerceiros = dadosTributacao
    return this.salveTribNat(configuracoes, dadosTributacao, 'atualizeNatTribTerceiros')
  }


  async salveTribNat( configuracoes: ConfiguracoesNotaFiscal, dadosTributacao: any, nomeMetodo: string) {
    let mapeadorTribNat = new MapeadorTributacaoNaturezaOperacao()

    if(dadosTributacao.id)
      await mapeadorTribNat.atualizeSync(dadosTributacao)
    else
      await mapeadorTribNat.insiraSync(dadosTributacao)

    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo(nomeMetodo),
      configuracoes);


  }

}
