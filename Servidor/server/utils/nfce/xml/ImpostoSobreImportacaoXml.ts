import {ClasseXml} from "./ClasseXml";
import {string} from "blockly/core/utils";
import {ImpostoSobreImportacao} from "../../../domain/nfce/ImpostoSobreImportacao";
import {XmlUtils} from "../comunicacao/XmlUtils";
import {FormatadorDeNumeros} from "../utils/FormatadorDeNumeros";

export class ImpostoSobreImportacaoXml extends ClasseXml {
  constructor(private impostoSobreImportacao: ImpostoSobreImportacao) {
    super();
  }

  obtenhaXml(): string {
    return `<II>
      ${XmlUtils.gereTag('vBC', FormatadorDeNumeros.formateDecimalNulo(this.impostoSobreImportacao.iiValbasecalc, 2))}
      ${XmlUtils.gereTag('vDespAdu', FormatadorDeNumeros.formateDecimalNulo(this.impostoSobreImportacao.iiValdespaduane, 2))}
      ${XmlUtils.gereTag('vII', FormatadorDeNumeros.formateDecimalNulo(this.impostoSobreImportacao.iiValor, 2))}
      ${XmlUtils.gereTag('vIOF', FormatadorDeNumeros.formateDecimalNulo(this.impostoSobreImportacao.iiValoriof, 2))}
  </II>`
  }

}
