import {ClasseXml} from "./ClasseXml";
import {string} from "blockly/core/utils";
import {XmlUtils} from "../comunicacao/XmlUtils";
import {FormatadorDeNumeros} from "../utils/FormatadorDeNumeros";
import {Cide} from "../../../domain/nfce/Cide";

export class CideXml extends ClasseXml {
  //gera o xml do cide baseado no objeto cide
  constructor(private cide: Cide) {
    super();
  }

  obtenhaXml(): string {
    return `
      <CIDE>
        ${XmlUtils.gereTag("qBCProd", this.cide.quantidadeBC)}
        ${XmlUtils.gereTag("vAliqProd", FormatadorDeNumeros.formateDecimalNulo(this.cide.aliquota, 4))}
        ${XmlUtils.gereTag("vCIDE", FormatadorDeNumeros.formateDecimalNulo(this.cide.valor, 2))}
      </CIDE>
    `;
  }

}
