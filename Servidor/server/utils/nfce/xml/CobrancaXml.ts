import {ClasseXml} from "./ClasseXml";
import {string} from "blockly/core/utils";
import {XmlUtils} from "../comunicacao/XmlUtils";
import {FormatadorDeNumeros} from "../utils/FormatadorDeNumeros";
import {Cobranca} from "../../../domain/nfce/Cobranca";

export class CobrancaXml extends ClasseXml {
  //gera o xml da cobrança com base nos dados do objeto cobranca
  constructor(private cobranca: Cobranca) {
    super();
  }
  obtenhaXml(): string {

    let faturasOrdenadas = this.cobranca.faturas.sort((a: any, b: any) => a.numeroFatura - b.numeroFatura);
    let xmlDuplicatas = faturasOrdenadas.map((fatura: any) => {
      return `<dup>
                ${XmlUtils.gereTag("nDup", fatura.numeroFatura)}
                ${XmlUtils.gereTag("dVenc", this.formatador.formate(fatura.dataVencimento))}
                ${XmlUtils.gereTag("vDup", FormatadorDeNumeros.formateDecimalNulo(fatura.valorFatura, 2))}
              </dup>`
    }).join('');

    let xmlFatura = !this.cobranca.numeroFatura ? '' :
      `<fat>
          ${XmlUtils.gereTag("nFat", this.cobranca.numeroFatura)}
          ${XmlUtils.gereTag("vOrig", FormatadorDeNumeros.formateDecimalNulo(this.cobranca.valorFatura, 2))}
          ${XmlUtils.gereTag("vDesc", FormatadorDeNumeros.formateDecimalNulo(this.cobranca.valorDesconto, 2))}
          ${XmlUtils.gereTag("vLiq", FormatadorDeNumeros.formateDecimalNulo(this.cobranca.valorLiquido, 2))}
      </fat>`;
    return `<cobr>
              ${xmlFatura}
              ${xmlDuplicatas}
            </cobr>`;



  }

}
