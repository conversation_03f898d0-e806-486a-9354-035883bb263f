import { XmlUtils } from "../comunicacao/XmlUtils";
import { ClasseXml } from "./ClasseXml";
import * as moment from "moment";
import {Cancelamento} from "../../../domain/nfce/Cancelamento";

export class CancelamentoXml extends ClasseXml {
  obtenhaXml(): string {
    return `<evento xmlns="http://www.portalfiscal.inf.br/nfe" versao="${this.obtenhaVersaoDados()}">
      <infEvento Id="${this.cancelamento .obtenhaIdentificador()}">
      <cOrgao>${this.obtenhaCodigoUf(this.cancelamento)}</cOrgao>
      <tpAmb>${this.obtenhaAmbienteDeEmissao()}</tpAmb>
      ${this.obtenhaTagDocumento(this.cancelamento)}
      <chNFe>${this.cancelamento.chaveDaNFe}</chNFe>
      <dhEvento>${this.obtenhaData(this.cancelamento)}</dhEvento>
      <tpEvento>${this.cancelamento.obtenhaTipoDeEvento()}</tpEvento>
      <nSeqEvento>${this.cancelamento.obtenhaNumeroDeSequencia()}</nSeqEvento>
      <verEvento>${this.obtenhaVersaoDados()}</verEvento>
      <detEvento versao='${this.obtenhaVersaoDados()}'>
        <descEvento>Cancelamento</descEvento>
        <nProt>${this.cancelamento.numeroProtocolo}</nProt>
        ${XmlUtils.gereTag('xJust', this.cancelamento.justificativa)}
      </detEvento>
      </infEvento>
    </evento>`;
  }

  constructor(private cancelamento: Cancelamento) {
    super();
  }

  obtenhaVersaoDados(): string {
    return "1.00";
  }

  obtenhaCodigoUf(cancelamento: Cancelamento): string {
    return this.cancelamento.chaveDaNFe.substring(0, 2);
  }

  obtenhaAmbienteDeEmissao(): number {
    return this.cancelamento.configuracoes.ambiente;
  }

  obtenhaTagDocumento(cancelamento: Cancelamento): string {
     return cancelamento.cnpjEmitente ? "<CNPJ>" + cancelamento.cnpjEmitente + "</CNPJ>" : "<CPF>" + cancelamento.cpfEmitente + "</CPF>"
  }

  obtenhaData(cancelamento: Cancelamento): string {
    const data = moment(cancelamento.dataEvento).format('YYYY-MM-DD[T]HH:mm:ss');
    const tdz = this.obtenhaTDZ(cancelamento.dataEvento);

    console.log(`Data para cc: ${data}${tdz}`);
    return `${data}${tdz}`;
  }

  obtenhaTDZ(data: Date): string {
    return moment(data).format('Z').replace(/(\d{2})(\d{2})/, '$1:$2');
  }

}
