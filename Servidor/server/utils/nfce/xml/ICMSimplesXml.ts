import {ClasseXml} from "./ClasseXml";
import {string} from "blockly/core/utils";
import {ICMSimples} from "../../../domain/nfce/ICMSimples";
import {XmlUtils} from "../comunicacao/XmlUtils";
import {FormatadorDeNumeros} from "../utils/FormatadorDeNumeros";

export class ICMSimplesXml extends ClasseXml {
  constructor(private icmsimples: ICMSimples) {
    super();
  }

  obtenhaXml(): string {
    let nomeTag = this.obtenhaNomeTag()
    return `
      <ICMS>
        <${nomeTag}>
            <orig>${this.icmsimples.origem}</orig>
            ${XmlUtils.gereTag('CSOSN', this.icmsimples.codigoSitOp)}
            ${XmlUtils.gereTag('modBC', this.icmsimples.modbasecalcIcms)}
            ${XmlUtils.gereTag('vBC', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.valorBaseDeCalculoICMS, 2))}
            ${XmlUtils.gereTag('pRedBC', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.percentualReducaoBaseDeCalculoICMS, 4))}
            ${XmlUtils.gereTag('pICMS', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.aliquotaICMS, 4))}
            ${XmlUtils.gereTag('vICMS', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.valorICMS, 2))}
            ${XmlUtils.gereTag('vBCSTRet', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.valorBCICMSRet, 2))}
            ${XmlUtils.gereTag('pST', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.percentualConsumidorST, 4))}
            ${XmlUtils.gereTag('vICMSSubstituto', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.valorICMSSubstituto, 2))}
            ${XmlUtils.gereTag('vICMSSTRet', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.valorICMSRet, 2))}
            ${XmlUtils.gereTag('modBCST', this.icmsimples.modBaseDeCalculoICMSSt)}
            ${XmlUtils.gereTag('pMVAST', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.percentualMargemValorICMSSt, 4))}
            ${XmlUtils.gereTag('pRedBCST', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.percentualReducaoBaseDeCalculoICMSSt, 4))}
            ${XmlUtils.gereTag('vBCST', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.valorBaseDeCalculoICMSSt, 2))}
            ${XmlUtils.gereTag('pICMSST', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.aliquotaICMSSt, 4))}
            ${XmlUtils.gereTag('vICMSST', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.valorICMSSt, 2))}
            ${XmlUtils.gereTag('vBCFCPST', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.valorBaseDeCalculoFCPST, 2))}
            ${XmlUtils.gereTag('pFCPST', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.percentualFCPST, 4))}
            ${XmlUtils.gereTag('vFCPST', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.valorFCPST, 2))}
            ${XmlUtils.gereTag('vBCFCPSTRet', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.valorBCFCPSTRet, 2))}
            ${XmlUtils.gereTag('pFCPSTRet   ', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.percentualFCPSTRet, 4))}
            ${XmlUtils.gereTag('vFCPSTRet', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.valorFCPSTRet, 2))}
            ${XmlUtils.gereTag('pRedBCEfet', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.percentualReducaoBCEfetiva, 4))}
            ${XmlUtils.gereTag('vBCEfet', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.valorBCEfetiva, 2))}
            ${XmlUtils.gereTag('pICMSEfet', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.percentualICMSEfetivo, 4))}
            ${XmlUtils.gereTag('vICMSEfet', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.valorICMSEfetivo, 2))}
            ${XmlUtils.gereTag('pCredSN', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.aliqutoAplicavelCalcCred, 4))}
            ${XmlUtils.gereTag('vCredICMSSN', FormatadorDeNumeros.formateDecimalNulo(this.icmsimples.valorCredito, 2))}
        </${nomeTag}>
      </ICMS>
    `;
  }

  private obtenhaNomeTag() {
    return "ICMS" + this.icmsimples.obtenhaTipoICMS()
  }
}
