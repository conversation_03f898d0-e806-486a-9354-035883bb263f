import {TratadorDeRespostaConsultaReciboRejeicao} from "../tratadores/TratadorDeRespostaConsultaReciboRejeicao";
import {TratadorEnvioNFeSincrono} from "../tratadores/TratadorEnvioNFeSincrono";
import {TratadorDeResposta} from "../tratadores/TratadorDeResposta";
import {DTONotaFiscalEletronica} from "../dto/DTONotaFiscalEletronica";
import {OperacaoWebService} from "./OperacaoWebService";
import {TratadorDeRespostaEnvioLoteSincrono} from "../tratadores/TratadorDeRespostaEnvioLoteSincrono";
import {TratadorDeRespostaEnvioLoteSincronoRejeicao} from "../tratadores/TratadorDeRespostaEnvioLoteSincronoRejeicao";
import {XMLParser} from 'fast-xml-parser';
import {XmlUtils} from "./XmlUtils";
import {TratadorResposta206Consulta} from "../tratadores/TratadorResposta206Consulta";
import {TratadorResposta104} from "../tratadores/TratadorResposta104";
import {TratadorDeRespostaNFeRejeicao} from "../tratadores/TratadorDeRespostaNFeRejeicao";
import {NotaFiscalEletronica} from "../../../domain/nfce/NotaFiscalEletronica";
import {TratadorResposta100} from "../tratadores/TratadorResposta100";
import {TratadorRespostaEnvioEvento} from "../tratadores/TratadorRespostaEnvioEvento";
import {TratadorRespostaEnvioLoteEvento} from "../tratadores/TratadorRespostaEnvioLoteEvento";
const classesTratadoras = {
  TratadorDeRespostaConsultaReciboRejeicao,
  TratadorDeRespostaEnvioLoteSincronoRejeicao,
  TratadorDeRespostaNFeRejeicao,
  TratadorResposta100,
  TratadorResposta104,
  TratadorRespostaEnvioLoteEvento
}



export class FabricaDeTratadorDeResposta {
  private static instance: FabricaDeTratadorDeResposta;
  private tratadores: { [index: string]: typeof TratadorDeResposta };
  private tratadoresRejeicao:  { [index: string]: typeof TratadorDeResposta };


  static obtenhaInstancia(): FabricaDeTratadorDeResposta {
    return this.getInstance();
  }


  public static getInstance(): FabricaDeTratadorDeResposta {
    if (!FabricaDeTratadorDeResposta.instance) {
      FabricaDeTratadorDeResposta.instance = new FabricaDeTratadorDeResposta();
    }
    return FabricaDeTratadorDeResposta.instance;
  }


  private constructor() {
    this.crieTratadores()
    this.crieTratadoresRejeicao()
  }

  crieTratadores() {
    this.tratadores = {}
    this.tratadores['TratadorResposta104'] = classesTratadoras['TratadorResposta104']
    this.tratadores['TratadorResposta100'] = classesTratadoras['TratadorResposta100']
    this.tratadores['TratadorRespostaEnvioLoteEvento'] = classesTratadoras['TratadorRespostaEnvioLoteEvento']
  }

  crieTratadoresRejeicao() {
    this.tratadoresRejeicao = {}
    this.tratadoresRejeicao[OperacaoWebService.CONSULTAR_LOTE] = classesTratadoras['TratadorDeRespostaConsultaReciboRejeicao']
    this.tratadoresRejeicao[OperacaoWebService.ENVIAR_LOTE_NFE_SINCRONO] = classesTratadoras['TratadorDeRespostaEnvioLoteSincronoRejeicao']
    this.tratadoresRejeicao[OperacaoWebService.CONSULTAR_NFE] = classesTratadoras['TratadorDeRespostaNFeRejeicao']
  }

  ObtenhaTratadorEnvioEvento(resposta: string): TratadorDeResposta {
    let xml = this.obtenhaXml(resposta)

    let tratadorCriado = this.crieTratador(resposta, xml, TratadorRespostaEnvioEvento)
    //tratadorCriado.nfe = nfe

    return tratadorCriado;
  }


  ObtenhaTratadorEnvioNFeSincrono(resposta: string, nfe: NotaFiscalEletronica): TratadorDeResposta {
    let xml = this.obtenhaXml(resposta)
    let tratador: typeof TratadorDeResposta = this.ObtenhaTratador(xml, OperacaoWebService.ENVIAR_LOTE_NFE_SINCRONO);

    let tratadorCriado = this.crieTratador(resposta, xml, tratador)
    tratadorCriado.nfe = nfe

    return tratadorCriado;
  }

  ObtenhaTratadorEnvioLoteEvento(resposta: string, nfe: NotaFiscalEletronica): TratadorDeResposta {
    let xml = this.obtenhaXml(resposta)
    let tratador: typeof TratadorDeResposta = this.ObtenhaTratador(xml, OperacaoWebService.ENVIAR_LOTE_EVENTO);

    let tratadorCriado = this.crieTratador(resposta, xml, tratador)
    tratadorCriado.nfe = nfe

    return tratadorCriado;
  }

  ObtenhaTratador(xml: any, operacaoWebService: string): typeof TratadorDeResposta {

    let codigo = this.obtenhaCodigo(xml, operacaoWebService)

    let tratador = this.tratadores['TratadorResposta' + codigo]

    if(!tratador) //assume que houve rejeição
      return this.tratadoresRejeicao[operacaoWebService]


    return tratador;
  }

  obtenhaCodigo(xml: any, operacaoWebservice: string): string {

    let codigo

    if(operacaoWebservice === OperacaoWebService.ENVIAR_LOTE_NFE_SINCRONO) {
      codigo = xml['retEnviNFe']['cStat']
   }

    if(operacaoWebservice === OperacaoWebService.ENVIAR_LOTE_EVENTO) {
      codigo = "EnvioLoteEvento"
    }


    if(operacaoWebservice === OperacaoWebService.RETORNO_ENVIO_EVENTO) {
      codigo = "EnvioEvento"
    }


    if(operacaoWebservice === OperacaoWebService.CONSULTAR_NFE) {
      codigo = xml['infProt']['cStat']
    }

    return codigo
  }


  ObtenhaTratadorConsultaNFe(resposta: string): TratadorDeResposta {
    const xml = this.obtenhaXml(resposta)

    let tratador: typeof TratadorDeResposta = this.ObtenhaTratador(xml, OperacaoWebService.CONSULTAR_NFE);

    if(!tratador) {
      let codigoResposta = this.obtenhaCodigo(resposta, OperacaoWebService.CONSULTAR_NFE)

      if(codigoResposta === "206") {
        tratador = TratadorResposta206Consulta;
      } else {
        tratador = this.tratadoresRejeicao[OperacaoWebService.CONSULTAR_NFE]
      }
    }
  /*
    if (!(tratador instanceof TratadorDeRespostaNFe)) {
      let codigoResposta: string = this.obtenhaCodigoResposta(resposta, OperacaoWebService.CONSULTAR_NFE);
      if(codigoResposta === "206"){
        tratador = this.crieTratador(resposta, TratadorResposta206Consulta);
      }
      else{
        tratador = this.crieTratador(resposta, this.tratadoresRejeicao[OperacaoWebService.CONSULTAR_NFE]);
      }
    }
*/
    return this.crieTratador(resposta, xml, tratador);
  }


  crieTratador(resposta: string, xml: any, tratador:  typeof  TratadorDeResposta): TratadorDeResposta {
    let objetoTratador = new tratador
    objetoTratador.resposta = resposta;
    objetoTratador.xmlResposta = xml;
    return objetoTratador;
  }

  private obtenhaXml(resposta: string): any {
    const parser = new XMLParser(XmlUtils.parserOptions);
    return parser.parse(resposta);
  }
}
