import {WebserviceSefaz} from "./WebserviceSefaz";
import {InvocadorSoapNfce} from "./InvocadorSoapNfce";
import {Operacao} from "./Operacao";
import {ChamadorDeServicoNfe4} from "./ChamadorDeServicoNfe4";
import {Certificado} from "../../../domain/nfce/Certificado";

export class ServicoNFeGenerico {
  chamador: ChamadorDeServicoNfe4
  constructor(private webService: WebserviceSefaz, private certificado: Certificado) {
    this.chamador = new ChamadorDeServicoNfe4()
    this.chamador.determineCertificado(certificado)
  }

  async realizeEnvioNFCe(mensagem: string, ambiente: number, versao: string): Promise<string> {
    const servicoAutorizacao = this.webService.obtenhaServicosNFCe(ambiente, versao).servicoAutorizacao;

    return this.chamador.realizeChamada(mensagem, servicoAutorizacao.url, servicoAutorizacao.nome, servicoAutorizacao.acao);
  }

  async realizeEnvioEventoNFCe(mensagem: string, ambiente: number, versao: string): Promise<string> {
    const servicoRecepcaoEvento = this.webService.obtenhaServicosNFCe(ambiente, versao).servicoRecepcaoEvento;

    return this.chamador.realizeChamada(mensagem, servicoRecepcaoEvento.url, servicoRecepcaoEvento.nome, servicoRecepcaoEvento.acao);
  }
}
