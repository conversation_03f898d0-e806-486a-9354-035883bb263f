import { WebserviceSefaz } from './WebserviceSefaz';
import { ColecaoDeServicos } from './ColecaoDeServicos';
import { ServicoWeb } from './ServicoWeb';

export class CarregadorDeWebserviceSefaz {

  static carregueWebservices() {
    // Adicionando URLs de homologação para NFCe
WebserviceSefaz.servicosNFCeHomologacao.set('AC', this.criarColecaoDeServicos({
  servicoAutorizacao: this.criarServicoWeb('https://homologacao.sefaz.ac.gov.br/nfce/services/NfeAutorizacao', 'NfeAutorizacao'),
  servicoRetornoAutorizacao: this.criarServicoWeb('https://homologacao.sefaz.ac.gov.br/nfce/services/NfeRetAutorizacao', 'NfeRetAutorizacao'),
  servicoConsulta: this.criarServicoWeb('https://homologacao.sefaz.ac.gov.br/nfce/services/NfeConsulta2', 'NfeConsulta2'),
  servicoStatus: this.criarServicoWeb('https://homologacao.sefaz.ac.gov.br/nfce/services/NfeStatusServico2', 'NfeStatusServico2'),
  servicoRecepcaoEvento: this.criarServicoWeb('https://homologacao.sefaz.ac.gov.br/nfce/services/RecepcaoEvento', 'RecepcaoEvento'),
  servicoQrCode2: this.criarServicoWeb('https://homologacao.sefaz.ac.gov.br/nfce/qrcode2', 'QrCode2'),
  urlConsulta2: this.criarServicoWeb('https://homologacao.sefaz.ac.gov.br/nfce/consulta2', 'Consulta2')
}));

WebserviceSefaz.servicosNFCeHomologacao.set('AL', this.criarColecaoDeServicos({
  servicoAutorizacao: this.criarServicoWeb('https://nfce.sefaz.al.gov.br/nfce/services/NfeAutorizacao', 'NfeAutorizacao'),
  servicoRetornoAutorizacao: this.criarServicoWeb('https://nfce.sefaz.al.gov.br/nfce/services/NfeRetAutorizacao', 'NfeRetAutorizacao'),
  servicoConsulta: this.criarServicoWeb('https://nfce.sefaz.al.gov.br/nfce/services/NfeConsulta2', 'NfeConsulta2'),
  servicoStatus: this.criarServicoWeb('https://nfce.sefaz.al.gov.br/nfce/services/NfeStatusServico2', 'NfeStatusServico2'),
  servicoRecepcaoEvento: this.criarServicoWeb('https://nfce.sefaz.al.gov.br/nfce/services/RecepcaoEvento', 'RecepcaoEvento'),
  servicoQrCode2: this.criarServicoWeb('https://nfce.sefaz.al.gov.br/nfce/qrcode2', 'QrCode2'),
  urlConsulta2: this.criarServicoWeb('https://nfce.sefaz.al.gov.br/nfce/consulta2', 'Consulta2')
}));

WebserviceSefaz.servicosNFCeHomologacao.set('AM', this.criarColecaoDeServicos({
  servicoAutorizacao: this.criarServicoWeb('https://homnfce.sefaz.am.gov.br/nfce-services/services/NfeAutorizacao', 'NfeAutorizacao'),
  servicoRetornoAutorizacao: this.criarServicoWeb('https://homnfce.sefaz.am.gov.br/nfce-services/services/NfeRetAutorizacao', 'NfeRetAutorizacao'),
  servicoConsulta: this.criarServicoWeb('https://homnfce.sefaz.am.gov.br/nfce-services/services/NfeConsulta2', 'NfeConsulta2'),
  servicoStatus: this.criarServicoWeb('https://homnfce.sefaz.am.gov.br/nfce-services/services/NfeStatusServico2', 'NfeStatusServico2'),
  servicoRecepcaoEvento: this.criarServicoWeb('https://homnfce.sefaz.am.gov.br/nfce-services/services/RecepcaoEvento', 'RecepcaoEvento'),
  servicoQrCode2: this.criarServicoWeb('https://homnfce.sefaz.am.gov.br/nfce/qrcode2', 'QrCode2'),
  urlConsulta2: this.criarServicoWeb('https://homnfce.sefaz.am.gov.br/nfce/consulta2', 'Consulta2')
}));

WebserviceSefaz.servicosNFCeHomologacao.set('AP', this.criarColecaoDeServicos({
  servicoAutorizacao: this.criarServicoWeb('https://homologacao.sefaz.ap.gov.br/nfce/services/NfeAutorizacao', 'NfeAutorizacao'),
  servicoRetornoAutorizacao: this.criarServicoWeb('https://homologacao.sefaz.ap.gov.br/nfce/services/NfeRetAutorizacao', 'NfeRetAutorizacao'),
  servicoConsulta: this.criarServicoWeb('https://homologacao.sefaz.ap.gov.br/nfce/services/NfeConsulta2', 'NfeConsulta2'),
  servicoStatus: this.criarServicoWeb('https://homologacao.sefaz.ap.gov.br/nfce/services/NfeStatusServico2', 'NfeStatusServico2'),
  servicoRecepcaoEvento: this.criarServicoWeb('https://homologacao.sefaz.ap.gov.br/nfce/services/RecepcaoEvento', 'RecepcaoEvento'),
  servicoQrCode2: this.criarServicoWeb('https://homologacao.sefaz.ap.gov.br/nfce/qrcode2', 'QrCode2'),
  urlConsulta2: this.criarServicoWeb('https://homologacao.sefaz.ap.gov.br/nfce/consulta2', 'Consulta2')
}));

    WebserviceSefaz.servicosNFCeHomologacao.set('SVRS', this.criarColecaoDeServicos({
      servicoAutorizacao: this.criarServicoWeb('https://nfce-homologacao.svrs.rs.gov.br/ws/NfeAutorizacao/NFeAutorizacao4.asmx', 'NFeAutorizacao4', 'nfeAutorizacaoLote'),
      servicoRetornoAutorizacao: this.criarServicoWeb('https://nfce-homologacao.svrs.rs.gov.br/ws/NfeRetAutorizacao/NFeRetAutorizacao4.asmx', 'NFeRetAutorizacao4', 'nfeRetAutorizacaoLote' ),
      servicoInutilizacao: this.criarServicoWeb('https://nfce-homologacao.svrs.rs.gov.br/ws/nfeinutilizacao/nfeinutilizacao4.asmx', 'NFeInutilizacao4', 'nfeInutilizacaoNF'),
      servicoConsulta: this.criarServicoWeb('https://nfce-homologacao.svrs.rs.gov.br/ws/NfeConsulta/NfeConsulta4.asmx', 'NFeConsultaProtocolo4', 'nfeConsultaNF' ),
      servicoStatus: this.criarServicoWeb('https://nfce-homologacao.svrs.rs.gov.br/ws/NfeStatusServico/NfeStatusServico4.asmx', 'NfeStatusServico4', 'nfeStatusServicoNF'),
      servicoRecepcaoEvento: this.criarServicoWeb('https://nfce-homologacao.svrs.rs.gov.br/ws/recepcaoevento/recepcaoevento4.asmx', 'NFeRecepcaoEvento4', 'NFeRecepcaoEvento4'),
      servicoQrCode2: this.criarServicoWeb('https://homologacao.sefaz.ap.gov.br/nfce/qrcode2', 'QrCode2'),
      urlConsulta2: this.criarServicoWeb('https://homologacao.sefaz.ap.gov.br/nfce/consulta2', 'Consulta2')
    }));

    //o da SEFAZ PA copia o da SVRS e altera apenas srevicoQrCode2 e urlConsulta2
    WebserviceSefaz.servicosNFCeHomologacao.set('PA', this.criarColecaoDeServicos(WebserviceSefaz.servicosNFCeHomologacao.get('SVRS')))

    let servicosPA = WebserviceSefaz.servicosNFCeHomologacao.get('PA')

    servicosPA.servicoQrCode2 = this.criarServicoWeb('https://appnfc.sefa.pa.gov.br/portal-homologacao/view/consultas/nfce/nfceForm.seam', 'QrCode2')
    servicosPA.urlConsulta2 = this.criarServicoWeb('http://www.sefa.pa.gov.br/nfce/consulta', 'Consulta2')


WebserviceSefaz.servicosNFCeHomologacao.set('BA', this.criarColecaoDeServicos({
  servicoAutorizacao: this.criarServicoWeb('https://hnfe.sefaz.ba.gov.br/webservices/NFeAutorizacao4/NFeAutorizacao4.asmx', 'NFeAutorizacao4'),
  servicoRetornoAutorizacao: this.criarServicoWeb('https://hnfe.sefaz.ba.gov.br/webservices/NFeRetAutorizacao4/NFeRetAutorizacao4.asmx', 'NFeRetAutorizacao4'),
  servicoConsulta: this.criarServicoWeb('https://hnfe.sefaz.ba.gov.br/webservices/NFeConsultaProtocolo4/NFeConsultaProtocolo4.asmx', 'NfeConsulta2'),
  servicoStatus: this.criarServicoWeb('https://hnfe.sefaz.ba.gov.br/webservices/NFeStatusServico4/NFeStatusServico4.asmx', 'NfeStatusServico2'),
  servicoRecepcaoEvento: this.criarServicoWeb('https://hnfe.sefaz.ba.gov.br/webservices/NFeRecepcaoEvento4/NFeRecepcaoEvento4.asmx', 'RecepcaoEvento'),
  servicoQrCode2: this.criarServicoWeb('https://hnfe.sefaz.ba.gov.br/nfce/qrcode2', 'QrCode2'),
  urlConsulta2: this.criarServicoWeb('https://hnfe.sefaz.ba.gov.br/nfce/consulta2', 'Consulta2')
}));

WebserviceSefaz.servicosNFCeHomologacao.set('CE', this.criarColecaoDeServicos({
  servicoAutorizacao: this.criarServicoWeb('https://nfceh.sefaz.ce.gov.br/nfe4/services/NFeAutorizacao4', 'NfeAutorizacao'),
  servicoRetornoAutorizacao: this.criarServicoWeb('https://nfceh.sefaz.ce.gov.br/nfe4/services/NFeRetAutorizacao4', 'NfeRetAutorizacao'),
  servicoConsulta: this.criarServicoWeb('https://nfceh.sefaz.ce.gov.br/nfe4/services/NFeConsultaProtocolo4', 'NfeConsulta2'),
  servicoStatus: this.criarServicoWeb('https://nfceh.sefaz.ce.gov.br/nfe4/services/NFeStatusServico4', 'NfeStatusServico2'),
  servicoRecepcaoEvento: this.criarServicoWeb('https://nfceh.sefaz.ce.gov.br/nfe4/services/NFeRecepcaoEvento4', 'RecepcaoEvento'),
  servicoQrCode2: this.criarServicoWeb('https://nfceh.sefaz.ce.gov.br/nfce/qrcode2', 'QrCode2'),
  urlConsulta2: this.criarServicoWeb('https://nfceh.sefaz.ce.gov.br/nfce/consulta2', 'Consulta2')
}));

WebserviceSefaz.servicosNFCeHomologacao.set('DF', this.criarColecaoDeServicos({
  servicoAutorizacao: this.criarServicoWeb('https://hom.nfce.fazenda.df.gov.br/NFeAutorizacao4.asmx', 'NfeAutorizacao'),
  servicoRetornoAutorizacao: this.criarServicoWeb('https://hom.nfce.fazenda.df.gov.br/NFeRetAutorizacao4.asmx', 'NfeRetAutorizacao'),
  servicoConsulta: this.criarServicoWeb('https://hom.nfce.fazenda.df.gov.br/NFeConsultaProtocolo4.asmx', 'NfeConsulta2'),
  servicoStatus: this.criarServicoWeb('https://hom.nfce.fazenda.df.gov.br/NFeStatusServico4.asmx', 'NfeStatusServico2'),
  servicoRecepcaoEvento: this.criarServicoWeb('https://hom.nfce.fazenda.df.gov.br/NFeRecepcaoEvento4.asmx', 'RecepcaoEvento'),
  servicoQrCode2: this.criarServicoWeb('https://hom.nfce.fazenda.df.gov.br/nfce/qrcode2', 'QrCode2'),
  urlConsulta2: this.criarServicoWeb('https://hom.nfce.fazenda.df.gov.br/nfce/consulta2', 'Consulta2')
}));

WebserviceSefaz.servicosNFCeHomologacao.set('ES', this.criarColecaoDeServicos({
  servicoAutorizacao: this.criarServicoWeb('https://homologacao.sefaz.es.gov.br/nfce/services/NfeAutorizacao', 'NfeAutorizacao'),
  servicoRetornoAutorizacao: this.criarServicoWeb('https://homologacao.sefaz.es.gov.br/nfce/services/NfeRetAutorizacao', 'NfeRetAutorizacao'),
  servicoConsulta: this.criarServicoWeb('https://homologacao.sefaz.es.gov.br/nfce/services/NfeConsulta2', 'NfeConsulta2'),
  servicoStatus: this.criarServicoWeb('https://homologacao.sefaz.es.gov.br/nfce/services/NfeStatusServico2', 'NfeStatusServico2'),
  servicoRecepcaoEvento: this.criarServicoWeb('https://homologacao.sefaz.es.gov.br/nfce/services/RecepcaoEvento', 'RecepcaoEvento'),
  servicoQrCode2: this.criarServicoWeb('https://homologacao.sefaz.es.gov.br/nfce/qrcode2', 'QrCode2'),
  urlConsulta2: this.criarServicoWeb('https://homologacao.sefaz.es.gov.br/nfce/consulta2', 'Consulta2')
}));

WebserviceSefaz.servicosNFCeHomologacao.set('GO', this.criarColecaoDeServicos({

  servicoAutorizacao: this.criarServicoWeb('https://homolog.sefaz.go.gov.br/nfe/services/NFeAutorizacao4', 'NfeAutorizacao'),
  servicoRetornoAutorizacao: this.criarServicoWeb('https://homolog.sefaz.go.gov.br/nfe/services/NFeRetAutorizacao4', 'NfeRetAutorizacao'),
  servicoConsulta: this.criarServicoWeb('https://homolog.sefaz.go.gov.br/nfe/services/NFeConsultaProtocolo4', 'NfeConsulta2'),
  servicoStatus: this.criarServicoWeb('https://homolog.sefaz.go.gov.br/nfe/services/NFeStatusServico4', 'NfeStatusServico2'),
  servicoRecepcaoEvento: this.criarServicoWeb('https://homolog.sefaz.go.gov.br/nfe/services/NFeRecepcaoEvento4', 'RecepcaoEvento'),
  servicoQrCode2: this.criarServicoWeb('http://homolog.sefaz.go.gov.br/nfeweb/sites/nfce/danfeNFCe', 'QrCode2'),
  urlConsulta2: this.criarServicoWeb('www.nfce.go.gov.br/post/ver/214413/consulta-nfc-e-homologacao', 'Consulta2')
}));

WebserviceSefaz.servicosNFCeHomologacao.set('MA', this.criarColecaoDeServicos({
  servicoAutorizacao: this.criarServicoWeb('https://homologacao.sefaz.ma.gov.br/nfce/services/NfeAutorizacao', 'NfeAutorizacao'),
  servicoRetornoAutorizacao: this.criarServicoWeb('https://homologacao.sefaz.ma.gov.br/nfce/services/NfeRetAutorizacao', 'NfeRetAutorizacao'),
  servicoConsulta: this.criarServicoWeb('https://homologacao.sefaz.ma.gov.br/nfce/services/NfeConsulta2', 'NfeConsulta2'),
  servicoStatus: this.criarServicoWeb('https://homologacao.sefaz.ma.gov.br/nfce/services/NfeStatusServico2', 'NfeStatusServico2'),
  servicoRecepcaoEvento: this.criarServicoWeb('https://homologacao.sefaz.ma.gov.br/nfce/services/RecepcaoEvento', 'RecepcaoEvento'),
  servicoQrCode2: this.criarServicoWeb('https://homologacao.sefaz.ma.gov.br/nfce/qrcode2', 'QrCode2'),
  urlConsulta2: this.criarServicoWeb('https://homologacao.sefaz.ma.gov.br/nfce/consulta2', 'Consulta2')
}));

WebserviceSefaz.servicosNFCeProducao.set('AM', this.criarColecaoDeServicos({
  servicoAutorizacao: this.criarServicoWeb('https://nfce.sefaz.am.gov.br/nfce-services/services/NfeAutorizacao', 'NfeAutorizacao'),
  servicoRetornoAutorizacao: this.criarServicoWeb('https://nfce.sefaz.am.gov.br/nfce-services/services/NfeRetAutorizacao', 'NfeRetAutorizacao'),
  servicoConsulta: this.criarServicoWeb('https://nfce.sefaz.am.gov.br/nfce-services/services/NfeConsulta2', 'NfeConsulta2'),
  servicoStatus: this.criarServicoWeb('https://nfce.sefaz.am.gov.br/nfce-services/services/NfeStatusServico2', 'NfeStatusServico2'),
  servicoRecepcaoEvento: this.criarServicoWeb('https://nfce.sefaz.am.gov.br/nfce-services/services/RecepcaoEvento', 'RecepcaoEvento'),
  servicoQrCode2: this.criarServicoWeb('https://nfce.sefaz.am.gov.br/nfce/qrcode2', 'QrCode2'),
  urlConsulta2: this.criarServicoWeb('https://nfce.sefaz.am.gov.br/nfce/consulta2', 'Consulta2')
}));

WebserviceSefaz.servicosNFCeProducao.set('CE', this.criarColecaoDeServicos({
  servicoAutorizacao: this.criarServicoWeb('https://nfce.sefaz.ce.gov.br/nfe4/services/NFeAutorizacao4', 'NfeAutorizacao'),
  servicoRetornoAutorizacao: this.criarServicoWeb('https://nfce.sefaz.ce.gov.br/nfe4/services/NFeRetAutorizacao4', 'NfeRetAutorizacao'),
  servicoConsulta: this.criarServicoWeb('https://nfce.sefaz.ce.gov.br/nfe4/services/NFeConsultaProtocolo4', 'NfeConsulta2'),
  servicoStatus: this.criarServicoWeb('https://nfce.sefaz.ce.gov.br/nfe4/services/NFeStatusServico4', 'NfeStatusServico2'),
  servicoRecepcaoEvento: this.criarServicoWeb('https://nfce.sefaz.ce.gov.br/nfe4/services/NFeRecepcaoEvento4', 'RecepcaoEvento'),
  servicoQrCode2: this.criarServicoWeb('https://nfce.sefaz.ce.gov.br/nfce/qrcode2', 'QrCode2'),
  urlConsulta2: this.criarServicoWeb('https://nfce.sefaz.ce.gov.br/nfce/consulta2', 'Consulta2')
}));

WebserviceSefaz.servicosNFCeProducao.set('GO', this.criarColecaoDeServicos({
  servicoAutorizacao: this.criarServicoWeb('https://nfce.sefaz.go.gov.br/nfe/services/v2/NFeAutorizacao4', 'NfeAutorizacao'),
  servicoRetornoAutorizacao: this.criarServicoWeb('https://nfce.sefaz.go.gov.br/nfe/services/v2/NFeRetAutorizacao4', 'NfeRetAutorizacao'),
  servicoConsulta: this.criarServicoWeb('https://nfce.sefaz.go.gov.br/nfe/services/v2/NFeConsultaProtocolo4', 'NfeConsulta2'),
  servicoStatus: this.criarServicoWeb('https://nfce.sefaz.go.gov.br/nfe/services/v2/NFeStatusServico4', 'NfeStatusServico2'),
  servicoRecepcaoEvento: this.criarServicoWeb('https://nfce.sefaz.go.gov.br/nfe/services/v2/NFeRecepcaoEvento4', 'RecepcaoEvento'),
  servicoQrCode2: this.criarServicoWeb('https://nfce.sefaz.go.gov.br/nfce/qrcode2', 'QrCode2'),
  urlConsulta2: this.criarServicoWeb('https://nfce.sefaz.go.gov.br/nfce/consulta2', 'Consulta2')
}));
    console.log('Serviços NFCe carregados com sucesso');
  }

  private static criarServicoWeb(url: string, nome: string, acao = ''): ServicoWeb {
    const servico = new ServicoWeb();
    servico.url = url;
    servico.nome = nome;
    servico.acao = acao;
    return servico;
  }

  private static criarColecaoDeServicos(servicos: Partial<ColecaoDeServicos>): ColecaoDeServicos {
    const colecao = new ColecaoDeServicos();
    Object.assign(colecao, servicos);
    return colecao;
  }
}
