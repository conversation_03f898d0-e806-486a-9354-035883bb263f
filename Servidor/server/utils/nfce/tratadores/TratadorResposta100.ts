import {TratadorDeRespostaNFe} from "./TratadorDeRespostaNFe";
import {RespostaConsultaNFe} from "../respostas/RespostaConsultaNFe";
import {ReciboEnvioLoteNFe} from "../../../domain/nfce/ReciboEnvioLoteNFe";
import {NotaFiscalEletronica} from "../../../domain/nfce/NotaFiscalEletronica";

export class TratadorResposta100 extends TratadorDeRespostaNFe {
  trate(nfe: NotaFiscalEletronica): any {
    this.nfe = nfe
    console.log("Nota foi autorizada: " + this.resposta)
    return this.obtenhaResposta(null, "Autorizada");
  }
}
