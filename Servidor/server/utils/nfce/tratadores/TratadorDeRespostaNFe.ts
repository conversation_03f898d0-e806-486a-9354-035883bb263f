import {TratadorDeResposta} from "./TratadorDeResposta";
import {RespostaConsultaNFe} from "../respostas/RespostaConsultaNFe";
import {DTONotaFiscalEletronica} from "../dto/DTONotaFiscalEletronica";
import {ReciboEnvioLoteNFe} from "../../../domain/nfce/ReciboEnvioLoteNFe";
import {NotaFiscalEletronica} from "../../../domain/nfce/NotaFiscalEletronica";
import {MapeadorDeNotaFiscalEletronica} from "../../../mapeadores/MapeadorDeNotaFiscalEletronica";
import {RespostaEnvioSincrono} from "../respostas/RespostaEnvioSincrono";

export abstract class TratadorDeRespostaNFe extends TratadorDeResposta {
//    static final Logger log = Logger.getLogger(TratadorDeRespostaNFe.class)

  public async obtenhaResposta(recibo: ReciboEnvioLoteNFe, estado: string): Promise<RespostaEnvioSincrono> {
    console.log(`Processando resposta: ${this.resposta.replace('\n', ' ')}`);
    let xmlConsultaNFe = this.xmlResposta
    //console.log(`tag infProt: ${xmlConsultaNFe['protNFe']['infProt']}`);

    xmlConsultaNFe = this.obtenhaInfProt(xmlConsultaNFe);

    let chaveDeAcesso: string = xmlConsultaNFe['chNFe'];
    let numeroProtocolo: string = xmlConsultaNFe['nProt'];
    let motivo: string = xmlConsultaNFe['xMotivo'];
    let dataHoraRecebimento: string = xmlConsultaNFe['dhRecbto'];

    if(!this.nfe)
      throw new Error("Deveria existir uma NFE no tratador de resposta")

    if (DTONotaFiscalEletronica.notaFoiAutorizada(estado) && dataHoraRecebimento) {
      const moment = require('moment');
      this.nfe.dataAutorizacao = moment(dataHoraRecebimento).toDate();
    }

    /*
    if (estado === "Rejeitada" || estado === "Duplicidade") {
      console.log("Nota fiscal foi rejeitada!");
      this.graveRespostaRejeicao(this.nfe, this.resposta);
    }

    if(estado === "Denegada"){
      console.log("Nota fiscal foi denegada!");
      this.graveRespostaDenegacao(this.nfe, this.resposta);
    }

     */

    let resposta: RespostaConsultaNFe = await this.trateResposta(estado, motivo, chaveDeAcesso, recibo, numeroProtocolo);

    return resposta;
  }

  obtenhaInfProt(xmlConsultaNFe: any) {
    console.log(`xmlConsultaNFe: ${xmlConsultaNFe}`);

    if (xmlConsultaNFe['infProt']) {
      console.log("*** Resposta possui infProt... atualizando objeto de consulta");
      xmlConsultaNFe = xmlConsultaNFe['infProt'];
    }
    if (xmlConsultaNFe['protNFe']) {
      console.log("*** Resposta possui protNFe... atualizando objeto de consulta");
      xmlConsultaNFe = xmlConsultaNFe['protNFe']['infProt'];
    }

    return xmlConsultaNFe;
  }

  public async trateResposta(estado: string, motivo: string, chaveDeAcesso: string,
                             recibo: ReciboEnvioLoteNFe, numeroProtocolo: string): Promise<RespostaConsultaNFe> {
    console.log("Carregando NFe");


    this.nfe.motivo = motivo;

    let resposta = new RespostaConsultaNFe(DTONotaFiscalEletronica.notaFoiAutorizada(estado), estado, chaveDeAcesso, numeroProtocolo);

    resposta.motivo = motivo;

    resposta.resposta = this.resposta;

    if(this.nfe){
      this.nfe.processe(resposta);
    }

    resposta.nfe = this.nfe;

    await this.persista(this.nfe)

    return resposta;
  }


  private graveRespostaDenegacao(nfe: NotaFiscalEletronica, resposta: string) {

  }

  private graveRespostaRejeicao(nfe: NotaFiscalEletronica, resposta: string) {

  }

  private async persista(nfe: NotaFiscalEletronica) {
    let mapeador = new MapeadorDeNotaFiscalEletronica();

    if(!nfe.id)
      await mapeador.insiraSync(nfe)
    else
      await mapeador.atualizeStatus(nfe)
  }
}
