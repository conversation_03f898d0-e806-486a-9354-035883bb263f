import {TratadorDeResposta} from "./TratadorDeResposta";
import {DTONotaFiscalEletronica} from "../dto/DTONotaFiscalEletronica";
import {RespostaEnvioSincrono} from "../respostas/RespostaEnvioSincrono";
import {NotaFiscalEletronica} from "../../../domain/nfce/NotaFiscalEletronica";

export class TratadorDeRespostaConsultaReciboRejeicao implements  TratadorDeResposta {
  resposta: string;
  codigo: string;
  xmlResposta: any;
  nfe: NotaFiscalEletronica;

  async trate(nfe: NotaFiscalEletronica): Promise<RespostaEnvioSincrono> {
    return undefined;
  }
}
