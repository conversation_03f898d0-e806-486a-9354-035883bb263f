import {ConfiguracoesNotaFiscal} from "../../../domain/nfce/configuracoes/ConfiguracoesNotaFiscal";
import {Pedido} from "../../../domain/delivery/Pedido";
import {NotaFiscalEletronica} from "../../../domain/nfce/NotaFiscalEletronica";
import {Transporte} from "../../../domain/nfce/Transporte";
import {EnumTipoDestinoOperacao} from "../enum/EnumTipoDestinoOperacao";
import {EnumFinalidadeNFe} from "../enum/EnumFinalidadeNFe";
import {MapeadorNotaFiscalEletronica} from "../mapeadores/MapeadorNotaFiscalEletronica";
import {MapeadorItemNFe} from "../mapeadores/MapeadorItemNFe";
import {MapeadorDetalhamentoItemVeiculo} from "../mapeadores/MapeadorDetalhamentoItemVeiculo";
import {MapeadorDetalhamentoItemCombustivel} from "../mapeadores/MapeadorDetalhamentoItemCombustivel";
import {MapeadorICMSimples} from "../mapeadores/MapeadorICMSimples";
import {MapeadorICMS} from "../mapeadores/MapeadorICMS";
import {MapeadorPis} from "../mapeadores/MapeadorPis";
import {MapeadorIpi} from "../mapeadores/MapeadorIpi";
import {MapeadorCofins} from "../mapeadores/MapeadorCofins";
import {MapeadorICMSUFDestino} from "../mapeadores/MapeadorICMSUFDestino";
import {MapeadorIssqn} from "../mapeadores/MapeadorIssqn";
import {MapeadorImpostoSobreImportacao} from "../mapeadores/MapeadorImpostoSobreImportacao";
import {MapeadorCide} from "../mapeadores/MapeadorCide";
import {MapeadorEnderecoNFe} from "../mapeadores/MapeadorEnderecoNFe";
import {MapeadorTransporte} from "../mapeadores/MapeadorTransporte";
import {MapeadorCobranca} from "../mapeadores/MapeadorCobranca";
import {MapeadorFaturaNFe} from "../mapeadores/MapeadorFaturaNFe";
import {MapeadorObservacoesContribuinte} from "../mapeadores/MapeadorObservacoesContribuinte";
import {MapeadorHistoricoEstados} from "../mapeadores/MapeadorHistoricoEstados";
import {MapeadorAutorizacaoDownload} from "../mapeadores/MapeadorAutorizacaoDownload";
import {MapeadorPagamentoNfe} from "../mapeadores/MapeadorPagamentoNfe";
import {ServicoNFeSefaz} from "../comunicacao/ServicoNFeSefaz";
import {ExecutorAsync} from "../../ExecutorAsync";
import {MapeadorDeNotaFiscalEletronica} from "../../../mapeadores/MapeadorDeNotaFiscalEletronica";

export class NotaFiscalEletronicaService {
  constructor(private configuracoes: ConfiguracoesNotaFiscal) {

  }

  async gereNfceDoPedido(pedido: Pedido): Promise<NotaFiscalEletronica> {
    return new Promise<NotaFiscalEletronica>(async (resolve, reject) => {
      let nfce = new NotaFiscalEletronica(this.configuracoes)

      nfce.numeroNFe = await this.obtenhaProximoNumeroNFe();
      nfce.dataDeEmissao = new Date()
      nfce.modeloDocumentoFiscal = 65
      nfce.tipoDoDocumento = 1
      nfce.tipoDestinoOperacao = EnumTipoDestinoOperacao.INTERNA //nfce é sempre interna
      nfce.finalidade = EnumFinalidadeNFe.NORMAL
      nfce.naturezaDaOperacao = this.configuracoes.naturezaDaOperacao ? this.configuracoes.naturezaDaOperacao : "Venda de mercadorias"
      nfce.indicadorIntermediador = 0


      nfce.determineEmpresaEmitente(pedido.empresa)
      nfce.determineDadosDestinatario(pedido.contato, pedido.endereco)

      let taxaPagamento = null
      for(let pagamento of pedido.pagamentos) {
        //inicialmente suporte a apenas uma forma com taxa no pedido
        if(pagamento.taxa) {
          taxaPagamento = pagamento.taxa
          break
        }

      }
      let taxaDesconto = pedido.desconto ? pedido.desconto / (pedido.obtenhaTotal() + pedido.desconto - pedido.taxaEntrega) : 0
      let taxaEntrega = pedido.taxaEntrega ? pedido.taxaEntrega / (pedido.obtenhaTotal() - pedido.taxaEntrega) : 0


      await nfce.preenchaItensPeloPedido(pedido.itens, taxaPagamento, taxaDesconto, taxaEntrega)
      //percentuais para distribuir as taxas pelos itens

      nfce.calculeTotais(pedido)
      nfce.crieGrupoTransporte(pedido)

      nfce.criePagamentos(pedido)

      await this.salve(nfce)

      resolve(nfce);
    })
    //gera uma NFCE a partir de um pedido



  }
  private async obtenhaProximoNumeroNFe(): Promise<number> {
    const mapeadorNfe = new MapeadorNotaFiscalEletronica();
    return await mapeadorNfe.obtenhaProximoNumeroNFe();
  }

  private async persistaEnderecos(nfce: NotaFiscalEletronica) {
    let mapeadorEnderecoNFe = new MapeadorEnderecoNFe();

    await mapeadorEnderecoNFe.insiraSync(nfce.enderecoEmitente);

    if (nfce.enderecoDestinatario) await mapeadorEnderecoNFe.insiraSync(nfce.enderecoDestinatario);
    if (nfce.localRetirada) await mapeadorEnderecoNFe.insiraSync(nfce.localRetirada);
    if (nfce.localEntrega) await mapeadorEnderecoNFe.insiraSync(nfce.localEntrega);
  }


  private salve(nfce: NotaFiscalEletronica) {
    return new Promise((resolve, reject) => {
      let mapeadorNfe = new MapeadorNotaFiscalEletronica()
      mapeadorNfe.transacao(async (conexao: any, commit: any) => {
        try{


          await this.persistaEnderecos( nfce);



          if(nfce.transporte) await new MapeadorTransporte().insiraGraph(nfce.transporte)
          await this.persistaCobranca(nfce);

          await mapeadorNfe.salveSync(nfce)

                    await this.persistaItens(nfce)

                    await this.persistaObservacoesContribuinte(nfce);

          await this.persistaHistoricoEstados(nfce)
          await this.persistaAutorizacoesDownload(nfce)
          await this.persistaPagamentos(nfce)

          commit()
          resolve(true)
        } catch (erro) {
          reject(erro)
        }

      })
    })

  }

  private async persistaPagamentos(nfce: NotaFiscalEletronica) {
    if(nfce.pagamentos) {
      let pagamento: any
      for(pagamento of nfce.pagamentos) {
        pagamento.notaFiscalEletronica = nfce
        await new MapeadorPagamentoNfe().insiraGraph(pagamento)
        pagamento.notaFiscalEletronica = null
      }
    }
  }

  private async persistaAutorizacoesDownload(nfce: NotaFiscalEletronica) {
    if(nfce.autorizacoesDownload) {
      let autorizacao: any
      for (autorizacao of nfce.autorizacoesDownload) {
        autorizacao.notaFiscalEletronica = nfce
        await new MapeadorAutorizacaoDownload().insiraGraph(autorizacao)
        autorizacao.notaFiscalEletronica = null
      }
    }

  }


  private async persistaHistoricoEstados(nfce: NotaFiscalEletronica) {
    if(nfce.historicoEstados) {
      let mapeadorHistoricoEstados = new MapeadorHistoricoEstados()
      let historico: any
      for(historico of nfce.historicoEstados) {
        historico.notaFiscalEletronica = nfce
        await mapeadorHistoricoEstados.insiraGraph(historico)
        historico.notaFiscalEletronica = null
      }

    }
  }

  private async persistaObservacoesContribuinte(nfce: NotaFiscalEletronica) {
    if (nfce.observacoesContribuinte) {
      let observacao: any
      for (observacao of nfce.observacoesContribuinte) {
        observacao.notaFiscalEletronica = nfce
        await new MapeadorObservacoesContribuinte().insiraGraph(observacao)
        observacao.notaFiscalEletronica = null
      }
    }
  }


  private async persistaCobranca(nfce: NotaFiscalEletronica) {
    if (nfce.cobranca) {
      await new MapeadorCobranca().insiraGraph(nfce.cobranca)

      if (nfce.cobranca.faturas && nfce.cobranca.faturas.length > 0) {
        let fatura: any
        for (fatura of nfce.cobranca.faturas) {
          fatura.cobranca = nfce.cobranca
          await new MapeadorFaturaNFe().insiraGraph(fatura)
          fatura.cobranca = null
        }
      }
    }
  }

  private async persistaItens(nfce: NotaFiscalEletronica) {
    return new Promise(async (resolve, reject) => {
      let mapeadorItens = new MapeadorItemNFe()
      for(let item of nfce.itens) {
        item.notaFiscalEletronica = nfce

        if(item.itemVeiculo) await new MapeadorDetalhamentoItemVeiculo().insiraGraph(item.itemVeiculo)
        if(item.itemCombustivel) {
          if(item.itemCombustivel.cide) await new MapeadorCide().insiraGraph(item.itemCombustivel.cide)
          await new MapeadorDetalhamentoItemCombustivel().insiraGraph(item.itemCombustivel)
        }
        if(item.icmsimples)  await new MapeadorICMSimples().insiraGraph(item.icmsimples)
        if(item.icms) await new MapeadorICMS().insiraSync(item.icms)
        if(item.pis) await new MapeadorPis().insiraGraph(item.pis)
        if(item.ipi) await new MapeadorIpi().insiraGraph(item.ipi)
        if(item.cofins) await new MapeadorCofins().insiraGraph(item.cofins)
        if(item.icmsufDestino) await new MapeadorICMSUFDestino().insiraGraph(item.icmsufDestino)
        if(item.issqn) await new MapeadorIssqn().insiraGraph(item.issqn)
        if(item.ii) await new MapeadorImpostoSobreImportacao().insiraGraph(item.ii)

        await mapeadorItens.insiraSync(item)
        item.notaFiscalEletronica = null
      }
      resolve(true)
    })
  }


  //é necessário tratar os status de erro na nota enviada
  async envieNfce(nota: any): Promise<any> {
    let enviador = new ServicoNFeSefaz()
    let mapeador = new MapeadorDeNotaFiscalEletronica()
    return new Promise(async (resolve, reject) => {
      mapeador.transacao(async (conexao: any, commit: any) => {
        ExecutorAsync.execute( async(callback: Function) => {
          let contexto = require('domain').active.contexto;
          contexto.empresa = nota.empresa;
          contexto.idEmpresa = nota.empresa.id;

          nota.gereXmlNFe().then(async (xmlNota: any) => {
            await mapeador.atualizeStatus(nota)
            enviador.envieNFeSincronamente(nota).then((resposta) => {
              resolve({nota: nota, xml: xmlNota, resposta: resposta});
            }).catch((erro) => {
              reject({erro: erro});
            })
          }).catch((erro: any) => {
            reject({erro: erro});
          })
        }, (erro: Error) => {
          reject(erro);
        }, 0)
      })
    })


  }

  async cancelarNfce(nota: NotaFiscalEletronica) {
    if(!nota || !nota.id || nota.status !== 2) {
      throw new Error('Nota fiscal não encontrada ou não está autorizada para cancelamento');
    }

    let enviador = new ServicoNFeSefaz();
    let mapeador = new MapeadorDeNotaFiscalEletronica();
    return new Promise(async (resolve, reject) => {
      enviador

    });
  }

  async obtenhaXmlDistribuicao(nota: NotaFiscalEletronica): Promise<string> {
    if (!nota || !nota.id) {
      throw new Error('Nota fiscal não encontrada');
    }

    if (!nota.autorizacaoDeUso) {
      throw new Error('Nota fiscal não possui autorização de uso');
    }

    try {
      // Obtém o XML da nota
      const xmlNota = await nota.gereXmlNFe();

      // Cria o XML de distribuição combinando o XML da nota com a autorização
      const xmlDistribuicao = `<?xml version="1.0" encoding="UTF-8"?><nfeProc versao="4.00" xmlns="http://www.portalfiscal.inf.br/nfe">${xmlNota}<protNFe versao="4.00" xmlns="http://www.portalfiscal.inf.br/nfe">${nota.autorizacaoDeUso}</protNFe></nfeProc>`;

      return xmlDistribuicao;
    } catch (erro) {
      console.error('Erro ao gerar XML de distribuição:', erro);
      throw new Error('Não foi possível gerar o XML de distribuição da nota');
    }
  }
}
