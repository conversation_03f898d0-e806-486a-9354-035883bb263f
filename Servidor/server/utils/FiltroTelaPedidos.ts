// @ts-ignore
import moment = require("moment");
import {EnumOrigemPedido} from "../lib/emun/EnumOrigemPedido";

export class FiltroTelaPedidos{
  constructor(public dados: any, public empresa: any, public usuario: any) {
  }

  toSqlQuery(){
    let formatHU = 'YYYYMMDDHHmmss';
    let dados = this.dados;
    let query: any = { inicio: dados.i || 0, total: dados.t || 10, orderBy: true };

    if(dados.cid) query.idContato = Number(dados.cid);
    if(dados.status) query.status =  dados.status;
    if(dados.a) query.emAberto = true;
    if(dados.e) query.encerrado = true;
    if(dados.snp) query.naoPagos = true;
    if(dados.pg) query.pagos = true;
    if(dados.pgnpg) query.pagosENaoPagos = true;
    if(dados.cancelados) query.foiCancelado = true;
    if(dados.ultimo) query.idUltimo = dados.ultimo;
    //hops nao filtrar origem
    if(dados.o && !this.empresa.ehHops())
      query.origens = dados.o.split(',');

    if(dados.fps  ) query.idsFormasPagamento = dados.fps.split(',');
    if(dados.cps  ) query.idsCupons = dados.cps.split(',');
    if(dados.des === 'delivery') query.delivery = true;
    if(dados.des === 'mesas') query.mesas = true;
    if(dados.fe ) query.idFormaEntrega = dados.fe;
    if(dados.hu) query.ultimaAtualizacao = moment(dados.hu, formatHU).format('YYYY-MM-DD HH:mm:ss')
    if(dados.dia) query.dia  =  moment(dados.dia).isValid()  ? moment(dados.dia).format('YYYY-MM-DD') :  null
    if(dados.f === 'pedidos') query.filtroPedidos = true;
    if(dados.f === 'comandas') query.filtroComandas = true;
    if(dados.f === 'agendados') query.filtroAgendados = true
    if(dados.q ) query.q = `%${dados.q}%`;
    if(dados.r) query.rede = this.usuario.adminRede;
    if(dados.errIn) query.erroIntegracao =  true;


    if(dados.di){
      if(dados.di.length === 8)
        dados.di = String(`${dados.di}000001`)

      if(moment(dados.di, 'YYYYMMDDHHmmss').isValid())
        query.dataInicio = moment(dados.di, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss')

    }
    if(dados.df)  {
      if(dados.df.length === 8)
        dados.df = String(`${dados.df}235959`)

      if(moment(dados.df, 'YYYYMMDDHHmmss').isValid())
        query.dataFim = moment(dados.df, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss')

    }


    if(dados.odbd){
      delete query.orderBy
      query.orderByDesc = true;
    }

    query.inicio = Number(query.inicio)
    query.total = Number(query.total)

    return query;
  }
}
