import {EnumStatusPagamentoCielo} from "./EnumStatusPagamentoCielo";
import {EnumStatusChargePagseguro, EnumStatusPagamentoPagseguro} from "./EnumStatusPagamentoPagseguro";
import {EnumStatusPagamentoMercadoPago} from "./EnumStatusPagamentoMercadoPago";
import {EnumStatusPagamentoPagarme} from "./EnumStatusPagamentoPagarme";
import {EnumStatusPagamentoRede} from "./EnumStatusPagamentoRede";
import {EnumStatusPagamentoTunaPay, EnumStatusPagamentoWebWookTunaPay} from "./EnumStatusPagamentoTunaPay";


export enum  EnumStatusPagamento {
  Registrado,
  Gerado ,
  EmAnalise ,
  Aprovado ,
  Cancelado ,
  Reembolsado ,
  Suspenso,
  Negado,
  ReembolsoSolicitado
}

export const StatusPagamentoPedidoLabel = new Map<EnumStatusPagamento, string>([
  [EnumStatusPagamento.Registrado, 'Aguardando pagamento'],
  [EnumStatusPagamento.Gerado, 'Aguardando pagamento'],
  [EnumStatusPagamento.EmAnalise, 'Aguardando pagamento'],
  [EnumStatusPagamento.Aprovado, 'Pago'],
  [EnumStatusPagamento.Cancelado, 'Cancelado'],
  [EnumStatusPagamento.Suspenso, 'Suspenso'],
  [EnumStatusPagamento.ReembolsoSolicitado, 'Suspenso'],
  [EnumStatusPagamento.Negado, 'Negado'],
  [EnumStatusPagamento.Reembolsado, 'Devolvido'],
]);


export const StatusPagamentoDeParaPagseguro = new Map<EnumStatusPagamentoPagseguro, EnumStatusPagamento>([
  [EnumStatusPagamentoPagseguro.AguardandoPagamento, EnumStatusPagamento.Gerado],
  [EnumStatusPagamentoPagseguro.EmAnalise, EnumStatusPagamento.EmAnalise],
  [EnumStatusPagamentoPagseguro.Paga, EnumStatusPagamento.Aprovado],
  [EnumStatusPagamentoPagseguro.Disponivel, EnumStatusPagamento.Aprovado],
  [EnumStatusPagamentoPagseguro.EmDisputa, EnumStatusPagamento.Suspenso],
  [EnumStatusPagamentoPagseguro.Devolvida, EnumStatusPagamento.Reembolsado],
  [EnumStatusPagamentoPagseguro.Cancelada, EnumStatusPagamento.Cancelado],
  [EnumStatusPagamentoPagseguro.Erro, EnumStatusPagamento.Negado]
]);

export const StatusChargeDeParaPagseguro = new Map<any, EnumStatusPagamento>([
  [EnumStatusChargePagseguro.Aguardando, EnumStatusPagamento.Gerado],
  [EnumStatusChargePagseguro.EmAnalise, EnumStatusPagamento.EmAnalise],
  [EnumStatusChargePagseguro.Pago, EnumStatusPagamento.Aprovado],
  [EnumStatusChargePagseguro.Recusado, EnumStatusPagamento.Negado],
  [EnumStatusChargePagseguro.Cancelado, EnumStatusPagamento.Cancelado],
]);

export const StatusPagamentoDeParaMercadoPago = new Map<EnumStatusPagamentoMercadoPago, EnumStatusPagamento>([
  [EnumStatusPagamentoMercadoPago.pending, EnumStatusPagamento.Gerado],
  [EnumStatusPagamentoMercadoPago.in_process, EnumStatusPagamento.EmAnalise],
  [EnumStatusPagamentoMercadoPago.approved, EnumStatusPagamento.Aprovado],
  [EnumStatusPagamentoMercadoPago.cancelled, EnumStatusPagamento.Cancelado],
  [EnumStatusPagamentoMercadoPago.refunded, EnumStatusPagamento.Reembolsado],
  [EnumStatusPagamentoMercadoPago.rejected, EnumStatusPagamento.Negado],
  [EnumStatusPagamentoMercadoPago.erro, EnumStatusPagamento.Negado],
]);

export const StatusPagamentoDeParaCielo = new Map<EnumStatusPagamentoCielo, EnumStatusPagamento>([
  [EnumStatusPagamentoCielo.Registrado, EnumStatusPagamento.Registrado],
  [EnumStatusPagamentoCielo.Pendente, EnumStatusPagamento.EmAnalise],
  [EnumStatusPagamentoCielo.Pago, EnumStatusPagamento.Aprovado],
  [EnumStatusPagamentoCielo.Negado, EnumStatusPagamento.Negado],
  [EnumStatusPagamentoCielo.Expirado, EnumStatusPagamento.Cancelado],
  [EnumStatusPagamentoCielo.Cancelado, EnumStatusPagamento.Cancelado],
  [EnumStatusPagamentoCielo.NaoFinalizado, EnumStatusPagamento.Cancelado],
  [EnumStatusPagamentoCielo.Autorizado, EnumStatusPagamento.Aprovado],
  [EnumStatusPagamentoCielo.Chargeback, EnumStatusPagamento.Reembolsado],
  [EnumStatusPagamentoCielo.Estornada, EnumStatusPagamento.Reembolsado],
]);


export const StatusPagamentoDeParaPagarme = new Map<EnumStatusPagamentoPagarme, EnumStatusPagamento>([
  [EnumStatusPagamentoPagarme.Pago, EnumStatusPagamento.Aprovado],
  [EnumStatusPagamentoPagarme.AguardandoPagamento, EnumStatusPagamento.Gerado],
  [EnumStatusPagamentoPagarme.Pendente, EnumStatusPagamento.EmAnalise],
  [EnumStatusPagamentoPagarme.Processando, EnumStatusPagamento.EmAnalise],
  [EnumStatusPagamentoPagarme.Cancelado, EnumStatusPagamento.Cancelado],
  [EnumStatusPagamentoPagarme.Falhou, EnumStatusPagamento.Negado],
  [EnumStatusPagamentoPagarme.Chargedback, EnumStatusPagamento.Reembolsado],
]);

export const StatusPagamentoDeParaRede = new Map<EnumStatusPagamentoRede, EnumStatusPagamento>([
[  EnumStatusPagamentoRede.Pendente, EnumStatusPagamento.Gerado],
[  EnumStatusPagamentoRede.Pago, EnumStatusPagamento.Aprovado],
[  EnumStatusPagamentoRede.Negado, EnumStatusPagamento.Negado],
[  EnumStatusPagamentoRede.Cancelado, EnumStatusPagamento.Cancelado]
]);

export const StatusPaymentDeParaTunaPay = new Map<any, EnumStatusPagamento>([
  [  EnumStatusPagamentoTunaPay.Inciada, EnumStatusPagamento.Gerado],
  [  EnumStatusPagamentoTunaPay.Cancelada, EnumStatusPagamento.Cancelado],
  [  EnumStatusPagamentoTunaPay.Abandonada, EnumStatusPagamento.Cancelado],
  [  EnumStatusPagamentoTunaPay.Negada, EnumStatusPagamento.Negado],
  [  EnumStatusPagamentoTunaPay.Pendente, EnumStatusPagamento.EmAnalise],
  [  EnumStatusPagamentoTunaPay.Capturada, EnumStatusPagamento.Aprovado],
  [  EnumStatusPagamentoTunaPay.Autorizada, EnumStatusPagamento.Aprovado],
  [  EnumStatusPagamentoTunaPay.Liberada, EnumStatusPagamento.Aprovado],
  [  EnumStatusPagamentoTunaPay.ReembolsadaParcial, EnumStatusPagamento.Reembolsado],
  [  EnumStatusPagamentoTunaPay.Reembolsada, EnumStatusPagamento.Reembolsado]
]);

export const StatusPaymentDeParaTunaPayWebhook = new Map<any, EnumStatusPagamento>([
  [  EnumStatusPagamentoWebWookTunaPay.Inciada, EnumStatusPagamento.Gerado],
  [  EnumStatusPagamentoWebWookTunaPay.Cancelada, EnumStatusPagamento.Cancelado],
  [  EnumStatusPagamentoWebWookTunaPay.Abandonada, EnumStatusPagamento.Cancelado],
  [  EnumStatusPagamentoWebWookTunaPay.Negada, EnumStatusPagamento.Negado],
  [  EnumStatusPagamentoWebWookTunaPay.Pendente, EnumStatusPagamento.EmAnalise],
  [  EnumStatusPagamentoWebWookTunaPay.Capturada, EnumStatusPagamento.Aprovado],
  [  EnumStatusPagamentoWebWookTunaPay.Liberada, EnumStatusPagamento.Aprovado],
  [  EnumStatusPagamentoWebWookTunaPay.Reembolsada, EnumStatusPagamento.Reembolsado],
  [  EnumStatusPagamentoWebWookTunaPay.ReembolsadaParcial, EnumStatusPagamento.Reembolsado]
]);
