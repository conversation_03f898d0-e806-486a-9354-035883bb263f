import {AcaoDoContato} from "../../domain/AcaoDoContato";
import {DTOPontuacaoRegistrada} from "./DTOPontuacaoRegistrada";
import {EnumTipoDeAcao} from "../emun/EnumTipoDeAcao";
// @ts-ignore
import moment = require("moment");


export class DTOAcaoContato {
  id: string;
  tipo: string;
  descricao: string;
  horario: string;
  horarioAbreviado: string;
  acumulado: string;
  contato:  any;
  pontuacao: any;
  brinde: any;
  credito: boolean
  debito: boolean
  ano: any;
  constructor(acaoContato: AcaoDoContato) {
     this.id = acaoContato.id;
     this.descricao = acaoContato.mensagem[0].toUpperCase() + acaoContato.mensagem.slice(1).toLowerCase().replace('r$', 'R$');
     this.horario = moment(acaoContato.horario).format('DD/MM/YYYY HH:mm:ss');
     this.horarioAbreviado =  moment(acaoContato.horario).format('DD/MMM');

     this.tipo = acaoContato.tipoDeAcao.toString();
     this.credito = acaoContato.gerouCredito();
     this.debito = acaoContato.gerouDebito();
     this.ano =   moment(acaoContato.horario).format('YYYY');

     if( acaoContato.pontos ){
       this.acumulado = acaoContato.cartao.obtenhaDescricaoPontos(acaoContato.pontos);
     } else {
       this.acumulado = '-'
     }
     this.contato = { nome: acaoContato.contato.nome, telefone: acaoContato.contato.telefone, codigoPais: acaoContato.contato.codigoPais };

     if(acaoContato.pontuacaoRegistrada)
        this.pontuacao = new DTOPontuacaoRegistrada(acaoContato.pontuacaoRegistrada);

     if(acaoContato.brindeResgatado && acaoContato.brindeResgatado.id)
       this.brinde = { id: acaoContato.brindeResgatado.id,
                       codigo: acaoContato.brindeResgatado.codigo
       }


  }
}
