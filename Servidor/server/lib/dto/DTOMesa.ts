import {EnumStatusComanda} from "../../domain/comandas/EnumStatusComanda";
import {Empresa} from "../../domain/Empresa";
import {Mesa} from "../../domain/Mesa";

export class DTOMesa {
  id: number;
  nome: string;
  removida: boolean;
  disponivel = true;
  naoGerarComanda = false;

  somenteLeitura = false;
  codigoPdv: string;
  comandas: Array<any>;
  valorTotal: number;
  tempo: any;

  constructor(mesa: Mesa) {
    this.id = mesa.id;
    this.nome = mesa.nome;
    this.removida = mesa.removida;
    this.disponivel = !(mesa.status === EnumStatusComanda.ABERTA);
    this.naoGerarComanda = mesa.naoGerarComanda;
    this.codigoPdv = mesa.codigoPdv;
    this.somenteLeitura = mesa.somenteLeitura;
  }
}
