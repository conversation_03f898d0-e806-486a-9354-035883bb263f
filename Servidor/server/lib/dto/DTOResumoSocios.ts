import {EnumStatusContato} from "../emun/EnumStatusContato";


export class DTOResumoSocios {
  total: number =0;
  qtdeAtivos: number = 0;
  qtdeInativos: number = 0;
  qtdeNovo: number = 0;
  constructor( listaResumoPorTipo: Array<any> ) {
    listaResumoPorTipo.forEach( ( resumoTipo ) => {
      if ( resumoTipo.status === EnumStatusContato.Novo)
         this.qtdeNovo = resumoTipo.qtde;

      if ( resumoTipo.status === EnumStatusContato.Ativo)
        this.qtdeAtivos = resumoTipo.qtde;

      if ( resumoTipo.status === EnumStatusContato.Inativo)
        this.qtdeInativos = resumoTipo.qtde;
    });
  }
}
