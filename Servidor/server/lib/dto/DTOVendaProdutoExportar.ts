import {DTOExportarCSV} from "./DTOExportarCSV";
// @ts-ignore
import moment = require("moment");
import {StatusPedidoLabel} from "../emun/EnumStatusPedido";
import {FormatadorUtils} from "../FormatadorUtils";

export class DTOVendaProdutoExportar extends DTOExportarCSV{
  constructor(public Pedido: string,  public Data: string, public Mes: string,
              public Cliente: string, public Telefone: string,
              public Bairro: string, public Logradouro: string,
              public Numero: string, public Complemento: string,
              public Produto: string, public Qtde: string, public Total: string,
              public Forma_De_Pagamento: string, public Data_Fechamento: string, public Mes_Fechamento: string,
              public Status: string, public Pago: string) {
    super();
  }
  static listeVendasProdutosExportar(vendasPorProdutos: Array<any>) {
    let dtos: any =  [];

    vendasPorProdutos.forEach( (vendaProduto: any) => {
      let unidade = vendaProduto.unidade || '';
      let qtdeDescricao = String(`${vendaProduto.qtde}${unidade}`).trim();

      dtos.push(new DTOVendaProdutoExportar( vendaProduto.pedido,    moment(vendaProduto.horarioPedido).format('DD/MM/YYYY'),
                                            moment(vendaProduto.horarioPedido).format('MMMM'),
                                            vendaProduto.clienteNome, vendaProduto.clienteTelefone,
                                            vendaProduto.enderecoBairro, vendaProduto.enderecoLogradouro,
                                            vendaProduto.enderecoNumero, vendaProduto.enderecoComplemento,
                                            vendaProduto.nome, qtdeDescricao,
                                            FormatadorUtils.numeroParaCurrency(vendaProduto.total),
                                            vendaProduto.pagamentos,  moment(vendaProduto.horarioFechamento).format('DD/MM/YYYY'),
                                            moment(vendaProduto.horarioFechamento).format('MMMM'),
                                            StatusPedidoLabel.get(Number(vendaProduto.statusPedido)),
                                            vendaProduto.pago  ? 'Sim' : 'Não'
                                          ))
    })


    return  dtos.map( (dto: any) => dto.toCSV());
  }
}

export class DTOVendaCupomExportar extends DTOExportarCSV{
  constructor( public CodigoLoja: number, public NomeLoja: string,
               public Cupom: string, public Qtde: number, public Faturamento: number ){
    super()
    this.Faturamento = Number(this.Faturamento.toFixed(2))
  }

  static listeVendasExportar(vendas: Array<any> ){
    let dtos: any =  [];

    vendas.forEach( (venda: any) => {
      let dto: any = new DTOVendaCupomExportar(venda.idEmpresa, venda.nomeEmpresa, venda.codigo, venda.qtde, venda.total);

      dtos.push(dto);
    })

    return  dtos.map( (dto: any) => dto.toCSV());

  }
}

export class DTOVendaPromocaoExportar extends DTOExportarCSV{
  constructor( public CodigoLoja: number, public NomeLoja: string,
               public Promocao: string, public Qtde: number, public Faturamento: number ){
    super()
    this.Faturamento = Number(this.Faturamento.toFixed(2))
  }

  static listeVendasExportar(vendas: Array<any> ){
    let dtos: any =  [];

    vendas.forEach( (venda: any) => {
      let dto: any = new DTOVendaPromocaoExportar(venda.idEmpresa, venda.nomeEmpresa, venda.descricao, venda.qtde, venda.total);

      dtos.push(dto);
    })

    return  dtos.map( (dto: any) => dto.toCSV());

  }
}


export class DTOVendaClienteCupomExportar extends DTOExportarCSV{
  constructor( public CodigoLoja: number, public NomeLoja: string,
               public  Cliente: string, Telefone: string,
               public Cupom: string, public Qtde: number, public Faturamento: number ){
    super()
    this.Faturamento = Number(this.Faturamento.toFixed(2))
  }

  static listeVendasExportar(vendas: Array<any> ){
    let dtos: any =  [];

    vendas.forEach( (venda: any) => {
      let dto: any = new DTOVendaClienteCupomExportar(venda.idEmpresa, venda.nomeEmpresa,
        venda.contato, venda.telefone, venda.codigo, venda.qtde, venda.total);


      dtos.push(dto);
    })

    return  dtos.map( (dto: any) => dto.toCSV());

  }
}

export class DTOVendaClientePromocaoExportar extends DTOExportarCSV{
  constructor( public CodigoLoja: number, public NomeLoja: string,
               public  Cliente: string, Telefone: string,
               public Promocao: string, public Qtde: number, public Faturamento: number ){
    super()
    this.Faturamento = Number(this.Faturamento.toFixed(2))
  }

  static listeVendasExportar(vendas: Array<any> ){
    let dtos: any =  [];

    vendas.forEach( (venda: any) => {
      let dto: any = new DTOVendaClientePromocaoExportar(venda.idEmpresa, venda.nomeEmpresa,
        venda.contato, venda.telefone, venda.descricao, venda.qtde, venda.total);


      dtos.push(dto);
    })

    return  dtos.map( (dto: any) => dto.toCSV());

  }
}
