import {DTOVendaPorForma} from "../../domain/DTOVendaPorForma";
import * as _ from 'underscore';
import * as moment from 'moment';
import {DTOVendaAdicional} from "../../domain/DTOVendaAdicional";
import {DTOVendaOpcaoAdicional} from "../../domain/DTOVendaOpcaoAdicional";
export class DTOResumoVendas {
  qtde = 0;
  qtdeLinhas = 0;
  total  = 0;
  taxaDeEntrega = 0;
  ticketMedio = 0;
  totalTaxaServico = 0;
  totalTaxaFormaPagamento = 0;
  totalPagamentosComTaxa = 0;
  totalComTaxa = 0;
  formasDePagamento: any = [];
  formasDeEntrega: any = [];
  cupons: any = [];
  promocoes: any = []
  diarias: any = [];
  lojas: any = [];



  constructor(vendasPorFormas: any = [], porEmpresa = false, porContato: boolean = false) {
    vendasPorFormas.forEach( ( vendaPorForma: DTOVendaPorForma) => {
      this.total += vendaPorForma.total;
      this.totalComTaxa += vendaPorForma.totalComTaxa;
      this.totalPagamentosComTaxa += vendaPorForma.totalPagamentosComTaxa;

      if(vendaPorForma.nome !== 'Cashback') {
        this.qtde += vendaPorForma.qtde;
        this.qtdeLinhas += vendaPorForma.qtdeLinhas;
        this.taxaDeEntrega += vendaPorForma.taxaDeEntrega;
        this.totalTaxaServico += vendaPorForma.taxaServico
      }

      this.totalTaxaFormaPagamento  += vendaPorForma.taxaFormaPagamento;
    });

    let porDias =  _.groupBy(vendasPorFormas, (venda: any) => venda.dia);
    let porLoja =  _.groupBy(vendasPorFormas, (venda: any) => venda.idEmpresa);
    let porFormasPagamento = _.groupBy(vendasPorFormas, (venda: any) => venda.nome || 'Todas');
    let porFormasEntrega = _.groupBy(vendasPorFormas.filter((item: any) =>  item.formaDeEntrega != null),
      (venda: any) => venda.formaDeEntrega);

    let vendasBalcao = vendasPorFormas.filter((item: any) =>  !item.formaDeEntrega  );

    if(vendasBalcao.length) porFormasEntrega['Balcao'] = vendasBalcao;

    let porCupom: any = _.groupBy(vendasPorFormas, (venda: any) =>
      porContato ? (String(`${venda.telefone}${venda.idEmpresa}${venda.cupom}`)) :
        (porEmpresa ? String(`${venda.idEmpresa}${venda.cupom}`) : venda.cupom));
    let porPromocao: any = _.groupBy(vendasPorFormas, (venda: any) => porContato ?
      (String(`${venda.telefone}${venda.idEmpresa}${venda.promocao}`)) :
      (porEmpresa ? String(`${venda.idEmpresa}${venda.promocao}`) : venda.promocao));

    Object.keys(porLoja).forEach( (loja: string) => {
      let totais: any = this.obtenhaTotais(porLoja[loja]);

      totais.codigoLoja = loja;
      totais.nomeLoja = porLoja[loja][0].nomeEmpresa;

      this.lojas.push( totais);

    });


    Object.keys(porDias).forEach( (dia: string) => {
      let totais: any = this.obtenhaTotais(porDias[dia]);

      totais.dia = dia;

      this.diarias.push( totais);
    })

    Object.keys(porFormasPagamento).forEach( (forma: any) => {
      if(forma !== 'Todas'){
        let qtde = _.reduce( porFormasPagamento[forma], (valor: number, resumoPorForma: any) => valor + resumoPorForma.qtde, 0 )
        let total  = _.reduce( porFormasPagamento[forma], (valor: number, resumoPorForma: any) => valor + resumoPorForma.total, 0 )
        let totalTaxas  = _.reduce( porFormasPagamento[forma],
          (valor: number, resumoPorForma: any) => valor + resumoPorForma.taxaFormaPagamento, 0 )
        this.formasDePagamento.push({ qtde: qtde, total: total, nome: forma, totalTaxas: totalTaxas })
      }

    })

    Object.keys(porFormasEntrega).forEach( (forma: any) => {
      let qtde = _.reduce( porFormasEntrega[forma], (valor: number, resumoPorForma: any) =>
        resumoPorForma.nome !== 'Cashback' ? valor + resumoPorForma.qtde : valor, 0 )
      let total  = _.reduce( porFormasEntrega[forma], (valor: number, resumoPorForma: any) =>
        valor + resumoPorForma.total, 0 )
      this.formasDeEntrega.push({ qtde: qtde, total: total, nome: forma })
    })



    Object.keys(porCupom).forEach( (key: string) => {
      if(key.indexOf('undefined') === -1 ){
        let dados: DTOVendaPorForma = porCupom[key][0];

        let qtde = _.reduce( porCupom[key], (valor: number, resumoPorCupom: any) =>
          resumoPorCupom.nome !== 'Cashback' ?  valor + resumoPorCupom.qtde : valor, 0 );

        let total  = _.reduce( porCupom[key], (valor: number, resumoPorCupom: any) => valor + resumoPorCupom.total, 0 );
        let totalDesconto  = _.reduce( porCupom[key], (valor: number, resumoPorCupom: any) => valor + resumoPorCupom.descontos, 0 );

        //depois tirar nome, impressao resumo tah dependente
        let infoCupom: any = { qtde: qtde,
          total: total,
          totalDesconto: totalDesconto,
          codigo: dados.cupom,
          nome: dados.cupom,
          idEmpresa: dados.idEmpresa,
          nomeEmpresa: dados.nomeEmpresa
        }

        if(porContato){
          infoCupom.telefone = dados.telefone;
          infoCupom.contato = dados.contato;
        }

        this.cupons.push(infoCupom)
      }
    })

    Object.keys(porPromocao).forEach( (key: string) => {
      if(key.indexOf('undefined') === -1 ){
        let dados: DTOVendaPorForma = porPromocao[key][0];

        let qtde = _.reduce( porPromocao[key], (valor: number, resumoPorPromocao: any) =>
          valor + resumoPorPromocao.qtde, 0 );

        let total  = _.reduce( porPromocao[key], (valor: number, resumoPorPromocao: any) => valor + resumoPorPromocao.total, 0 );
        let totalDesconto  = _.reduce( porPromocao[key], (valor: number, resumoPorPromocao: any) => valor + resumoPorPromocao.descontos, 0 );

        //depois tirar nome, impressao resumo tah dependente
        let infoPromocao: any = {
          qtde: qtde,
          total: total,
          totalDesconto: totalDesconto,
          codigo: dados.promocao,
          descricao: dados.promocao,
          idEmpresa: dados.idEmpresa,
          nomeEmpresa: dados.nomeEmpresa
        }

        if(porContato){
          infoPromocao.telefone = dados.telefone;
          infoPromocao.contato = dados.contato;
        }

        this.promocoes.push(infoPromocao)


      }
    })

    this.diarias = _.sortBy( this.diarias, (resumoDia: any) => -Number(moment(resumoDia.dia, 'DD/MM/YYYY').format('YYYYMMDD')));

    if(this.qtde)
      this.ticketMedio = this.total /  this.qtde;
  }

  private obtenhaTotais(listaAgrupada: any){
    let qtde =  _.reduce(listaAgrupada, (valor: number, resumo: any) =>
      resumo.nome !== 'Cashback' ? valor + resumo.qtde : valor, 0 );

    let total  = _.reduce( listaAgrupada, (valor: number, resumo: any) => valor + resumo.total, 0 );

    let totalTaxaDeEntrega  = _.reduce( listaAgrupada, (valor: number, resumo: any) =>
      resumo.nome !== 'Cashback' ? valor + resumo.taxaDeEntrega : valor, 0 );

    let totalTaxaServico = _.reduce(listaAgrupada, (valor: number, resumo: any) =>
      resumo.nome !== 'Cashback' ? valor + resumo.taxaServico : valor, 0);

    let totalComTaxa = _.reduce(listaAgrupada, (valor: number, resumo: any) =>
      resumo.nome !== 'Cashback' ? valor + resumo.totalComTaxa : valor, 0);

    let totalPagamentosComTaxa = _.reduce(listaAgrupada, (valor: number, resumo: any) =>
      resumo.nome !== 'Cashback' ? valor + resumo.totalPagamentosComTaxa : valor, 0);


    let ticketMedio = total /  qtde

    return { qtde: qtde,
      total: Number(total.toFixed(2)),
      ticketMedio: Number(ticketMedio.toFixed(2)),
      totalTaxaDeEntrega: Number(totalTaxaDeEntrega.toFixed(2)),
      totalTaxaServico: Number(totalTaxaServico.toFixed(2)),
      totalComTaxa: Number(totalComTaxa.toFixed(2)),
      totalPagamentosComTaxa: Number(totalPagamentosComTaxa.toFixed(2))
    }

  }
}

export class DTOResumoVendasProduto {
  qtde = 0;
  total  = 0;
  ticketMedio = 0;
  produtos: any = [];

  constructor(vendas: any = [], public totalDescontos: number) {
    vendas.forEach( ( venda : DTOVendaPorForma) => {
      this.qtde += venda.qtde;
      this.total += venda.total;
    })

    vendas.forEach( (produto: any) => {
      let unidade = produto.unidade || '';
      produto.qtdeDescricao = String(`${produto.qtde.toLocaleString("pt-BR", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 3
      }).replace(/[.,]00$/, "")}${unidade}`).trim();
    })

    this.produtos = _.sortBy( vendas, (resumo: any) => -resumo.qtde);

    if(this.qtde)
      this.ticketMedio = this.total /  this.qtde;
  }
}

export class DTOResumoVendasGarcon{
  qtde = 0;
  total  = 0;
  ticketMedio = 0;
  garcons: any = [];

  constructor(vendas: any = []) {
    vendas.forEach( ( vendaPorForma: DTOVendaPorForma) => {
      this.qtde += vendaPorForma.qtde;
      this.total += vendaPorForma.total;
    })

    vendas.forEach( (venda: any) => {

    })

    this.garcons = _.sortBy( vendas, (resumo: any) => -resumo.qtde);

    if(this.qtde)
      this.ticketMedio = this.total /  this.qtde;
  }
}

export class DTOResumoVendasAdicionais {
  qtde = 0;
  total  = 0;
  totalDescontos  = 0;
  ticketMedio = 0;
  adicionais: any = [];
  constructor(vendasAdicionaisSimples: any = [],  vendasAdicionaisMultiplos: any = [], ordernarNome = true) {
    vendasAdicionaisSimples.forEach( (dtoResumo: DTOVendaAdicional) => {
      this.computeResumo(dtoResumo.adicional0, dtoResumo.qtde, dtoResumo.valor0, dtoResumo.codigo0, dtoResumo.produto);
      this.computeResumo(dtoResumo.adicional1, dtoResumo.qtde, dtoResumo.valor1, dtoResumo.codigo1,  dtoResumo.produto);
      this.computeResumo(dtoResumo.adicional2, dtoResumo.qtde, dtoResumo.valor2, dtoResumo.codigo2, dtoResumo.produto);
      this.computeResumo(dtoResumo.adicional3, dtoResumo.qtde, dtoResumo.valor3, dtoResumo.codigo3, dtoResumo.produto);
      this.computeResumo(dtoResumo.adicional4, dtoResumo.qtde, dtoResumo.valor4, dtoResumo.codigo4, dtoResumo.produto);
      this.computeResumo(dtoResumo.adicional5, dtoResumo.qtde, dtoResumo.valor5, dtoResumo.codigo5, dtoResumo.produto);
      this.computeResumo(dtoResumo.adicional6, dtoResumo.qtde, dtoResumo.valor6, dtoResumo.codigo6, dtoResumo.produto);
      this.computeResumo(dtoResumo.adicional7, dtoResumo.qtde, dtoResumo.valor7, dtoResumo.codigo7, dtoResumo.produto);
      this.computeResumo(dtoResumo.adicional8, dtoResumo.qtde, dtoResumo.valor8, dtoResumo.codigo8, dtoResumo.produto);
      this.computeResumo(dtoResumo.adicional9, dtoResumo.qtde, dtoResumo.valor9,  dtoResumo.codigo9, dtoResumo.produto);
    })

    vendasAdicionaisMultiplos.forEach((dtoResumo: DTOVendaOpcaoAdicional) => {
      this.computeResumo(dtoResumo.nome, dtoResumo.qtde, dtoResumo.valor, dtoResumo.codigo, dtoResumo.produto)
    })

    if(ordernarNome)
      this.adicionais = _.sortBy( this.adicionais, (resumo: any) => resumo.nome);
    else
      this.adicionais = _.sortBy( this.adicionais, (resumo: any) => -resumo.qtde);
  }


  computeResumo(nomeAdicional: string, qtde: number,  valor: number, codigo: string, produto: string){
    if(!nomeAdicional) return;

    let resumoAdicional: any = this.adicionais.find( (dtoAdicional: any) => dtoAdicional.nome === nomeAdicional)

    if(!resumoAdicional){
      resumoAdicional = { nome: nomeAdicional, codigo: codigo || '-', qtde: 0, total: 0, produto: produto};
      this.adicionais.push(resumoAdicional)
    }

    resumoAdicional.qtde += qtde;
    resumoAdicional.total += valor * qtde;

    resumoAdicional.qtdeDescricao  =  resumoAdicional.qtde;
    resumoAdicional.total = Number((    resumoAdicional.total.toFixed(2)));

    this.qtde += qtde;
    this.total +=     valor * qtde
  }
}

export class DTOResumoVendasCupons {
  qtde = 0;
  total = 0;
  ticketMedio = 0;
  cupons: any = [];
  constructor(vendas: any) {
    vendas.forEach( ( venda: DTOVendaPorForma) => {
      this.qtde += venda.qtde;
      this.total += venda.total;
    })

    this.total = Number(this.total.toFixed(2))

    this.cupons = _.sortBy( vendas, (resumo: any) => -resumo.qtde);

    if(this.qtde){
      this.ticketMedio = this.total /  this.qtde;
    }

  }

}
