export class RespostaFazEntregaKml {
  sucesso: boolean;
  erro: string;
  fazEntrega: boolean;
  distancia: number;
  valor: number;

  static erro(erro: string): RespostaFazEntregaKml {
    const resposta = new RespostaFazEntregaKml();

    resposta.erro = erro;
    resposta.sucesso = false;

    return resposta;
  }

  static entrega(valor: number, distancia: number): RespostaFazEntregaKml {
    const resposta = new RespostaFazEntregaKml();

    resposta.valor = valor;
    resposta.sucesso = true;
    resposta.fazEntrega = true;
    resposta.distancia = distancia;

    return resposta;
  }
}
