import {Produto} from "../../../domain/Produto";
import {Categoria} from "../../../domain/delivery/Categoria";
import {EnumDisponibilidadeProduto} from "../../emun/EnumDisponibilidadeProduto";
import * as uuid from "uuid";
import * as moment from "moment";
export class BlueSoftProdutoUtils{

  static converaEstoqueEmProdutos(items: any, loja: number){
     /*
          "saldoOnline": 3.0,
          "saldoFisico": 3.0,
          "saldoDisponivel": 3.0,
      */

    let produtos: any = [];
    for(let i = 0 ; i < items.length; i++) {
      let item: any = items[i];

      if(item.lojaKey === loja){
        let produto: any = new Produto(null, '', 0, '');
        produto.codigoPdv = item.produtoKey.toString();

        produto.disponibilidade =  (!item.lojaPossuiControleEstoqueEcommerce || item.saldoDisponivel > 0)
          ? EnumDisponibilidadeProduto.SempreDisponivel : EnumDisponibilidadeProduto.NaoDisponivel;

        produtos.push(produto)
      }

    }

    return produtos;
  }

  static converaPrecosEmProdutos(items: any){
    let produtos: any = [];
    for(let i = 0 ; i < items.length; i++){
      let item: any = items[i];

      let produto: any = new Produto(null, '',  0, '');
      produto.codigoPdv = item.produtoKey.toString();

      BlueSoftProdutoUtils.setPrecoProduto(item, produto)

      produtos.push(produto)
    }

    return produtos;
  }

  static setPrecoProduto(itemPreco: any, produto: any){
    produto.preco = itemPreco.precoVenda || itemPreco.precoEmVigor;

    let novoPreco = itemPreco.precoEmVigor;

    if(itemPreco.precoOferta && itemPreco.precoOferta < novoPreco){
      novoPreco = itemPreco.precoOferta ;
    }

    if(itemPreco.precoEcommerce && itemPreco.precoEcommerce < novoPreco){
      novoPreco = itemPreco.precoEcommerce ;
    }

    if(itemPreco.precoOfertaFidelidade && itemPreco.precoOfertaFidelidade < novoPreco){
      if(itemPreco.dataFinalOfertaFidelidade){
        let dataInicialOfertaFidelidade: any = moment(itemPreco.dataInicialOfertaFidelidade, 'DD/MM/YYYY').startOf('d');
        let dataFinalOfertaFidelidade: any =  moment(itemPreco.dataFinalOfertaFidelidade, 'DD/MM/YYYY').startOf('d');

        if(moment().startOf('d').
        isBetween(dataInicialOfertaFidelidade, dataFinalOfertaFidelidade, null, "[]")){
          novoPreco = itemPreco.precoOfertaFidelidade
        }
      } else {
        novoPreco = itemPreco.precoOfertaFidelidade
      }

    }

    if(itemPreco.precoFidelidade && itemPreco.precoFidelidade < novoPreco){
      if(itemPreco.dataFinalPrecoFidelidade){
        let dataInicialPrecoFidelidade: any = moment(itemPreco.dataInicialPrecoFidelidade, 'DD/MM/YYYY').startOf('d');
        let dataFinalPrecoFidelidade: any =  moment(itemPreco.dataFinalPrecoFidelidade, 'DD/MM/YYYY').startOf('d');

        if(moment().startOf('d').
        isBetween(dataInicialPrecoFidelidade, dataFinalPrecoFidelidade, null, "[]")){
          novoPreco = itemPreco.precoFidelidade
        }
      } else {
        novoPreco = itemPreco.precoFidelidade
      }
    }

    produto.preco = Number(  produto.preco.toFixed(2));

    if(novoPreco < produto.preco)
      produto.setPrecoPromocional( Number(  novoPreco.toFixed(2)))


  }

  static convertaParaProdutos(loja: number, produtosErp: any, cagetoriasEcomerce: any, estoques: any, precos: any) {
    let categorias: any = [];
    let produtos = [], naoCategorizados = [],  semPrecos: any = [], semEstoque: any = []

    for(let i = 0 ; i < produtosErp.length; i++){
      let produtoBS: any = produtosErp[i];
      let categoria: any = {};

    //  console.log(String(`Produto ${produtoBS.produtoKey} -> ${produtoBS.descricao}`))
      if(produtoBS.produtoKey === 116){ //parar aqui
         console.log(produtoBS)
      }

      let produto: any = new Produto(null, produtoBS.tituloDeEcommerce, -1, produtoBS.descricao);
      produto.codigoPdv = produtoBS.produtoKey.toString();

      if(produtoBS.embalagemKey === 'KG'){
        produto.tipoDeVenda = 'Peso';
        produto.incremento = 1;
        produto.valorInicial = 1;
        produto.unidadeMedida = { id: 1, nome: 'Quilo', sigla: 'Kg'} // depois usar UnidadeMedida.Kilograma
      }

      if(produtoBS.foto){
        produto.urlImagemExterna = produtoBS.foto.original;
        produto.linkImagem =  String(`${uuid()}.${produto.urlImagemExterna.split('.').pop()}`)
      }

      if(precos.length){
        let produtoPreco: any =  precos.find((item: any) => item.codigoPdv === produto.codigoPdv)

        if(produtoPreco){
          produto.preco = produtoPreco.preco;
          produto.novoPreco = produtoPreco.novoPreco;
          produto.percentualDesconto = produtoPreco.percentualDesconto;
        }

      } else {
        if(produtoBS.estoquePrecos){
          let estoquePreco: any =  produtoBS.estoquePrecos.find((estoque: any) => estoque.lojaKey ===  loja);

          if(estoquePreco)
            if(!estoquePreco.lojaPossuiControleEstoque || estoquePreco.saldoEstoqueOnline)
              BlueSoftProdutoUtils.setPrecoProduto(estoquePreco, produto);

        }
      }

      produto.disponibilidade =  EnumDisponibilidadeProduto.NaoDisponivel;

      if(estoques.length){
        let produtoEstoque: any =  estoques.find((item: any) => item.codigoPdv === produto.codigoPdv)

        if(produtoEstoque && produtoEstoque.disponibilidade === EnumDisponibilidadeProduto.SempreDisponivel){
          produto.disponibilidade = EnumDisponibilidadeProduto.SempreDisponivel;
        }

      } else {
        if(produtoBS.ativoNoEcommerce)
          produto.disponibilidade =  EnumDisponibilidadeProduto.SempreDisponivel

      }

      produtoBS.categorias.forEach((categoriaBS: any) => {
        let categoriaErp: any =    cagetoriasEcomerce.find((cat: any) =>   categoriaBS.categoriaVendaKey ===  cat.categoriaVendaKey);

        let categoriaPromokit = new Categoria( null, categoriaErp.descricao, null, [],
          categoriaErp.categoriaVendaKey.toString(), categoriaErp.nivel);

        categoriaPromokit.posicao = categoriaErp.posicao;

        if(categoriaErp.categoriaVendaPaiKey){
          let categoriaBSPai =  cagetoriasEcomerce.find((cat: any) =>   categoriaErp.categoriaVendaPaiKey ===  cat.categoriaVendaKey);
          categoriaPromokit.categoriaPai  = new Categoria( null, categoriaBSPai.descricao, null, [],
            categoriaBSPai.categoriaVendaKey.toString(), categoriaBSPai.nivel);

          categoriaPromokit.categoriaPai.posicao = categoriaBSPai.posicao;

        }

        if( categoriaErp.categoriaVendaKey ===  produtoBS.categoriasVendaKeys[0]){
          produto.categoria  = categoriaPromokit
        } else {
          produto.categoriasAcima.push(categoriaPromokit)
        }
      })

      //por hora so importa disponivel e com categoria
      if(produto.categoria  ){ //
        if(!categorias.find((cat: any) => cat.codigoPdv === produto.categoria.codigoPdv ))
          categorias.push(categoria)

        if(produto.preco >= 0){
          if( produto.disponibilidade ===  EnumDisponibilidadeProduto.SempreDisponivel){
            produtos.push(produto)
          } else {
            semEstoque.push(produto)
          }
        } else {
          semPrecos.push(produto)
        }
      } else{
        naoCategorizados.push(produtoBS)
      }
    }

    return produtos
  }
}
