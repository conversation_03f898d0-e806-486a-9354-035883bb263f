import {Produto} from "../../domain/Produto";
import {EnumDisponibilidadeProduto} from "../emun/EnumDisponibilidadeProduto";
import {AdicionalDeProdutoEscolhaSimples} from "../../domain/delivery/AdicionalDeProdutoEscolhaSimples";
import {OpcaoDeAdicionalDeProduto} from "../../domain/delivery/OpcaoDeAdicionalDeProduto";
import {DependenciaOpcaoDeAdicional} from "../../domain/delivery/DependenciaOpcaoDeAdicional";
import * as _ from 'underscore';
import {AdicionalDeProduto} from "../../domain/delivery/AdicionalDeProduto";
import {AdicionalDeProdutoMultiplaEscolha} from "../../domain/delivery/AdicionalDeProdutoMultiplaEscolha";
import {FormatadorUtils} from "../FormatadorUtils";
import {EnumTipoDeOrigem} from "../emun/EnumTipoDeOrigem";

const axios = require('axios');
const BEBIDAS = 'Bebidas', ACOMPANHAMENTO = 'Acompanhamento';
/*
  host teste:  'p7065383c1tst-store.occa.ocs.oraclecloud.com'
  config:
  {
    auth: {
      username: 'admin',
      password: 'admin'
    }}
    }
 */
export class ImportardorTrendFoods {
  _encode = 'utf-8'
  instance: any;
  appId = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI0ZmJmOTNlZC1hYjRhLTRiZTYtOTQ0MC00YjRlZmZiMDhiN2IiLCJpc3MiOiJhcHBsaWNhdGlvbkF1dGgiLCJleHAiOjE2NTIxOTQ1NzcsImlhdCI6MTYyMDY1ODU3N30=.qDlqPjiSxahIuEIoveny76BCLHL6bTk3NYkyLoCWhFU='
  login: any = { token: null, validade: null};
  baseURL = 'https://p7065383c1tst-admin.occa.ocs.oraclecloud.com/ccadmin/v1'
  unidade: string
  catalogo: number
  categoriaRoot: string;
  headersSite: any = { 'request-context': 'appId=cid-v1:cfa3aad2-a37c-43a1-ba94-37961f5a32a9'}
  hostSite: string
  siteParams: string;
  constructor(integracao: any) {
    if(integracao.unidadeChina){
      this.unidade = integracao.unidadeChina
      this.catalogo = 2;
      this.categoriaRoot = 'cib'
      this.hostSite = 'www.chinainbox.com.br'
      this.siteParams = 'siteUS'
    } else if(integracao.unidadeGendai){
      this.unidade = integracao.unidadeGendai
      this.catalogo = 3;
      this.categoriaRoot = 'gendai'
      this.hostSite = 'www.gendai.com.br'
      this.siteParams = '100001'
    }

    //teste homologação gcom
    if(integracao.sistema === 'gcom'){
      //this.hostSite = 'p7065383c1tst-store.occa.ocs.oraclecloud.com'
      this.categoriaRoot = integracao.unidadeGendai ? 'gendai-gcom' :  'cib-gcom';
    }

    this.headersSite = {
      'authority': this.hostSite
    }
    this.instance = axios.create({
      baseURL: this.baseURL
    });

  }
  facaLogin(): Promise<any>{
    return new Promise(  (resolve) => {
      if(this.login.token && this.login.validade.getTime() > new Date().getTime()){
        console.log('  login api ja relizado')
        return resolve('');
      }
      delete   this.instance.defaults.headers.common['Authorization'];

      console.log('fazer login api e pegar token')
      let inicio = new Date().getTime();
      this.instance.post('/login',
        "grant_type=client_credentials",
        { headers: {    'Content-Type': 'application/x-www-form-urlencoded',
            Authorization: "Bearer " + this.appId   }},


      ).then( (response: any) => {
        console.log(String(`Tempo para fazer login: ${new Date().getTime() - inicio}`))
        if(response.data.access_token){
          this.login.validade = new Date()
          this.login.validade.setSeconds(response.data.expires_in)
          this.login.token = response.data.access_token;

          this.instance.defaults.headers.common['Authorization'] = String(`Bearer ${this.login.token}`);
          //this.instance.defaults.headers.common['x-ccpricelistgroup'] = this.unidade

          resolve('');
        } else {
          resolve(response.data);
        }
      }).catch(   (result: any) => {
        let erro = result.response ? result.response.data : result.message;
        console.log(erro)
        resolve(erro)
      })
    })
  }

  async listeCategoriasSite(){
    return new Promise(async (resolve, reject) => {
      let inicio = new Date().getTime();
      console.log('Buscar categorias root china')

      if(true){//pegar direto site, nao lista alguns que sai da promo
        let url: string;
        url = String(`https://${this.hostSite}/ccstoreui/v1/collections/${this.categoriaRoot.trim()}?depth=4&maxLevel=3&expand=childCategories`);

        console.log(url)
        let   response = await  axios.get(url,
          { headers: this.headersSite}) .catch( (result: any) => {
          console.log('Erro  buscar combos: ' + this.unidade)
          console.log(result.response.data)
        })
        let categoriasAtivas = response.data.childCategories.filter( (cat: any) => cat.active)
        let categorias: any = [];

        // esta listando categorias repetidas com mesmo nome, mais ids diferentes
        for(let i = 0 ; i < categoriasAtivas.length; i++){
          if(!categorias.find( (cat: any) => cat.displayName === categoriasAtivas[i].displayName))
            categorias.push(categoriasAtivas[i])
        }


        resolve(categorias  );
      } else {
        await this.facaLogin();
        this.instance.get(String(`/collections/${this.categoriaRoot}?depth=4&maxLevel=3&expand=childCategories`))
          .then( (response: any) => {
            console.log(String(`Tempo para buscar categorias: ${new Date().getTime() - inicio}`))
            let categorias = response.data.childCategories.filter( (cat: any) => cat.active)

            resolve(categorias  )

          }).catch((result: any) => {
          reject(result.response ? result.response.data : result)
        })
      }


    })
  }

  async listeProdutosOracle(categoriaChina: any){
    return new Promise(async (resolve, reject) => {
      await this.facaLogin();
      let path = '/products?categoryId=' + categoriaChina.id + '&expand=true&catalogId=2';

      this.instance.get(path)
        .then( (response: any) => {
          let produtos = response.data.items;

          resolve(produtos  )

        }).catch((result: any) => {
        reject(result.response ? result.response.data : result)
      })
    });


  }

  async listeCategorias(){
    return new Promise(async (resolve, reject) => {
      let inicio = new Date().getTime();
      console.log('Buscar categorias api china')
      await this.facaLogin();
      this.instance.get('/collections').then( (response: any) => {
        console.log(String(`Tempo para buscar categorias: ${new Date().getTime() - inicio}`))
        let categorias = response.data.items

        resolve(categorias  )

      }).catch((result: any) => {
        reject(result.response.data)
      })

    })
  }

  private obtenhaPromocao(id: string){
    return new Promise(async (resolve, reject) => {
      let inicio = new Date().getTime();
      console.log('buscar promo -> ' + id)
      return this.instance.get('/promotions/' + id).then( (response: any) => {
        console.log(String(`Tempo para promo ${id} : ${new Date().getTime() - inicio}`))
        resolve( response.data)
      }).catch((result: any) => {
        console.log( result.response ? result.response.data : result.message)
        resolve(null)
      })

    })

  }

  async listeLojas(){
    return new Promise(async (resolve, reject) => {
      await this.facaLogin();
      this.instance.get('/locales').then( async (response: any) => {
        resolve(response.data)
      }).catch((result: any) => {
        reject( result.response ? result.response.data : result.message)
      })
    });

  }

  async listePromocoes() {
    return new Promise(async (resolve, reject) => {
      await this.facaLogin();
      let inicio = new Date().getTime();
      console.log('Buscar promocoes api china')
      this.instance.get('/promotions').then( async (response: any) => {
        console.log(String(`Tempo para buscar promocoes: ${new Date().getTime() - inicio}`))
        let promocoes = response.data.items

        console.log('Total promoçoes retornadas: ' + promocoes.length)

        promocoes = promocoes.filter((promo: any) => promo.enabled)

        console.log('Total promoçoes ativas: ' + promocoes.length)

        let promisses =  promocoes.map( (promo: any) => this.obtenhaPromocao(promo.id));

        Promise.all(promisses).then((resposta) => {
          console.log(String(`Tempo montar promocoes: ${new Date().getTime() - inicio}`))
          resolve(resposta  )
        }).catch((result: any) => {
          reject(result)
        })

      }).catch((result: any) => {
        reject( result.response ? result.response.data : result.message)
      })
    })

  }

  async obtenhaCombo(idPromocao: any){
    let url =
      String(`https://${this.hostSite}/ccstorex/custom/v1/integracao/combo/${idPromocao}`);
    let combos: any = [];
    console.log('url busca de combo  ' +  idPromocao)
    console.log(url)
    let   response = await  axios.get(url,
      { headers: this.headersSite}) .catch( (result: any) => {
      console.log('Erro  buscar combos: ' + this.unidade)
      console.log(result.response.data)
    })

    if(response && response.data && response.data.items     )
      return response.data.items
  }

  async obtenhaCombosUnidade(todasPromocoes: any = []){
    let url =
      String(`https://${this.hostSite}/ccstorex/custom/v1/integracao/combo/list?site=${this.siteParams}&pricegroup=${this.unidade}`);
    let combos: any = [];
    console.log('url busca de combos habilitadas na unidade ' +   this.unidade)
    console.log(url)
    let   response = await  axios.get(url,
      { headers: this.headersSite}) .catch( (result: any) => {
      console.log('Erro  buscar combos: ' + this.unidade)
      console.log(result.response.data)
    })

    if(response.data  &&  Array.isArray(response.data.items)    )
      combos = response.data.items
    else{
      console.log('Retorno nao esperado: ')
      console.log( response.data ? response.data : response.messageText)
    }

    return combos
  }

  async obtenhaProdutosDaCategoria(categoriaChina: any){
    let path = '/products?categoryId=' + categoriaChina.id + '&expand=true&catalogId=2';
    let response: any;
    return new Promise(  async (resolve, reject) => {
      response = await  axios.get(String(`https://${this.hostSite}/ccstoreui/v1`) + path,
        { headers: this.headersSite}) .catch( (result: any) => {
        let erro = String(`Erro buscar categoria "${ categoriaChina.displayName}"`);
        if( result.message)
          erro  = String(`${erro}: ${result.message}`);

        if( result.response && result.response.data){
          if( result.response.data.message)
            erro  = String(`${erro}: ${result.response.data.message}`);
          else
            erro  = String(`${erro}: ${result.response.data}`);
        }

        reject(erro)
      })

      if(response) resolve(response.data.items)
    });


  }

  async obtenhaProduto(id: any){
    let path = '/products/' + id + '?expand=true';
    await this.facaLogin();
    console.log(path)
    let   response = await this.instance.get(path, {})
      .catch( (result: any) => {
        console.log('Erro  produto: ' + id)
        console.log(result.response ? result.response.data : result.message)
      })

    return response && response.data ?  response.data : {};
  }

  async obtenhaSku(sku: string){
    await this.facaLogin();

    console.log('skus')
    let   response = await this.instance.get('/skus/' + sku, {})
      .catch( (resulte: any) => {
        console.log('Erro  skus: ' + this.unidade)
        console.log(resulte.response ? resulte.response.data : resulte.message)
      })

    return response && response.data ?  response.data : {};
  }
  async obtenhaSkus(codigos: any = []){
    await this.facaLogin();
    let params: any = {}
    console.log('skus')
    if(codigos.length) params.skuIds = codigos.join(',')
    let   response = await this.instance.get('/skus', { params: params})
      .catch( (result: any) => {
        console.log('Erro  skus: ' + this.unidade)
        console.log(result.response ? result.response.data : result.message)
      })

    return response && response.data ?  response.data.items : [];
  }

  async obtenhaApenasProdutosComPrecos(produtosDaRede: any, produtosComSku: any){
    let produtosComPreco: any = [];
    let produtosCombosConverter = produtosDaRede.filter( (produto: any) => produto.combo);
    let produtosImportar = produtosDaRede.filter( (produto: any) => !produto.combo);

    produtosImportar.forEach( (produto: any) => {
      let produtoCopia =  Object.assign( new Produto(), produto);
      produtoCopia.camposAdicionais = [];
      produtoCopia.categoria = Object.assign({}, produto.categoria);

      let produtoComPreco = false;
      let temObrigatorioSemPreco = false;

      if(produtoCopia.listPrices){
        this.setPrecoVendaProduto(produtoCopia, produto, produtosComSku);
        if(produtoCopia.preco >  0)
          produtoComPreco = true;
      }

      if(produtoCopia.preco  != null ){
        produto.camposAdicionais.forEach( (adicional: any) => {
          let adicionalCopiado = Object.assign({}, adicional);
          let opcoes = adicional.opcoesDisponiveis;
          adicionalCopiado.opcoesDisponiveis = [];

          opcoes.forEach( (opcao: any) => {
            let opcaoCopiada = Object.assign({}, opcao);

            if(!opcaoCopiada.listPrices)
              throw String(`"${produto.codigoPdv} - ${produto.nome}", "${adicionalCopiado.nome}" tem uma opção sem lista de preços: "${opcaoCopiada.codigoPdv} - ${opcaoCopiada.nome}"`)

            this.setPrecoVendaOpcao(opcaoCopiada, opcao, produtosComSku)

            adicionalCopiado.opcoesDisponiveis.push(opcaoCopiada)
          })

          adicionalCopiado.opcoesDisponiveis =   adicionalCopiado.opcoesDisponiveis.filter( (opcao: any) => {
            let temValor = opcao.valor > 0 || opcao.valor === 0 ; // opcao.valor >=0 não funciona :(
            return temValor;
          });

          if(adicionalCopiado.obrigatorio && adicionalCopiado.opcoesDisponiveis.length)
            produtoComPreco = true;

          if(adicionalCopiado.obrigatorio && !adicionalCopiado.opcoesDisponiveis.length)
            temObrigatorioSemPreco = true;

          produtoCopia.camposAdicionais.push(adicionalCopiado)
        })

        if(produtoComPreco && !temObrigatorioSemPreco)
          produtosComPreco.push(produtoCopia)

      }
    })

    if(produtosCombosConverter.length){
      let todasPromocoes = produtosCombosConverter.map( (produto: any) => produto.combo);

      todasPromocoes = _.uniq(todasPromocoes)

      let combosChina = await this.obtenhaCombosUnidade(todasPromocoes),
        produtosCombos: any = [];

      let skusNaoListadosSite: any = [];

      combosChina.forEach((combo: any) => {
        if(combo &&  combo.condition) {
          combo.condition.includedSkus.forEach((skus: any) => {
            skus.forEach((sku: string) => {
              if (!produtosComSku[sku] && skusNaoListadosSite.indexOf(sku) === -1)
                skusNaoListadosSite.push(sku)
            })
          })
        }
      })

      if(skusNaoListadosSite.length){
        let produtosDeCombo = await this.obtenhaSkus(skusNaoListadosSite);

        produtosDeCombo.forEach((produtoSku: any) => {
          this.setProdutoComSku(produtoSku, produtosComSku)
        })
      }

      for(let i = 0; i < produtosCombosConverter.length; i++){
        let produto = produtosCombosConverter[i];

        let produtoComboCopia = Object.assign( new Produto(), produto);

        produtoComboCopia.camposAdicionais = [];
        produtoComboCopia.categoria = Object.assign({}, produto.categoria);
        let dadosCombo = produto.combo;

        await this.monteAdicionaisDoCombo(produtoComboCopia, dadosCombo.childSKUs[0], dadosCombo.idPromocao, combosChina, produtosComSku)

        if(produtoComboCopia.camposAdicionais.length)
          produtosCombos.push(produtoComboCopia)

      }


      produtosCombos.forEach((produtoCombo: any) => {

        produtoCombo.camposAdicionais.forEach( (adicional: any) => {
          adicional.opcoesDisponiveis =   adicional.opcoesDisponiveis.filter( (opcao: any) => {
            return  opcao.nome !== ""
          });
        })

        if(produtoCombo.temEstoque )
          produtosComPreco.push(produtoCombo)
      })
    }

    return produtosComPreco;
  }

  obtenhaProdutos(){
    return new Promise(async (resolve, reject) => {
      let  erroLogin = await this.facaLogin();
      if(erroLogin) return reject(erroLogin)
      // [{id: 35, nome : '2 Por R$36,90'}] //
      let categorias: any = await this.listeCategoriasSite().catch( (erro) => reject(erro))

      if(categorias && categorias.length){
        let itens: any = [], combos: any = []
        let promisses = [], erros: any = []

        if(this.categoriaRoot === 'cib-gcom' || this.categoriaRoot === 'gendai-gcom'){
          categorias.splice(0, 0, {id: '8999', displayName: 'OCULTOS GCOM' , oculta: true })
        } else {
          categorias.splice(0, 0, {id: '999', displayName: 'OCULTOS' , oculta: true });
          categorias.splice(0, 0, {id: '998', displayName: 'OCULTOS' , oculta: true });
        }

        if(this.categoriaRoot.indexOf('gendai') >= 0 )
          categorias.splice(0, 0, {id: '910001', displayName: 'BRINDES GENDAI' , oculta: true })


        for(let i = 0; i < categorias.length; i++){
          promisses.push(new Promise(async (resolveProduto) => {
            let categoriaChina = categorias[i];
            console.log(String(`Buscar produtos api china categoria: ${categoriaChina.id} - ${categoriaChina.displayName} `)  )
            let inicio = new Date().getTime();
            let produtosDaCategoria: any =  await this.obtenhaProdutosDaCategoria(categoriaChina).catch( erro => {
              erros.push(erro)
            })

            if(produtosDaCategoria){
              console.log(String(`Tempo para buscar produtos: ${new Date().getTime() - inicio}`))
              produtosDaCategoria.forEach( (item: any) => {
                item.category = categoriaChina;
                itens.push(item);
              })
            }

            resolveProduto('')
          }))
        }

        await  Promise.all(promisses);

        if(erros.length) return reject(erros.join(', '))

        try{
          let resposta: any =  await  this.convertaProdutos(itens, categorias, combos);
          resolve(resposta)
        } catch (erro){
          console.log(erro)
          reject(erro.message)
        }
      }

    })
  }

  private async facaBindProdutoDireto(produto: Produto, produtoSKU: any, produtosComSku: any){
    produto.codigoPdv = produtoSKU.repositoryId
    produto.nome = produtoSKU.displayName // será pega nome do sku???

    this.setListaPrecos(produto, produtoSKU);
    this.setProdutoComSku(produtoSKU, produtosComSku)

    produto.disponibilidade = produtoSKU.active ? EnumDisponibilidadeProduto.SempreDisponivel :
      EnumDisponibilidadeProduto.NaoDisponivel;

    let ordemAdicionais =  1;

    await this.monteOsAdicionaisAddOn(produto, produtoSKU, ordemAdicionais, produtosComSku)

    this.adicioneAcompanhamentosEBebidas(produto, produtoSKU, ordemAdicionais)
  }

  private setPrecoVendaOpcao(opcao: any, produtoChina: any , produtosComSku: any){
    let preco =  produtoChina.listPrices[this.unidade];
    if(produtoChina.salePrices[this.unidade] != null)
      preco = produtoChina.salePrices[this.unidade];

    opcao.valor = preco;

    delete opcao.listPrices
    delete opcao.salePrices
    delete opcao.faixaPreco


    if( produtosComSku[opcao.codigoPdv] &&  !produtosComSku[opcao.codigoPdv].preco){
      produtosComSku[opcao.codigoPdv].nome = opcao.nome;
      produtosComSku[opcao.codigoPdv].preco = preco;
    }

  }

  private setPrecoVendaProduto(produto: any, produtoSKU: any , produtosComSku: any){
    let preco = produtoSKU.listPrices[this.unidade];
    let precoVenda = produtoSKU.salePrices[this.unidade];

    if(preco != null || precoVenda != null){
      produto.preco = preco;
      if( precoVenda != null){
        produto.novoPreco = precoVenda;
        produto.percentualDesconto = Number((  100 - (precoVenda / produto.preco ) * 100 ));
      }
      if(produto.preco  > 0)
        this.setProdutoComSku(produto, produtosComSku)

      delete produto.listPrices
      delete produto.salePrices
      delete produto.faixaPreco

    } else {
      produto.preco = null;
      //console.log('Produto sem preço: ' + (produtoSKU.displayName || produtoSKU.nome))
    }
  }

  private async convertaProdutosCombos(){}

  private async convertaProdutos(produtosChinna: any, categoriasChinna: any, combosChina: Array<any>) {
    let produtos: any = [], produtosComSku: any = {}, categorias: any = [],   promocoes = []

    for(let i = 0 ; i < categoriasChinna.length; i++){
      let categoriaChinna = categoriasChinna[i];

      let categoria: any = { nome:  categoriaChinna.displayName, codigoPdv:  categoriaChinna.id.toString(), posicao: i + 1 };

      categorias.push(categoria)

      let itensDaCategoria =   produtosChinna.filter((prod: any) => prod.category.id === categoriaChinna.id)

      for(let j = 0 ; j < itensDaCategoria.length; j++){
        let item = itensDaCategoria[j];
        let descricao = item.longDescription || item.description || '';

        let produto: any = new Produto(null, item.displayName, 0, descricao, null, null, null,
          false,  EnumDisponibilidadeProduto.NaoDisponivel, false, categoria);

        produto.codigoPdv = item.repositoryId.toString();
        produto.origem = EnumTipoDeOrigem.ImportadoChinaInBox
        produto.dataCadastro = new Date()

        if(produto.codigoPdv === '9100000180'){
          console.log(produto)
        }

        let ordemAdicionais = 1;

        if(item.primaryFullImageURL){
          produto.urlImagemExterna = String(`https://${this.hostSite}${item.primaryFullImageURL}`)
          produto.linkImagem = item.primaryFullImageURL;
        }

        if(item.childSKUs){
          if(item.type === 'TrendfoodsMonteSeuCombo'){
            produto.combo =  { idPromocao:  item.c4_promotion, childSKUs: item.childSKUs}
            produtos.push(produto) // combos serão montadas depois

          } else if(item.childSKUs.length === 1){ // criar como produto direto, sem ser por adicional.
            if(item.addOnProducts && item.addOnProducts.length)
              item.childSKUs[0].addOnProducts = item.addOnProducts

            await this.facaBindProdutoDireto(produto, item.childSKUs[0], produtosComSku)
          } else { // vai virar adicional os produtos
            let adicionalPrincipal = new AdicionalDeProdutoEscolhaSimples(item.displayName, true, [], 'produto');
            adicionalPrincipal.ordem = ordemAdicionais;
            produto.camposAdicionais.push(adicionalPrincipal)
            ordemAdicionais++;

            await this.monteAdicionaisComSku(produto, adicionalPrincipal, item, ordemAdicionais, produtosComSku)

            produto.disponibilidade = adicionalPrincipal.opcoesDisponiveis.length ? EnumDisponibilidadeProduto.SempreDisponivel :
              EnumDisponibilidadeProduto.NaoDisponivel;
          }
        }

        if(item.relatedProducts && item.relatedProducts.length){
          // console.log('aqui  novo tipo de adicional????')
        }

        produto.temEstoque = produto.disponibilidade !== EnumDisponibilidadeProduto.NaoDisponivel

        if( produto.temEstoque && !categoriaChinna.oculta)
          produtos.push(produto)

      }
    }

    //acompanhamentos e bebidas vem aqui
    this.preenchaAsReferenciasAdicionais(produtos, produtosComSku)

    return { produtos: produtos, produtosComSku: produtosComSku};
  }

  private async monteOsAdicionaisAddOn(produto: Produto, item: any, ordemAdicionais: any, produtosComSku: any){
    //adicionais de todos produtos criados vem  aqui
    if(item.addOnProducts && item.addOnProducts.length){
      for(let i = 0; i <   item.addOnProducts.length; i++){
        let addOnProduct: any = item.addOnProducts[i];
        let adicionaisMap: any =  _.groupBy( addOnProduct.addOnOptions,  (option: any) => option.product.repositoryId)

        let chaves =   Object.keys(adicionaisMap);

        for(let j = 0; j <   chaves.length; j++){
          let idProduto: string = chaves[j];
          let options: Array<any> = adicionaisMap[idProduto];
          let nomeAdicional  = options[0].product.displayName;

          let adicionalSimples: any  = new AdicionalDeProdutoEscolhaSimples(    nomeAdicional, true, [], 'produto');

          let adicionalExtraComValor: any  =
            new AdicionalDeProdutoMultiplaEscolha(    nomeAdicional + ' Adicionais', false, [], 0, 10, true);


          for(let option of options){
            let opcao: any  = new OpcaoDeAdicionalDeProduto(option.sku.displayName, null, option.sku.active);
            opcao.codigoPdv = option.sku.repositoryId

            let produtoComSku = produtosComSku[opcao.codigoPdv];

            if(!produtoComSku){
              let produtoChina: any = await this.obtenhaProduto(idProduto)
              if(produtoChina.childSKUs){
                for(let produtoChildSKU of produtoChina.childSKUs) {
                  if(! produtosComSku[produtoChildSKU.repositoryId]) //nem todos skus do produto vao entrar
                    this.setProdutoComSku(produtoChildSKU, produtosComSku)

                  if(produtoChildSKU.repositoryId === opcao.codigoPdv)
                    produtoComSku = produtoChildSKU
                }
              } else {
                console.log('Nao veio produtoChina.childSKUs')
                console.log(produtoChina)
              }

            }

            this.setListaPrecos(opcao, produtoComSku)

            if(this.temValorVinculado(opcao)){
              adicionalExtraComValor.opcoesDisponiveis.push(opcao);
            } else {
              adicionalSimples.opcoesDisponiveis.push(opcao);
            }
          }

          if(adicionalSimples.opcoesDisponiveis.length){
            adicionalSimples.ordem = ordemAdicionais++;
            produto.camposAdicionais.push(adicionalSimples)
          }

          if(adicionalExtraComValor.opcoesDisponiveis.length){
            adicionalExtraComValor.ordem = ordemAdicionais++;
            produto.camposAdicionais.push(adicionalExtraComValor)
          }
        }

      }
    }
  }

  private temValorVinculado(opcao: any){
    let keys = Object.keys(opcao.listPrices)

    return keys.find( key => opcao.listPrices[key] > 0 )
  }

  private setProdutoComSku(item: any, produtosComSku: any){
    let sku = item.repositoryId || item.codigoPdv;

    produtosComSku[sku] = item;
  }

  private async monteAdicionaisComSku(produto: Produto, adicionalPrincipal: AdicionalDeProduto, item: any,
                                      ordemAdicionais: any,   produtosComSku: any) {


    for(let produtoSKU of item.childSKUs) {
      //  if(produtoSKU.derivedSalePriceFrom !== unidadeChinna)
      //   throw Error(String(`Unidade chinna retornada não é a esperada: ` + produtoSKU.derivedSalePriceFrom))
      //item.productVariantOptions[0].optionName pode pegar o nome aqui? da variante

      if(produtoSKU.active){
        let opcao = new OpcaoDeAdicionalDeProduto(produtoSKU.displayName, null, true, produtoSKU.description);

        opcao.codigoPdv = produtoSKU.repositoryId.toString();

        this.setListaPrecos(opcao, produtoSKU);
        if(!produtosComSku[opcao.codigoPdv])
          this.setProdutoComSku(produtoSKU, produtosComSku)

        if(produtoSKU.c4_escolha)
          opcao.descricao = produtoSKU.c4_escolha

        adicionalPrincipal.opcoesDisponiveis.push(opcao);
      } else {
        console.log('Produto desativado: ' + produtoSKU.displayName)
      }
    }

    if(adicionalPrincipal.opcoesDisponiveis.length){
      await this.monteOsAdicionaisAddOn(produto, item, ordemAdicionais, produtosComSku)

      adicionalPrincipal.opcoesDisponiveis.forEach((opcao: any) => {
        let produtoSKU = item.childSKUs.find((child: any) => child.repositoryId.toString() === opcao.codigoPdv)

        if(produtoSKU)
          this.adicioneAcompanhamentosEBebidas(produto, produtoSKU, ordemAdicionais, opcao)
      })

    }
  }

  private adicioneAcompanhamentosEBebidas(produto: Produto, produtoSKU: any, ordemAdicionais: any,
                                          opcaoDependente: any = null) {
    if(produtoSKU.dynamicPropertyMapBigString && Object.keys(produtoSKU.dynamicPropertyMapBigString).length){
      let adicionalBebidas,   adicionalAcompanhamento;
      let keys = Object.keys(produtoSKU.dynamicPropertyMapBigString);

      for(let key of keys){
        let bebidas = key.endsWith('Drinks');
        let adicionalSugestao: any;

        if(bebidas){
          adicionalBebidas = produto.camposAdicionais.find( adicional => adicional.nome === BEBIDAS)

          if(!adicionalBebidas) {
            adicionalBebidas = new AdicionalDeProdutoMultiplaEscolha(    BEBIDAS, false, [], 0, 10, true);
            adicionalBebidas.ordem = 2;
            ordemAdicionais++
            produto.camposAdicionais.push(adicionalBebidas)
          }
          adicionalSugestao = adicionalBebidas;

        } else {
          adicionalAcompanhamento = produto.camposAdicionais.find( adicional => adicional.nome === ACOMPANHAMENTO)

          if(!adicionalAcompanhamento){
            adicionalAcompanhamento = new AdicionalDeProdutoMultiplaEscolha(ACOMPANHAMENTO, false, [], 0, 10, true);
            adicionalAcompanhamento.ordem = 3;
            ordemAdicionais++
            produto.camposAdicionais.push(adicionalAcompanhamento)
          }
          adicionalSugestao = adicionalAcompanhamento;
        }

        let codigosPdvs = produtoSKU.dynamicPropertyMapBigString[key].split(',');

        codigosPdvs.forEach((codigo: any) => {
          if(codigo.match(/\d+/)){
            let codigoPdv =  codigo.match(/\d+/)[0];

            let opcaoDaSugestao: OpcaoDeAdicionalDeProduto =
              adicionalSugestao.opcoesDisponiveis.find((opcaoAdicionada: any) => opcaoAdicionada.codigoPdv === codigoPdv);

            if( !opcaoDaSugestao ){
              // pegar depois os dados na lista final de produtos
              opcaoDaSugestao = new OpcaoDeAdicionalDeProduto("", -1, true);
              opcaoDaSugestao.codigoPdv = codigoPdv
              adicionalSugestao.opcoesDisponiveis.push(opcaoDaSugestao);
            }

            if(opcaoDependente)
              opcaoDaSugestao.dependencias.push(new DependenciaOpcaoDeAdicional({}, opcaoDependente))
          } else {
            console.log(String(`codigo sku inválido: ${codigo}`));
          }
        })

        adicionalSugestao.qtdeMaxima =  adicionalSugestao.opcoesDisponiveis.length * 2;
      }
    }
  }

  private setListaPrecos(item: any, produtoSku: any){
    item.listPrices = produtoSku.listPrices;
    item.salePrices = produtoSku.salePrices;

    let menorPreco, maiorPreco;

    for(let unidade of  Object.keys(produtoSku.listPrices)){
      let preco =  produtoSku.listPrices[unidade];
      let precoVenda =  produtoSku.salePrices[unidade];

      if(!menorPreco) menorPreco = preco;
      if(!maiorPreco) maiorPreco = preco;

      if(preco < menorPreco)  menorPreco = preco;
      if(precoVenda && precoVenda < menorPreco)  menorPreco = precoVenda;

      if(preco > maiorPreco) maiorPreco = preco;
      if(precoVenda && precoVenda > maiorPreco) maiorPreco = precoVenda
    }

    if(menorPreco && maiorPreco)
      item.faixaPreco = String(`${FormatadorUtils.numeroParaCurrency(menorPreco)} até  ${FormatadorUtils.numeroParaCurrency(maiorPreco)}`)

  }


  private preenchaAsReferenciasAdicionais(produtos: any, produtosComSku: any) {
    //percorrer produtos e setar preços das bebeidas e acompanhamentos
    produtos.forEach( (produto: Produto) => {

      produto.camposAdicionais.forEach( (adicionalOpcional: any) => {
        let opcoesNaoDiponiveisRemover: any = []

        for(let i = 0 ; i <  adicionalOpcional.opcoesDisponiveis.length; i++){
          let opcao: any = adicionalOpcional.opcoesDisponiveis[i];

          if(opcao.nome === ""){
            let produtoChina = produtosComSku[opcao.codigoPdv]

            if(produtoChina){
              opcao.nome = produtoChina.displayName ;
              if(opcao.valor === -1){
                this.setListaPrecos(opcao, produtoChina)
              }
            } else{
              console.log(String(`Não achou adicional:  ${ produto.nome}(${adicionalOpcional.nome}) -> ${ opcao.codigoPdv}`))
              opcoesNaoDiponiveisRemover.push(opcao.codigoPdv)
            }
          }
        }

        if(opcoesNaoDiponiveisRemover.length){
          adicionalOpcional.opcoesDisponiveis =
            adicionalOpcional.opcoesDisponiveis.filter( (opcao: any) => opcoesNaoDiponiveisRemover.indexOf(opcao.codigoPdv) === -1 )

          console.log('total removido: ' + opcoesNaoDiponiveisRemover.length)
        }
      })

    })
  }

  private async monteAdicionaisDoCombo(produto: Produto, produtoSKU: any, idPromocao: string, combosChina: any, produtosComSku: any) {

    let monteCombo =   (comboChina: any) => {
      produto.preco =  0;
      produto.naoAceitaCupom = true;
      produto.disponibilidade = produtoSKU.active ? EnumDisponibilidadeProduto.SempreDisponivel :
        EnumDisponibilidadeProduto.NaoDisponivel;

      let includedSkus = comboChina.condition.includedSkus;

      console.log('Item de promoção: ' + idPromocao)
      let percentualDesconto: number;
      let valorFixo: number;

      if(comboChina.discount_type_value === 'percentOff'){
        percentualDesconto = Number(comboChina.discount_value) / 100;
      } else if(comboChina.discount_type_value === 'fixedPrice'){
        valorFixo = Number(comboChina.discount_value);
      } else {
        console.log('Tipo de desconto nao implementado: ' + idPromocao)
      }

      if(this.temMesmosItens(includedSkus)){
        this.crieComboComoAdicionalMultiplo(produto, includedSkus[0],  includedSkus.length, produtosComSku, valorFixo, percentualDesconto)
      } else {
        this.crieComboComoAdicionaisSimples(produto, includedSkus, produtosComSku, valorFixo, percentualDesconto)
      }
    }

    let _comboChina = combosChina.find((_combo: any) => _combo && _combo.id === idPromocao);

    if(_comboChina ){
      monteCombo(_comboChina)
    }  else {
      let combo = await this.obtenhaCombo(idPromocao.toString())

      if(combo)
        monteCombo(combo)
    }
  }

  private crieComboComoAdicionaisSimples(produto: any, listaSkus: any,   produtosComSku: any,
                                         precoFixo: number, percentualDesconto: number){
    let ordem = 1, adicionais: any = [];


    for(let i = 0; i < listaSkus.length; i++ ){
      let skus = listaSkus[i];

      let adicionalCombo =
        new AdicionalDeProdutoEscolhaSimples(    String(`Confirme seu ${ordem}° item`), true,   []);

      let produtoComPrecoOriginal: any;

      skus.forEach( (sku: string) => {
        let produtoSku = produtosComSku[sku];
        if(produtoSku && (precoFixo || produtoSku.preco > 0) ) {
          let nome = produtoSku.nome || produtoSku.displayName;

          let opcao: any = new OpcaoDeAdicionalDeProduto(nome, 0, true);
          opcao.codigoPdv = sku

          if (percentualDesconto) {
            let valorDesconto = produtoSku.preco * percentualDesconto;
            opcao.valor = Number(produtoSku.preco - valorDesconto).toFixed(2)
          } else {
            opcao.valor =  Number(i === 0 ? precoFixo : 0).toFixed(2);
          }
          adicionalCombo.opcoesDisponiveis.push(opcao);
          produtoComPrecoOriginal = produtoSku;
        } else {
          console.log('Produtos não encontrado: ' + sku)
        }
      })

      if(adicionalCombo.opcoesDisponiveis.length){
        adicionalCombo.ordem = ordem++;
        adicionais.push(adicionalCombo)
      } else {
        console.log('Combo não tem adicionais disponveis: ' + skus.join(', '))
      }
    }

    if(adicionais.length === listaSkus.length){ // todas os adicionais do combo disponiveis
      produto.temEstoque = produto.disponibilidade !== EnumDisponibilidadeProduto.NaoDisponivel
      produto.camposAdicionais = adicionais;
    }

  }

  private crieComboComoAdicionalMultiplo(produto: any, listaSkus: any, qtdeMaximaEMimina: any, produtosComSku: any,
                                         precoFixo: number, percentualDesconto: number = null){

    let adicionalCombo =
      new AdicionalDeProdutoMultiplaEscolha(    'Confirme seus itens', true, [], qtdeMaximaEMimina, qtdeMaximaEMimina, true);



    listaSkus.forEach( (sku: string) => {
      let produtoSku = produtosComSku[sku];
      let preco: number; let nome: string;

      if(produtoSku){
        preco = produtoSku.preco >= 0 ? produtoSku.preco :  produtoSku.listPrice;
        nome = produtoSku.nome ? produtoSku.nome : produtoSku.displayName;

        //todo: correção parcial: alguns produtos da combo nao sao processados os preços
        if(preco == null){
          // produtos combos vem codigo sku add um 22 ou 33 na frente do sku
          if((sku.startsWith('22') || sku.startsWith('33'))){
            let produtoSkuComPreço = produtosComSku[sku.substring(2)]

            if(produtoSkuComPreço){
              preco = produtoSkuComPreço.preco >= 0 ? produtoSkuComPreço.preco : produtoSkuComPreço.listPrice;
              nome = produtoSkuComPreço.nome ? produtoSkuComPreço.nome : produtoSkuComPreço.displayName;
            }
          }
        }

        if(preco >= 0 && nome != null) {
          let opcao: any = new OpcaoDeAdicionalDeProduto(nome, -1, true);
          opcao.codigoPdv = sku

          if (percentualDesconto && preco) {
            let valorDesconto = preco * percentualDesconto;
            opcao.valor = Number(preco - valorDesconto).toFixed(2)
          } else {
            opcao.valor =  Number((precoFixo / qtdeMaximaEMimina).toFixed(2));
          }

          if(opcao.valor >= 0 )
            adicionalCombo.opcoesDisponiveis.push(opcao);
        }
      }
    })

    if(adicionalCombo.opcoesDisponiveis.length){
      adicionalCombo.ordem = 1;
      produto.temEstoque = produto.disponibilidade !== EnumDisponibilidadeProduto.NaoDisponivel
      produto.camposAdicionais.push(adicionalCombo)
    }
  }

  private temMesmosItens(listaSkus: any){
    let tem = true;

    for(let i = 0;  i < listaSkus.length - 1; i++){
      for(let j = i + 1; j < listaSkus.length; j++){
        if(listaSkus[i].length === listaSkus[j].length){
          for(let k = 0; k < listaSkus[i].length; k++){
            if(listaSkus[i][k] !== listaSkus[j][k] ){
              tem = false;
            }
          }
        } else {
          tem = false;
        }
      }
    }
    return tem;

  }
}
