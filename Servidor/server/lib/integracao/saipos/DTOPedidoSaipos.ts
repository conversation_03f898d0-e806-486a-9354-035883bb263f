import {Pedido} from "../../../domain/delivery/Pedido";
import {ItemPedido} from "../../../domain/delivery/ItemPedido";
import {PagamentoPedido} from "../../../domain/delivery/PagamentoPedido";
import {
  ItemPedidoIntegradoUtils, ProdutoExtraPedidoIntegrado,
} from "../ItemPedidoIntegradoUtils";
import * as moment from "moment";

export class DTOPedidoDadosSaipos{
  items: Array<any> = []
  order_id: any;
  display_id: any;
  cod_store: string;
  created_at: string;
  notes: string
  total_discount: number;
  total_amount: number;
  customer: any
  order_method: any;

  protected obtenhaDTOItemExtra(itemProdutoExtra: ProdutoExtraPedidoIntegrado){
    let dtoItem: DTOItem = new DTOItem();

    dtoItem.integration_code = itemProdutoExtra.codigoPdv
    dtoItem.desc_item = itemProdutoExtra.descricao;
    dtoItem.quantity = itemProdutoExtra.qtde;
    dtoItem.unit_price = itemProdutoExtra.valorProduto; // nao pega valor total
    dtoItem.notes = itemProdutoExtra.obs || ''
    dtoItem.choice_items = [];

    this.items.push(dtoItem);

  }

  protected obtenhaDTOItens(itemPedido: ItemPedido) {
    let dtoItem: DTOItem = new DTOItem();

    let dtoItemPedido: ItemPedidoIntegradoUtils = new ItemPedidoIntegradoUtils(itemPedido, true)

    dtoItem.integration_code = dtoItemPedido.codigoPdv
    dtoItem.desc_item = dtoItemPedido.descricao;
    dtoItem.quantity = dtoItemPedido.qtde;
    dtoItem.unit_price = dtoItemPedido.valorProduto; // nao pega valor total
    dtoItem.notes = dtoItemPedido.obs || ''
    dtoItem.choice_items = [];

    dtoItemPedido.adicionais.forEach((adicional: any) => {
      let dtoItemAdicional = {  integration_code:  adicional.codigoPdv,
        desc_item_choice: adicional.descricao,
        aditional_price: adicional.saborPizza ? 0 : adicional.valor,
        quantity: adicional.qtde >= 1 ? adicional.qtde : 1  ,
        notes: ""
      }
      dtoItem.choice_items.push(dtoItemAdicional)
    })

    this.items.push(dtoItem);
  }
}

export class DTOPedidoSaipos extends DTOPedidoDadosSaipos{
  delivery_address: any
  payment_types:  Array<any> = [];

  constructor(pedido: Pedido, empresa: any, codStore: any) {
    super();
    this.order_id = pedido.id.toString();
    this.display_id =  pedido.codigo.toString();
    this.cod_store = codStore ;
    this.created_at = new Date(pedido.horario).toISOString();
    this.notes = pedido.observacoes || '';
    this.customer = {
       id: pedido.contato.id.toString() , // id externo ?
       name: pedido.contato.nome,
       phone: pedido.contato.telefone
    }

    this.total_discount = pedido.desconto;
    this.total_amount = pedido.obtenhaTotalPagar();

    this.order_method = {
      mode: pedido.ehDelivery()  ? "DELIVERY" : "TAKEOUT", // DELIVERY, TAKEOUT - Ver Tipos de pedidos
      delivery_by: "RESTAURANT", // PARTNER , RESTAURANT - Ver Método de entrega
      delivery_fee: pedido.taxaEntrega,
      scheduled: pedido.horarioEntregaAgendada != null
    }

    // "2020-10-08T01:35:49.992093Z", // Tempo de entrega prometido pro cliente no app do parceiro
    if(pedido.horarioEntregaAgendada)
      this.order_method.delivery_date_time =  this.formateData(pedido.horarioEntregaAgendada)
    else {
      let formaDeEntrega: any =
        empresa.formasDeEntrega.find((forma: any) => forma.formaDeEntrega.id === pedido.formaDeEntrega.id );

      formaDeEntrega.valideTempoMiminimoConfigurado();

      let dataEstimadaEntrega: Date = formaDeEntrega.obtenhaHorarioPrevisaoEntrega(pedido.horario) ;

      this.order_method.delivery_date_time  = this.formateData(dataEstimadaEntrega)
    }

    if(pedido.endereco){
      this.delivery_address = {
        country: "BR",
        state: pedido.endereco.cidade.estado.sigla,
        city: pedido.endereco.cidade.nome,
        district: pedido.endereco.bairro,
        street_name: pedido.endereco.logradouro,
        street_number: pedido.endereco.numero ?  pedido.endereco.numero.toString() : '',
        postal_code: pedido.endereco.cep  ? pedido.endereco.cep.toString() : '',
        reference: pedido.endereco.pontoDeReferencia || '',
        complement: pedido.endereco.complemento || ''
      }

      if(pedido.endereco.localizacao){
        let coordenadas =  pedido.endereco.localizacao.split(',');
        if(coordenadas.length > 1 && coordenadas[0] !== 'undefined'){
          this.delivery_address.coordinates = {
            latitude: Number(coordenadas[0]),
            longitude: Number(coordenadas[1])
          }
        }
      }
    }

    if(pedido.obtenhaTotalPagar()){
      if(!pedido.pagamentos ||  pedido.pagamentos.length === 0)
        throw Error(String(`Nenhuma forma de pagamento  associado ao pedido`))

      let pagamentoCashback: PagamentoPedido ;

      pedido.pagamentos.forEach((pagamento: PagamentoPedido) => {
        if (!pagamento.foiPorCashback()) {
          let formaDePagamentoSaipos: any = pagamento.formaDePagamento.formaIntegrada;

          if(!formaDePagamentoSaipos)
            throw Error(String(`Forma de pagamento "${pagamento.formaDePagamento.nome}" não configurada na loja`))


          let pagamentoSaipos: any = {
            code:  formaDePagamentoSaipos.codigo ,
            amount: Number( pagamento.valor.toFixed(2)),
            change_for: 0
          };

          if(pagamentoSaipos.code === "DIN")
            if(pagamento.trocoPara)
              pagamentoSaipos.change_for = pagamento.trocoPara

          if(pagamento.formaDePagamento.bandeirasCartaoIntegrada.length)
            pagamentoSaipos.complement = pagamento.formaDePagamento.bandeirasCartaoIntegrada[0].codigoPdv;

          this.payment_types.push(pagamentoSaipos)
        } else{
          pagamentoCashback = pagamento
        }
      })

      if(pagamentoCashback){ // aplicar cashback como desconto por nao aceita 2 formas de pagamento
        // this.payment_types[0].amount =  Number((this.payment_types[0].amount  -  pagamentoCashback.valor).toFixed(2));
        this.total_discount = Number((    this.total_discount  + pagamentoCashback.valor).toFixed(2));
      }
    }

    pedido.itens.forEach(( itemPedido: ItemPedido) => {
      this.obtenhaDTOItens(itemPedido);
    })

    let itensExtras: Array<ProdutoExtraPedidoIntegrado> = ProdutoExtraPedidoIntegrado.gereItensPedido(pedido);

    itensExtras.forEach(( itemExtra: ProdutoExtraPedidoIntegrado) => {
      this.obtenhaDTOItemExtra(itemExtra);
    })

  }

  formateData(data: Date){
    // "2020-10-08T01:25:49.992093Z",
    return   moment(data).toDate().toISOString();
  }
}

export class DTOItem{
  integration_code: string;
  desc_item: string;
  quantity: number;
  unit_price: number
  notes = ''
  choice_items: any
  constructor() {}

  static novoDeItemProdutoPizza(itemPedido: ItemPedido){
    let dtoItem: any = new DTOItem();

    dtoItem.desc_item = itemPedido.descricao;
    dtoItem.unit_price = Number(itemPedido.valor.toFixed(2));
    dtoItem.notes = itemPedido.observacao || ''
    dtoItem.choice_items = [];

    return dtoItem
  }


}
