// @ts-ignore

import {Pedido} from "../../../domain/delivery/Pedido";
import {Empresa} from "../../../domain/Empresa";
import {PagamentoPedido} from "../../../domain/delivery/PagamentoPedido";
import {Endereco} from "../../../domain/delivery/Endereco";
import {ItemPedido} from "../../../domain/delivery/ItemPedido";
import {ItemPedidoIntegradoUtils} from "../ItemPedidoIntegradoUtils";
import {Contato} from "../../../domain/Contato";
import {EnumSexo} from "../../emun/EnumSexo";
import * as moment from "moment";

export class DTOPedidoTotvsChef{
  CodigoExternoPedido: string;
  //CodigoExternoPedidoReduzido
  PagamentoOnline = false
  RetirarNaLoja = false;
  Cliente: any;
  ObservacaoPedido: string;
  TaxaDeEntrega = 0;
  Troco = 0;
  Desconto = 0;
  DescontoTaxaEntrega = 0;
  ValorTotal = 0;
  AceitaPedidoAutomaticamente  = true;
  MinutosRejeitaAutomaticamente =  0;
  Pagamentos: any = [];
  Itens: any = [];
  IncluirCPFNaNota = false;
  constructor(pedido: Pedido, empresa: Empresa) {
    this.CodigoExternoPedido = pedido.codigo.toString();
    this.ValorTotal = pedido.obtenhaTotalPagar();
    this.Desconto = pedido.desconto;

    this.Cliente = {
      TipoPessoa: 0,
      NomeCompleto: pedido.contato.nome,
      CpfCnpj: pedido.contato.cpf || ''
    }

    if(pedido.contato.sexo ===  EnumSexo.MULHER)
      this.Cliente.Sexo = 1

    if(pedido.contato.sexo ===  EnumSexo.HOMEM)
      this.Cliente.Sexo = 0

    this.IncluirCPFNaNota = pedido.contato.cpf != null;

    if(pedido.ehDelivery()){
      this.TaxaDeEntrega = pedido.taxaEntrega;
      this.DescontoTaxaEntrega = pedido.descontoTaxaEntrega;
      this.Cliente.Endereco  = new DTOEnderecoTotvs(pedido.endereco, pedido.contato);
      this.Cliente.EnderecoEntrega =       this.Cliente.Endereco;

      if(pedido.horarioEntregaAgendada)
        this.ObservacaoPedido =
          String(`Entrega agendada para: ${this.obtenhaAgendamento(pedido.horarioEntregaAgendada)}`)

    } else {
      this.RetirarNaLoja = true;
      let enderecosCadastro: any = (pedido.contato as any).enderecos;

      this.Cliente.Endereco = enderecosCadastro && enderecosCadastro.length ?
        new DTOEnderecoTotvs(enderecosCadastro[0],  pedido.contato) :
        DTOEnderecoTotvs.obtenhaEnderecoPadrao(empresa, pedido.contato);

      if(pedido.horarioEntregaAgendada)
        this.ObservacaoPedido =
          String(`Retirada agendada para: ${this.obtenhaAgendamento(pedido.horarioEntregaAgendada)}`)
    }

    pedido.pagamentos.forEach((pagamento: PagamentoPedido) => {
       if(pagamento.foiOnline()) this.PagamentoOnline = true;

       if(!pagamento.foiPorCashback()){
         if(!pagamento.formaDePagamento.formaIntegrada || Number.isNaN(Number(pagamento.formaDePagamento.formaIntegrada.codigo)))
           throw Error(String(`Forma de pagamento não configurada em Loja->Formas de Pagamento: "${pagamento.formaDePagamento.descricao}"`))

         this.Pagamentos.push({
           Valor: pagamento.foiPorDinheiro() && pagamento.trocoPara ? ( pagamento.trocoPara)  :  pagamento.valor,
           Tipo: Number(pagamento.formaDePagamento.formaIntegrada.codigo),
           CodigoReferencia:  pagamento.formaDePagamento.id.toString(),
           PagoOnline: pagamento.foiOnline()
         })
       } else {
         this.Desconto +=   pagamento.valor
       }
    })

    if(pedido.temTroco())
      this.Troco   = pedido.obtenhaValorTroco();

    pedido.itens.forEach((itemPedido: ItemPedido) => {
      let item = new DTOItemPedidoTotvs(itemPedido);
      console.log(item)
      this.Itens.push(item)
    })

  }

  private obtenhaAgendamento(horarioEntregaAgendada: Date) {
    return moment(horarioEntregaAgendada).format('DD/MMM/YYYY [as] HH[h]:mm')
  }
}

export class DTOItemPedidoTotvs{
  TipoItem: number;
  Produto: any;
  Quantidade: number;
  ValorTotal: number;
  Desconto = 0;
  Acrescimo = 0;
  MotivoAcrescimoDesconto: string;
  AcrescimoDiferencaFracionada = 0;
  ValorDescontoItem = 0;
  ValorServicoItem = 0;
  ValorDescontoComboItem = 0;
  ValorAcrescimoItem = 0;
  TipoOperacao = 0;
  Observacao: string;
  ItensAdicionais: any;
  ItensFracao: any;
  constructor(itemPedido: ItemPedido) {
    let dtoItemPedido: ItemPedidoIntegradoUtils = new ItemPedidoIntegradoUtils(itemPedido, true)

    this.TipoItem = 0; // 1 para fracionado
    this.Quantidade = dtoItemPedido.qtde;
    this.ValorTotal = dtoItemPedido.total;
    this.Observacao = dtoItemPedido.obs;
    this.Produto = {
      Descricao: dtoItemPedido.descricao,
      Codigo: dtoItemPedido.codigoPdv,
      PrecoVenda: dtoItemPedido.valorProduto,
      /*PrecoOriginal: 0,
      PrecoPromocional: 0,
      Pesavel: true,
      Processado: false,
      ProdutoComposto: false,
      BaixarEstoqueOnline: false,
      QuantidadeEstoque: 0 */
    }

    if(dtoItemPedido.adicionais && dtoItemPedido.adicionais.length){
      this.ItensAdicionais = [];
      dtoItemPedido.adicionais.forEach((adicional: any) => {
        let dtoItemAdicional: any = {
          TipoItem: 0, // 1 para fracionado
          Quantidade: adicional.qtde >= 1 ? adicional.qtdeSomada : 1,
          ValorTotal: adicional.totalSomado ,
          Produto: {
            Descricao: adicional.descricao,
            Codigo: adicional.codigoPdv,
            PrecoVenda: adicional.saborPizza ? 0 : adicional.valor
           /* PrecoOriginal: 0,
            PrecoPromocional: 0,
            Pesavel: true,
            Processado: false,
            ProdutoComposto: false,
            BaixarEstoqueOnline: false,
            QuantidadeEstoque: 0 */
          },
          TipoOperacao: 0,
          Acrescimo: 0,
          Desconto: 0,
          AcrescimoDiferencaFracionada: 0,
          ValorDescontoItem: 0,
          ValorServicoItem: 0,
          ValorDescontoComboItem: 0,
          ValorAcrescimoItem: 0
        }
        this.ItensAdicionais.push(dtoItemAdicional);
        //Total nao compor item
        this.ValorTotal  -= dtoItemAdicional.ValorTotal;
      })
    }
  }
}

export class DTOEnderecoTotvs {
  Logradouro: string;
  Numero: string;
  Complemento: string;
  Bairro: string;
  Municipio: string;
  MunicipioNumeroIBGE: number;
  //UFNumeroIBGE: number;
  UF: string;
  CEP: string;
  DDD: string;
  Telefone: string;
  EMail: string;
  IdentificacaoEndereco: string;
  InformacoesAdicionais: string;
  constructor(endereco: Endereco, contato: Contato) {
    this.Logradouro = endereco.logradouro;
    this.Numero = endereco.numero;
    this.Complemento = endereco.complemento;
    this.Bairro = endereco.bairro;
    this.Municipio = endereco.cidade.nome;
    this.MunicipioNumeroIBGE = Number(endereco.cidade.codigoIbge);
    this.UF = endereco.cidade.estado.sigla;
    this.CEP = endereco.cep;
    this.DDD = contato.telefone.substr(0, 2);
    this.Telefone = contato.telefone.substr(2);
    this.EMail = contato.email;
  }


  static obtenhaEnderecoPadrao(empresa: any, contato: any){
    let enderecoPadrao: any = {
      Logradouro: 'Rua',
      Complemento: "Complemento",
      Numero: 1,
      Bairro: 'Bairro',
      CEP: '99999999'
    }

    if(!empresa.enderecoCompleto)
      throw Error('Endereço Padrão nao pôde ser obtido, informe o endereço completo no cadastro da empresa ')

    enderecoPadrao.UF  = empresa.enderecoCompleto.cidade.estado.sigla
    enderecoPadrao.Municipio = empresa.enderecoCompleto.cidade.nome;
    enderecoPadrao.MunicipioNumeroIBGE = Number(empresa.enderecoCompleto.cidade.codigoIbge);

    enderecoPadrao.DDD = contato.telefone.substr(0, 2);
    enderecoPadrao.Telefone = contato.telefone.substr(2);
    enderecoPadrao.EMail = contato.email;

    return enderecoPadrao;
  }
}
