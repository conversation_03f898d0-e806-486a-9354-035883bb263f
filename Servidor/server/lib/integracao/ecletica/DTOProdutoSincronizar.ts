import {AdicionalDeProduto} from "../../../domain/delivery/AdicionalDeProduto";
import {EnumDisponibilidadeProduto} from "../../emun/EnumDisponibilidadeProduto";

export class DTOProdutoSincronizar{
  constructor(produto: any, precosAtualizado: any,
              sincronizarSoAdicionais: boolean = null, indisponivel: boolean = null, sincronizarTamanhos: boolean = null) {
    this.id = produto.id;
    this.nome = produto.nome;
    this.codigoPdv = produto.codigoPdv;
    this.idIfood = produto.idIfood;
    this.sincronizarSoAdicionais  = sincronizarSoAdicionais;
    this.sincronizarTamanhos = sincronizarTamanhos
    this.linkImagem = produto.imagens && produto.imagens.length ? produto.imagens[0].linkImagem : null;
    if(this.linkImagem) this.linkImagem = String(`/images/empresa/${this.linkImagem}`)

    this.indisponivel = indisponivel;

    if(sincronizarSoAdicionais || indisponivel || sincronizarTamanhos) {
      this.preco = produto.obtenhaPreco();
    } else {
      this.setNovoPreco(produto, precosAtualizado)
    }

    this.categoria = produto.categoria ? produto.categoria : { nome: "Outros"};
  }
  id: number;
  nome: string;
  linkImagem: string;
  novaImagem: any;
  codigoPdv: string;
  idIfood: string;
  precoAntigo: number;
  precoSemDesconto: number;
  precoAtualizado: number;
  novoPreco: number
  opcoesAtualizadas: any = [];
  tamanhosAtualizados: any = [];
  opcoesRemovidas: any = [];
  camposAdicionais: any = [];
  camposAdicionaisRemovidos: any  = [];
  sincronizarSoAdicionais: boolean;
  sincronizarTamanhos: boolean;
  indisponivel: boolean;
  categoria: any;
  preco: number
  descricao: string
  percentualDesconto: number
  novaDescricao: string;
  idTamanho: number;
  voltouParaOSite: boolean;
  atualizouDesconto: boolean
  static obtenhaProdutoSincronizar(produtoImportar: any, produtoExistente: any, produtosAtualizar: Array<any>,
                                   sincronizarPrecos  = false, sincronizarImagens = false, apenasPrecos = false) {

    if(produtoExistente && produtoExistente.naoSincronizar) return;

    let dtoProdutoSincronizar =  new DTOProdutoSincronizar(produtoExistente, null, true);

    if(produtoExistente.descricao !== produtoImportar.descricao && produtoImportar.descricao != null ){
      dtoProdutoSincronizar.sincronizarSoAdicionais = false;
      dtoProdutoSincronizar.novaDescricao = produtoImportar.descricao;
    }

    if(produtoImportar.disponibilidade === EnumDisponibilidadeProduto.SempreDisponivel && !produtoExistente.naoSincronizar){
      if(!produtoExistente.disponivelParaDelivery)
        dtoProdutoSincronizar.voltouParaOSite = true;
    }

    if(sincronizarImagens){
      dtoProdutoSincronizar.sincronizarSoAdicionais = false;
      dtoProdutoSincronizar.setNovaImagem(produtoImportar)
    }

    produtoExistente.camposAdicionais.forEach( (campoAdicionalExistente: AdicionalDeProduto) => {
      let adicionalImportar =
        produtoImportar.camposAdicionais.find( (adicional: any) =>
          (campoAdicionalExistente.codigoIfood && adicional.codigoIfood === campoAdicionalExistente.codigoIfood) ||
          adicional.nome.toUpperCase().trim() === campoAdicionalExistente.nome.toUpperCase().trim());

      if(!adicionalImportar &&  !apenasPrecos)
        dtoProdutoSincronizar.camposAdicionaisRemovidos.push(campoAdicionalExistente);
    })

    produtoImportar.camposAdicionais.forEach( (campoAdicionalImportar: any) => {
      let adicionalExistente: any;

      if(produtoExistente.idIfood)
        adicionalExistente =
          produtoExistente.camposAdicionais.find( (adicional: any) => adicional.codigoIfood === campoAdicionalImportar.codigoIfood)

      if(!adicionalExistente)
        adicionalExistente =  produtoExistente.camposAdicionais.find(
          (adicional: any) => adicional.nome.toUpperCase() === campoAdicionalImportar.nome.toUpperCase());


      if(!adicionalExistente){
        if(!apenasPrecos || campoAdicionalImportar.sincronizarModelo )
          dtoProdutoSincronizar.camposAdicionais.push(campoAdicionalImportar)
      } else if(!apenasPrecos) {

        campoAdicionalImportar.id = adicionalExistente.id;

        if(!adicionalExistente.opcoesDisponiveis)
          adicionalExistente.opcoesDisponiveis = [];

        //removido ser sempre primeiro
        adicionalExistente.opcoesDisponiveis.forEach( (opcaoExistente: any) => {
          let opcaoImportar: any =
            campoAdicionalImportar.opcoesDisponiveis.find(
              (_opcaoImportar: any) => opcaoExistente.mesmoParaImportar(_opcaoImportar)) ;

          if(!opcaoImportar){
            opcaoExistente.nomeAdicional = campoAdicionalImportar.nome;
            opcaoExistente.idAdicional = campoAdicionalImportar.id;
            if(campoAdicionalImportar.codigoIfood)
              opcaoExistente.nomeAdicional = String(`${ opcaoExistente.nomeAdicional} (${campoAdicionalImportar.codigoIfood})`)

            dtoProdutoSincronizar.opcoesRemovidas.push(opcaoExistente)
          }
        })

        let novasOpcoes: any = [], opcoesAtualizadas: any = [];
        campoAdicionalImportar.opcoesDisponiveis.forEach( (opcao: any) => {
          let opcaoExistente: any =
            adicionalExistente.opcoesDisponiveis ?
              adicionalExistente.opcoesDisponiveis.find((_opcaoExistente: any) => _opcaoExistente.mesmoParaImportar(opcao)) : null;

          if(!opcaoExistente)
            novasOpcoes.push(opcao)
          else if(opcaoExistente.descricao !== opcao.descricao){
            opcaoExistente.novaDescricao = opcao.descricao;
            opcaoExistente.nomeAdicional = campoAdicionalImportar.nome;
            opcaoExistente.idAdicional = campoAdicionalImportar.id;
            opcoesAtualizadas.push(opcaoExistente)
          }

        })

        if(opcoesAtualizadas.length)
          dtoProdutoSincronizar.opcoesAtualizadas = opcoesAtualizadas;

        if(novasOpcoes.length){
          campoAdicionalImportar.opcoesDisponiveis = novasOpcoes;
          dtoProdutoSincronizar.camposAdicionais.push(campoAdicionalImportar)
        }
      }
    });

    if(sincronizarPrecos || apenasPrecos){
      let novoPreco =  produtoImportar.obtenhaPreco();

      if(novoPreco !== produtoExistente.obtenhaPreco() || produtoImportar.preco !== produtoExistente.preco){
        dtoProdutoSincronizar.sincronizarSoAdicionais = false;
        if( (novoPreco - produtoExistente.obtenhaPreco()) > 5 ){
          console.log('preço acima esperado: ' + produtoImportar.nome)
        }

        dtoProdutoSincronizar.setNovoPreco(produtoExistente, produtoImportar.obtenhaPrecos())
      }

      produtoExistente.camposAdicionais.forEach( (campoAdicional: AdicionalDeProduto) => {
        if(campoAdicional.opcoesDisponiveis){
          campoAdicional.opcoesDisponiveis.forEach((opcao: any) => {
            if (opcao.codigoPdv || opcao.idIfood) {
              produtoImportar.camposAdicionais.forEach( (campoAdicionalSincronizar: any) => {

                if( (campoAdicional.codigoIfood && campoAdicionalSincronizar.codigoIfood === campoAdicional.codigoIfood) ||
                  campoAdicionalSincronizar.nome === campoAdicional.nome){ // para importados antes, so pega se tiver mesmo nome

                  let opcaoSincronizar: any;
                  if(opcao.idIfood){
                    opcaoSincronizar = campoAdicionalSincronizar.opcoesDisponiveis.find(
                      (_opcao: any) => _opcao.idIfood ===  opcao.idIfood);
                  }

                  if(!opcaoSincronizar && opcao.codigoPdv)
                    opcaoSincronizar = campoAdicionalSincronizar.opcoesDisponiveis.find(
                      (_opcao: any) => _opcao.codigoPdv ===  opcao.codigoPdv);


                  if(opcaoSincronizar && opcaoSincronizar.valor !== opcao.valor){
                    if( (opcao.valor - opcaoSincronizar.valor) > 5 ){
                      console.log('preço acima esperado: ' + produtoImportar.nome)
                    }

                    opcao.precoAntigo =  opcao.valor ;
                    opcao.novoPreco = opcaoSincronizar.valor;
                    opcao.nomeAdicional = campoAdicional.nome;
                    opcao.idAdicional = campoAdicional.id;

                    dtoProdutoSincronizar.opcoesAtualizadas.push(opcao)
                  }
                }
              })
            }
          })
        }
      })
    }

    if(dtoProdutoSincronizar.teveAlteracao())
      produtosAtualizar.push(dtoProdutoSincronizar)

  }

  setTamanho(tamanho: any, identificador: string){
    this.codigoPdv = tamanho.codigoPdv;
    this.nome = String(`${identificador} ${this.nome} - ${tamanho.descricao}`);
    this.id = tamanho.id;
    this.idTamanho = tamanho.id;
    this.setNovoPreco(tamanho, { preco: this.precoAtualizado });
  }

  private setNovoDesconto(produto: any, precosAtualizado: any){
    this.precoAntigo = produto.preco;
    this.precoAtualizado = precosAtualizado.preco;
    this.novoPreco = produto.novoPreco;
    this.atualizouDesconto = true;
  }



  setNovoPreco(produto: any, precosAtualizado: any){
    delete this.preco;

    if(produto.novoPreco != null && produto.novoPreco === precosAtualizado.novoPreco && produto.preco !== precosAtualizado.preco){
      this.setNovoDesconto(produto, precosAtualizado)
    } else {
      if( precosAtualizado.novoPreco){
        this.precoSemDesconto = precosAtualizado.preco;
        this.precoAtualizado = precosAtualizado.novoPreco;
        this.precoAntigo =  produto.novoPreco != null ? produto.novoPreco :  produto.preco;
      } else {
        this.precoSemDesconto = null
        this.precoAtualizado = precosAtualizado.preco;
        this.precoAntigo = produto.novoPreco != null ? produto.novoPreco :  produto.preco;
      }
    }
  }

  private teveAlteracao() {
    return this.camposAdicionais.length || this.opcoesAtualizadas.length || this.opcoesRemovidas.length ||
           this.novaDescricao || this.novaImagem || this.voltouParaOSite || this.precoAtualizado ||
            this.camposAdicionaisRemovidos.length
  }

  setNovaImagem(produtoImportar: any) {
    this.novaImagem = {
      urlImagemExterna : produtoImportar.urlImagemExterna,
      linkImagem : produtoImportar.linkImagem
    }
  }
}
