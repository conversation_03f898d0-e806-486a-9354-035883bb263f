import {ProdutoService} from "../../service/ProdutoService";
import {MapeadorDeProduto} from "../../mapeadores/MapeadorDeProduto";
import {MapeadorDeCategoria} from "../../mapeadores/MapeadorDeCategoria";
import {Categoria} from "../../domain/delivery/Categoria";
import {MapeadorDeOpcaoDeAdicionalDeProduto} from "../../mapeadores/MapeadorDeOpcaoDeAdicionalDeProduto";
import {Ambiente} from "../../service/Ambiente";
import * as https from "https";
import {ImagemDoProduto} from "../../domain/ImagemDoProduto";
import {MapeadorDeAdicionalDeProduto} from "../../mapeadores/MapeadorDeAdicionalDeProduto";
import {MapeadorDeEmpresa} from "../../mapeadores/MapeadorDeEmpresa";
import {Produto} from "../../domain/Produto";
import {DTOProdutoSincronizar} from "./ecletica/DTOProdutoSincronizar";
import {RegistroDeOperacaoService} from "../../service/RegistroDeOperacaoService";
import {MapeadorDeImagemDoProduto} from "../../mapeadores/MapeadorDeImagemDoProduto";
import {Empresa} from "../../domain/Empresa";
import {IServiceIntegracaoExternaERP} from "../../domain/integracoes/IServiceIntegracaoExternaERP";
import {EcleticaProdutoUtils} from "./ecletica/EcleticaProdutoUtils";
import {AdicionalDeProduto} from "../../domain/delivery/AdicionalDeProduto";
import {ProdutoPizza} from "../../domain/ProdutoPizza";
import {ProdutoTamanho} from "../../domain/templates/ProdutoTamanho";
import * as _ from "underscore";
import {MapeadorDeProdutoTemplate} from "../../mapeadores/MapeadorDeProdutoTemplate";
import {EmpresaService} from "../../service/EmpresaService";
import {Catalogo} from "../../domain/catalogo/Catalogo";
import {EnumDisponibilidadeProduto} from "../emun/EnumDisponibilidadeProduto";
import {IFonteProdutosExternos} from "../../domain/integracoes/IFonteProdutosExternos";
import {Disponibilidade} from "../../domain/Disponibilidade";
import {MapeadorDeCatalogo} from "../../mapeadores/MapeadorDeCatalogo";

let redis = require("redis");
let client = redis.createClient();

const uuidv1 = require('uuid/v1');
let path = require('path');
let fs = require('fs');
let TAREFAPREFIXOREDIS = 'tarefa#'
export class ImportadorProduto {
  registroDeOperacaoService: any;

  static obtenhaRespostaImportacao(idTarefa: number){
    let chave = String( `${TAREFAPREFIXOREDIS}${idTarefa}` )

    return new Promise<any>( async (resolve, reject) => {
      client.get(chave, (err: any, reply: string) => {
        if(reply){
          const dados = JSON.parse(reply);
          resolve(dados);
          client.del(chave)
        } else {
          resolve(null)
        }
      });
    })
  }

  static setRespostaImportacao(idTarefa: number, resposta: any){
    let chave = String( `${TAREFAPREFIXOREDIS}${idTarefa}` )

    client.set(chave, JSON.stringify(resposta));
  }

  processeTodosCatalogo(empresa: Empresa, todosProdutos: Array<Produto>): Promise<any>{
    return new Promise<any>( async (resolve, reject) => {
      let resposta: any = { novos: [], precosSincronizar: [], disponibildadeAtualizar: []}
      let service: IFonteProdutosExternos = empresa.integracaoDelivery.obtenhaService();
      console.log('carregar todas categorias...')
      let todasCategorias = await new MapeadorDeCategoria(empresa.catalogo).listeAsync({integrado: true});

      console.log('total de categorias: ' + todasCategorias.length)

      let produtosImportar: any = await  service.listeProdutosConvertidos(null).catch((erroProdutos: any) => {
        console.error(erroProdutos)
        reject(erroProdutos)
      })

      if(produtosImportar && produtosImportar.length){
        console.log('Total produtos retornados na integração: ' + produtosImportar.length)
        produtosImportar.forEach( (produtoErp: Produto) => {
          let produtoExistente =
            todosProdutos.find( (produto: any) => produto.codigoPdv &&  produto.codigoPdv === produtoErp.codigoPdv  );

          if(!produtoExistente){
            if(produtoErp.categoria){
              let categoriaExistente = todasCategorias.find( (categoria: any) => categoria.nome === produtoErp.categoria.nome);
              if(categoriaExistente)
                produtoErp.categoria.id = categoriaExistente.id;
            }
            produtoErp.origem = service.obtenhaTipoDeOrigem();
            produtoErp.dataCadastro = new Date()
            resposta.novos.push(produtoErp)
          }
        })

        todosProdutos.forEach( (produtoExistente: any) => {
          let produtoImportar = produtosImportar.find((item: any) => item.codigoPdv === produtoExistente.codigoPdv);

          if(produtoExistente.estaDisponivel()) {
            if (!produtoImportar || produtoImportar.disponibilidade === EnumDisponibilidadeProduto.NaoDisponivel)
              resposta.disponibildadeAtualizar.push(new DTOProdutoSincronizar(produtoExistente, null, null, true))
          } else{
            if(produtoImportar && produtoImportar.disponibilidade === EnumDisponibilidadeProduto.SempreDisponivel)
              resposta.disponibildadeAtualizar.push(
                new DTOProdutoSincronizar(produtoExistente, produtoExistente.obtenhaPrecos(), null, false))
          }
        })

        resposta.precosSincronizar = this.obtenhaProdutosPrecosAlterados(todosProdutos, produtosImportar);

        resolve(resposta)
      } else {
        console.log('Nao retornou produots da integração.')
      }
    })
  }


  obtenhaNovosProdutos(empresa: Empresa, openDelivery = false): Promise<any>{
    return new Promise<any>( async (resolve, reject) => {
      let novosProdutos: any = [],  categorias: any = [];

      let service: IFonteProdutosExternos = openDelivery ?  empresa.integracaoOpendelivery.obtenhaService() :
                                                            empresa.integracaoDelivery.obtenhaService();
      console.log('carregar todos categorias...')
      let todasCategorias = await new MapeadorDeCategoria(empresa.catalogo).listeAsync({});
      console.log('carregar todos produtos...')
      let todosProdutos = await new MapeadorDeProduto(empresa.catalogo).listeAsync({});

      let ultimaSincronizacao = openDelivery ? null : empresa.integracaoDelivery.ultimaSincronizacaoProdutos

      service.listeProdutosConvertidos(ultimaSincronizacao).then(  (produtos) => {
        console.log('Total produtos retornados na integração: ' + produtos.length)

        produtos.forEach( (produtoImportar: Produto) => {
          let produtoExistente = todosProdutos.find( (produto: any) =>
            produto.codigoPdv === produtoImportar.codigoPdv && produto.codigoPdv !== EcleticaProdutoUtils.CODIGO_CORINGA);

          if(!produtoExistente){
            if(produtoImportar.categoria){
              let categoriaExistente = todasCategorias.find( (categoria: any) => categoria.nome === produtoImportar.categoria.nome);
              if(categoriaExistente)
                produtoImportar.categoria.id = categoriaExistente.id;
            }
            produtoImportar.origem = service.obtenhaTipoDeOrigem();
            produtoImportar.dataCadastro = new Date()
            novosProdutos.push(produtoImportar)
          } else if(!produtoExistente.origem) {
            produtoExistente.origem = service.obtenhaTipoDeOrigem();
            if(!produtoExistente.dataCadastro)
              produtoExistente.dataCadastro = new Date()
          }

        })

        novosProdutos.forEach( (produto: any) => {
          if(produto.categoria){
            if(!categorias.find( (categoria:  any) => { return produto.categoria.id ? produto.categoria.id === categoria.id :
              produto.categoria.nome === categoria.nome} )){
              categorias.push(produto.categoria)
            }
          }
        })

        categorias = categorias.sort( (a: any, b: any) => (a.nivel === b.nivel ) ?  a.posicao - b.posicao : a.nivel - b.nivel );

        resolve({ novosProdutos: novosProdutos, categorias: categorias, produtosAtualizar: [] })

      }).catch( (erroIntegracao: any) => {
        reject(erroIntegracao)
      })
    })
  }

  obtenhaNovosPrecos(empresa: Empresa, todos = false): Promise<any>{
    return new Promise(async (resolve, reject) => {
      let produtosIntegrados = await new MapeadorDeProduto(empresa.catalogo).listeAsync({ integrado: true});

      if(produtosIntegrados.length){
        let service: IServiceIntegracaoExternaERP = empresa.integracaoDelivery.obtenhaService(),
          ultimaSincronizacao = todos ? null  : empresa.integracaoDelivery.ultimaSincronizacaoPrecos

        let produtosPrecos: any =
            await service.listePrecosProdutos(ultimaSincronizacao)
              .catch( (erro) => {   reject(erro)  })

        if(!produtosPrecos) return;

        resolve(this.obtenhaProdutosPrecosAlterados(produtosIntegrados, produtosPrecos))
      } else {
        resolve([])
      }
    })
  }

  private obtenhaProdutosPrecosAlterados(produtosIntegrados: any, produtosPrecos: any){
    let produtosSincronizar: Array<DTOProdutoSincronizar> =  [];

    produtosIntegrados.forEach( (produto: Produto) => {
      let produtoSincronizar: DTOProdutoSincronizar;
      if(produto.codigoPdv === EcleticaProdutoUtils.CODIGO_CORINGA){
        let opcoesAtualizadas: any = []
        produto.camposAdicionais.forEach( (campoAdicional: AdicionalDeProduto) => {
          campoAdicional.opcoesDisponiveis.forEach((opcao: any) => {
            if (opcao.codigoPdv ) {
              let produtoVendido: Produto =
                produtosPrecos.find( (_produtoErp: Produto) => _produtoErp.codigoPdv ===  opcao.codigoPdv)

              if(produtoVendido){
                let valorVendaProduto: number = produtoVendido.preco;

                if(valorVendaProduto !== opcao.valor){
                  opcao.precoAntigo =  opcao.valor ;
                  opcao.novoPreco = valorVendaProduto
                  opcoesAtualizadas.push(opcao)
                }
              }
            }
          })
        })
        if(opcoesAtualizadas.length){
          produtoSincronizar = new DTOProdutoSincronizar(produto, produto.obtenhaPrecos(), true)
          produtoSincronizar.opcoesAtualizadas = opcoesAtualizadas;
        }
      }  else {
        let produtoErp: Produto;

        if(produto instanceof ProdutoPizza) {

          produto.tamanhos.forEach((produtoTamanho: ProdutoTamanho) => {

            produtoErp = produtosPrecos.find((_produtoErp: Produto) => _produtoErp.codigoPdv === produtoTamanho.codigoPdv)
            if (produtoErp) {
              let novoPreco: number = produtoErp.preco;

              if( produtoTamanho.obtenhaPreco() !== novoPreco){
                let produtoTamanhoSincronizar = new DTOProdutoSincronizar(produto, produtoErp.obtenhaPrecos())
                produtoTamanhoSincronizar.setTamanho(produtoTamanho, produto.template.identificador)
                produtosSincronizar.push(produtoTamanhoSincronizar)
              }
            }
          })
        } else {
          produtoErp =  produtosPrecos.find( (_produtoErp: Produto) => _produtoErp.codigoPdv ===  produto.codigoPdv)

          if(produtoErp &&  produtoErp.preco != null){
            let novoPreco: number =  produtoErp.obtenhaPreco(null, null);

            if( produto.obtenhaPreco(null, null) !== novoPreco  || produtoErp.preco !== produto.preco)
              produtoSincronizar = new DTOProdutoSincronizar(produto, produtoErp.obtenhaPrecos())

            produto.camposAdicionais.forEach( (campoAdicional: AdicionalDeProduto) => {
              campoAdicional.opcoesDisponiveis.forEach((opcao: any) => {
                if(opcao.codigoPdv){
                  produtoErp.camposAdicionais.forEach( (campoAdicionalErp: any) => {
                    let opcaoErp = campoAdicionalErp.opcoesDisponiveis.find( (_opcao: any) => opcao.codigoPdv === _opcao.codigoPdv)

                    if(opcaoErp && opcaoErp.valor !== opcao.valor){
                      opcao.precoAntigo =  opcao.valor ;
                      opcao.novoPreco = opcaoErp.valor;

                      if(!produtoSincronizar)
                        produtoSincronizar =  new DTOProdutoSincronizar(produto,  null, true);

                      produtoSincronizar.opcoesAtualizadas.push(opcao)
                    }
                  })
                }
              })
            })
          }
        }
      }
      if(produtoSincronizar)
        produtosSincronizar.push(produtoSincronizar)
    })
    produtosSincronizar = _.sortBy(produtosSincronizar, (produto: any) => produto.nome)

    return produtosSincronizar;
  }


  async importeCategoria(categoriaImportar: any, catalogo: any){
   let categoriaExistente

   if(categoriaImportar.codigoIfood)
     categoriaExistente =  await new MapeadorDeCategoria(catalogo).selecioneSync({codigoIfood: categoriaImportar.codigoIfood})

   if (!categoriaExistente){
     if(categoriaImportar.codigoPdv)
       categoriaExistente =  await new MapeadorDeCategoria(catalogo).selecioneSync({codigoPdv: categoriaImportar.codigoPdv})

     if (!categoriaExistente){
       let query: any = {nome:  categoriaImportar.nome};

       if(categoriaImportar.categoriaPai){
         if( categoriaImportar.categoriaPai.id)
           query.idPai =  categoriaImportar.categoriaPai.id
         else
           query.codigoPdvPai =  categoriaImportar.categoriaPai.codigoPdv
       } else {
         query.nivel = 1
       }

       categoriaExistente = await new MapeadorDeCategoria(catalogo).selecioneSync(query)
     }
   }

   if(categoriaExistente) return categoriaExistente;

    let categoriaNova = Object.assign(new Categoria(null, null, null), categoriaImportar)

    categoriaNova.catalogo = catalogo;

    if(categoriaNova.categoriaPai)
      categoriaNova.categoriaPai = await this.importeCategoria(categoriaNova.categoriaPai, catalogo);

    //nao atualizar posiçoes, contexto importando todos nao da certo
    //await new MapeadorDeCategoria().recalculeOrdens(empresa);
    await new MapeadorDeCategoria(catalogo).insiraGraph(categoriaNova);

    await new MapeadorDeEmpresa().removaCacheCatalogos(catalogo);

    return categoriaNova;
  }

  importeProduto(catalogo: any, produto: any): Promise<any> {

    return new Promise<any>((resolve, reject) => {
      this.baixeImagens([produto]).then(() => {
        let produtoService = new ProdutoService();

        new MapeadorDeProduto(catalogo).transacao(async (conexao: any, commit: Function) => {

          //garantir categorias pai seja criadas primeiro
          for(let i = 0; i < produto.categoriasAcima.length; i++)
            await this.importeCategoria(produto.categoriasAcima[i], catalogo)

          //depis importa a categoria de venda
          if (produto.categoria && !produto.categoria.id)
            produto.categoria  = await this.importeCategoria(produto.categoria, catalogo)

          produto.dataCadastro = produto.dataCadastro ? new Date(produto.dataCadastro) : new Date();
          produto.catalogo = catalogo

          let erro = await produtoService.salveSemTransacao(produto);

          if (!erro) {
            try{
              if (produto.camposAdicionais) {
                for (let adicional of produto.camposAdicionais)
                  await produtoService.insiraAdicionalImportado(produto.catalogo, produto, adicional);

                await produtoService.insiraDependenciasImportadas(produto)
              }

              commit(() => {
                resolve(produto)
              })
            }catch (error){
              conexao.rollback(() => {
                console.log(error)
                reject(error.message || error)
              })
            }
          } else {
            conexao.rollback(() => {
              reject(erro)
            })
          }
        });
      })
    })
  }

  //todo: pensar nesse metodo depois atualizar todos produto espelahdos.
  sincronizeProdutoDaRede(catalogo: Catalogo, produtoSincronizar: DTOProdutoSincronizar): Promise<any>{
    return new Promise<Number>( async (resolve, reject) => {

        let catalogosEspelhados = await new MapeadorDeCatalogo().listeAsync({ idModelo: catalogo.id});
        let outrosProdutos = []
        for(let i = 0; i < catalogosEspelhados.length; i++){
          let produtosEspelhados =
            await new MapeadorDeProduto(catalogosEspelhados[i]).listeAsync(
              { idCatalogo: catalogosEspelhados[i].id, codigoPdv: produtoSincronizar.codigoPdv, naoFazerCache: true})

          produtosEspelhados.forEach((produtosEspelhado: Produto) => {
             if(produtosEspelhado.categoria && produtosEspelhado.categoria.ehIgual(produtoSincronizar.categoria)){
               let outroProduto = Object.assign({}, produtoSincronizar)

               outroProduto.id = produtosEspelhado.id;
               outroProduto.categoria = produtosEspelhado.categoria;

               outrosProdutos.push(outroProduto)
             }
          })
        }

      console.log(String(`Atualizar produto ${produtoSincronizar.nome} +${outrosProdutos.length} espelhados...`))
      // @ts-ignore
      resolve(produtoSincronizar)
    })
  }

  async sincronizeProduto(catalogo: any, produto: DTOProdutoSincronizar){
    const mapeador = new MapeadorDeProduto(catalogo);
    let produtoService = new ProdutoService()

    if(produto.precoAtualizado  != null ){

      if(produto.atualizouDesconto){
        produto.preco = produto.precoAtualizado;
        produto.percentualDesconto =   Number((  100 - ( produto.novoPreco / produto.preco ) * 100 ).toFixed(2));
      } else {
        if(produto.precoSemDesconto){
          produto.preco = produto.precoSemDesconto;
          produto.novoPreco = produto.precoAtualizado;
          produto.percentualDesconto =   Number((  100 - ( produto.novoPreco / produto.preco ) * 100 ).toFixed(2));
        } else {
          produto.preco = produto.precoAtualizado;
          produto.novoPreco = null;
          produto.percentualDesconto = null;
        }
      }

      if(produto.idTamanho){
        await mapeador.atualizePrecoTamanho(produto)
      } else {
        await mapeador.atualizePreco(produto);
      }

      await this.obtenhaRegistroDeOperacaoService().sincronizouPrecoProduto(produto)
    }

    if(produto.novaImagem)
      await this.sincronizeImagem(produto)


    if(produto.novaDescricao){
      produto.descricao = produto.novaDescricao;
      await mapeador.atualizeDescricao(produto);
      await this.obtenhaRegistroDeOperacaoService().sincronizouDescricaoProduto(produto);
    }

    for(let j = 0; j < produto.opcoesAtualizadas.length; j++){
      let opcaoDeAdicional: any = produto.opcoesAtualizadas[j];
      if(opcaoDeAdicional.novoPreco){
        opcaoDeAdicional.valor = opcaoDeAdicional.novoPreco;
        await new MapeadorDeOpcaoDeAdicionalDeProduto().atualizeValor(opcaoDeAdicional)
        await this.obtenhaRegistroDeOperacaoService().alterouPrecoAdicionalProduto(produto,
          { nome: opcaoDeAdicional.nomeAdicional} , opcaoDeAdicional)

      }

      if(opcaoDeAdicional.novaDescricao){
        opcaoDeAdicional.descricao = opcaoDeAdicional.novaDescricao;
        await new MapeadorDeOpcaoDeAdicionalDeProduto().atualizeDescricao(opcaoDeAdicional);
        await this.obtenhaRegistroDeOperacaoService().alterouDescricaoAdicionalProduto(produto,
          { nome: opcaoDeAdicional.nomeAdicional} , opcaoDeAdicional)
      }
    }

    for(let j = 0; j < produto.camposAdicionaisRemovidos.length; j++){
      let adicional: any = produto.camposAdicionaisRemovidos[j];

      await new ProdutoService().removaOuDesvincule( adicional, produto, catalogo)
      await this.obtenhaRegistroDeOperacaoService().removeuAdicionalDeProduto((produto as any), adicional)
    }

    for(let j = 0; j < produto.opcoesRemovidas.length; j++){
      let opcaoDeAdicional: any = produto.opcoesRemovidas[j];
      let adicional = { id: opcaoDeAdicional.idAdicional, nome: opcaoDeAdicional.nomeAdicional}

      await new MapeadorDeOpcaoDeAdicionalDeProduto().removaTodos(adicional, [opcaoDeAdicional])
      await this.obtenhaRegistroDeOperacaoService().removeuAdicionalProduto(produto,
        adicional , opcaoDeAdicional)
    }

    if(produto.voltouParaOSite){ // antigamente removidos eram marcados pra nao exibir no no catalogo
      (produto as any).disponivelParaDelivery = true;
      (produto as any).disponivelNaMesa = true;

      await mapeador.atualizeDisponibilidadeCatalogo(produto);

      await this.obtenhaRegistroDeOperacaoService().sincronizouDisponiblidadeCatalogo(produto,
        'Voltou ao catálogo do site')
    }

    if(produto.camposAdicionais.length){
      for(let j = 0 ; j < produto.camposAdicionais.length; j++){
        let campoAdicional: any = produto.camposAdicionais[j];

        if(!campoAdicional.opcoesDisponiveis || !campoAdicional.opcoesDisponiveis.length)
          throw String(`Adicional ${campoAdicional.nome} do produto ${produto.codigoPdv} - ${produto.nome} sem opçoes`)


        if(!campoAdicional.id || campoAdicional.compartilhado){
          await produtoService.insiraAdicionalImportado(catalogo, produto, campoAdicional);
        }   else{
          await new MapeadorDeOpcaoDeAdicionalDeProduto().insiraTodos(campoAdicional, campoAdicional.opcoesDisponiveis);
        }
      }

      //carregar todos para garantir a inseção das dependencias
      produto.camposAdicionais = await new MapeadorDeAdicionalDeProduto().listeAsync({idProduto: produto.id})

      await produtoService.insiraDependenciasImportadas(produto)
    }

  }

  sincronizeProdutos(catalogo: Catalogo, produtos: Array<DTOProdutoSincronizar>): Promise<any>{
    return new Promise<Number>( async (resolve, reject) => {

      new MapeadorDeProduto(catalogo).transacao(  async (conexao: any, commit: any) => {
        try{
          for(let i = 0; i <  produtos.length; i++ ){
            let produto: DTOProdutoSincronizar   = produtos[i];

            await this.sincronizeProduto(catalogo, produto);

           }

          commit( () => {
            resolve( produtos.length);
          })
        } catch (erro){
          console.log(erro)
          conexao.rollback( () => {
            reject(erro.message ? erro.message : erro)
          })
        }
      });
    })
  }

  removaProdutos(catalogo: Catalogo, produtos: Array<any>){
    return new Promise<Number>( async (resolve, reject) => {
      const mapeador = new MapeadorDeProduto(catalogo);

      mapeador.transacao(  async (conexao: any, commit: any) => {
        for(let i = 0; i <  produtos.length; i++ ) {
          let produto: any = produtos[i];

          await mapeador.removaProduto(produto );

          await this.obtenhaRegistroDeOperacaoService().removeuProduto(produto,
            'Removeu do site (sincronizou)');

        }
        commit( () => {
          resolve(produtos.length);
        })
      })
    })
  }



  removaDoCatalogo(catalogo: Catalogo, produtos: Array<any>): Promise<any>{
    return new Promise<Number>( async (resolve, reject) => {
      const mapeador = new MapeadorDeProduto(catalogo);

      mapeador.transacao(  async (conexao: any, commit: any) => {
        for(let i = 0; i <  produtos.length; i++ ) {
          let produto: any = produtos[i];
          produto.disponivelParaDelivery = false;
          produto.disponivelNaMesa = false;

          await mapeador.atualizeDisponibilidadeCatalogo(produto);

          await this.obtenhaRegistroDeOperacaoService().sincronizouDisponiblidadeCatalogo(produto,
            'Removeu do catálogo (saiu do site)')
        }
        commit( () => {
          resolve(produtos.length);
        })
      })
    })
  }


  copieProdutos(catalogoOrigem: Catalogo, catalogoDestino: Catalogo, operador: any): Promise<any> {
    return new Promise((resolve: any) => {
      let mapeadorDeCategoria = new MapeadorDeCategoria(null)
      let mapeadorDeProdutoTemplate = new MapeadorDeProdutoTemplate()
      let mapeadorDeAdicional = new MapeadorDeAdicionalDeProduto()

      mapeadorDeCategoria.buscarTodosCatalogos()
      mapeadorDeProdutoTemplate.desativeMultiCliente()

      console.log(`Importando de ${catalogoOrigem.id} para ${catalogoDestino.id}`);

      mapeadorDeProdutoTemplate.listeAsync({idCatalogo: catalogoOrigem.id, usados: true}).then((produtosTemplate: any[]) => {
        let mapaTemplates: any = {}, mapaTemplatesTamanho: any = {}, mapaTemplateAdicionais: any = {}, mapaTemplatesOpcao: any = {};

        try{
          let templatesCopiados = produtosTemplate.map((produtoTemplate: any) => {
            let templateClonado = produtoTemplate.clone();
            templateClonado.catalogo = catalogoDestino
            mapaTemplates[produtoTemplate.id] = produtoTemplate


            for(let tamanho of produtoTemplate.tamanhos) {
              tamanho.template = produtoTemplate
              if(!mapaTemplatesTamanho[produtoTemplate.id])
                mapaTemplatesTamanho[produtoTemplate.id] = {}

              mapaTemplatesTamanho[produtoTemplate.id][tamanho.descricao] = tamanho
              tamanho.templateClonado.template = templateClonado
            }

            produtoTemplate.templateClonado = templateClonado
            produtoTemplate.tipo = 'pizza'

            templateClonado.adicionais.forEach((adicionalTemplateClonado: any) => {
              let adicionalOriginal = produtoTemplate.adicionais.find((adicionalTemplate: any) =>
                adicionalTemplate.descricao === adicionalTemplateClonado.descricao)

              if(!adicionalOriginal)
                throw Error(String(`Nenhum template de adicinal encontrado com essa descricao: ` + adicionalTemplateClonado.descricao ))

              mapaTemplateAdicionais[adicionalOriginal.id] = adicionalTemplateClonado

              adicionalTemplateClonado.opcoes.forEach((opcaoTemplateClonado: any) => {
                if(opcaoTemplateClonado.tamanho){
                   let tamanhoNovo = templateClonado.tamanhos.find((item: any) => item.descricao === opcaoTemplateClonado.tamanho.descricao)

                  if (!tamanhoNovo)  throw Error(String(`Nenhum teplante tamanho encontado:` +  opcaoTemplateClonado.tamanho.descricao))

                  opcaoTemplateClonado.tamanho = tamanhoNovo;
                }

                let opcaoOriginal =
                  adicionalOriginal.opcoes.find((opcaoTemplate: any) => {
                    let mesmoNome =  opcaoTemplate.nome === opcaoTemplateClonado.nome;

                    if(!opcaoTemplateClonado.tamanho) return mesmoNome;

                    let mesmoTamanho = opcaoTemplate.tamanho && opcaoTemplateClonado.tamanho.descricao === opcaoTemplate.tamanho.descricao;

                    return mesmoNome && mesmoTamanho

                  })

                if(!opcaoOriginal){
                  let descricao =  opcaoTemplateClonado.descricao
                  if(opcaoTemplateClonado.tamanho) descricao  = descricao + ' do tamanho: ' + opcaoTemplateClonado.tamanho.descricao;

                  throw Error(String(`Nenhum template de opção de adicinal encontrado com essa descricao: ` +  descricao))
                }

                mapaTemplatesOpcao[opcaoOriginal.id] = opcaoTemplateClonado;
              })
            })

            return templateClonado
          })

          let promisesTemplates = templatesCopiados ? templatesCopiados.map((templateSalvo) => {
            return new Promise((resolveTemplate: any) => {
              console.log(`Importando template produto:  ${templateSalvo.nome}`);
              new EmpresaService().salveTemplateProduto(templateSalvo).then(() => {
                resolveTemplate();
              })
            })
          }) : []

          Promise.all(promisesTemplates).then(() => {
            mapeadorDeCategoria.listeAsync({idCatalogo: catalogoOrigem.id}).then((categorias: any[]) => {
              let mapaCategorias: any = {};
              let categoriasCopiadas = categorias.map((categoria: any) => {
                let categoriaCopiada = {
                  nome: categoria.nome,
                  posicao: categoria.posicao,
                  catalogo: catalogoDestino
                } as Categoria

                mapaCategorias[categoria.id] = categoria

                categoria.categoriaCopiada = categoriaCopiada

                categoriaCopiada.categoriaOriginal = categoria

                return categoriaCopiada
              })

              let promisesCategorias = categoriasCopiadas.map((categoriaSalva) => {
                return new Promise((resolveCategoria: any) => {
                  console.log(`Importando categoria:  ${categoriaSalva.nome}`);
                  mapeadorDeCategoria.insiraSync(categoriaSalva).then(() => {
                    resolveCategoria();
                  })
                })
              })

              Promise.all(promisesCategorias).then(() => {
                  let mapDisponibilidades: any = {};
                  let disponibiliadesCopiadas = catalogoOrigem.disponibilidades.map((disponibilidade: Disponibilidade) => {
                    let disponibilidadeCopia = disponibilidade.clone();

                    disponibilidadeCopia.catalogo = catalogoDestino;

                    mapDisponibilidades[disponibilidade.id] = disponibilidadeCopia;

                    return disponibilidadeCopia;

                  })

                  let promisesDisponibilidades = disponibiliadesCopiadas.map((disponibilidade: Disponibilidade) => {
                    return new Promise((_resolve: any) => {
                      console.log(`Importando disponibilidade...`);
                      disponibilidade.salve().then(() => {
                        _resolve();
                      })
                    })
                  })

                  Promise.all(promisesDisponibilidades).then(() => {
                    let mapeadorDeProduto = new MapeadorDeProduto(catalogoOrigem)

                    mapeadorDeProduto.buscarTodosCatalogos()

                    mapeadorDeProduto.listeAsync({idCatalogo: catalogoOrigem.id}).then(async (produtos: Produto[]) => {
                      let adicionaisCompartilhados = new Map();
                      const produtoService = new ProdutoService()


                      for(let produto of produtos) {
                        if(produto.camposAdicionais) {
                          for(let adicional of produto.camposAdicionais) {
                            if(adicional.compartilhado && !adicionaisCompartilhados.has(adicional.id)) {
                              let adicionalClonado: any = adicional.cloneParaImportar();
                              adicionalClonado.catalogo = catalogoDestino;
                              await  this.insiraAdicional(produtoService, adicionalClonado, null, catalogoDestino );
                              adicionalClonado.idAdicionalOrigem = adicional.id;
                              adicionaisCompartilhados.set(adicional.id, adicionalClonado);
                            }
                          }
                        }
                      }

                      let promisesProdutos = produtos.map((produto: Produto) => {
                        let produtoClonado = produto.clone();
                        produtoClonado.catalogo = catalogoDestino;

                        if(produto.categoria && produto.categoria.id)
                          produtoClonado.categoria = mapaCategorias[produto.categoria.id].categoriaCopiada

                        produtoClonado.disponibilidades =  produtoClonado.disponibilidades.map((disponibilidade: any) => {
                             return mapDisponibilidades[disponibilidade.id]
                        })

                        if(produto.tipo === 'pizza') {
                          let produtoPizza = produto as ProdutoPizza
                          let produtoClonadoPizza = produtoClonado as ProdutoPizza

                          if(produtoPizza.template) {
                            produtoClonadoPizza.template = mapaTemplates[produtoPizza.template.id].templateClonado

                            for(let tamanhoClonado of  produtoClonadoPizza.tamanhos)
                              tamanhoClonado.template =
                                mapaTemplatesTamanho[produtoPizza.template.id][tamanhoClonado.descricao].templateClonado

                            if(produtoClonadoPizza.camposAdicionais){
                              produtoClonadoPizza.camposAdicionais.forEach((adicionalClonado: any) => {
                                if(adicionalClonado.template){
                                  adicionalClonado.template = mapaTemplateAdicionais[   adicionalClonado.template.id];

                                  adicionalClonado.opcoesDisponiveis.forEach((opcaoClonada: any) => {
                                    if(opcaoClonada.template)
                                      opcaoClonada.template  = mapaTemplatesOpcao[opcaoClonada.template.id];
                                  })
                                }
                              })
                            }
                          }
                        }

                        if(produtoClonado.camposAdicionais) {
                          produtoClonado.camposAdicionais = produtoClonado.camposAdicionais.map((adicional: any) => {
                            if(!adicional.compartilhado) return adicional;

                            const adicionalReferencia = adicionaisCompartilhados.get(adicional.idAdicionalOrigem);

                            if(!adicionalReferencia)
                              throw Error(`Adicional compartilhado "${adicional.nome}" com id referenciado: ${adicional.idAdicionalOrigem}`)

                            return adicionalReferencia;

                          });
                        }

                        return new Promise(async (resolveProduto: any) => {
                          console.log(`********** Importando produto :  ${produtoClonado.nome}`);
                          produtoService.salveProduto(produtoClonado, false).then(async () => {
                            console.log( 'Produto ' + produtoClonado.nome + ' inserido com sucesso')
                            if(produtoClonado.camposAdicionais && produtoClonado.camposAdicionais.length > 0)
                              console.log(`Importando adicionais do  produto :  ${produtoClonado.nome}`);
                            for(let adicional of produtoClonado.camposAdicionais)
                              await this.insiraAdicional(produtoService, adicional, produtoClonado, catalogoDestino)

                            console.log('Importou todos adicionais do  produto ' + produtoClonado.nome)
                            resolveProduto();
                          })
                        })
                      })
                      Promise.all(promisesProdutos).then(async () => {

                        await this.finalizouImportacaoProdutos(catalogoOrigem, catalogoDestino, promisesProdutos.length, operador);


                        let msg = "Produtos inseridos com sucesso: " + promisesProdutos.length;
                        resolve(msg);
                      })
                    })
                  })
                }
              )
            })
          })
        }catch (err){
          if(err){
            console.log('importação encerrou com erro: ')
            console.warn(err)
          }

          resolve(err)
        }
      })
    })
  }

  baixeImagensOpcoes(opcoes: any []) {
    return new Promise((resolve) => {
      let promises = []
      for(let opcao of opcoes) {
        let urlImagemExterna = opcao.urlImagemExterna

        delete opcao.urlImagemExterna

        if(urlImagemExterna)
          promises.push(new Promise<any>((resolveInterno) => {
            let partes = urlImagemExterna.split('&')[0].split("/");
            partes =  partes[partes.length - 1].split(".");

            let extensao = partes[partes.length - 1],
              nomeArquivo = String(`${uuidv1()}.${extensao}`),
              arquivo =  path.join(Ambiente.Instance.config.caminhoImagens, 'empresa', nomeArquivo)

            if(extensao === 'jfif')
              return resolveInterno({ erro: "Não foi possível fazer o download da imagem da opção " + opcao.nome});

            const file = fs.createWriteStream(arquivo);
            let fileInfo: any = null;

            const proto = https;

            const request = proto.get(urlImagemExterna, response => {
              if (response.statusCode !== 200) {
                resolveInterno({ erro: `Falha ao fazer download imagem '${nomeArquivo}' (${response.statusCode})`, nomeOpcao: opcao.nome});

                return
              } else {
                fileInfo = {
                  mime: response.headers['content-type'],
                  size: parseInt(response.headers['content-length'], 10),
                };

                response.pipe(file);
              }
            });

            // The destination stream is ended by the time it's called
            file.on('finish', () => {
              opcao.linkImagem = nomeArquivo
              resolveInterno(fileInfo)
            });

            request.on('error', (err: any) => {
              console.log(err)
              fs.unlink(arquivo, () => resolve({erro: err ,  opcao: opcao.nome}));
            });

            request.end();

          }))
      }


      Promise.all(promises).then((resultados) => {
        resolve(resultados);
      })
    })
  }

  async sincronizeImagem(produtoAtualizar: any){

    produtoAtualizar.linkImagem = produtoAtualizar.novaImagem.linkImagem;
    produtoAtualizar.urlImagemExterna = produtoAtualizar.novaImagem.urlImagemExterna;
    await this.baixeImagens([produtoAtualizar]);
    if(produtoAtualizar.imagens && produtoAtualizar.imagens.length){
      await new MapeadorDeImagemDoProduto().atualizeImagens(produtoAtualizar);
      await this.obtenhaRegistroDeOperacaoService().sincronizouFotoProduto(produtoAtualizar)
    }
  }

  baixeImagens(produtos: any[]) {
    return new Promise((resolve) => {
      let promises = []
      for(let produto of produtos) {
        let urlImagemExterna = produto.urlImagemExterna

        delete produto.urlImagemExterna
        if(urlImagemExterna)
          promises.push(new Promise<any>((resolveInterno) => {
            let  urlImagem = produto.linkImagem,
                 partes = urlImagemExterna.split('&')[0].split("/");

              partes =  partes[partes.length - 1].split(".");

              let extensao = partes[partes.length - 1],
              nomeArquivo = String(`${uuidv1()}.${extensao}`),
              arquivo =  path.join(Ambiente.Instance.config.caminhoImagens, 'empresa', nomeArquivo)

            if(extensao === 'jfif')
              return resolveInterno({ erro: "Não foi possível fazer o download da imagem", idIfood: produto.idIfood});

            const file = fs.createWriteStream(arquivo);
            let fileInfo: any = null;

            const proto = https;

            const request = proto.get(urlImagemExterna, response => {
              if (response.statusCode !== 200) {
                resolveInterno({ erro: `Falha ao fazer download imagem '${urlImagem}' (${response.statusCode})`, idIfood: produto.idIfood});

              } else {
                fileInfo = {
                  mime: response.headers['content-type'],
                  size: parseInt(response.headers['content-length'], 10),
                };

                response.pipe(file);
              }
            });

            // The destination stream is ended by the time it's called
            file.on('finish', () => {
              if(!produto.imagens)
                produto.imagens = []
              produto.imagens[0] = new ImagemDoProduto(nomeArquivo, 0);
              resolveInterno(fileInfo)
            });

            request.on('error', (err: any) => {
              console.log(err)
              fs.unlink(arquivo, () => resolve({erro: err ,  idIfood: produto.idIfood}));
            });

            request.end();
          }))

      }


      Promise.all(promises).then((resultados) => {
        resolve(resultados);
      })
    })
  }

  private obtenhaRegistroDeOperacaoService(): RegistroDeOperacaoService {
    if(!this.registroDeOperacaoService) this.registroDeOperacaoService = new RegistroDeOperacaoService(
      Ambiente.Instance.usuarioLogado(), Ambiente.Instance.ip()
    )

    return this.registroDeOperacaoService
  }

  private async insiraAdicional(produtoService: ProdutoService, adicional: AdicionalDeProduto,
                                produtoClonado: Produto, catalogoDestino: Catalogo) {
    return new Promise((resolveAdicional: any) => {
      console.log(`Importando adicional :  ${adicional.nome}`);
      if(adicional.nome === 'Adicional Compartilhado'){
        console.log(adicional.opcoesDisponiveis.length)
      }
      produtoService.insiraAdicionalImportado(catalogoDestino, produtoClonado, adicional).then(() => {
        console.log('Adicional ' + adicional.nome + ' inserido com sucesso')
        resolveAdicional();
      })
    })
  }

  private async finalizouImportacaoProdutos(catalogoOrigem: Catalogo, catalogoDestino: any, totalProdutos: number, operador: any,) {
    await new MapeadorDeCatalogo().atualizeDataUltimaAtualizacaoProdutos(catalogoDestino);
    let empresa: any = Ambiente.Instance.contexto().empresa;
    new RegistroDeOperacaoService(operador, Ambiente.Instance.ip()).importouProdutosDoCatalogo(empresa, catalogoOrigem, totalProdutos);
    await new MapeadorDeProduto(catalogoDestino).removaCacheProdutos();
  }
}
