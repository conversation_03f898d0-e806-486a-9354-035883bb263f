import {Empresa} from "../../domain/Empresa";
import {Ambiente} from "../../service/Ambiente";
import {Produto} from "../../domain/Produto";
import {MapeadorDeCategoria} from "../../mapeadores/MapeadorDeCategoria";
import {Categoria} from "../../domain/delivery/Categoria";
import {MapeadorDeProduto} from "../../mapeadores/MapeadorDeProduto";
import {MapeadorDeEmpresa} from "../../mapeadores/MapeadorDeEmpresa";
import {AdicionalDeProdutoMultiplaEscolha} from "../../domain/delivery/AdicionalDeProdutoMultiplaEscolha";
import {OpcaoDeAdicionalDeProduto} from "../../domain/delivery/OpcaoDeAdicionalDeProduto";
import {EnumDisponibilidadeProduto} from "../emun/EnumDisponibilidadeProduto";
import {EnumTipoDeCobrancaDeAdicional} from "../emun/EnumTipoDeCobrancaDeAdicional";
import {ImportadorProduto} from "./ImportadorProduto";
import {ProdutoService} from "../../service/ProdutoService";
import {EnumTipoDeOrigem} from "../emun/EnumTipoDeOrigem";
import {Catalogo} from "../../domain/catalogo/Catalogo";

const uuidv1 = require('uuid/v1');
let path = require('path');
let fs = require('fs');
let readline = require('readline');



export class ImportadorProdutosCSV {
  constructor(private empresa: Empresa) {}
  CSVtoArray(text: string) {
    //var re_valid = /^\s*(?:'[^'\\]*(?:\\[\S\s][^'\\]*)*'|"[^"\\]*(?:\\[\S\s][^"\\]*)*"|[^,'"\s\\]*(?:\s+[^,'"\s\\]+)*)\s*(?:,\s*(?:'[^'\\]*(?:\\[\S\s][^'\\]*)*'|"[^"\\]*(?:\\[\S\s][^"\\]*)*"|[^,'"\s\\]*(?:\s+[^,'"\s\\]+)*)\s*)*$/;
    let re_value = /(?!\s*$)\s*(?:'([^'\\]*(?:\\[\S\s][^'\\]*)*)'|"([^"\\]*(?:\\[\S\s][^"\\]*)*)"|([^,'"\s\\]*(?:\s+[^,'"\s\\]+)*))\s*(?:,|$)/g;
    // Return NULL if input string is not well formed CSV string.
    //if (!re_valid.test(text)) return null;
    let a = [];                     // Initialize array to receive values.
    text.replace(re_value, // "Walk" the string using replace with callback.
      function(m0: any, m1: any, m2: any, m3: any) {
        // Remove backslash from \' in single quoted values.
        if      (m1 !== undefined) a.push(m1.replace(/\\'/g, "'"));
        // Remove backslash from \" in double quoted values.
        else if (m2 !== undefined) a.push(m2.replace(/\\"/g, '"'));
        else if (m3 !== undefined) a.push(m3);
        return ''; // Return empty string.
      });
    // Handle special case of empty last value.
    if (/,\s*$/.test(text)) a.push('');
    return a;
  };

  importeProdutosDoArquivo(uploadedFile: any, tipo: string, config: any) {
    return new Promise((resolve, reject) => {
      let hoje = new Date();
      let diretorio = Ambiente.Instance.config.caminhoImagens,
        nomeArquivo = String(`${uuidv1()}.${tipo}`),
        arquivo: string = path.join(diretorio, 'empresa', nomeArquivo);

      if(!fs.existsSync(diretorio)) {
        console.log(diretorio)
        return resolve({
          sucesso: false,
          erro: "Diretório upload de arquivo csv não existe"
        })
      }

      console.log(arquivo)
      console.log('mimetype: ' + uploadedFile.mimetype)

      let mapaCategorias =  new Map()

      let novasCategorias: any[] = []
      let novosProdutos: any[] = []

      if(!this.empresa.catalogo.categorias) this.empresa.catalogo.categorias = []

      for(let categoria of this.empresa.catalogo.categorias)
        mapaCategorias.set(categoria.nome, categoria)


      let mapeadorDeEmpresa = new MapeadorDeEmpresa()

      mapeadorDeEmpresa.transacao(async(conexao: any, commit: any) => {
        uploadedFile.mv(arquivo).then(async (err: any) => {
          if(!err){
            let fileStream  = fs.createReadStream(arquivo)

            let rd = readline.createInterface({
              input: fileStream,
              crlfDelay: Infinity
            });

            let i = 0;
            let produtoAtual: Produto = null;
            let adicionalAtual: any = null;
            let ordemOpcao = 0;
            let ordemAdicional = 0;
            let todasOpcoes: any = []
            for await (const line of rd) {
              if(i++ === 0)
                continue;

              let colunas = this.CSVtoArray(line)

              // Each line in input.txt will be successively available here as `line`.

              if(!colunas) continue;

              let coluna_nome: any,
                coluna_preco: any,
                coluna_descricao: any,
                coluna_categoria: any,
                coluna_adicionais: any,
                coluna_codigopdv: any,
                coluna_linkimagem: any,
                coluna_nomeadicional: any,
                coluna_opcaoadicional: any;

              let separadorDecimal = config ? config.separadorDecimal : '.'


              if(config && config.colunas)
              {
                let indiceColuna: any;

                for(indiceColuna in config.colunas) {
                  let valor = config.colunas[indiceColuna]
                  switch (valor) {
                    case 'Nome':
                      coluna_nome = indiceColuna;
                      break;
                    case 'Preço':
                      coluna_preco = indiceColuna;
                      break;
                    case 'Categoria':
                      coluna_categoria = indiceColuna;
                      break;
                    case 'Descricao':
                      coluna_descricao = indiceColuna;
                      break;
                    case 'Adicionais':
                      coluna_adicionais = indiceColuna;
                      break;
                    case 'CodigoPDV':
                      coluna_codigopdv = indiceColuna;
                      break;
                    case 'LinkImagem':
                      coluna_linkimagem = indiceColuna;
                      break;
                    case 'NomeAdicional':
                      coluna_nomeadicional = indiceColuna;
                      break;
                    case 'OpcaoAdicional':
                      coluna_opcaoadicional = indiceColuna;
                      break;
                  }

                }
              }

              if(! colunas[coluna_preco])
                throw Error(String(`linha ${i} invalida , nao tem preço definido: "${line}"`))


              let nome = colunas[coluna_nome]
              let preco = Number(config.separadorDecimal.codigo === "," ?  colunas[coluna_preco].replace(',', '.') : colunas[coluna_preco])
              let nomeCategoria = coluna_categoria ?  colunas[coluna_categoria] : null
              let adicionais = coluna_adicionais && colunas[coluna_adicionais] ? colunas[coluna_adicionais].split(',') : null
              let codigoPDV = coluna_codigopdv ? colunas[coluna_codigopdv] : null
              let linkImagem = coluna_linkimagem ? colunas[coluna_linkimagem] : null
              let nomeAdicional = coluna_nomeadicional ? colunas[coluna_nomeadicional] : null
              let opcaoAdicional = coluna_opcaoadicional ? colunas[coluna_opcaoadicional] : null
              let descricao = coluna_descricao ? colunas[coluna_descricao] : ''

              if(descricao)
                descricao = descricao.replace(/<style[^>]*>.*<\/style>/gm, '')
                  // Remove script tags and content
                  .replace(/<script[^>]*>.*<\/script>/gm, '')
                  // Remove all opening, closing and orphan HTML tags
                  .replace(/<[^>]+>/gm, '')
                  // Remove leading spaces and repeated CR/LF
                  .replace(/([\r\n]+ +)+/gm, '');

              if(produtoAtual && nome && nome !== produtoAtual.nome) {
                adicionalAtual = null
                produtoAtual = null
                ordemAdicional = 0;
              }

              if(!nome && nomeAdicional) {
                if(!adicionalAtual || adicionalAtual.nome !== nomeAdicional) {
                  adicionalAtual = new AdicionalDeProdutoMultiplaEscolha(nomeAdicional, true, [],
                    1, 10, true, EnumTipoDeCobrancaDeAdicional.SOMA, 'produto')
                  produtoAtual.camposAdicionais.push(adicionalAtual)
                  ordemOpcao = 0;
                  adicionalAtual.ordem = ordemAdicional++
                }

                if(descricao)
                  descricao = descricao.substring(0, 254)

                let opcao: any = new OpcaoDeAdicionalDeProduto(opcaoAdicional, preco, true, descricao)
                opcao.ordem = ordemOpcao++
                if(linkImagem) opcao.urlImagemExterna = linkImagem
                //incluir importação de imagem
                if(!adicionalAtual.opcoesDisponiveis)
                  adicionalAtual.opcoesDisponiveis = []
                adicionalAtual.opcoesDisponiveis.push(opcao)
                todasOpcoes.push(opcao)

              } else {
                let categoria = mapaCategorias.get(nomeCategoria)

                if(!categoria) {
                  categoria = new Categoria(null, nomeCategoria, this.empresa.catalogo)
                  categoria.posicao = this.empresa.categorias.length

                  mapaCategorias.set(categoria.nome, categoria)

                  this.empresa.catalogo.categorias.push(categoria)

                  novasCategorias.push(categoria)
                }

                let novoProduto: any = new Produto(null, nome, preco, descricao,
                  null, null, this.empresa.catalogo, false, EnumDisponibilidadeProduto.SempreDisponivel, false, categoria)

                produtoAtual = novoProduto

                novoProduto.temEstoque = true
                novoProduto.codigoPdv = codigoPDV
                novoProduto.origem = EnumTipoDeOrigem.ImportadoCsv
                novoProduto.dataCadastro = hoje

                if(linkImagem){
                  novoProduto.urlImagemExterna =  linkImagem
                }

                novosProdutos.push(novoProduto)

                if(adicionais && adicionais.length > 0) {
                  let opcoes = []

                  for(let adicional of adicionais)
                    opcoes.push(new OpcaoDeAdicionalDeProduto(adicional, 0, true))

                  let adicionalDeProdutoMultiplaEscolha = new AdicionalDeProdutoMultiplaEscolha('Adicionais',
                    false, opcoes, 0, 10, true, EnumTipoDeCobrancaDeAdicional.SOMA, 'produto');

                  novoProduto.camposAdicionais.push(adicionalDeProdutoMultiplaEscolha)

                }
              }
            }

            let mapeadorDeCategoria = new MapeadorDeCategoria(this.empresa.catalogo)

            let promises: any = []

            for(let novasCategoria of novasCategorias) promises.push(mapeadorDeCategoria.insiraGraph(novasCategoria))
            promises.push(new MapeadorDeProduto(this.empresa.catalogo).removaCacheProdutos())
            promises.push(new Promise((resolveCacheDeLista: any) =>
              {
                mapeadorDeEmpresa.removaListaDeCategoriasDaCache(this.empresa)
                resolveCacheDeLista();
              }))



            let importador = new ImportadorProduto()
            importador.baixeImagens(novosProdutos).then(() => {
              importador.baixeImagensOpcoes(todasOpcoes).then (() => {
                Promise.all(promises).then((inseriuTodas: any) => {
                  promises = []

                  let mapeadorDeProduto = new MapeadorDeProduto(this.empresa.catalogo)

                  for(let novoProduto of novosProdutos) {
                    let camposAdicionais = novoProduto.camposAdicionais
                    delete novoProduto.camposAdicionais
                    let produtoService = new ProdutoService()

                    promises.push(new Promise((resolveProduto: any) => {
                      produtoService.salveProduto(novoProduto).then(() => {
                        let promisesAdicionais = []


                        for (let adicional of camposAdicionais) {
                          promisesAdicionais.push(produtoService.insiraAdicionalProduto(this.empresa.catalogo, novoProduto, adicional))
                        }

                        Promise.all(promisesAdicionais).then(() => {
                          resolveProduto();
                        })
                      })
                    }))
                  }

                  Promise.all(promises).then((inseriuTodos: any) => {
                    new MapeadorDeProduto(this.empresa.catalogo).recalculeOrdens(this.empresa.catalogo).then(() => {
                      (new MapeadorDeEmpresa()).removaDasCaches(this.empresa)
                      commit(() => {
                        resolve({
                          sucesso: true,
                          data: {
                            categoriasInseridas: novasCategorias.length,
                            produtosInseridos: novosProdutos.length
                          }
                        });
                      })

                    })
                  })
                })

              }).catch((erro) => {
                resolve({
                  sucesso: false,
                  erro: "Houve um erro ao baixar as imagens das opções: " + erro
                })
              })
            }).catch((erro) => {
              resolve({
                sucesso: false,
                erro: "Houve um erro ao baixar as imagens: " + erro
              })
            })


          }
        }).catch((reason: any) => {
          console.error(reason)
          conexao.rollback(() => {
            resolve({
              sucesso: false,
              erro: 'Houve um erro ao importar o arquivo csv: ' + reason
            })
          })
        })
      })




      /*

       */

    })
  }

  obtenhaProdutos(uploadedFile: any, tipo: string, config: any){
    return new Promise((resolve, reject) => {
      let diretorio = Ambiente.Instance.config.caminhoImagens,
        nomeArquivo = String(`${uuidv1()}.${tipo}`),
        arquivo: string = path.join(diretorio, 'empresa', nomeArquivo);

      let produtos: any = []

      if(!fs.existsSync(diretorio)) {
        console.log(diretorio)
        return reject("Diretório upload de arquivo csv1 não existe")
      }

      console.log(arquivo)
      console.log('mimetype: ' + uploadedFile.mimetype)

      uploadedFile.mv(arquivo).then(async (err: any) => {
        if(!err) {

          let fileStream = fs.createReadStream(arquivo)

          let rd = readline.createInterface({
            input: fileStream,
            crlfDelay: Infinity
          });
          let i = 0;
          for await (const line of rd) {
            if (i++ === 0)
              continue;

            let colunas = this.CSVtoArray(line)
            if (!colunas) continue;
            let coluna_nome: any,
              coluna_preco: any,
              coluna_codigopdv: any;

            if (config && config.colunas) {
              let indiceColuna: any;

              // tslint:disable-next-line:forin
              for (indiceColuna in config.colunas) {
                let valor = config.colunas[indiceColuna]
                switch (valor) {
                  case 'Nome':
                    coluna_nome = indiceColuna;
                    break;
                  case 'Preço':
                    coluna_preco = indiceColuna;
                    break;
                  case 'CodigoPDV':
                    coluna_codigopdv = indiceColuna;
                }
              }
            }

            if(! colunas[coluna_preco])
              return reject(String(`linha ${i} invalida , nao tem preço definido: "${line}"`))


            if(! colunas[coluna_codigopdv])
              return reject(String(`linha ${i} invalida , nao tem codigo pdv definido: "${line}"`))

            let nome = colunas[coluna_nome]
            let preco = Number(config.separadorDecimal.codigo === "," ?  colunas[coluna_preco].replace(',', '.') : colunas[coluna_preco])
            let codigoPDV = coluna_codigopdv ? colunas[coluna_codigopdv] : null


            let produto: any = { nome: nome, preco: preco, codigoPdv: codigoPDV}

            produtos.push(produto)

          }
          resolve(produtos)
        } else {
          reject(err)
        }
      })
    })
  }
}
