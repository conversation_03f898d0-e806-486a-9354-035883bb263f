export class EnumUtils{

   static ConvertEnumKeyToReadableString(enumKey: any) {
     // Divida a string em palavras usando letras maiúsculas como delimitadores
     const words = enumKey.split(/(?=[A-Z])/);

     // Capitalize a primeira letra de cada palavra e adicione um espaço entre elas
     const readableString = words.map((word: any) => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');

     return readableString;
  }
}
