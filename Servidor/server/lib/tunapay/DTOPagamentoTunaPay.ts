import * as moment from "moment";
import {PedidoGenerico} from "../../domain/delivery/PedidoGenerico";
import {Ambiente} from "../../service/Ambiente";


export class DTOPagamentoTunaPay{
  partnerUniqueId: string;
  customer: any;
  paymentItems: any =  {items: []};
  paymentData: any = { paymentMethods: [], deliveryAddress: null, countryCode: "BR"};
  tokenSession: string;
  frontData: any;
  constructor(pedido: PedidoGenerico, dadosPagamento: any) {
    this.partnerUniqueId = pedido.id.toString();
    this.customer  = {
      id: "", //string
      email: pedido.contato.email || dadosPagamento.email,
      name: pedido.contato.nome,
      daysSinceRegistration: moment().diff(  moment( pedido.contato.dataCadastro), 'days'),
      daysSinceLastLogin: moment().diff(  moment(pedido.contato.ultimaVisita), 'days'),
      data: {
        phoneNumber: pedido.contato.telefone
      }
    }

    if(pedido.contato.cpf || dadosPagamento.cpf){
      this.customer.documentType = "CPF"
      this.customer.document = pedido.contato.cpf || dadosPagamento.cpf
    }

    if(!pedido.empresa.meucardapioPay) throw Error('Meucardapio Pay não esta ativo')

    let merchantId: any =  pedido.empresa.meucardapioPay.idLoja;

    let itemUnico: any = {
      "amount": pedido.obtenhaTotalPagar(),
      "productDescription": String(`Pedido #${pedido.codigo} - ${pedido.empresa.nome}`),
      "detailUniqueId": pedido.codigo,
      "itemQuantity": 1,
      "split": {
        "merchantID": merchantId
      }
    }

    this.paymentItems.items.push(itemUnico)


    if(pedido.endereco){
      this.paymentData.deliveryAddress =  {
        "street":  pedido.endereco.logradouro,
        "number": pedido.endereco.numero || "",
        "neighborhood": pedido.endereco.bairro,
        "city": pedido.endereco.cidade.nome,
        "state":  pedido.endereco.cidade.estado.sigla,
        "postalCode": pedido.endereco.cep,
        "phone": ``,   //"(11) 6536-8864"
        "country": "BR"
      }
    }

    if(Ambiente.Instance.producao)
      this.paymentData.callbackUrl =  `${pedido.empresa.obtenhaHostLoja()}/pagamentos/tunapay/payment`
  }

  getValorTaxaSplitMeucadapio(taxaPix: number, pixTaxaMinima: number){
    const split: any =  this.paymentItems.items[0].split;
    //enviado valor minimo
    if(  split && split.amount != null)
      return pixTaxaMinima;

    const valorPedido: number = this.getValorPagamento();

    return  Number((valorPedido * taxaPix).toFixed(2));
  }

  setPagamentoCartao(dadosCartao: any, descricaoCartao: string,
                     taxaPix: number, pixTaxaMinima: number){
    this.tokenSession  = dadosCartao.tokenSession;

    let dadosPagamento: any = {
      "paymentMethodType": "1",
      "amount": this.paymentItems.items[0].amount,
      "installments":  dadosCartao.parcela ? dadosCartao.parcela : 1,
      "softDescriptor": descricaoCartao,
      cardInfo: { }
    }

    if(dadosCartao.externalProvider === 'GooglePay'){
      let tokenGoogle = JSON.parse( dadosCartao.token)

      console.log(tokenGoogle)

      dadosPagamento.cardInfo = {
        tokenProvider:  dadosCartao.externalProvider,
        token: dadosCartao.token,
        data: {
          walletKey: tokenGoogle.signedMessage
        }
      }
    } else  if(dadosCartao.externalProvider === 'ApplePay'){
      let tokenApple =  dadosCartao.token;
      console.log( tokenApple)
      dadosPagamento.cardInfo = {
        tokenProvider:  dadosCartao.externalProvider,
        token: JSON.stringify(tokenApple.paymentData)
      }
    } else {
      dadosPagamento.cardInfo = {
        tokenProvider: "Tuna",
        token: dadosCartao.token,
        brandName: dadosCartao.bandeira,
        cardHolderName: dadosCartao.nome.replace(/\s+/, ' '),
        saveCard: false,
        billingInfo:  {
          document: dadosCartao.cpf.replace(/\D/g, ''),
          documentType: "CPF",
          address: new DTOEndereco(dadosCartao.endereco)
        }
      }


      //"cardHolderName": "Captured",
      //  "expirationMonth": 12,
      //  "expirationYear": 2023,
    }


    this.paymentData.paymentMethods.push(dadosPagamento)

    if(dadosCartao.deviceInfo)
      this.frontData  = dadosCartao.deviceInfo

  }

  setPagamentoPix(taxaPix: number, pixTaxaMinima: number, descricaoPagador: string){
    let dadosPix: any = {
      "paymentMethodType": "D",
      "amount": this.paymentItems.items[0].amount,
      "pix": {
        "name":  this.customer.name,
        "expirationSeconds": 60 * 60  //1h
      },
      "softDescriptor": descricaoPagador || "MeuCardapio ai"
    }

    if(this.customer.documentType){
      dadosPix.pix.documentType = this.customer.documentType;
      dadosPix.pix.document = this.customer.document;
    }

    this.paymentData.paymentMethods.push(dadosPix)

    try{
      let valorTaxa = this.getValorPagamento() * taxaPix;

      if(valorTaxa < pixTaxaMinima){
        let valorRepasse = this.getRepasseComSplitMinimo(pixTaxaMinima);
        this.paymentItems.items[0].split.amount = valorRepasse //sobrepor o condititon cadastrado no tuna
      }
    } catch (err){
      console.warn('valor minimo nao sera aplicado...')
      console.warn(err)
    }
  }

  obtenhaDataExpiracao(){
    let pixInfo: any =  this.paymentData.paymentMethods.find((item: any) => item.pix)

    return pixInfo ?  new Date(new Date().getTime() +  pixInfo.pix.expirationSeconds  * 1000) : null;
  }

  private getRepasseComSplitMinimo(pixTaxaMinima: number){
    const valorPedido: number = this.getValorPagamento();

    let valorRepasse = Number((valorPedido - pixTaxaMinima).toFixed(2))

    console.log( `Valor minimo taxa "${pixTaxaMinima}" nao foi atingido. Valor repasse sera enviado : ${valorRepasse}`);

    return valorRepasse;
  }

  getValorPagamento(){
    if(this.paymentItems.items.length > 1) throw Error('Calculo valor minimo feito apenas para item unico no carrinho')


    return this.paymentItems.items.reduce((soma: number, item: any) => soma + item.amount, 0)
  }
}



class DTOEndereco{
  Street: string
  Number: string
  Complement: string
  Neighborhood: string ;
  City: string
  State: string
  Country: "BR"
  PostalCode: string
  Phone: string; //"(61) 324 421 21"
  constructor(endereco: any) {
    this.City = endereco.cidade.nome;
    this.State = endereco.cidade.estado.sigla;
    this.Neighborhood = endereco.bairro;
    this.PostalCode = endereco.cep
    this.Number = `${endereco.numero || 'SN'}`
    this.Street =  endereco.logradouro || '';
    this.Complement = endereco.complemento || ''
  }
}
