export class ErrosRedeTransacao {
  static TABELAERROS: any = {
    "1": "Parâmetro enviado com tamanho inválido",
    "2": "Ano expiração cartão inválido",
    "3": "Parâmetro obrigatório não está presente",
    "4": "CAVV com tamanho inválido",
    "5": "CAVV inválido",
    "6": "Parâmetro enviado com tamanho inválido",
    "7": "Cep inválido",
    "8": "Parâmetro obrigatório não está presente",
    "9": "Parâmetro enviado com tamanho inválido",
    "10": "Complemento inválido",
    "11": "Taxa embarque inválido",
    "12": "Parâmetro enviado com tamanho inválido",
    "13": "Numero documento inválido",
    "14": "Parâmetro obrigatório não está presente",
    "15": "Parâmetro enviado com tamanho inválido",
    "16": "Código segurança inválido",
    "17": "Código segurança com tamanho inválido",
    "18": "Formato do parâmetro inválido: distributorAffiliation",
    "19": "Parâmetro enviado com tamanho inválido",
    "20": "Formato do parâmetro inválido: eci",
    "21": "Parâmetro obrigatório para Visa não está presente",
    "22": "Parâmetro obrigatório não está presente",
    "23": "Formato do parâmetro inválido: street",
    "24": "Parâmetro enviado com tamanho inválido",
    "25": "Formato do parâmetro inválido: affiliation",
    "26": "Parâmetro obrigatório não está presente",
    "27": "Parâmetro cavv ou eci não está presente",
    "28": "Parâmetro enviado com tamanho inválido",
    "29": "Formato do parâmetro inválido: code",
    "30": "Parâmetro obrigatório não está presente",
    "31": "Parâmetro enviado com tamanho inválido",
    "32": "Formato do parâmetro inválido: softdescriptor",
    "33": "Formato do parâmetro inválido: expirationMonth",
    "34": "Formato do parâmetro inválido: code",
    "35": "Parâmetro obrigatório não está presente",
    "36": "Parâmetro enviado com tamanho inválido",
    "37": "Número do cartão inválido.",
    "38": "Parâmetro obrigatório não está presente",
    "39": "Parâmetro enviado com tamanho inválido",
    "40": "Formato do parâmetro inválido: reference",
    "41": "Parâmetro obrigatório não está presente",
    "42": "Número de pedido já existente",
    "43": "Parâmetro enviado com tamanho inválido",
    "44": "Formato do parâmetro inválido: number",
    "45": "Parâmetro obrigatório não está presente",
    "46": "Quantidade de parcelas não corresponde com a transação autorizada",
    "47": "Formato do parâmetro inválido: origin",
    "48": "Parâmetro enviado com tamanho inválido",
    "49": "O valor da transação excede o valor autorizado",
    "50": "Formato do parâmetro inválido: installments",
    "51": "Produto ou serviço desabilitado para esse lojista. Contate a Rede",
    "53": "Transação não permitida para este emissor. Contate a Rede",
    "54": "Parâmetro não permitido para esta transação",
    "55": "Parâmetro enviado com tamanho inválido",
    "56": "Erro nos dados reportados. Tente novamente",
    "57": "Lojista inválido enviado no parâmetro",
    "58": "Não autorizado. Contate a central do cartão",
    "59": "Formato do parâmetro inválido: cardHolderName",
    "60": "Parâmetro enviado com tamanho inválido",
    "61": "Formato do parâmetro inválido: subscription  ",
    "63": "Produto não habilitado",
    "64": "Transação não processada. Tente novamente",
    "65": "Chave de integração não está presente",
    "66": "Parâmetro enviado com tamanho inválido",
    "67": "Formato do parâmetro inválido: departureTax",
    "68": "Parâmetro obrigatório não está presente",
    "69": "Transação não permitida para este produto ou serviço",
    "70": "Parâmetro enviado com tamanho inválido",
    "71": "Formato do parâmetro inválido: amount",
    "72": "Contate a central do cartão",
    "73": "Parâmetro obrigatório não está presente",
    "74": "Falha na comunicação, tente novamente",
    "75": "Parâmetro não deve ser enviado para este tipo de transação",
    "76": "Formato do parâmetro inválido: kind",
    "78": "Transação não existe",
    "79": "Cartão vencido. Não tente novamente e contate a central do cartão",
    "80": "Saldo insuficiente. Contate a central do cartão",
    "82": "Transação não autorizada para cartão de débito",
    "83": "Não autorizado. Contate a central do cartão",
    "84": "Não autorizado. Não tente novamente e contate a central do cartão",
    "85": "Parâmetro enviado com tamanho inválido",
    "86": "Cartão vencido",
    "87": "Campo tid ou reference não está preenchido.",
    "88": "Lojista não aprovado. Regularize seu site e contate a Rede para voltar a transacionar.",
    "89": "Chave de integração inválida",
    "97": "Parâmetro enviado com tamanho inválido",
    "98": "Formato do parâmetro inválido: tid",
    "99": "Formato do parâmetro inválido: BusinessApplicationIdentifier",
    "100": "Formato do parâmetro inválido: WalletId",
    "132": "Parâmetro enviado com tamanho inválido.",
    "133": "Valor do parâmetro inválido.",
    "150": "Tempo esgotado. Tente novamente",
    "151": "Valor do parâmetro maior do que o permitido",
    "153": "Valor do parâmetro inválido",
    "154": "Formato do parâmetro inválido: embedded",
    "155": "Parâmetro obrigatório não está presente",
    "156": "Parâmetro enviado com tamanho inválido",
    "157": "Parâmetro obrigatório não está presente",
    "158": "Valor não permitido para esta transação",
    "159": "Parâmetro enviado com tamanho inválido",
    "160": "Parâmetro obrigatório não está presente",
    "161": "Formato do parâmetro inválido: urls",
    "167": "Pedido JSON inválido",
    "169": "Content-Type inválido",
    "171": "Operação não permitida para essa transação",
    "173": "Autorização expirou",
    "176": "Parâmetro obrigatório não está presente",
    "370": "Pedido falhou. Contate a Rede",
    "898": "PV com ip de origem inválido",
    "899": "Sem sucesso. Por favor, contate a Rede",
    "1002": "Parâmetro enviado com tamanho inválido",
    "1003": "Parâmetro obrigatório não está presente.",
    "1018": "Parâmetro enviado com tamanho inválido",
    "1019": "Parâmetro obrigatório não está presente",
    "1020": "Formato do parâmetro inválido: MCC",
    "1021": "Parâmetro enviado com tamanho inválido",
    "1023": "Formato do parâmetro inválido: PaymentFacilitatorID",
    "1027": "Parâmetro enviado com tamanho inválido: SubMerchant",
    "1030": "Parâmetro enviado com tamanho inválido: CitySubMerchant",
    "1032": "Parâmetro enviado com tamanho inválido: SubMerchant",
    "1034": "Parâmetro enviado com tamanho inválido: CountrySubMerchant",
    "1036": "Parâmetro enviado com tamanho inválido: CepSubMerchant",
    "1038": "Parâmetro enviado com tamanho inválido: CnpjSubMerchant",
    "3020": "Parâmetro enviado com tamanho inválido.",
    "3025": "Negativa de categoria 01: Este cartão não deve ser usado novamente",
    "3026": "Negativa de categoria 02: Esse cartão não deve ser usado novamente nesse PV",
    "3027": "Negativa de categoria 03: Esse cartão não deve ser usado novamente nesse PV",
    "3028": "Parâmetro obrigatório não está presente",
    "3029": "Parâmetro enviado com tamanho inválido",
    "3030": "Formato do parâmetro inválido: Cryptogram",
    "3031": "Parâmetro obrigatório não está presente",
    "3032": "Parâmetro enviado com tamanho inválido",
    "3033": "Parâmetro enviado com tamanho inválido",
    "3034": "Parâmetro enviado com tamanho inválido.",
    "3035": "Formato do parâmetro inválido: DSubMerchant",
    "3036": "QrCode expirado.",
    "3052": "Parâmetro obrigatório não está presente.",
    "3053": "Formato do parâmetro inválido: Wallet Code",
    "3054": "Parâmetro enviado com tamanho inválido.",
    "3055": "Parâmetro não permitido para esta transação",
    "3056": "Parâmetro não permitido para esta transação",
    "3064": "Parâmetro enviado com tamanho inválido.",
    "3065": "Formato do parâmetro inválido: Sai",
    "3066": "Parâmetro obrigatório não está presente.",
    "3067": "Parâmetro obrigatório não está presente.",
    "3068": "Parâmetro obrigatório não está presente",
    "3069": "Formato do parâmetro inválido: Credential Id",
    "3070": "Parâmetro enviado com tamanho inválido",
    "3076": "QrCode: Campo Expiration Date não foi informado",
    "3077": "QrCode: Campo Expiration Date enviado com valor inválido",
    "3078": "QrCode: Campo Expiration Date enviado em formato inválido",
    "3079": "QrCode não processado. Tente novamente",
    "3081": "QrCode: Campo Expiration Date enviado em tamanho inválido",
    "3084": "Erro na geração do campo qrCodeImage. Use a API de Consulta para obter essa informação, caso seja necessário",
    "3085": "Erro na geração do campo qrCodeImage. Tente novamente",
    "3089": "Qr Code não gerado, contate a Rede",
    "3090": "Chave Pix Inválida na geração de qrCode",
    "3091": "Erro na devolução, tente novamente",
    "3092": "Falha na geração de qrCode, tente novamente",
    "3094": "Sem sucesso. Contate a Rede",
    "3095": "Chave Pix não cadastrada",
    "3096": "Sem sucesso. Tente novamente mais tarde",
    "3097": "Não disponível. Tente novamente mais tarde",
    "3098": "Serviço não autorizado",
    "3099": "Falha na comunicação. Tente novamente mais tarde"
  }
}
