import * as https from "https";
import {Resposta} from "../../utils/Resposta";
import {Empresa} from "../../domain/Empresa";

export class ApiCloudfare {
  private token = "nlM6l63-UruiZY3qC0iBVq-j_6TZE6cbIcltGxOY"
  private host = 'api.cloudflare.com'
  private path = '/client/v4/zones/'
  private _encode: any = 'utf8';

  public static getApiMeuCatalogo(): ApiCloudfare {
    return new ApiCloudfare("7842fdc58cd69a9e7beb78fc57a4dbaf", "meucatalogo.ai")
  }

  public static getApiMeuCardapio(): ApiCloudfare {
    return new ApiCloudfare("7842fdc58cd69a9e7beb78fc57a4dbaf", "meucardapio.ai")
  }

  public static getApiChinaInBoxZap(): ApiCloudfare {
    return new ApiCloudfare("ae9ba58c43c6b8fe5a42f2022538da79", "chinainbox-zap.com.br")
  }

  public static getApiGendaiZap(): ApiCloudfare {
    return new ApiCloudfare("c419901f6a1f45fbffdcac1c256abafa", "gendai-zap.com.br")
  }

  public static getApiPromokit(): ApiCloudfare {
    return new ApiCloudfare("854d7be83946cfddab185d7df3061b95", "promokit.com.br")
  }

  public static getApiZapkit(): ApiCloudfare {
    return new ApiCloudfare( "47ded96cc70b22dfebde6ab7b7555f34", "zapkit.com.br")
  }

  private basePath(): string {
    return this.path + this.idZona
  }

  constructor(private idZona: string, private url: string) {}

  public insiraRegistroA(dominio: string, ip: string) {
    return new Promise((resolve: any) => {
      console.log('[CLOUDFARE] Iniciando a criação do registro A do domínio ' + dominio + ' para o ip ' + ip)
      let chamada = "/dns_records"

      let dados = {
        type: "A",
        name: dominio + '.' + this.url,
        content: ip,
        proxied: true,
        ttl: 1
      }

      this.executReqPost(chamada, dados).then((resultado: any) => {
        if(!resultado.sucesso) {
          console.log('[CLOUDFARE] Iniciando a criação do registro A do domínio ' + dominio + ' para o ip ' + ip)
          return resolve(resultado)
        }

        let resposta = resultado.data

        if(!resposta.success) {
          let msgErro = resposta.errors.join(", ")
          console.log('[CLOUDFARE] Aconteceu um erro ao inserir o registro: ' + resposta.errors.join(", "))
          return resolve({
            sucesso: false,
            erro: "Aconteceu um erro ao inserir o registro: " + msgErro
          })

        }

        console.log('[CLOUDFARE] O registro A para o domínio ' + dominio + ' foi inserido com sucesso')

        resolve({
          sucesso: true,
          data: {
            criou: true
          }
        })


      })

    })
  }

  public atualizeOuInsiraRegistroA(empresas: Empresa[], ip: string) {
    return new Promise((resolve) => {
      console.log('[CLOUDFARE] Iniciando algoritmo para atualizar/inserir registro A no IP ' + ip +
        ' para ' + empresas.length + '  empresas.')

      let promises = []

      for(let empresa of empresas) {
        if(empresa.dominio === 'winecloud')
          continue;
        console.log('[CLOUDFARE] Preparando para iniciar cadastro da empresa ' + empresa.dominio)
        promises.push(this.atualizeOuInsiraRegistroADominio(empresa.dominio, ip))
      }

      Promise.all(promises).then((respostas: any[]) => {
        resolve({
          sucesso: true,
          data: respostas
        })
      })


      /*
      this.obtenhaTodosRegistroA().then((resultado: any) => {

        if(!resultado.sucesso) return resolve(resultado)

        console.log('[CLOUDFARE] Criando mapa de registros já existentes')

        let mapaRegistros = this.obtenhaMapaRegistros(resultado.registros)

        let promises = []

        for(let empresa of empresas) {
          console.log('[CLOUDFARE] Verificando registro da empresa ' + empresa.dominio)
          if(empresa.dominio in mapaRegistros)
            promises.push(this.atualizeRegistroA(empresa.dominio, mapaRegistros[empresa.dominio].id,  ip))
          else
            promises.push(this.insiraRegistroA(empresa.dominio, ip))
        }

        Promise.all(promises).then((respostas: any[]) => {
          resolve({
            sucesso: true,
            data: respostas
          })
        })
      })
       */
    })
  }

  public crieRegistroAParaNovaEmpresa(empresa: any, ip: string) {
    return new Promise((resolve) => {
      let dominio = empresa.dominio
      this.consulteEAtualizeRegistroA(dominio, ip).then((resultado: any) => {
        if(!resultado.sucesso) return resolve(Resposta.erro("Não foi possível verificar se o domínio já existia"))

        if(resultado.sucesso && resultado.data.existe) return resolve({
          sucesso: true
        })

        this.insiraRegistroA(dominio, ip).then((resultadoInserir: any) => {
          if(!resultadoInserir.sucesso) return resolve(Resposta.erro("Não foi possível inserir o novo registro:" + resultadoInserir.erro))

          resolve({
            sucesso: true
          })

        })

      })
    })
  }


  public atualizeRegistroA(dominio: string, idDominio: string, novoIp: string) {
    return new Promise((resolve) => {
      console.log('[CLOUDFARE] Iniciando atualização do registro A do domínio ' + dominio + ' para o ip ' + novoIp)
      let chamada = "/dns_records/" + idDominio

      let dados = {
        type: "A",
        name: dominio + '.' + this.url,
        proxied: true,
        content: novoIp,
        ttl: 1
      }

      this.executeReqPut(chamada, dados).then((resultadoPut: any) => {
        if(!resultadoPut.sucesso) {
          console.log('[CLOUDFARE] Ocorreu um erro ao atualizar o registro A:' + resultadoPut.erro)
          return resolve(resultadoPut);
        }


        let resposta = resultadoPut.data;

        if(!resposta.success)
        {
          let msgErro = resposta.errors.join(", ")
          console.log('[CLOUDFARE] Ocorreu um erro ao atualizar o registro A:' + msgErro)
          return resolve({
            sucesso: false,
            erro: "Aconteceu um erro ao atualizar o registro: " + msgErro
          })

        }

        console.log('[CLOUDFARE] O domínio ' + dominio + ' foi atualizado com sucesso.')
        return  resolve({
          sucesso: true,
          data: {
            atualizou: true
          }
        })
      })
    })

  }

  public atualizeOuInsiraRegistroADominio(dominio: string, novoIp: string) {
    return new Promise((resolve: any) => {
      this.consulteEAtualizeRegistroA(dominio, novoIp).then((resultado: any) => {
        if(!resultado.sucesso) return resolve(resultado)

        if(resultado.sucesso && resultado.data.atualizou) return resolve(resultado)

        this.insiraRegistroA(dominio, novoIp).then((resultadoInsercao: any) => {
          resolve(resultadoInsercao)
        })

      })
    })
  }


  public consulteEAtualizeRegistroA(dominio: string, novoIp: string) {
    return new Promise((resolve: any) => {
      console.log('[CLOUDFARE] Verificando existência do domínio ' + dominio)
      this.obtenhaRegistroA(dominio).then((resultado: any) => {
        if(!resultado.sucesso) {
          console.log('[CLOUDFARE] Houve um erro ao consultar o domínio ' + dominio + ' - ' + resultado.erro)
          return resolve(resultado)
        }


        let dadosDominio = resultado.data

        if(!dadosDominio.existe) {
          console.log('[CLOUDFARE] O domínio ' + dominio + ' ainda não tem registro cadastrado.')
          return resolve({
            sucesso: true,
            data: {
              atualizou: false,
              existe: false,
            }
          })
        }

        if(dadosDominio.registro.conteudo === novoIp) return resolve({
          sucesso: true,
          data: {
            atualizou: true
          }
        })

        console.log('[CLOUDFARE] O domínio ' + dominio + ' já foi cadastrado.')
        this.atualizeRegistroA(dominio, dadosDominio.registro.id, novoIp).then((resultadoAtualizacao: any) => {
          resolve(resultadoAtualizacao)
        })
      })


    })
  }

  public obtenhaTodosRegistroA() {
    return new Promise((resolve: any) => {
      console.log('[CLOUDFARE] Preparando para buscar todos registros A da url ' + this.url)
      let chamada = "/dns_records?type=A"

      this.executeReqGet(chamada).then((resultado: any) => {
        if(!resultado.sucesso) {
          console.log('[CLOUDFARE] Houve um erro ao tentar obter todos os registros: ' + resultado.erro)
          return resolve(resultado)
        }

        let resposta = resultado.data;

        if(!resposta.success) {
          let msgErro = resposta.errors.join(", ")
          console.log('[CLOUDFARE] Houve um erro ao tentar obter todos os registros: ' + msgErro)
          return resolve(Resposta.erro("Houve um erro ao chamar a API do Cloudfare. " + msgErro))
        }

        let registros = []

        for(let registroCloudfare of resposta.result) {
          let dominio = registroCloudfare.name.replace("." + this.url, "")
          console.log('[CLOUDFARE] Convertendo registro do domínio ' + dominio)
          registros.push(this.obtenhaRegistro(registroCloudfare, dominio))
        }
        console.log('[CLOUDFARE] Foram obtidos ' + registros.length + ' registros de domínios')


        resolve({
          sucesso: true,
          registros: registros
        })
      })
    })
  }



  public obtenhaRegistroA(dominio: string) {
    return new Promise((resolve: any) => {
      let chamada = "/dns_records?type=A&name=" + dominio + "." + this.url

      this.executeReqGet(chamada).then((resultado: any) => {
        if(!resultado.sucesso) return resolve(resultado);

        let resposta = resultado.data;

        if(!resposta.success) return resolve({
          sucesso: false,
          erro: "Houve um erro ao chamar a API do Cloudfare." + resposta.errors.join(", ")
        })

        if(!resposta.result || resposta.result.length === 0)
          return resolve({
            sucesso: true,
            data: {
              existe: false
            }
          })

        let objeto = {
          existe: true,
          registro: this.obtenhaRegistro(resposta.result[0], dominio)

        }

        resolve({
          sucesso: true,
          data: objeto
        })

      })
    })


  }

  private obtenhaRegistro(objetoCloudfare: any, dominio: string) {
    return {
      id: objetoCloudfare.id,
      tipo: "A",
      nome: dominio,
      proxied: true,
      conteudo: objetoCloudfare.content
    };
  }

  private obtenhaHttpOptions(chamada: any, metodo: any) {
    return {
      host: this.host,
      path: this.basePath() + chamada,
      method: metodo,
      headers: this.getHeaders()
    }

  }

  private executeReqPut(chamada: string, dados: any) {
    return this.executeChamadaHTTPS( this.obtenhaHttpOptions(chamada, 'PUT'), dados)
  }

  private executReqPost(chamada: string, dados: any) {
    return this.executeChamadaHTTPS( this.obtenhaHttpOptions(chamada, 'POST'), dados)
  }


  private executeReqGet(chamada: string) {
    return this.executeChamadaHTTPS( this.obtenhaHttpOptions(chamada, 'GET'), null)

  }

  private executeChamadaHTTPS(httpOptions: any, data: any) {
    return new Promise((resolve) => {
      let req = https.request(httpOptions)

      req.on('error',   (e) => {
        console.log('problem with request: ' + e.message);
        console.log(e)

        resolve({
          sucesso: false,
          erro: "Houve um problema com a request: " + e.message
        })
      });

      req.on('response',   (res) => {
        let response = '';
        res.setEncoding(this._encode);


        res.on('data',   (chunk: any) => {
          response += chunk;
        });

        res.on('end', function () {
          if(res.statusCode === 204) // no content
            return resolve( {
              sucesso: false,
              erro: 'O link informado não retornou conteúdo.'
            });

          let resposta = JSON.parse(response);

          resolve({
            sucesso: true,
            data: resposta
          })
        });
      })

      if(data)
        req.write(JSON.stringify(data));

      req.end();

    })



  }




  private getHeaders() {
    return {
      "Authorization": "Bearer " + this.token,
      "Content-Type": "application/json"
    }
  }

  private obtenhaMapaRegistros(registros: any[]) {
    let dicionario: any = {}

    for(let registro of registros) {
      dicionario[registro.nome] = registro
    }

    return dicionario
  }
}
