
import {<PERSON><PERSON><PERSON><PERSON>} from "cron";
import {IFoodUtils} from "../service/integracoes/IFoodUtils";
// @ts-ignore
import * as mybatis from 'mybatisnodejs';

const Contexto = mybatis.Contexto;

export class TarefasDev {

  inicie(){
    console.log('####INICIANDO TAREFAS DEV NO MAIN CORE#####')

    // tslint:disable-next-line:no-unused-expression
    new CronJob('*/30 * * * * *', () => {
      this.executeNoDomain('Job dev pooling lojas ifood', async ( contexto: any ) => {
        require('domain').active.contexto.idEmpresa =  {id: -1};
        await IFoodUtils.executePollingLojas();
      })
    }, null, true);
  }


  private setEmpresaContexto(empresa: any){
    require('domain').active.contexto.idEmpresa = empresa.id;
  }


  private executeNoDomain(nome: string, cb: Function){
    console.log('executar job: ' + nome)
    console.log(new Date())

    let reqDomain = require('domain').create();

    reqDomain.contexto = new Contexto();

    reqDomain.on('error',   (er: any) => {
      console.log(er)
      console.log('==============Erro no JOB ==================>'  + nome)
      try{
        if(reqDomain.contexto )
          reqDomain.contexto.release();
      }catch(e){

      }
    })

    reqDomain.run(function(){
      cb(reqDomain.contexto)
    });
  }
}
