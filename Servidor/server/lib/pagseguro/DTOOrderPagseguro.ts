import {Pedido} from "../../domain/delivery/Pedido";
import * as moment from "moment";
import {PagamentoPedido} from "../../domain/delivery/PagamentoPedido";
import {Ambiente} from "../../service/Ambiente";

export class DTOOrderPagseguro{
  reference_id: any;
  customer: any;
  items: any = [];
  notification_urls: any;
  qr_codes: any;
  charges: any;
  shipping: any;
  constructor(pedido: Pedido) {
    let usuario: any = pedido.contato;

    this.reference_id = pedido.id;
    this.customer = {
      name: usuario.nome.substring(0, 30),
      email:  usuario.email,
      tax_id: usuario.cpf,
      phones: [
        {
          country: "55",
          area:  usuario.telefone.replace(/\D/gm, '').substr(0, 2),
          number:  usuario.telefone.replace(/\D/gm, '').substr(2),
          type: "MOBILE"
        }
      ]
    }

    this.items = [ ];

    if(pedido.obtenhaCashback() > 0 || pedido.temDescontosOuTaxas()){
      let item: any = {
        reference_id: pedido.codigo,
        name: String(`Pedido #${pedido.codigo} - ${pedido.empresa.nome}`).substring(0, 64),
        quantity: 1,
        unit_amount:  this.toValor(pedido.obtenhaTotalPagar() - pedido.taxaEntrega)
      }
      this.items.push(item)
    } else {
      for(let i = 1; i <= pedido.itens.length; i++) {
        let item: any = {
          reference_id: pedido.itens[i - 1].produto.id,
          name: pedido.itens[i - 1].produto.nome.substring(0, 64),
          quantity: pedido.itens[i - 1].qtde,
          unit_amount:  this.toValor(pedido.itens[i - 1].obtenhaValorUnitario())
        }
        this.items.push(item)
      }
    }

    this.notification_urls = [this.obtenhaHost(pedido.empresa) + '/pagamentos/pagseguro/order']

    if(pedido.ehDelivery()){
      this.shipping = {
        "address": {
          "street": pedido.endereco.logradouro,
            "number": pedido.endereco.numero ? pedido.endereco.numero.toString() :  's/n',
            "complement": pedido.endereco.complemento || "complemento",
            "locality": pedido.endereco.bairro,
            "city": pedido.endereco.cidade.nome,
            "region_code": pedido.endereco.cidade.estado.sigla,
            "country": "BRA",
            "postal_code":  pedido.endereco.cep
        }
      }
    }
  }

  setPagamentoCartao(pedido: any, pagamento: PagamentoPedido,  dadosCartao: any, descricaoCartao: string){
    let totalPagar  =  pedido.obtenhaTotalPagar();

    if(!this.customer.email)
      this.customer.email = dadosCartao.email;
    if(!this.customer.tax_id)
      this.customer.tax_id = dadosCartao.cpf.replace(/\D/g, '');

    let charge: any = {
      reference_id: pagamento.id,
      description: String(`Pedido #${pedido.codigo}`),
      soft_descriptor: descricaoCartao.substring(0, 17),
      amount: {
        value:  this.toValor(totalPagar),
        currency: "BRL"
      },
      payment_method: {
        type: dadosCartao.tipoDoCartao.id  ,
        soft_descriptor : descricaoCartao.substring(0, 17),
        installments: 1,
        capture: true,
        card: { }
      },
      notification_urls:  [this.obtenhaHost(pedido.empresa) + '/pagamentos/pagseguro/charge']
    }

    //security_code: dadosCartao.cvv,
    charge.payment_method.card = {
      encrypted: dadosCartao.token,
      holder: {
        name: dadosCartao.nome
      },
      store: false
    }

    if(dadosCartao.autenticacao3ds){
      charge.payment_method.authentication_method = {
        type: "THREEDS",
        id: dadosCartao.autenticacao3ds
      }
    }

    this.charges = [ charge ];
  }


  setDadosPix(valor: number){
    this.qr_codes = [
      {
        amount: {
          value: this.toValor(valor)
        },
        expiration_date: moment().add(8, 'h').toDate().toISOString(),
      }
    ]
  }

  private obtenhaHost(empresa: any){
    if(!Ambiente.Instance.producao)
      return 'https://d72d-2804-d59-9a2e-df00-72e0-a3d8-3e-8138.ngrok-free.app'

    return  empresa.obtenhaHostLoja();
  }

  private toValor(preco: number, precisao = 2){
    // tslint:disable-next-line:radix
    return parseInt((preco * 100).toFixed(3))
  }
}
