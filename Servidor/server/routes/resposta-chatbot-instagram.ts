import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {MapeadorDeRespostaChatbotInstagram} from "../mapeadores/MapeadorDeRespostaChatbotInstagram";
import {MapeadorDeOpcaoRespostaChatbotInstagram} from "../mapeadores/MapeadorDeOpcaoRespostaChatbotInstagram";
import {RespostaChatbotInstagram} from "../domain/instagram/RespostaChatbotInstagram";
import {OpcaoRespostaChatbotInstagram} from "../domain/instagram/OpcaoRespostaChatbotInstagram";

const router: Router = Router();

// ==========================================
// CONFIGURAÇÕES PADRÃO PARA INICIALIZAÇÃO
// ==========================================

const CONFIGURACOES_PADRAO = [
  {
    chaveResposta: 'get_started',
    titulo: 'Boas Vindas',
    mensagem: 'Olá! Bem-vindo a {{nomeEmpresa}} 😊\n\nA qualquer momento, você pode usar o menu abaixo para que eu possa te ajudar.\n\nO que você deseja?',
    icone: 'fas fa-handshake',
    ordem: 1,
    opcoes: [
      { texto: 'Ver Cardápio', tipo: 'resposta_rapida', destino: 'cardapio', icone: 'fas fa-eye' },
      { texto: 'Fazer Pedido', tipo: 'resposta_rapida', destino: 'fazer_pedido', icone: 'fas fa-shopping-cart' },
      { texto: 'Horários de Funcionamento', tipo: 'resposta_rapida', destino: 'horario_atendimento', icone: 'fas fa-clock' }
    ]
  },
  {
    chaveResposta: 'fazer_pedido',
    titulo: 'Fazer Pedido',
    mensagem: 'Clique no botão abaixo para fazer o seu pedido.',
    icone: 'fas fa-shopping-cart',
    ordem: 2,
    opcoes: [
      { texto: 'Acessar Cardápio', tipo: 'link_externo', url: '{{link_cardapio}}', icone: 'fas fa-external-link-alt' }
    ]
  },
  {
    chaveResposta: 'cardapio',
    titulo: 'Cardápio',
    mensagem: 'Segue nosso cardápio 🍽',
    icone: 'fas fa-utensils',
    ordem: 3,
    opcoes: [
      { texto: 'Ver Cardápio Online', tipo: 'link_externo', url: '{{link_cardapio}}', icone: 'fas fa-external-link-alt' }
    ]
  },
  {
    chaveResposta: 'atendente',
    titulo: 'Atendente',
    mensagem: 'Para falar com um atendente, clique no botão abaixo e você será direcionado para o nosso WhatsApp.\n\nNosso horário de atendimento: {{horario}}',
    icone: 'fas fa-headset',
    ordem: 4,
    opcoes: [
      { texto: 'Falar no WhatsApp', tipo: 'link_externo', url: 'https://wa.me/{{whatsapp}}?text=Oi vim do instagram de vocês.', icone: 'fab fa-whatsapp' },
      { texto: 'Voltar ao Menu', tipo: 'resposta_rapida', destino: 'get_started', icone: 'fas fa-home' }
    ]
  },
  {
    chaveResposta: 'horario_atendimento',
    titulo: 'Horários',
    mensagem: 'Nossos horários de funcionamento:\n\n🕐 {{horarios}}\n\nComo posso te ajudar?',
    icone: 'fas fa-clock',
    ordem: 5,
    opcoes: [
      { texto: 'Fazer Pedido', tipo: 'resposta_rapida', destino: 'fazer_pedido', icone: 'fas fa-shopping-cart' },
      { texto: 'Ver Cardápio', tipo: 'resposta_rapida', destino: 'cardapio', icone: 'fas fa-eye' }
    ]
  },
  {
    chaveResposta: 'agradecimento',
    titulo: 'Agradecimento',
    mensagem: 'Por nada. Estou sempre por aqui. Só mandar um oi. :-)',
    icone: 'fas fa-heart',
    ordem: 6,
    opcoes: [
      { texto: 'Voltar ao Menu', tipo: 'resposta_rapida', destino: 'get_started', icone: 'fas fa-home' }
    ]
  },
  {
    chaveResposta: 'localizacao',
    titulo: 'Localização',
    mensagem: 'Estamos localizados em:\n\n📍 {{enderecoEmpresa}}\n\nComo posso te ajudar?',
    icone: 'fas fa-map-marker-alt',
    ordem: 7,
    opcoes: [
      {
        texto: 'Como Chegar',
        tipo: 'link_externo',
        url: 'https://maps.google.com/maps/dir/{{enderecoEmpresa}}',
        icone: 'fas fa-route'
      },
      {
        texto: 'Ver Cardápio',
        tipo: 'link_externo',
        url: '{{link_cardapio}}',
        icone: 'fas fa-route'
      },
      {
        texto: 'Voltar ao Menu',
        tipo: 'resposta_rapida',
        destino: 'get_started',
        icone: 'fas fa-home'
      }
    ]
  },
  {
    chaveResposta: 'fallback',
    titulo: 'Não Entendi',
    mensagem: 'Desculpe-me, mas eu não entendo "{{message}}".',
    icone: 'fas fa-question-circle',
    ordem: 8,
    opcoes: [
      { texto: 'Voltar ao Menu', tipo: 'resposta_rapida', destino: 'get_started', icone: 'fas fa-home' },
      { texto: 'Falar com Atendente', tipo: 'resposta_rapida', destino: 'atendente', icone: 'fas fa-headset' }
    ]
  }
];

// ==========================================
// FUNÇÃO DE CRIAÇÃO EM LOTE
// ==========================================

async function criarConfiguracoesPadrao(empresa: any) {
  const mapeadorResposta = new MapeadorDeRespostaChatbotInstagram();
  const mapeadorOpcao = new MapeadorDeOpcaoRespostaChatbotInstagram();

  console.log('🚀 Iniciando criação de configurações padrão para empresa:', empresa.id, empresa.nome);

  const respostasCriadas = [];

  for (const config of CONFIGURACOES_PADRAO) {
    console.log('📝 Criando resposta:', config.chaveResposta);

    // Criar resposta
    const resposta = new RespostaChatbotInstagram();
    resposta.empresa_id = empresa.id;
    resposta.chaveResposta = config.chaveResposta;
    resposta.titulo = config.titulo;
    resposta.mensagem = processarPlaceholders(config.mensagem, empresa);
    resposta.icone = config.icone;
    resposta.ativa = true;
    resposta.ordem = config.ordem;
    resposta.dadosInstagram = null; // Definir como null para evitar erro no mapeamento

    console.log('💾 Dados da resposta a ser inserida:', {
      empresa_id: resposta.empresa_id,
      chaveResposta: resposta.chaveResposta,
      titulo: resposta.titulo,
      mensagem: resposta.mensagem.substring(0, 50) + '...',
      icone: resposta.icone,
      ativa: resposta.ativa,
      ordem: resposta.ordem
    });

    try {
      const respostaSalva = await mapeadorResposta.insiraSync(resposta);
      console.log('✅ Resposta salva com ID:', respostaSalva.id);

      // Criar opções desta resposta
      for (let i = 0; i < config.opcoes.length; i++) {
        const opcaoConfig = config.opcoes[i];
        console.log('🔘 Criando opção:', opcaoConfig.texto);

        const opcao = new OpcaoRespostaChatbotInstagram();
        opcao.empresa_id = empresa.id;
        opcao.respostaChatbotInstagram = respostaSalva;
        opcao.texto = opcaoConfig.texto;
        opcao.tipo = opcaoConfig.tipo;
        opcao.destino = (opcaoConfig as any).destino || null;
        opcao.url = (opcaoConfig as any).url ? processarPlaceholders((opcaoConfig as any).url, empresa) : null;
        opcao.icone = opcaoConfig.icone;
        opcao.ordem = i;
        opcao.ativo = true;

        try {
          const opcaoSalva = await mapeadorOpcao.insiraSync(opcao);
          console.log('✅ Opção salva com ID:', opcaoSalva.id);
        } catch (errorOpcao) {
          console.error('❌ Erro ao inserir opção:', errorOpcao);
          throw errorOpcao;
        }
      }

      respostasCriadas.push(respostaSalva);
      console.log('📋 Resposta completa criada:', config.chaveResposta);

    } catch (errorResposta) {
      console.error('❌ Erro ao inserir resposta:', errorResposta);
      throw errorResposta;
    }
  }

  console.log('🎉 Total de respostas criadas:', respostasCriadas.length);
  return respostasCriadas;
}

// ==========================================
// FUNÇÃO AUXILIAR PARA PROCESSAR PLACEHOLDERS
// ==========================================

function processarPlaceholders(texto: string, empresa: any): string {
  if (!texto) return texto;

  let textoProcessado = texto;

  // Substituir {{nomeEmpresa}}
  if (empresa.nome) {
    textoProcessado = textoProcessado.replace(/\{\{nomeEmpresa\}\}/g, empresa.nome);
  }

  // Substituir {{dominioEmpresa}}
  if (empresa.dominio) {
    textoProcessado = textoProcessado.replace(/\{\{dominioEmpresa\}\}/g, empresa.dominio);
  } else {
    // Usar um domínio padrão se não tiver configurado
    textoProcessado = textoProcessado.replace(/\{\{dominioEmpresa\}\}/g, 'meurestaurante.com');
  }

  return textoProcessado;
}

// ==========================================
// ROTAS
// ==========================================

// POST - Inicializar configurações padrão para empresa nova
router.get('/inicializar-padrao', async (req: any, res: any) => {
  const mapeadorResposta = new MapeadorDeRespostaChatbotInstagram();
  const mapeadorOpcao = new MapeadorDeOpcaoRespostaChatbotInstagram();

  try {
    // Verificar se já existe configuração para esta empresa
    const respostasExistentes = await mapeadorResposta.listeAsync({
      idEmpresa: req.empresa.id
    });

    if (respostasExistentes.length > 0) {
      return res.json(Resposta.erro('Esta empresa já possui configurações de chatbot. Use a interface para editar as configurações existentes.'));
    }

    // Criar todas as configurações padrão em transação
    let respostasCriadas: any[] = [];

    respostasCriadas = await criarConfiguracoesPadrao(req.empresa);

    // Verificar se as respostas foram criadas com sucesso
    if (!respostasCriadas || respostasCriadas.length === 0) {
      throw new Error('Nenhuma configuração foi criada');
    }

    // Calcular estatísticas para resposta
    const totalOpcoes = CONFIGURACOES_PADRAO.reduce((acc, config) => acc + config.opcoes.length, 0);
    const totalLinks = CONFIGURACOES_PADRAO.reduce((acc, config) =>
      acc + config.opcoes.filter(opcao => opcao.tipo === 'link_externo').length, 0
    );
    const totalConexoes = CONFIGURACOES_PADRAO.reduce((acc, config) =>
      acc + config.opcoes.filter(opcao => opcao.tipo === 'resposta_rapida').length, 0
    );

    res.json(Resposta.sucesso({
      mensagem: 'Configurações padrão do chatbot Instagram criadas com sucesso!',
      empresa: req.empresa.nome || req.empresa.id,
      totalRespostas: CONFIGURACOES_PADRAO.length,
      totalOpcoes: totalOpcoes,
      totalLinks: totalLinks,
      totalConexoes: totalConexoes,
      configuracoesCriadas: respostasCriadas.map((r: any) => ({
        id: r.id,
        chaveResposta: r.chaveResposta,
        titulo: r.titulo
      }))
    }));

  } catch (error) {
    console.error('Erro ao criar configurações padrão:', error);
    res.json(Resposta.erro('Erro ao criar configurações padrão do chatbot: ' + error.message));
  }
});

// GET - Listar respostas do chatbot
router.get('/', async (req: any, res: any) => {
  const mapeador = new MapeadorDeRespostaChatbotInstagram();

  try {
    const respostas = await mapeador.listeAsync({
      idEmpresa: req.empresa.id,
      ativa: req.query.ativa,
      chaveResposta: req.query.chaveResposta
    });

    res.json(Resposta.sucesso(respostas));
  } catch (error) {
    res.json(Resposta.erro('Erro ao listar respostas do chatbot: ' + error.message));
  }
});

// GET - Obter resposta específica
router.get('/:id', async (req: any, res: any) => {
  const mapeador = new MapeadorDeRespostaChatbotInstagram();

  try {
    const resposta = await mapeador.selecioneSync({
      idEmpresa: req.empresa.id,
      id: req.params.id
    });

    if (!resposta) {
      return res.json(Resposta.erro('Resposta não encontrada'));
    }

    res.json(Resposta.sucesso(resposta));
  } catch (error) {
    res.json(Resposta.erro('Erro ao obter resposta: ' + error.message));
  }
});

// POST - Criar nova resposta
router.post('/', async (req: any, res: any) => {
  const mapeador = new MapeadorDeRespostaChatbotInstagram();

  try {
    // Verificar se já existe uma resposta com a mesma chave
    const existe = await mapeador.existeSync({
      empresa: req.empresa,
      chaveResposta: req.body.chaveResposta
    });

    if (existe) {
      return res.json(Resposta.erro('Já existe uma resposta com esta chave'));
    }

    const resposta = new RespostaChatbotInstagram();
    resposta.empresa_id = req.empresa.id;
    resposta.chaveResposta = req.body.chaveResposta;
    resposta.titulo = req.body.titulo;
    resposta.mensagem = req.body.mensagem;
    resposta.icone = req.body.icone;
    resposta.ativa = req.body.ativa !== undefined ? req.body.ativa : true;
    resposta.ordem = req.body.ordem || 0;

    const respostaSalva = await mapeador.insiraSync(resposta);

    res.json(Resposta.sucesso(respostaSalva));
  } catch (error) {
    res.json(Resposta.erro('Erro ao criar resposta: ' + error.message));
  }
});

// PUT - Atualizar resposta
router.put('/:id', async (req: any, res: any) => {
  const mapeador = new MapeadorDeRespostaChatbotInstagram();

  try {
    // Verificar se a resposta existe e pertence à empresa
    const respostaExistente = await mapeador.selecioneSync({
      idEmpresa: req.empresa.id,
      id: req.params.id
    });

    if (!respostaExistente) {
      return res.json(Resposta.erro('Resposta não encontrada'));
    }

    // Verificar se a chave não está sendo usada por outra resposta
    if (req.body.chaveResposta && req.body.chaveResposta !== respostaExistente.chaveResposta) {
      const existe = await mapeador.existeSync({
        empresa: req.empresa,
        chaveResposta: req.body.chaveResposta,
        id: req.params.id
      });

      if (existe) {
        return res.json(Resposta.erro('Já existe outra resposta com esta chave'));
      }
    }

    const resposta = new RespostaChatbotInstagram();
    resposta.id = Number(req.params.id);
    resposta.empresa_id = req.empresa.id;
    resposta.chaveResposta = req.body.chaveResposta || respostaExistente.chaveResposta;
    resposta.titulo = req.body.titulo || respostaExistente.titulo;
    resposta.mensagem = req.body.mensagem || respostaExistente.mensagem;
    resposta.icone = req.body.icone || respostaExistente.icone;
    resposta.ativa = req.body.ativa !== undefined ? req.body.ativa : respostaExistente.ativa;
    resposta.ordem = req.body.ordem !== undefined ? req.body.ordem : respostaExistente.ordem;

    const respostaAtualizada = await mapeador.atualizeSync(resposta);

    res.json(Resposta.sucesso(respostaAtualizada));
  } catch (error) {
    res.json(Resposta.erro('Erro ao atualizar resposta: ' + error.message));
  }
});

// DELETE - Remover resposta
router.delete('/:id', async (req: any, res: any) => {
  const mapeador = new MapeadorDeRespostaChatbotInstagram();

  try {
    // Verificar se a resposta existe e pertence à empresa
    const resposta = await mapeador.selecioneSync({
      idEmpresa: req.empresa.id,
      id: req.params.id
    });

    if (!resposta) {
      return res.json(Resposta.erro('Resposta não encontrada'));
    }

    await mapeador.removaAsync({
      id: req.params.id,
      empresa: req.empresa
    });

    res.json(Resposta.sucesso({ id: req.params.id, removida: true }));
  } catch (error) {
    res.json(Resposta.erro('Erro ao remover resposta: ' + error.message));
  }
});

export const RespostaChatbotInstagramController: Router = router;
