import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {Contrato} from "../domain/faturamento/Contrato";
import {DTOContrato} from "../lib/dto/DTOContrato";
import {DTOAssinatura} from "../lib/dto/DTOAssinatura";
import {DTOFatura} from "../lib/dto/DTOFatura";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {Fatura} from "../domain/faturamento/Fatura";
import {ContratoService} from "../service/ContratoService";
import {IuguService} from "../service/IuguService";
import {EnumFormaPagamentoIugu} from "../lib/emun/EnumFormaPagamentoIugu";
import {Assinatura} from "../domain/faturamento/Assinatura";
import * as moment from "moment"
import {EnumStatusFatura} from "../lib/emun/EnumStatusFatura";
import {MapeadorDeFatura} from "../mapeadores/MapeadorDeFatura";
import {MapeadorDeContrato} from "../mapeadores/MapeadorDeContrato";
import {DateUtils} from "../lib/DateUtils";
import {MapeadorDeAssinatura} from "../mapeadores/MapeadorDeAssinatura";
import {MapeadorDePlanoEmpresarial} from "../mapeadores/MapeadorDePlanoEmpresarial";
import {RegistroDeOperacaoService} from "../service/RegistroDeOperacaoService";
import {Ambiente} from "../service/Ambiente";
import {Empresa} from "../domain/Empresa";


const router: Router = Router();

function ehAdmin(req: any, res: any, next: any){
  let usuario: any = req.user;

  if(!usuario)
    return  res.json(Resposta.erro('Faça login para realizar a operação'))

  if(!usuario.admin)
    res.json(Resposta.erro('Operação não permitida'))

  return next();
}

function estaLogado(req: any, res: any, next: any){
  let usuario: any = req.user;

  if(!usuario)
    return  res.json(Resposta.erro('Faça login para realizar a operação'))

  return next();
}

function respostaErro(res: any, e: any){
  let erro = typeof e === 'string' ? e : e.message;
  res.json(Resposta.erro(erro))
}

router.get( '/', estaLogado, async (req: any, res) => {
  let empresa = req.empresa;

  let contratoAntigo: boolean = empresa.id <= 74;

  let contrato: Contrato = await Contrato.get({ idEmpresa: empresa.id});

  if( !contrato &&  contratoAntigo)
    return res.json(Resposta.sucesso({ ativo: true }));

  let dto = new DTOContrato(contrato);
  if(contratoAntigo) dto.ativo = true;

 // let contatosPontuado: EmpresaContatosPontuado = await new MapeadorDeEmpresa().obtenhaLimiteContatos(empresa);

  //if(contatosPontuado)
  //  dto.percentualAtivos = (contatosPontuado.qtdePontuado / contatosPontuado.qtdeContratado * 100)

  res.json(Resposta.sucesso(dto))

})

router.get('/:id', ehAdmin, async (req, res) => {
  let contrato = await Contrato.get( { idEmpresa: Number(req.params.id) } );

  res.json(Resposta.sucesso(contrato ? new DTOContrato(contrato) : {}))
});

router.post('/:id', ehAdmin, async (req, res) => {
  let dados: any = req.body;
  let empresa: any = await new MapeadorDeEmpresa().selecioneSync(Number(req.params.id));
  let contrato = new Contrato(empresa, dados.plano, dados.dataAtivacao, dados.diaVencimento,
                              dados.diasGratis, dados.valorNegociado,  dados.limiteContatosNegociado, dados.taxaAdesao);

  if(dados.parcelar && dados.numeroParcelas)
    contrato.numeroParcelas = dados.numeroParcelas

  let erro = await new ContratoService().salveContrato(contrato);

  if(!erro){
    res.json(Resposta.sucesso(new DTOContrato(contrato)))
  } else {
    res.json(Resposta.erro(erro))
  }
});

router.put('/:id', ehAdmin, async (req, res) => {
  let dados: any = req.body;
  let empresa: any = await new MapeadorDeEmpresa().selecioneSync(Number(req.params.id));
  let contrato = new Contrato(empresa, dados.plano, dados.dataAtivacao, dados.diaVencimento,
    dados.diasGratis, dados.valorNegociado, dados.limiteContatosNegociado, dados.taxaAdesao);

  contrato.id = dados.id;
  contrato.numeroParcelas = dados.numeroParcelas

  if( dados.dataAtivacao)
      return  res.json(Resposta.erro('Contrato já está ativo'))

  await new MapeadorDeContrato().atualizeSync(contrato);

  res.json(Resposta.sucesso(new DTOContrato(contrato)))
});

router.put('/:id/taxaadesao',  ehAdmin, async  (req, res) => {
  let dados: any = req.body;
  let taxaAdesao = dados.taxaAdesao;

  if(!taxaAdesao)
    return res.json(Resposta.erro('Dados inválidos'))

  let contrato: Contrato = await Contrato.get(Number(req.params.id));

  if(contrato.assinatura && contrato.assinatura.dataPrimeiroPagamento)
     return   res.json(Resposta.erro('Taxa de adesão nao pode ser mais alterada.'))

  contrato.taxaAdesao =  Number(taxaAdesao)

  try{
    let subscription =  await new IuguService().altereValorAssinatura(contrato, contrato.assinatura)

    await  new MapeadorDeContrato().atualizeTaxaAdesao(contrato)

    await new ContratoService().sincronizeAssinaturaIugu( contrato, subscription);

    res.json(Resposta.sucesso(  ))

  } catch (e) {
    let erro = typeof e === 'string' ? e : e.message;
    res.json(Resposta.erro(erro))
  }

});

router.put('/:id/valornegociado',  ehAdmin, async  (req, res) => {
  let dados: any = req.body;
  let valornegociado = dados.valornegociado;

  if(!valornegociado)
    return res.json(Resposta.erro('Dados inválidos'))

  let contrato: Contrato = await Contrato.get(Number(req.params.id));

  contrato.valorNegociado =  Number(valornegociado)

  if( contrato.assinatura){
    try{
      let subscription =  await new IuguService().altereValorAssinatura(contrato, contrato.assinatura)

      await  new MapeadorDeContrato().atualizeValorNegociado(contrato)

      await new ContratoService().sincronizeAssinaturaIugu( contrato, subscription);

      await new RegistroDeOperacaoService(Ambiente.Instance.usuarioLogado(),
        Ambiente.Instance.ip()).alterouValorContrato(contrato);

      res.json(Resposta.sucesso(  ))

    } catch (e) {
      let erro = typeof e === 'string' ? e : e.message;
      res.json(Resposta.erro(erro))
    }

  } else {
    await  new MapeadorDeContrato().atualizeValorNegociado(contrato)
    res.json(Resposta.sucesso(  ))
  }


});

router.put('/:id/limitecontatos',  ehAdmin, async  (req, res) => {
  let dados: any = req.body;
  let limitecontatos = dados.limitecontatos;

  if(!limitecontatos)
    return res.json(Resposta.erro('Dados inválidos'))

  let contrato: Contrato = await Contrato.get(Number(req.params.id));

  contrato.limiteContatosNegociado =  Number(limitecontatos)

  await  new MapeadorDeContrato().atualizeLimiteContatos(contrato)

  res.json(Resposta.sucesso( contrato.limiteContatosNegociado))

});

router.put('/:id/numeroparcelas',  ehAdmin, async  (req, res) => {
  let dados: any = req.body;
  let numeroparcelas = dados.numeroparcelas;

  if(!numeroparcelas)
    return res.json(Resposta.erro('Dados inválidos'));

  let contrato = await Contrato.get(Number(req.params.id));

  contrato.numeroParcelas = numeroparcelas;

  await  new MapeadorDeContrato().atualizeNumeroParcelas(contrato)

  res.json(Resposta.sucesso())
});

router.put('/:id/plano',  ehAdmin, async  (req, res) => {
  let dados: any = req.body;
  let plano = dados.plano;
  console.log( Ambiente.Instance.ip())

  if(!plano)
    return res.json(Resposta.erro('Dados inválidos'));

  let contrato = await Contrato.get(Number(req.params.id));
  let novoPlano = await new MapeadorDePlanoEmpresarial().selecioneSync(plano.id);

  let erro: any = await new ContratoService().alterePlano(contrato, novoPlano);

  if(!erro){
    await new RegistroDeOperacaoService(Ambiente.Instance.usuarioLogado(),
      Ambiente.Instance.ip()).alterouPlano(contrato);

    res.json(Resposta.sucesso())
  } else {
    res.json(Resposta.erro(erro))
  }
});


router.put('/:id/datafimtrial',  ehAdmin, async  (req, res) => {
  let dados: any = req.body;
  let dataFimTrial = dados.dataFimTrial;

  if(!dataFimTrial)
    return res.json(Resposta.erro('Dados inválidos'));

  let contrato = await Contrato.get(Number(req.params.id));

  dataFimTrial = moment(dados.dataFimTrial)

  contrato.dataFimTrial = dataFimTrial.toDate();

  await  new MapeadorDeContrato().atualizeDataFimTrial(contrato)

  res.json(Resposta.sucesso(dataFimTrial.format('DD-MM-YYYY')))
});

router.put('/:id/ativacao',  ehAdmin, async  (req, res) => {
    let dados: any = req.body;
    let dataAtivacao = dados.dataAtivacao;

    if(!dataAtivacao)
      return res.json(Resposta.erro('Dados inválidos'))

    let contrato = await Contrato.get(Number(req.params.id));

    dataAtivacao = moment(dados.dataAtivacao);

    if(!dataAtivacao.isValid())
      return res.json( Resposta.erro('Data ativação inválida: ' + dataAtivacao))

    if(dataAtivacao.isAfter(moment()))
      return res.json( Resposta.erro('Data ativação não pode ser futura'));

    contrato.dataAtivacao = dataAtivacao.toDate();

    await  new MapeadorDeContrato().atualizeDataAtivacao(contrato)

    res.json(Resposta.sucesso(dataAtivacao.format('DD-MM-YYYY')))

})

router.put('/:id/datavencimento', ehAdmin, async  (req, res) => {
  let dados: any = req.body;

  if(!dados.dataVencimento)
    return res.json(Resposta.erro('Dados inválidos'))

  let contrato = await Contrato.get(Number(req.params.id));

  let erro: any =  await new ContratoService().altereDataVencimento(contrato, new Date(dados.dataVencimento), req.user);

  if(!erro){
    res.json(Resposta.sucesso({ contrato: new DTOContrato(contrato), dataBloqueio: contrato.empresa.dataBloqueioAuto}))
  } else {
    res.json(Resposta.erro(erro))
  }
});

router.put('/faturas/lancamento', ehAdmin, async (req, res) => {
  let dados = req.body;
  let fatura: Fatura = await Fatura.get(dados.id);

  let erro = await new ContratoService().salveLancamento(fatura, dados.lancamento);

  if(!erro){
    res.json(Resposta.sucesso(new DTOFatura(fatura)))
  } else {
    res.json(Resposta.erro(erro))
  }
});

router.put('/faturas/cancele', ehAdmin, async (req, res) => {
  let id = req.body.id;
  let iuguService: IuguService = new IuguService();

  let fatura: Fatura = await Fatura.get(Number(id));

  if(fatura.codigo){
    let invoice: any = await iuguService.obtenhaFatura(fatura.codigo);

    if(!invoice) return res.json(Resposta.erro('Fatura não encontrada'))

    try{
      await iuguService.canceleFatura(invoice.id);

      let motivo = 'Fatura cancelada pelo operador.';
      await new ContratoService().mudouStatusFatura(fatura.contrato, fatura, EnumStatusFatura.Cancelada, motivo);

      res.json(Resposta.sucesso())
    } catch (e) {
      let erro = typeof e === 'string' ? e : e.message;
      res.json(Resposta.erro(erro))
    }
  } else {
    let motivo = 'Fatura cancelada pelo operador.';
    await new ContratoService().mudouStatusFatura(fatura.contrato, fatura, EnumStatusFatura.Cancelada, motivo);
    res.json(Resposta.sucesso())
  }
});

router.put( '/faturas/lancamento/remova', ehAdmin, async (req, res) => {
  let dados = req.body;
  let fatura: Fatura = await Fatura.get(dados.id);

  let erro = await new ContratoService().excluaLancamento(fatura, dados.lancamento);

  if(!erro){
    res.json(Resposta.sucesso(new DTOFatura(fatura)))
  } else {
    res.json(Resposta.erro(erro))
  }
});

router.put('/faturas/informepagamento' ,  ehAdmin,  async (req, res) => {
  let idFatura = req.body.fid,
     dataPagamento =  req.body.dtpg;

  if(!idFatura || !dataPagamento)
    return res.json(Resposta.erro('Parametros inválidos'));

  let fatura: Fatura =  await Fatura.get(Number(idFatura));

  if(fatura.estaPaga())
    return  res.json(Resposta.erro('Fatura já está paga'));

  if(fatura.estaCancelada())
    return  res.json(Resposta.erro('Fatura está cancelada'));

  fatura.status = EnumStatusFatura.Paga;
  fatura.dataPagamento =  moment(dataPagamento).toDate();

  await new MapeadorDeFatura().atualizePagamento(fatura)

  res.json(Resposta.sucesso(new DTOFatura(fatura)));

})

router.put('/faturas/sincronize' ,  ehAdmin,  async (req, res) => {
    let codigo = req.body.codigo;

    if(!codigo)
      return res.json(Resposta.erro('Parametros inválidos'));

    let invoice = await  new IuguService().obtenhaFatura(codigo);

    if(!invoice)
      return res.json(Resposta.erro('Fatura inexistente'));

    let fatura = await new ContratoService().sincronizeFaturaIugu(invoice);

    res.json(Resposta.sucesso(new DTOFatura(fatura)));

})

router.get('/faturas/aberta', async (req: any, res) => {
  let empresa = req.empresa,  dtoFatura;

  if(empresa){
    let contrato: Contrato = await Contrato.get({ idEmpresa: empresa.id});
    if(contrato){
      let fatura = contrato.obtenhaFaturaEmAberto()
      if(fatura) dtoFatura = new DTOFatura(fatura)
    }
  }

  res.json(Resposta.sucesso(dtoFatura))

})

router.post('/:id/assinaturanova', ehAdmin,  async (req, res) => {
  let idEmpresa = req.params.id;
  let formaDePagamento: string = req.body.fp;
  let itens: any = req.body.itens;
  if(!idEmpresa || !formaDePagamento)
    return res.json(Resposta.erro('Parametros inválidos'))

  let empresa: any = await new MapeadorDeEmpresa().selecioneSync(Number(idEmpresa));

  try{

    let contrato: Contrato = await Contrato.get( { idEmpresa: empresa.id });
    let subscription: any = await  new IuguService().crieAssinatura(empresa, contrato, formaDePagamento, itens);
    let assinatura: any =  await new ContratoService().sincronizeAssinaturaIugu(contrato, subscription);

    res.json(Resposta.sucesso(new DTOAssinatura(assinatura)));

  } catch (e) {
    respostaErro(res, e);
  }
})

router.post('/:id/assinatura', ehAdmin,  async (req, res) => {
  let idEmpresa = req.params.id;
  let formaDePagamento: string = req.body.fp;
  if(!idEmpresa || !formaDePagamento)
    return res.json(Resposta.erro('Parametros inválidos'))

  let empresa: any = await new MapeadorDeEmpresa().selecioneSync(Number(idEmpresa));

  try{

    let contrato: Contrato = await Contrato.get( { idEmpresa: empresa.id });
    let subscription: any = await  new IuguService().crieAssinatura(empresa, contrato, formaDePagamento);
    let assinatura: any =  await new ContratoService().sincronizeAssinaturaIugu(contrato, subscription);

    res.json(Resposta.sucesso(new DTOAssinatura(assinatura)));

  } catch (e) {
    respostaErro(res, e);
  }
});


router.put( '/assinaturas/remova', ehAdmin, async (req, res) => {
  let idContrato = req.body.cid;
  let dados: any =  req.body.item;
  let codigo = req.body.codigo;

  if(!idContrato)
    return res.json(Resposta.erro('Parametros inválidos'));

  let contrato = await Contrato.get(Number(idContrato));

  try{

    let subscription: any =
          await  new IuguService().removaItemAssinatura(codigo, dados);

      await new ContratoService().sincronizeAssinaturaIugu( contrato, subscription);

      await new RegistroDeOperacaoService(Ambiente.Instance.usuarioLogado(),
      Ambiente.Instance.ip()).removeuItemAssinatura(contrato.assinatura, contrato.empresa, dados);

    res.json(Resposta.sucesso());

  } catch (e) {
    respostaErro(res, e);
  }
});

router.put( '/assinaturas/add', ehAdmin, async (req, res) => {
  let idContrato = req.body.cid;
  let dados: any =  req.body.item;
  let codigo = req.body.codigo;

  if(!idContrato)
    return res.json(Resposta.erro('Parametros inválidos'));

  let contrato = await Contrato.get(Number(idContrato));

  try{

    let faturaPendente = dados.gerar2via && dados.fatura ? dados.fatura : null;

    let subscription: any =
      await  new IuguService().adicioneItemAssinatura(codigo, dados.descricao, dados.qtde, dados.valor, dados.recorrente , faturaPendente);

    await new ContratoService().sincronizeAssinaturaIugu( contrato, subscription);

    await new RegistroDeOperacaoService(Ambiente.Instance.usuarioLogado(),
      Ambiente.Instance.ip()).adicionouItemAssinatura(contrato.assinatura, contrato.empresa, dados);

    res.json(Resposta.sucesso());

  } catch (e) {
    respostaErro(res, e);
  }

});

router.put( '/assinaturas/suspenda', ehAdmin, async (req, res) => {
  let codigo = req.body.codigo;
  let idContrato = req.body.cid;
  if(!codigo || !idContrato)
    return res.json(Resposta.erro('Parametros inválidos'));

  let contrato = await Contrato.get(Number(idContrato));
  try{
    let subscription: any =   await  new IuguService().suspendaAssinatura(codigo);
    await new ContratoService().sincronizeAssinaturaIugu( contrato, subscription);

    await new RegistroDeOperacaoService(Ambiente.Instance.usuarioLogado(),
      Ambiente.Instance.ip()).supendeuAssinatura(contrato.assinatura, contrato.empresa);

    res.json(Resposta.sucesso());

  } catch (e) {
    respostaErro(res, e);
  }
})

router.put( '/assinaturas/ative', ehAdmin, async (req, res) => {
  let codigo = req.body.codigo;
  let novoVencimento = req.body.novoVencimento;
  let idContrato = req.body.cid;
  if(!codigo || !idContrato )
    return res.json(Resposta.erro('Parametros inválidos'));

  if(!novoVencimento)
    return res.json(Resposta.erro('Data vencimento nao informada'));

  let contrato = await Contrato.get(Number(idContrato));

  try{
    let erro: any =   await  new ContratoService().reativeAssinatura(contrato, new Date(novoVencimento), req.user);

    if(!erro){
      res.json(Resposta.sucesso())
    } else {
      respostaErro(res, erro);
    }
  } catch (e) {
    respostaErro(res, e);
  }

})

router.put( '/assinaturas/associe/pai', ehAdmin, async (req, res) => {
  let codigo = req.body.codigo;
  let idContrato = req.body.cid;

  if(!codigo || !idContrato)
    return res.json(Resposta.erro('Parametros inválidos'));

  let contrato: Contrato = await Contrato.get( Number(idContrato) );

  if(!contrato)
    return res.json(Resposta.erro('Contrato não existe'));

  if(contrato.assinatura)
    return res.json(Resposta.erro('Contrato já possui assinatuar vinculada.'));

  let assinaturaPai = await new MapeadorDeAssinatura().selecioneSync({codigo: codigo});

  if(!assinaturaPai)
    return res.json(Resposta.erro('Nennhum asssinatura pai com esse codigo: ' + codigo));

  try{

    let assinatura: any = await new ContratoService().vinculeAhAssinaturaPai( contrato, assinaturaPai);

    res.json(Resposta.sucesso(new DTOAssinatura(assinatura)));
  } catch (e) {
    respostaErro(res, e);
  }

})

router.put( '/assinaturas/associe', ehAdmin, async (req, res) => {
  let codigo = req.body.codigo;
  let idContrato = req.body.cid;

  if(!codigo || !idContrato)
    return res.json(Resposta.erro('Parametros inválidos'));

  let contrato: Contrato = await Contrato.get( Number(idContrato) );

  if(!contrato)
    return res.json(Resposta.erro('Contrato não existe'));

  let subscription: any =   await  new IuguService().obtenhaAssinatura(codigo);

  if(!subscription)
    return res.json(Resposta.erro('Assinatura não existe no Iugu'));

  if(contrato.empresa.nome.toLowerCase().trim() !==  subscription.customer_ref.toLowerCase().trim())
   return res.json(Resposta.erro('Empresa não tem o mesmo nome: ' + subscription.customer_ref))

  if(contrato.plano.obtenhaIdentificador() !== subscription.plan_identifier)
    return res.json(Resposta.erro('Assinatura está em outro plano: ' + subscription.plan_identifier.toLowerCase()))

  try{

    let assinatura: any = await new ContratoService().sincronizeAssinaturaIugu( contrato, subscription);

    res.json(Resposta.sucesso(new DTOAssinatura(assinatura)));
  } catch (e) {
    respostaErro(res, e);
  }

})

router.put('/:id/assinaturas/sincronize',  ehAdmin,  async (req, res) => {
  let codigo = req.body.codigo;

  if(!codigo)
    return res.json(Resposta.erro('Parametros inválidos'));

  try{
    let contrato = await Contrato.get(req.params.id);

    let subscription =   await  new IuguService().obtenhaAssinatura(codigo);

    if(!subscription)
      return res.json(Resposta.erro('Nenhuma assinatura encontrada no iugu com esse código: ' + codigo));

    let assinatura: any = await new ContratoService().sincronizeAssinaturaIugu( contrato, subscription);

    res.json(Resposta.sucesso(new DTOAssinatura(assinatura)));

  } catch (e) {
    respostaErro(res, e);
  }

})

router.put('/assinaturas/formapagamento', ehAdmin,  async (req, res) => {
  let id = req.body.id;
  let formaDePagamento = req.body.fp;

  if(!id || !formaDePagamento)
    return res.json(Resposta.erro('Parametros inválidos'));

  let assinatura: Assinatura = await Assinatura.get( Number(id) );

  if(assinatura.formasDePagamento === formaDePagamento)
    return res.json(Resposta.erro('Assinatura já está com forma de pagamento: ' + formaDePagamento))

  if(assinatura.codigoPai)
    return res.json(Resposta.erro('Alteraçao permitida apenas no contrato principal.'))

  try {
    if( assinatura.codigo ){
      let iuguService = new IuguService();
      let contrato: Contrato = await Contrato.get( { idEmpresa: assinatura.empresa.id });

      let subscription = await iuguService.altereFormaDePagamentoAssinatura(contrato,  assinatura, formaDePagamento);

      await new ContratoService().sincronizeAssinaturaIugu( contrato, subscription);

      await new RegistroDeOperacaoService(Ambiente.Instance.usuarioLogado(),
        Ambiente.Instance.ip()).alterouFormaDePagamento(assinatura);

     } else {
      await assinatura.atualize();
     }
     res.json( Resposta.sucesso());
  } catch (e) {
    respostaErro(res, e);
  }
})

router.post('/fatura/adesao', ehAdmin,  async (req, res) => {
  let dados = req.body,
    formaDePagamento = dados.formaDePagamento;

  let dataVencimento =   DateUtils.obtenhaVencimentoDiaUtil().toDate()
  try{
    let formapagamentoIugu: string;
    // tslint:disable-next-line:forin
    for (let key in EnumFormaPagamentoIugu){
      if(EnumFormaPagamentoIugu[key as keyof  typeof EnumFormaPagamentoIugu] === formaDePagamento)
        formapagamentoIugu = key;
    }

    let invoice: any =
      await  new IuguService().crieFaturaAdesao(dados.empresa, dados.taxaAdesao, formapagamentoIugu  ,  dataVencimento);

    await new ContratoService().sincronizeFaturaIugu(invoice, null, dados.empresa, true);

    res.json(Resposta.sucesso( {codigo: invoice.id, url: invoice.secure_url} ));
  } catch (e) {
    respostaErro(res, e);
  }

})

router.post('/assinaturas/fatura',  ehAdmin,  async (req, res) => {
  let codigo = req.body.codigo;
  if(!codigo)
    return res.json(Resposta.erro('Parametros inválidos'))

  let assinatura = await Assinatura.get( { codigo: codigo } );

  if(!assinatura)
    return res.json(Resposta.erro('Assinatura não existe'))

  try{
    let dataVencimento =   DateUtils.obtenhaVencimentoDiaUtil().toDate()

    let contrato: Contrato = await Contrato.get( { idEmpresa: assinatura.empresa.id });

    let invoice =   await  new IuguService().crieFatura(assinatura, contrato,  dataVencimento);

    await new ContratoService().sincronizeFaturaIugu(invoice, contrato);

    res.json(Resposta.sucesso(new DTOAssinatura(assinatura)));

  } catch (e) {
    respostaErro(res, e);
  }
})

router.put('/cartao/remova', async (req: any, res: any) => {
  let cartao: any = req.body.cartao;
  let empresa: any = req.body.empresa;


  if(!cartao || !empresa)
    return res.json(Resposta.erro('Parametros inválidos'))

  let resposta =
    await new IuguService().removaFormaDePagamento(empresa, cartao ).catch( (erro) => {
      res.json(Resposta.erro(erro))
    })

  if(resposta) {
    let contrato: Contrato = await Contrato.get( { idEmpresa: empresa.id });

    await contrato.trouCartao(null);

    await new RegistroDeOperacaoService(Ambiente.Instance.usuarioLogado(),
      Ambiente.Instance.ip()).removeuCartaoAssinatura(contrato.assinatura, contrato.empresa);

    res.json(Resposta.sucesso());
  }

})

router.put('/faturas/novatentativapagamento', async (req: any, res: any) => {
  let dadosFatura: any = req.body;

  if(!dadosFatura || !dadosFatura.id)
    return res.json(Resposta.erro('Parametros inválidos'))

  if(!dadosFatura.cartaoNovaTentativa)
    return res.json(Resposta.erro('Nenhum cartão associado a assinatura'))

  let assinatura: any = await new MapeadorDeAssinatura().selecioneSync(dadosFatura.assinatura.id);
  let numeroParcelas = assinatura.contrato.plano.intervalo > 1 ? assinatura.contrato.numeroParcelas : null;
  let resposta =
    await new IuguService().executeCobrancaCartao(dadosFatura, dadosFatura.cartaoNovaTentativa.codigo, numeroParcelas ).catch( (erro) => {
      res.json(Resposta.erro(erro))
    })

  if(resposta) {
    let invoice =   await  new IuguService().obtenhaFatura(dadosFatura.codigo);
    let faturaSincronizada = await new ContratoService().sincronizeFaturaIugu(invoice, assinatura.contrato);

    res.json(Resposta.sucesso(new DTOFatura(faturaSincronizada)));
  }

})

router.get('/faturas/:codigo/proximovencimento', async (req: any, res: any) => {
  let fatura = await  new MapeadorDeFatura().selecioneSync({ codigo: req.params.codigo})

  if(fatura){
    let proximoVencimento = fatura.obtenhaProximoVencimento(fatura.contrato.getDiaVencimento(), fatura.contrato.plano.intervalo);

    res.json({ proximoVencimento: proximoVencimento,
               proximoBloqueio:   moment(proximoVencimento).add(Empresa.DiasAntesBloqueio, 'days').toDate(),
                fatura: fatura})

  } else {
    res.json(Resposta.erro('fatura nao existe: ' + req.params.codigo))
  }
})

router.get('/iugu/caculeProximosVencimentos', async (req: any, res: any) => {
   let contratos: any = await new MapeadorDeContrato().listeAsync({  ativadosIugu: true, semVencimento: true });

   console.log('total contratos: ' + contratos.length)

  if(contratos.length){
    let assinaturas: any  =  await new IuguService().listeTodasAssinaturas();
    let respostaAtivas: any = { atualizar: { total: 0, itens: []},
                          analisar: { total: 0, itens: []},
                          naoEcontrados: { total: 0, itens: []} }

    let respostaSuspensas: any =   { atualizar: { total: 0, itens: []},
      analisar: { total: 0, itens: []},
      naoEcontrados: { total: 0, itens: []} }

    console.log('total assinaturas: ' + assinaturas.length);

    for(let i = 0; i < contratos.length; i++){
      let contrato: Contrato = contratos[i];

      let item: any  = { contrato: contrato.id, empresa: contrato.empresa.id,
                         diaVencimentoContrato: contrato.diaVencimento, assinatura: contrato.assinatura.codigo }

      let assinaturaIugu = assinaturas.find((assinatura: any) => assinatura.id === contrato.assinatura.codigo);

      if(assinaturaIugu){
        let dataVencimento: Date = moment(assinaturaIugu.expires_at, 'YYYY-MM-DD').toDate();

        item.vencimentoAssinatura = dataVencimento;
        item.dataBloqueio = contrato.empresa.dataBloqueioAuto;
        item.diasDoBloqueio = contrato.empresa.diasDoBloqueio();

        let venceDepoisQuePagou =
          moment( assinaturaIugu.expires_at).startOf('day').isAfter(moment(contrato.assinatura.dataUltimoPagamento).startOf('day'));

        if(venceDepoisQuePagou){
          if(contrato.diaVencimento !==  dataVencimento.getDate())
           await contrato.atualizeDiaVencimento(dataVencimento.getDate(), req.user)

          await contrato.atualizeProximoVencimento(dataVencimento, 'Data próximo vencimento sincronizada com iugu')

          if(!contrato.assinatura.suspensa){
            respostaAtivas.atualizar.itens.push(item)
          } else {
            respostaSuspensas.atualizar.itens.push(item)
          }
        } else {
          if(!contrato.assinatura.suspensa){
            respostaAtivas.analisar.itens.push(item)
          } else {
            respostaSuspensas.analisar.itens.push(item)
          }
        }
      } else {
        if(!contrato.assinatura.suspensa){
          respostaAtivas.naoEcontrados.itens.push(item);
        } else {
          respostaSuspensas.naoEcontrados.itens.push(item);
        }

      }
    }

    respostaAtivas.atualizar.total =   respostaAtivas.atualizar.itens.length;
    respostaAtivas.analisar.total =   respostaAtivas.analisar.itens.length;
    respostaAtivas.naoEcontrados.total =   respostaAtivas.naoEcontrados.itens.length;

    respostaSuspensas.atualizar.total =   respostaSuspensas.atualizar.itens.length;
    respostaSuspensas.analisar.total =   respostaSuspensas.analisar.itens.length;
    respostaSuspensas.naoEcontrados.total =   respostaSuspensas.naoEcontrados.itens.length;

    let resposta: any = {
       ativas: { total: 0, data: respostaAtivas},
       suspensas: { total: 0, data: respostaSuspensas}
    }


    resposta.ativas.total =
      respostaAtivas.atualizar.total +    respostaAtivas.analisar.total +   respostaAtivas.naoEcontrados.total;

    resposta.suspensas.total =
      respostaSuspensas.atualizar.total +    respostaSuspensas.analisar.total +   respostaSuspensas.naoEcontrados.total;

    res.json(Resposta.sucesso( resposta))
  } else {
    res.json(Resposta.erro('Nenhum contrato com assinatura carregado'))
  }
});

router.get('/empresa/:id/vencimento/mesCorrente', async (req: any, res: any) => {

  let empresa = await new MapeadorDeEmpresa().selecioneSync(req.params.id);

  let resposta: any = await Contrato.obtenhaDadosVencimentoMesCorrente(empresa);

  res.json(resposta);
})

export const ContratosController: Router = router;
