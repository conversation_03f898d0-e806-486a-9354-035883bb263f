import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {MapeadorDeMesa} from "../mapeadores/MapeadorDeMesa";
import {SessaoMesa} from "../domain/SessaoMesa";
import {MapeadorDeSessaoMesa} from "../mapeadores/MapeadorDeSessaoMesa";
import {Ambiente} from "../service/Ambiente";
import {Empresa} from "../domain/Empresa";

const router: Router = Router();

async function handleMesaRequest(req: any, res: any, mesaIdentifier: string, isId: boolean) {
  let empresa: Empresa = req.empresa;

  if (!mesaIdentifier) {
    return res.json(Resposta.erro(`É necessário informar o ${isId ? 'id' : 'nome'} da mesa.`));
  }

  let mapeadorMesa = new MapeadorDeMesa();
  let query = isId ? { id: mesaIdentifier } : { nome: mesaIdentifier };

  mapeadorMesa.selecioneSync(query).then((mesa: any) => {
    if (!mesa) {
      return res.json(Resposta.erro(`Não foi encontrada uma mesa com o ${isId ? 'id' : 'nome'} ` + mesaIdentifier));
    }

    let sessaoMesa = SessaoMesa.CrieSessao(mesa);

    mapeadorMesa.transacao((conexao: any, commit: any) => {
      new MapeadorDeSessaoMesa().insiraGraph(sessaoMesa).then((linhasAfetadas: any) => {
        if (!linhasAfetadas) {
          return res.json(Resposta.erro("Não foi possível criar uma sessão para a mesa " + mesaIdentifier));
        }

        commit(() => {
          req.session.acessoMesa = true;
          res.redirect(empresa.obtenhaLinkLoja(Ambiente.Instance.producao) + "/local/" + sessaoMesa.hash);
        });

      }).catch((erro: any) => {
        conexao.rollback(() => {
          return res.json(Resposta.erro("Houve um erro ao criar a sessão: " + erro));
        });
      });
    });

  });
}

router.get('/:id', async (req: any, res) => {
  const idMesa = req.params.id;

  handleMesaRequest(req, res, idMesa, true);
});

router.get('/n/:nome', async (req: any, res) => {
  const nomeMesa = req.params.nome;
  handleMesaRequest(req, res, nomeMesa, false);
});


export const LinkMesaController: Router = router;
