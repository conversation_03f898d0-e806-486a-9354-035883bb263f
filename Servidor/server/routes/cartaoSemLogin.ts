import {Router} from "express";
import {MapeadorDeCartao} from "../mapeadores/MapeadorDeCartao";
import {Resposta} from "../utils/Resposta";
import {Contato} from "../domain/Contato";
import {CartaoService} from "../service/CartaoService";
import {Cartao} from "../domain/Cartao";
import {MapeadorDePlano} from "../mapeadores/MapeadorDePlano";
import {EnumStatusContato} from "../lib/emun/EnumStatusContato";


const router: Router = Router();

router.get('/', async (req, res) => {
  new MapeadorDeCartao().listeAsync({}).then( cartoes => {
    res.json(Resposta.sucesso(cartoes));
  });
});

router.post('/novo', async (req: any, res) => {
  const dados = req.body,
    dadosContato = dados.contato;
  const contato =  new Contato(dadosContato.id, dadosContato.nome, dadosContato.telefone,
                                dadosContato.genero, dadosContato.dataNascimento, dados.cpf, dados.email);
  contato.ultimaVisita = new Date();
  contato.status = EnumStatusContato.Ativo;

  if( dadosContato.quemIndicou && dadosContato.quemIndicou.id ) {
    contato.quemIndicou = new Contato(dadosContato.quemIndicou.id, '', '', '', '');
  }

  const mapeadorDePlano = new MapeadorDePlano();

  const planos = await mapeadorDePlano.listeAsync({});

  for( let i = 0; i < planos.length; i++ ) {
    const plano = planos[i];

    const cartao = new Cartao(dados.id, contato, plano, 0);
    cartao.empresa = req.empresa;
    if(i === 0)
      await new CartaoService().salve(cartao);
    else
      await new CartaoService().salveSemNotificar(cartao)
  }

  res.json({
    sucesso: true,
    data: {
      sucesso: true,
      id: contato.id
    }
  });
});

export const CartoesSemLoginController: Router = router;
