import {Router} from "express";
import {Cardapio} from "../domain/delivery/Cardapio";
import {MapeadorDeCardapio} from "../mapeadores/MapeadorDeCardapio";
import {Resposta} from "../utils/Resposta";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {MapeadorDeProduto} from "../mapeadores/MapeadorDeProduto";
import {Produto} from "../domain/Produto";
import {ProdutoTamanho} from "../domain/templates/ProdutoTamanho";
import {OpcaoDeAdicionalDeProduto} from "../domain/delivery/OpcaoDeAdicionalDeProduto";
import {Empresa} from "../domain/Empresa";
const router: Router = Router();
import _ = require("underscore");
import {Ambiente} from "../service/Ambiente";
import {CardapioPDFService} from "../service/CardapioPDFService";
import {ProdutoService} from "../service/ProdutoService";
let path = require('path');

router.get('/index', async (req: any, res) => {
  const empresa: Empresa = req.empresa;

  await new CardapioPDFService().gerePDF(empresa).then( () => {
    res.json({
      sucesso: true
    });
  });
});

router.get('/veja', async(req: any, res) => {
  const tipoCardapio = 'PDF'
  let empresa: Empresa = req.empresa;

  new ProdutoService().obtenhaProdutoseCategoriasAVendaNaLoja(empresa, tipoCardapio).then( (resposta: any) => {
    let produtosPorCategoria =  _.groupBy(resposta.produtos, (produto: any) => produto.categoria ? produto.categoria.id : 'Outros');

    const listaDeProdutos = resposta.categorias.map((categoria: any) => {
      return {
        produtos: produtosPorCategoria[categoria.id],
        categoria: categoria.nome
      };
    });

    res.render('cardapiopdf.ejs', {produtos: listaDeProdutos, empresa: empresa});
  });
});

export const CardapioPDFController: Router = router;
