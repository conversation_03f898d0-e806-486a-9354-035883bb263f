import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {MapeadorDePontuacaoRegistrada} from "../mapeadores/MapeadorDePontuacaoRegistrada";
import moment = require("moment");
import {MapeadorDeBrindeResgatado} from "../mapeadores/MapeadorDeBrindeResgatado";

const router: Router = Router();



function obtenhaQueryTela (dados: any) {

  let query: any  = {
    inicio:   0,
    total: 10,
  }

  console.log(dados)
  if(dados.i) query.inicio = Number(dados.i)
  if(dados.t) query.total = Number(dados.t)

  if(dados.di){
    if(dados.di.length === 8)
      dados.di = String(`${dados.di}000001`)

    if(moment(dados.di, 'YYYYMMDDHHmmss').isValid())
      query.dataInicio = moment(dados.di, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss')

  }
  if(dados.df)  {
    if(dados.df.length === 8)
      dados.df = String(`${dados.df}235959`)

    if(moment(dados.df, 'YYYYMMDDHHmmss').isValid())
      query.dataFim = moment(dados.df, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss')

  }

  if(dados.pid) query.idPlano = Number(dados.pid)
  if(dados.cid) query.idContato = Number(dados.cid)
  if(dados.bid) query.idBrinde = Number(dados.bid)

  return query;
}


router.get('/pontuacoes', async (req: any, res ) => {
  let query: any = obtenhaQueryTela(req.query)

  let pontucaoHistorica  = await new MapeadorDePontuacaoRegistrada().listeAsync(query);

  pontucaoHistorica.forEach((pontuacao: any) => {
    pontuacao.pontosTexto = pontuacao.cartao.obtenhaDescricaoPontos(pontuacao.pontos);
  })

  res.json(Resposta.sucesso(pontucaoHistorica))

})

router.get('/resgastes', async (req: any, res ) => {
  let query: any = obtenhaQueryTela(req.query)

  let brindes: any = await new MapeadorDeBrindeResgatado().listeAsync(query);

  brindes.forEach((brindeResgatado: any) => {
    brindeResgatado.pontosTexto = brindeResgatado.cartao.obtenhaDescricaoPontos(brindeResgatado.valorEmPontos);
  })

  res.json(Resposta.sucesso(brindes))
})


export const FidelidadeController: Router = router;
