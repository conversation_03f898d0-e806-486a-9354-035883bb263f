import {Router} from "express";
import * as fs from "fs";
import {WebserviceSefaz} from "../utils/nfce/comunicacao/WebserviceSefaz";
import {ColecaoDeServicos} from "../utils/nfce/comunicacao/ColecaoDeServicos";
import {ServicoWeb} from "../utils/nfce/comunicacao/ServicoWeb";
import {ServicoNFeSefaz} from "../utils/nfce/comunicacao/ServicoNFeSefaz";
import {MapeadorConfiguracoesNotaFiscal} from "../utils/nfce/mapeadores/MapeadorConfiguracoesNotaFiscal";
import {Resposta} from "../utils/Resposta";
import {NotaFiscalEletronica} from "../domain/nfce/NotaFiscalEletronica";
import {Certificado} from "../domain/nfce/Certificado";
import {ConfiguracoesNotaFiscal} from "../domain/nfce/configuracoes/ConfiguracoesNotaFiscal";
import {Ambiente} from "../service/Ambiente";
import * as path from "path";
import {MapeadorCertificado} from "../utils/nfce/mapeadores/MapeadorCertificado";
import {DTOConfiguracoesNotaFiscal} from "../lib/dto/nfce/DTOConfiguracoesNotaFiscal";
import {MapeadorOrigemProduto} from "../utils/nfce/mapeadores/MapeadorOrigemProduto";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {Pedido} from "../domain/delivery/Pedido";
import {NotaFiscalEletronicaService} from "../utils/nfce/services/NotaFiscalEletronicaService";
import {MapeadorCFOP} from "../utils/nfce/mapeadores/MapeadorCFOP";
import {MapeadorRegimeTributario} from "../utils/nfce/mapeadores/MapeadorRegimeTributario";
import {MapeadorProdutoConfigFiscal} from "../utils/nfce/mapeadores/MapeadorProdutoConfigFiscal";
import {MapeadorDeProduto} from "../mapeadores/MapeadorDeProduto";
import {MapeadorTipoDeTributacaoICMS} from "../utils/nfce/mapeadores/MapeadorTipoDeTributacaoICMS";
import {MapeadorModalidadeBaseDeCalculoICMS} from "../utils/nfce/mapeadores/MapeadorModalidadeBaseDeCalculoICMS";
import {MapeadorModalidadeBaseDeCalculoICMSST} from "../utils/nfce/mapeadores/MapeadorModalidadeBaseDeCalculoICMSST";
import {MapeadorTipoDeTributacaoIPI} from "../utils/nfce/mapeadores/MapeadorTipoDeTributacaoIPI";
import {MapeadorSeloDeControleIPI} from "../utils/nfce/mapeadores/MapeadorSeloDeControleIPI";
import {ProdutoConfigFiscal} from "../domain/ProdutoConfigFiscal";
import {CarregadorDeWebserviceSefaz} from "../utils/nfce/comunicacao/CarregadorDeWebserviceSefaz";
import {MapeadorDeNotaFiscalEletronica} from "../mapeadores/MapeadorDeNotaFiscalEletronica";
import {DTOImpressaoNfce} from "../utils/nfce/impressao/DTOImpressaoNfce";
const  QRCode = require('qrcode')

const ejs = require('ejs');

const router: Router = Router();
router.get('/', async (req: any, res: any) => {
  try {
    const mapeadorNFe = new MapeadorDeNotaFiscalEletronica();
    const notas = await mapeadorNFe.listeAsync({idEmpresa: req.empresa.id, ordenarDecrescente: true});

    // DTO simplificado apenas com campos necessários para a grid
    const notasDTO = notas.map((nota: NotaFiscalEletronica) => ({
      id: nota.id,
      numeroNFe: nota.numeroNFe,
      serie: nota.serie,
      nomeDestinatario: nota.nomeDestinatario,
      dataDeEmissao: nota.dataDeEmissao,
      valorTotalNFe: nota.valorTotalNFe,
      status: nota.status,
      chaveDeAcesso: nota.chaveDeAcesso
    }));

    res.json(Resposta.sucesso(notasDTO));
  } catch (erro) {
    res.json(Resposta.erro(`Erro ao buscar notas fiscais: ${erro}`));
  }
});

router.get('/teste', async (req, res) => {
  //cria uma nota fiscal eletrônica com dados fictícios, usando a classe NotaFiscalEletronica
  //carrega usando fs o xml do arquivo nota.xml
  let xml = fs.readFileSync('nota.xml', 'utf8');

  let nota = NotaFiscalEletronica.obtenhaNFeAPartirDeNotaEmXML(xml);
  let configuracoes = new ConfiguracoesNotaFiscal()
  configuracoes.ambiente = 2
  configuracoes.versaoNFe = '4.00'
  configuracoes.idToken = '1'
  configuracoes.csc = '02dcc69c58g763b8'


  WebserviceSefaz.servicosNFCeHomologacao.set('4.00', new Map<string, ColecaoDeServicos>());
  WebserviceSefaz.servicosNFCeHomologacao.get('4.00').set('GO', new ColecaoDeServicos())

  let servicoAutorizacao = new ServicoWeb()
  WebserviceSefaz.servicosNFCeHomologacao.get('4.00').get('GO').servicoAutorizacao = servicoAutorizacao

  servicoAutorizacao.url = 'https://homolog.sefaz.go.gov.br/nfe/services/NFeAutorizacao4?wsdl'
  servicoAutorizacao.nome = 'NFeAutorizacao4'
  servicoAutorizacao.acao = 'nfeAutorizacaoLote'


  let servicoQrCode2 = new ServicoWeb()

  WebserviceSefaz.servicosNFCeHomologacao.get('4.00').get('GO').servicoQrCode2 = servicoQrCode2
  servicoQrCode2.url = 'http://www.sefaz.go.gov.br/nfce/qrcode'

  let servicoUrlConsulta2 = new ServicoWeb()

  WebserviceSefaz.servicosNFCeHomologacao.get('4.00').get('GO').urlConsulta2 = servicoUrlConsulta2
  servicoUrlConsulta2.url = 'http://www.sefaz.go.gov.br/nfce/consulta'


  configuracoes.sefaz = new WebserviceSefaz()
  configuracoes.sefaz.estado = 'GO'
  nota.configuracoes = configuracoes
  configuracoes.certificado = new Certificado('certificados/fibo.pfx')
  configuracoes.certificado.setSenha('1234')
  let xmlNota = await nota.gereXmlNFe()

  let enviador = new ServicoNFeSefaz()
  enviador.envieNFeSincronamente(nota).then((resposta) => {
    res.json({nota: nota, xml: xmlNota, resposta: resposta});
  }).catch((erro) => {
    res.json({erro: erro});
  })
});


router.get('/configuracoes', async (req: any, res: any) => {
  let configuracoes: any = await (new MapeadorConfiguracoesNotaFiscal()).obtenhaPorEmpresa(req.empresa)

  if(configuracoes) {
    configuracoes.nomeArquivoPfx = configuracoes.certificado.arquivo
  }

  res.json(Resposta.sucesso(new DTOConfiguracoesNotaFiscal(configuracoes)))
})

router.post('/tribnatop', async(req: any, res: any) => {
  let dadosTributacao = req.body.dados
  let tipo: string = req.body.tipo

  if(!tipo)
    return res.json(Resposta.erro('É necessário informar o tipo de tributação.'))

  let configuracoes: any = await (new MapeadorConfiguracoesNotaFiscal()).obtenhaPorEmpresa(req.empresa)

  if(!configuracoes)
    return res.json(Resposta.erro("É necessário salvar as configurações básicas primeiro."))

  let mapeadorConfiguracoes = new MapeadorConfiguracoesNotaFiscal()

    if(tipo.toUpperCase() === 'PROPRIA')
    await mapeadorConfiguracoes.salveTribNatProprio(configuracoes, dadosTributacao)
  else if(tipo.toUpperCase() === 'TERCEIROS')
    await mapeadorConfiguracoes.salveTribNatTerceiros(configuracoes, dadosTributacao)
  else
    return res.json(Resposta.erro("Tipo de tributação inválido"))



  res.json(Resposta.sucesso(new DTOConfiguracoesNotaFiscal(configuracoes, configuracoes.senhaCertificado)))

})

router.post('/uploadcertificado', async (req: any, res: any) => {
  let file: any = null, partesNome: any = null, extensao = null;

  if( req.files ) {
    file = req.files.files;
    partesNome = file.name.split('.');
    extensao = file.name.split('.')[partesNome.length - 1];
  }

  if(['pfx', 'p12'].indexOf(extensao) === -1) {
    return res.json(Resposta.erro('Extensão inválida'))
  }

  let diretorio = Ambiente.Instance.config.caminhoArquivos,
    nomeArquivo = file.name,
    diretorioEmpresa: string = path.join(diretorio, 'empresa',  req.empresa.id.toString()),
    diretorioCertificados  = path.join(diretorioEmpresa, 'certificados'),
    arquivo: string = path.join(diretorioCertificados, nomeArquivo);

  if(!fs.existsSync(diretorio) ) {
    console.log(diretorio)
    return res.status(400).json(Resposta.erro("Diretório de uploads de arquivos não existe"));
  }

  if( !fs.existsSync(diretorioEmpresa) ) {
    fs.mkdirSync(diretorioEmpresa);
  }

  if(!fs.existsSync(diretorioCertificados)) {
    fs.mkdirSync(diretorioCertificados);
  }


  file.mv(arquivo).then(async (err: any) => {
      return res.json(Resposta.sucesso({
        mensagem: "Arquivo enviado com sucesso",
        nomeArquivo: nomeArquivo
      }));
  })
})
router.post('/uploadcertificado', async (req: any, res: any) => {
  let file: any = null, partesNome: any = null, extensao = null;

  if( req.files ) {
    file = req.files.files;
    partesNome = file.name.split('.');
    extensao = file.name.split('.')[partesNome.length - 1];
  }

  if(['pfx', 'p12'].indexOf(extensao) === -1) {
    return res.json(Resposta.erro('Extensão inválida'))
  }

  let diretorio = Ambiente.Instance.config.caminhoArquivos,
    nomeArquivo = file.name,
    diretorioEmpresa: string = path.join(diretorio, 'empresa',  req.empresa.id.toString()),
    diretorioCertificados  = path.join(diretorioEmpresa, 'certificados'),
    arquivo: string = path.join(diretorioCertificados, nomeArquivo);

  if(!fs.existsSync(diretorio) ) {
    console.log(diretorio)
    return res.status(400).json(Resposta.erro("Diretório de uploads de arquivos não existe"));
  }

  if( !fs.existsSync(diretorioEmpresa) ) {
    fs.mkdirSync(diretorioEmpresa);
  }

  if(!fs.existsSync(diretorioCertificados)) {
    fs.mkdirSync(diretorioCertificados);
  }


  file.mv(arquivo).then(async (err: any) => {
      return res.json(Resposta.sucesso({
        mensagem: "Arquivo enviado com sucesso",
        nomeArquivo: nomeArquivo
      }));
  })
})


router.post('/configuracoes', async (req: any, res: any) => {
  let configuracoes = req.body
  let empresa = req.empresa

  let configuracoesNotaFiscal: ConfiguracoesNotaFiscal = await (new MapeadorConfiguracoesNotaFiscal()).obtenhaPorEmpresa(req.empresa)

  if(configuracoesNotaFiscal != null) {
    return res.json(Resposta.erro('Já existe uma configuração para esta empresa'))
  }

  configuracoesNotaFiscal = new ConfiguracoesNotaFiscal()

  configuracoesNotaFiscal.ambiente = configuracoes.ambiente
  configuracoesNotaFiscal.versaoNFe = configuracoes.versaoNFe
  configuracoesNotaFiscal.idToken = configuracoes.idToken
  configuracoesNotaFiscal.csc = configuracoes.csc
  configuracoesNotaFiscal.empresa = empresa
  configuracoesNotaFiscal.tipoImpressaoDoDanfe = configuracoes.tipoImpressaoDoDanfe
  configuracoesNotaFiscal.regimeTributario = configuracoes.regimeTributario
  configuracoesNotaFiscal.inscricaoEstadual = configuracoes.inscricaoEstadual
  configuracoesNotaFiscal.cnae = configuracoes.cnae
  configuracoesNotaFiscal.naturezaDaOperacao = configuracoes.naturezaOperacao
  configuracoesNotaFiscal.enviarAutomaticamente = configuracoes.enviarAutomaticamente


  configuracoesNotaFiscal.certificado = new Certificado(configuracoes.nomeArquivoPfx)
  await configuracoesNotaFiscal.certificado.setSenha(configuracoes.senhaCertificado)

  configuracoesNotaFiscal.certificado.empresa = req.empresa

  try {
    await configuracoesNotaFiscal.certificado.extraiaPEM();
  } catch (error) {
    return res.json(Resposta.erro('Erro ao processar certificado: ' + error.message));
  }

  await (new MapeadorCertificado()).insiraSync(configuracoesNotaFiscal.certificado)
  await (new MapeadorConfiguracoesNotaFiscal()).insiraSync(configuracoesNotaFiscal)

  res.json(Resposta.sucesso(new DTOConfiguracoesNotaFiscal(configuracoesNotaFiscal, configuracoes.senhaCertificado)))
})

router.put('/configuracoes', async (req: any, res: any) => {
  let configuracoes = req.body
  let empresa = req.empresa
  let atualizouCertificado: boolean = false


  let configuracoesNotaFiscal = await (new MapeadorConfiguracoesNotaFiscal()).obtenhaPorEmpresa(req.empresa)

  if(!configuracoesNotaFiscal)
    return res.json(Resposta.erro('Não existe uma configuração para esta empresa'))

  configuracoesNotaFiscal.ambiente = configuracoes.ambiente
  configuracoesNotaFiscal.versaoNFe = configuracoes.versaoNFe
  configuracoesNotaFiscal.idToken = configuracoes.idToken
  configuracoesNotaFiscal.csc = configuracoes.csc
  configuracoesNotaFiscal.seriePadrao = configuracoes.seriePadrao
  configuracoesNotaFiscal.numeroInicial = configuracoes.numeroInicial
  configuracoesNotaFiscal.empresa = empresa
  configuracoesNotaFiscal.tipoImpressaoDoDanfe = configuracoes.tipoImpressaoDoDanfe
  configuracoesNotaFiscal.regimeTributario = configuracoes.regimeTributario
  configuracoesNotaFiscal.inscricaoEstadual = configuracoes.inscricaoEstadual
  configuracoesNotaFiscal.cnae = configuracoes.cnae
  configuracoesNotaFiscal.naturezaDaOperacao = configuracoes.naturezaOperacao
  configuracoesNotaFiscal.enviarAutomaticamente = configuracoes.enviarAutomaticamente

  configuracoesNotaFiscal.certificado.empresa = req.empresa


  if(configuracoes.nomeArquivoPfx) {
    configuracoesNotaFiscal.certificado.arquivo = configuracoes.nomeArquivoPfx
    atualizouCertificado = true
  }

  if(configuracoes.senhaCertificado) {
    await configuracoesNotaFiscal.certificado.setSenha(configuracoes.senhaCertificado)
    try {
      await configuracoesNotaFiscal.certificado.extraiaPEM();
    } catch (error) {
      return res.json(Resposta.erro('Erro ao processar certificado: ' + error.message));
    }
  }

  await (new MapeadorConfiguracoesNotaFiscal()).atualizeSync(configuracoesNotaFiscal)
  if(atualizouCertificado)
    await (new MapeadorCertificado()).atualizeSync(configuracoesNotaFiscal.certificado)
  res.json(Resposta.sucesso(configuracoesNotaFiscal))
})

router.get('/origens', async (req: any, res: any) => {
  let origens = await (new MapeadorOrigemProduto()).listeAsync({})
  res.json(Resposta.sucesso(origens))
})

router.get('/cfops', async (req: any, res: any) => {
  let cfops = await (new MapeadorCFOP()).listeAsync({permitidaNaNfce: true})
  res.json(Resposta.sucesso(cfops))
})

router.get('/regimesTributarios', async (req: any, res: any) => {
  let regimes = await (new MapeadorRegimeTributario()).listeAsync({})
  res.json(Resposta.sucesso(regimes))
})

router.get('/tiposDeTributacaoICMS', async (req: any, res: any) => {
  let tipos = await (new MapeadorTipoDeTributacaoICMS()).listeAsync({})
  res.json(Resposta.sucesso(tipos))
})

router.get('/tiposDeTributacaoIPI', async (req: any, res: any) => {
  let tipos = await (new MapeadorTipoDeTributacaoIPI()).listeAsync({})
  res.json(Resposta.sucesso(tipos))
})

router.get('/selosDeControleIPI', async (req: any, res: any) => {
  let selos = await (new MapeadorSeloDeControleIPI()).listeAsync({})
  res.json(Resposta.sucesso(selos))
})

router.get('/modalidadesBCICMS', async (req: any, res: any) => {
  let modalidades = await (new MapeadorModalidadeBaseDeCalculoICMS()).listeAsync({})
  res.json(Resposta.sucesso(modalidades))
})

router.get('/modalidadesBCICMSST', async (req: any, res: any) => {
  let modalidades = await (new MapeadorModalidadeBaseDeCalculoICMSST()).listeAsync({})
  res.json(Resposta.sucesso(modalidades))
})

router.get('/liste/nao-impressas', async(req: any, res: any) => {
  let nfcs = await (new MapeadorDeNotaFiscalEletronica().listeAsync({
    idEmpresa: req.empresa.id,
    impressa: false,
    status: 2
  }))

  let configuracoes = await (new MapeadorConfiguracoesNotaFiscal()).obtenhaPorEmpresa(req.empresa)
  configuracoes.empresa = req.empresa

  let resposta: any = {
    nfces: nfcs.map((nfce: NotaFiscalEletronica) => {
      nfce.configuracoes = configuracoes
      nfce.empresa = configuracoes.empresa
      return new DTOImpressaoNfce(nfce, req.empresa)
    })
  }

  //retorna a lista das últimas nfces emitidas para impressão automática
  res.json(Resposta.sucesso(resposta))
})

router.get('/:id/impressao', async(req: any, res: any) => {
  try {
    const id = req.params.id;

    // Busca a nota fiscal pelo ID
    const mapeador = new MapeadorDeNotaFiscalEletronica();
    const nfce = await mapeador.selecioneSync({ id: id });

    if (!nfce) {
      return res.json(Resposta.erro('Nota fiscal não encontrada'));
    }

    // Obtém as configurações da empresa
    const configuracoes = await (new MapeadorConfiguracoesNotaFiscal()).obtenhaPorEmpresa(req.empresa);
    configuracoes.empresa = req.empresa;

    // Configura a nota fiscal com as configurações da empresa
    nfce.configuracoes = configuracoes;
    nfce.empresa = configuracoes.empresa;

    // Cria o DTO para impressão
    const dtoImpressao = new DTOImpressaoNfce(nfce, req.empresa);

    // Retorna o DTO para impressão
    res.json(Resposta.sucesso(dtoImpressao));
  } catch (erro) {
    res.json(Resposta.erro(`Erro ao obter dados para impressão: ${erro}`));
  }
})


router.put('/:id/impressa' , async (req: any, res: any) => {
  let id = req.params.id

  let mapeador = new MapeadorDeNotaFiscalEletronica()



  await mapeador.marqueImpressa({id: id, impressa: true} as NotaFiscalEletronica)

  res.json(Resposta.sucesso(true))
})
router.post('/emitir', async (req: any, res: any) => {
  try {
    const { idPedido } = req.body;

    if (!idPedido) {
      return res.json(Resposta.erro('ID do pedido não informado'));
    }

    // Obter configurações fiscais da empresa
    const configuracoes = await (new MapeadorConfiguracoesNotaFiscal()).obtenhaPorEmpresa(req.empresa);
    configuracoes.empresa = req.empresa;

    configuracoes.certificado.empresa = req.empresa;
    // Buscar o pedido pelo ID
    const mapeadorDePedido = new MapeadorDePedido();
    const pedido = await mapeadorDePedido.selecioneSync({ id: idPedido });

    if (!pedido) {
      return res.json(Resposta.erro('Pedido não encontrado'));
    }

    pedido.empresa = req.empresa;

    // Criar serviço de NFC-e
    const nfceService = new NotaFiscalEletronicaService(configuracoes);

    // Gerar NFC-e do pedido usando o método assíncrono
    const nfce = await nfceService.gereNfceDoPedido(pedido);

    // Atualizar o pedido com a referência da NFC-e gerada
    pedido.nfce = nfce;
    await mapeadorDePedido.atualizeNfce(pedido);

    // Enviar a NFC-e para a SEFAZ
    nfceService.envieNfce(nfce); // Executa em paralelo, sem aguardar

    return res.json(Resposta.sucesso({
      mensagem: 'NFC-e emitida com sucesso',
      nfce: nfce
    }));
  } catch (erro) {
    console.error('Erro ao emitir NFC-e:', erro);
    return res.json(Resposta.erro(erro.message || 'Erro ao emitir NFC-e'));
  }
})

//metodo para testar o cancelamento de uma nota fiscal em ambiente de homologação
router.put('/cancelar', async (req: any, res: any) => {
  try {
    const { idNota, justificativa } = req.body;

    if (!idNota) {
      return res.json(Resposta.erro('ID da nota não informado'));
    }

    if (!justificativa || justificativa.length < 15) {
      return res.json(Resposta.erro('Justificativa deve ter pelo menos 15 caracteres'));
    }

    // Buscar a nota fiscal pelo ID
    const mapeadorDeNota = new MapeadorDeNotaFiscalEletronica();
    const nota = await mapeadorDeNota.selecioneSync({ id: idNota });

    if (!nota) {
      return res.json(Resposta.erro('Nota fiscal não encontrada'));
    }

    // Obter configurações fiscais da empresa
    const configuracoes = await (new MapeadorConfiguracoesNotaFiscal()).obtenhaPorEmpresa(req.empresa);

    if(configuracoes.ambiente !== 2)
      return res.json(Resposta.erro('Cancelamento de notas em ambiente de produção não é permitido neste endpoint'));

    //configurações da nota
    configuracoes.empresa = req.empresa;
    nota.configuracoes = configuracoes;

    // Criar serviço de NFC-e e cancelar a nota
    const servicoSefaz = new ServicoNFeSefaz();
    const resposta = await servicoSefaz.cancelarNFeSincronamente(nota, justificativa);

    if(!resposta.resposta || !resposta.resposta.foiRegistrado)
      return res.json(Resposta.erro("Não foi possível efetuar o cancelamento. " + resposta?.resposta?.motivo ))

    return res.json(Resposta.sucesso(resposta.resposta.motivo))
  } catch (erro) {
    console.error('Erro ao cancelar nota fiscal:', erro);
    return res.json(Resposta.erro(erro.message || 'Erro ao cancelar nota fiscal'));
  }
})



// Método para reenviar uma NFCe existente
router.post('/reenviar/:idNota', async (req: any, res: any) => {
  try {
    const idNota = req.params.idNota;

    if (!idNota) {
      return res.json(Resposta.erro('ID da nota não informado'));
    }

    // Buscar a nota fiscal pelo ID
    const mapeadorDeNota = new MapeadorDeNotaFiscalEletronica();
    const nota = await mapeadorDeNota.selecioneSync({ id: idNota });

    if (!nota) {
      return res.json(Resposta.erro('Nota fiscal não encontrada'));
    }

    // Atualiza a data de emissão para o momento atual
    nota.dataDeEmissao = new Date();
    await mapeadorDeNota.atualizeDataEmissao(nota);

    // Obter configurações fiscais da empresa
    const configuracoes = await (new MapeadorConfiguracoesNotaFiscal()).obtenhaPorEmpresa(req.empresa);

    // Configurações da nota
    configuracoes.empresa = req.empresa;
    nota.configuracoes = configuracoes;

    // Criar serviço de NFC-e e reenviar a nota
    const servicoSefaz = new ServicoNFeSefaz();

    try {
      await nota.gereNovoXmlNFe(false);
      const resposta = await servicoSefaz.envieNFeSincronamente(nota);

      return res.json(Resposta.sucesso({
        resposta: {
          foiAutorizada: resposta.foiAutorizada,
          chaveDeAcesso: resposta.chaveDeAcesso,
          numeroProtocolo: resposta.numeroProtocolo
        }
      }));
    } catch (erroEnvio) {
      console.error('Erro ao reenviar nota fiscal:', erroEnvio);
      return res.json(Resposta.erro(`Erro ao reenviar nota fiscal: ${erroEnvio.message || erroEnvio}`));
    }
  } catch (erro) {
    console.error('Erro ao processar reenvio da nota fiscal:', erro);
    return res.json(Resposta.erro(`Erro ao processar reenvio: ${erro.message || erro}`));
  }
});


router.get('/envie/pedido/:codigoPedido', async (req: any, res: any) => {
  //cria uma NotaFiscalEletronica a partir de um pedido
  let codigoPedido = req.params.codigoPedido

  let configuracoes = await (new MapeadorConfiguracoesNotaFiscal()).obtenhaPorEmpresa(req.empresa)

  let mapeadorDePedido = new MapeadorDePedido()
  let pedido: Pedido = await mapeadorDePedido.selecioneSync({codigo: codigoPedido} )
  pedido.empresa = req.empresa
  configuracoes.empresa = req.empresa

  let nfceService = new NotaFiscalEletronicaService(configuracoes)

    nfceService.gereNfceDoPedido(pedido).then(async (nota: NotaFiscalEletronica) => {
      pedido.nfce = nota;

      await mapeadorDePedido.atualizeNfce(pedido)

      let enviador = new ServicoNFeSefaz()
      nota.gereXmlNFe().then((xmlNota) => {
        enviador.envieNFeSincronamente(nota).then((resposta) => {
          res.json({nota: nota, xml: xmlNota, resposta: resposta});
        }).catch((erro) => {
          res.json({erro: erro});
        })
      }).catch((erro) => {
        res.json({erro: erro});
      })

      /*
      nota.gereXmlNFe().then((xml: any) => {


        res.type('application/xml');
        res.send(xml);
        //res.json(Resposta.sucesso(xml))
      }).catch((erro) => {

        res.json(Resposta.erro(erro))
      })

       */

    }).catch((erro) => {
        res.json(Resposta.erro(erro))
    })
})



router.post('/produto/configuracoes', async (req: any, res: any) => {
  let idProduto = req.body.idProduto
  let configuracoes = req.body.configuracaoFiscal

  let mapeador = new MapeadorProdutoConfigFiscal()


  let prodConfigFiscal = await mapeador.selecioneSync({produtoId: idProduto})

  //se não existir, cria uma nova configuração
  if(!prodConfigFiscal) {
    prodConfigFiscal = new ProdutoConfigFiscal()

  }

  prodConfigFiscal.produto = {id: idProduto}
  prodConfigFiscal.origem = configuracoes.origem
  prodConfigFiscal.ncm = configuracoes.ncm
  prodConfigFiscal.gtin = configuracoes.gtin
  prodConfigFiscal.cest = configuracoes.cest
  prodConfigFiscal.unidade = configuracoes.unidade
  prodConfigFiscal.producaoPropria = configuracoes.producaoPropria
  prodConfigFiscal.tipoDeTributacaoIPI = configuracoes.tipoDeTributacaoIPI
  prodConfigFiscal.codigoEnquadramentoIPI = configuracoes.codigoEnquadramentoIPI
  prodConfigFiscal.cnpjProdutor = configuracoes.cnpjProdutor
  prodConfigFiscal.tipoCalculoIPI = configuracoes.tipoCalculoIPI
  prodConfigFiscal.aliquotaIPI = configuracoes.aliquotaIPI
  prodConfigFiscal.valorPorUnidadeIPI = configuracoes.valorPorUnidadeIPI
  prodConfigFiscal.seloDeControleIPI = configuracoes.seloDeControleIPI
  prodConfigFiscal.quantidadeSeloDeControleIPI = configuracoes.quantidadeSeloDeControleIPI
  prodConfigFiscal.ipiCompoeBaseCalculoICMS = configuracoes.ipiCompoeBaseCalculoICMS
  prodConfigFiscal.aliquotaAtributosAproximados = configuracoes.aliquotaAtributosAproximados
  prodConfigFiscal.informacoesAdicionais = configuracoes.informacoesAdicionais



  mapeador.persista(prodConfigFiscal).then(() => {

    new MapeadorDeProduto(req.empresa.catalogo).removaCacheProdutos(true)

    res.json(Resposta.sucesso(prodConfigFiscal))
  }).catch((erro: any) => {
    res.json(Resposta.erro(erro))
  })

})

router.get('/:id/distribuicao', async(req: any, res: any) => {
  try {
    const id = req.params.id;

    // Busca a nota fiscal pelo ID
    const mapeador = new MapeadorDeNotaFiscalEletronica();
    const nfce = await mapeador.selecioneSync({ id: id });

    if (!nfce) {
      return res.json(Resposta.erro('Nota fiscal não encontrada'));
    }

    // Obtém as configurações da empresa
    const configuracoes = await (new MapeadorConfiguracoesNotaFiscal()).obtenhaPorEmpresa(req.empresa);
    configuracoes.empresa = req.empresa;

    // Configura a nota fiscal com as configurações da empresa
    nfce.configuracoes = configuracoes;
    nfce.empresa = configuracoes.empresa;

    // Cria o serviço e obtém o XML de distribuição
    const servico = new NotaFiscalEletronicaService(configuracoes);
    const xmlDistribuicao = await servico.obtenhaXmlDistribuicao(nfce);

    // Nome do arquivo usando a chave de acesso conforme padrão SEFAZ
    const nomeArquivo = `${nfce.chaveDeAcesso}-procNFe.xml`;

    // Configura o response como download de XML
    res.set({
      'Content-Type': 'application/xml',
      'Content-Disposition': `attachment; filename=${nomeArquivo}`
    });
    res.send(xmlDistribuicao);

  } catch (erro) {
    console.error('Erro ao obter XML de distribuição:', erro);
    res.json(Resposta.erro(`Erro ao obter XML de distribuição: ${erro}`));
  }
});

router.get('/:id/danfe', async(req: any, res) => {
  const nfce = await (new MapeadorDeNotaFiscalEletronica()).selecioneSync({ id: req.params.id });
  const configuracoes = await (new MapeadorConfiguracoesNotaFiscal()).obtenhaPorEmpresa(req.empresa);
  configuracoes.empresa = req.empresa;

  // Configura a nota fiscal com as configurações da empresa
  nfce.configuracoes = configuracoes;
  nfce.empresa = configuracoes.empresa;

  if(nfce) {
    const dto = new DTOImpressaoNfce(nfce, req.empresa);
    dto.logo = req.empresa.obtenhaLinkLogo(Ambiente.Instance.producao);

    res.render('danfe-nfce-80mm.ejs', { nfce: dto });
  } else {
    res.end(String(`Nota fiscal não encontrada: ${req.params.id}`), 'utf8');
  }
});

router.get('/:id/qrcode', async (req: any, res: any) => {
  try {
    const nfce = await (new MapeadorDeNotaFiscalEletronica()).selecioneSync({ id: req.params.id });
    if (!nfce) {
      return res.status(404).send('Nota fiscal não encontrada');
    }

    const qrCodeBuffer = await QRCode.toBuffer(nfce.qrCode, {
      errorCorrectionLevel: 'H',
      type: 'png',
      width: 256,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#ffffff'
      }
    });

    res.writeHead(200, {
      'Content-Type': 'image/png',
      'Content-Length': qrCodeBuffer.length
    });
    res.end(qrCodeBuffer);
  } catch (erro) {
    console.error('Erro ao gerar QR Code:', erro);
    res.status(500).send('Erro ao gerar QR Code');
  }
});

export const NfceController: Router = router;
