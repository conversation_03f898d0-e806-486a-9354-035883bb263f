import {Router} from 'express';
import {Resposta} from "../utils/Resposta";
import {Campanha} from "../domain/Campanha";
import {CampanhaService} from "../service/CampanhaService";
import {MapeadorDeContato} from "../mapeadores/MapeadorDeContato";
import {MapeadorDeCampanha} from "../mapeadores/MapeadorDeCampanha";
import {VariaveisDeRequest} from "../service/VariaveisDeRequest";
import {TarefaEnvioCampanha} from "../domain/TarefaEnvioCampanha";
import {MapeadorDeMensagemEnviada} from "../mapeadores/MapeadorDeMensagemEnviada";
import {Empresa} from "../domain/Empresa";
import {Contato} from "../domain/Contato";
import {EnumStatusCampanha} from "../domain/EnumStatusCampanha";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {EnumStatusAprovacao} from "../domain/EnumStatusAprovacao";
import {MapeadorDeCampanhaRede} from "../mapeadores/MapeadorDeCampanhaRede";
import {CampanhaRede} from "../domain/CampanhaRede";

let redis = require("redis");


let client = redis.createClient();

const router: Router = Router();

router.put('/', async (req, res) => {
  let file: any = null, partesNome: any = null, extensao = null;

  let dados: any = JSON.parse(req.body.data);

  let campanha: CampanhaRede = await  new MapeadorDeCampanhaRede().selecioneSync({id: dados.id});

  Object.assign(campanha, dados);
  if (dados.horarioEnvio)
    campanha.horarioEnvio = new Date(dados.horarioEnvio);

  new MapeadorDeCampanhaRede().atualize(campanha).then((atualizou: any) => {
    if (atualizou) {
      res.json(Resposta.sucesso({id: campanha.id}))
    } else {
      res.json(Resposta.erro('Não foi possível salvar a campanha'));
    }
  });
});

router.post('/replique', async(req: any, res) => {
  const id = req.body.id;
  const mapeador = new MapeadorDeCampanhaRede();

  const campanha: CampanhaRede = await mapeador.selecioneSync({id: id});

  if(!req.empresa.dadosRede)
    return res.json(Resposta.erro(String(`Empresa "${req.empresa.nome}" não tem dados da rede`)))

  let query: any = {}
  if(campanha.redes && campanha.redes.length){
    query.redes =  campanha.redes
  } else {
    query.redeDaEmpresa = req.empresa.dadosRede.grupo
  }

  const empresasDaRede: Array<Empresa> = await new MapeadorDeEmpresa().listeEmpresasRede(query);

  for( let empresa of empresasDaRede ) {
    console.log(empresa.id + ' -> ' + empresa.nome);
    const campanhaEmpresa: Campanha = new Campanha();

    campanhaEmpresa.nome = campanha.nome;
    campanhaEmpresa.dataCriacao = campanha.dataCriacao;
    campanhaEmpresa.empresa = empresa;
    campanhaEmpresa.mensagem = '';
    campanhaEmpresa.linkImagem = campanha.linkImagem || '';
    campanhaEmpresa.status = EnumStatusCampanha.Aceitar;
    campanhaEmpresa.tipoDeEnvio = campanha.tipoDeEnvio;
    campanhaEmpresa.horarioEnvio = campanha.horarioEnvio;
    campanhaEmpresa.ativa = true;
    campanhaEmpresa.origemContatos = campanha.origemContatos;
    campanhaEmpresa.foiTestada = true;
    campanhaEmpresa.foiAceita = false;
    campanhaEmpresa.campanhaRede = campanha;
    campanhaEmpresa.qtdeDiasUltimaNotificacao = 3;
    campanhaEmpresa.naoEnviarMsgParaQuemRecebeuRecente = true;

    const mapeadorDeCampanha = new MapeadorDeCampanha();
    mapeadorDeCampanha.desativeMultiCliente();

    mapeadorDeCampanha.insiraGraph(campanhaEmpresa);
  }

  campanha.foiReplicada = true;

  await mapeador.atualizeSync(campanha);

  res.json(Resposta.sucesso("Campanha foi replicada para " + empresasDaRede.length + ' empresa(s).'));
});

router.post('/', async (req , res) => {
  let file: any = null, partesNome: any = null, extensao = null;

  let dados: any = JSON.parse(req.body.data);

  let campanha = new CampanhaRede();
  Object.assign(campanha, dados);

  if ( dados.horarioEnvio )
    campanha.horarioEnvio = new Date(dados.horarioEnvio);

  new MapeadorDeCampanhaRede().insira(campanha).then( (idInserido: any) => {
    if(idInserido) {
      res.json(Resposta.sucesso({id: campanha.id}))
    } else {
      res.json(Resposta.erro('Erro ao salvar campanha'))
    }
  });
});

router.post('/aprove', async(req, res) => {
  const idCampanha = req.body.id;

  const mapeadorDeCampanha = new MapeadorDeCampanha();
  mapeadorDeCampanha.desativeMultiCliente();

  const campanha: Campanha = await mapeadorDeCampanha.selecioneSync({id: idCampanha});

  if( !campanha ) {
    return res.json(Resposta.erro("Parâmetros inválidos"));
  }

  campanha.statusAprovacao = EnumStatusAprovacao.Aprovada;

  new MapeadorDeCampanha().atualizeSync(campanha).then( (resposta: any) => {
    res.json(Resposta.sucesso({
      msg: "Campanha aprovada com sucesso! Não se esqueça de avisar o cliente.",
      status: campanha.statusAprovacao
    }));
  });
});

router.put('/ative', async (req, res) => {
  const dados: any = req.body;
  const ativar = dados.ativar;

  let campanha = new CampanhaRede();
  Object.assign(campanha, dados);

  const mapeadorDeCampanhaRede = new MapeadorDeCampanhaRede();

  mapeadorDeCampanhaRede.selecioneSync({id: campanha.id}).then( (objCampanha) => {
    objCampanha.ativa = ativar;
    mapeadorDeCampanhaRede.atualizeSync(objCampanha).then( (atualizou: any) => {
      if(atualizou > 0){
        res.json(Resposta.sucesso({id: campanha.id}))
      }else{
        res.json(Resposta.erro('Não foi possível atualizar'));
      }
    });
  });
});

router.get('/liste-empresas/:id', async (req: any, res) => {
  const id: any = req.params.id;
  const mapeadorDeCampanhaRede = new MapeadorDeCampanhaRede();

  const query: any = {id: id};

  if( req.query.p === 'true' ) {
    query.pendentes = true;
  }

  const empresas: any = await mapeadorDeCampanhaRede.selecioneEmpresasAceitaram(query).catch( (erro: any) => {
    console.log(erro);
  });

  res.json(Resposta.sucesso({
    empresas: empresas,
    total: empresas.length
  }));
});

router.get('/liste', async (req: any, res) => {
  const tipoEnvio = req.query.t;
  const status = req.query.s;
  const pendentes = req.query.p;
  const mapeadorDeCampanhaRede = new MapeadorDeCampanhaRede(),
    query: any  = {orderBy: true};

  if( tipoEnvio === 'recorrentes' ) {
    query['tipoDeEnvio'] = 'Agendado';
  } else if( tipoEnvio === 'envio-unico' ) {
    query['tipoDeEnvio'] = 'Unico';
  }

  if( pendentes ) {
    query['pendentes'] = true;
    mapeadorDeCampanhaRede.desativeMultiCliente();
  }

  if( tipoEnvio === 'desativadas' ) {
    query['desativadas'] = true;
  }

  if( status ) {
    query['status'] = status.split(',');
  }

  console.log(query);

  const campanhas: any = await mapeadorDeCampanhaRede.listeAsync(query).catch( (erro) => {
    console.log(erro);
  });

  campanhas.forEach( (campanha: Campanha) => {
    delete campanha.contatos
  });

  let total = await mapeadorDeCampanhaRede.selecioneTotal(query);

  res.json( Resposta.sucesso(
    {
      campanhas: campanhas,
      total: total
    }
  ));
});

router.get('/envioTeste/inicie', async(req, res) => {
  const mapeadorDeCampanha = new MapeadorDeCampanha();
  const id: any = req.query.idc;
  const telefone: any = req.query.tel;

  console.log(id);

  const campanha: Campanha = await mapeadorDeCampanha.selecioneSync({id: id});

  if( campanha == null ) {
    return res.json(Resposta.erro('Campanha não existe!'));
  }

  if( campanha.status === 'Enviada' ) {
    return res.json(Resposta.erro('Campanha já foi enviada!'));
  }

  const query = {};

  let contato  = await new MapeadorDeContato().selecioneSync({telefone: telefone});

  if( contato == null ) {
    contato = new Contato(-1, '', telefone);
  }

  const contatos = [contato];

  new VariaveisDeRequest().obtenhaEmpresaLogada().then( async (empresa: Empresa) => {
    const tarefaEnvioCampanha = new TarefaEnvioCampanha(contatos.length);

    res.json({
      sucesso: true,
      enviando: true,
      status: tarefaEnvioCampanha
    });

    await new CampanhaService().facaEnvioTeste(empresa, campanha, contatos, tarefaEnvioCampanha);
  });
});


router.get('/marqueComoTestada/:idc', async(req, res) => {
  const mapeadorDeCampanha = new MapeadorDeCampanha();
  const id: any = req.params.idc;

  console.log(id);

  const campanha: Campanha = await mapeadorDeCampanha.selecioneSync({id: id});

  if( campanha == null ) {
    return res.json(Resposta.erro('Campanha não existe!'));
  }

  if( campanha.status === 'Enviada' ) {
    return res.json(Resposta.erro('Campanha já foi enviada!'));
  }

  campanha.foiTestada = true;

  new MapeadorDeCampanha().atualizeSync(campanha).then( (atualizou: any) => {
    res.json({
      sucesso: true
    });
  }).catch( (erro: any) => {
    res.json({
      sucesso: false,
    });
  });
});

router.get('/:id', async (req, res) => {
  const mapeadorDeCampanha = new MapeadorDeCampanhaRede();
  const id: any = req.params.id;

  const campanha: CampanhaRede = await mapeadorDeCampanha.selecioneSync({id: id});

  res.json({
    sucesso: true,
    data: campanha
  });
});

router.get('/:id/qtdeLidas', async (req, res) => {
  const mapeadorDeCampanha = new MapeadorDeCampanha();
  const id: any = req.params.id;

  // tslint:disable-next-line:radix
  const qtdeLidas = await mapeadorDeCampanha.obtenhaQtdeLidas({id: parseInt(id)});

  res.json(Resposta.sucesso(qtdeLidas))
});

router.get('/:id/mensagens', async (req, res) => {
  const mapeador = new MapeadorDeMensagemEnviada();
  const id: any = req.params.id || -1;
  const inicio = req.query.p || 1;
  const total = req.query.t || 10;

  let query: any = {idCampanha: id, inicio: Number(inicio), total: Number(total)};

  if(req.query.recebeu === 'true')
     query.soQueRecebeu = true;

  console.log(req.query);
  console.log(query);

  let mensagens = await mapeador.listeAsync(query)
  let qtdetotal = await mapeador.selecioneTotal(query)

  res.json(Resposta.sucesso({mensagens: mensagens, total: qtdetotal}));
});

router.get('/statusEnvio/:idc', async(req, res) => {
  const mapeadorDeCampanha = new MapeadorDeCampanha();
  const id: any = req.params.idc;

  console.log(id);
  const campanha: Campanha = await mapeadorDeCampanha.selecioneSync({id: id});

  if( campanha == null ) {
    return res.json(Resposta.erro('Campanha não existe!'));
  }

  client.get('tarefaenvio_' + campanha.id, (err: any, reply: string) => {
    if( err ) {
      return Resposta.erro(err.message);
    }

    console.log(reply);

    const dados = JSON.parse(reply);
    if( dados && dados.contatoAtual ) {
      delete dados.contatoAtual.empresa
    }

    res.json({
      sucesso: true,
      data: dados
    });
  });
});

export const CampanhaRedeController: Router = router;
