import {Router} from "express";
import {IntegracaoUberdirect} from "../domain/integracoes/IntegracaoUberdirect";
import {UberDirectService} from "../service/integracoes/UberDirectService";
import {Resposta} from "../utils/Resposta";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {DeliveryPedido} from "../domain/integracoes/DeliveryPedido";
import {MapeadorDeTaxaDeEntregaCalculada} from "../mapeadores/MapeadorDeTaxaDeEntregaCalculada";
import {TaxaDeEntregaCalculada} from "../domain/delivery/TaxaDeEntregaCalculada";
import {Empresa} from "../domain/Empresa";
import {Pedido} from "../domain/delivery/Pedido";
import {MapeadorDeNotificacaoDelivery} from "../mapeadores/MapeadorDeNotificacaoDelivery";
import {NotificacaoDelivery} from "../domain/integracoes/NotificacaoDelivery";
import {EnumTipoDeCobranca} from "../domain/delivery/EnumTipoDeCobranca";

const router: Router = Router();


router.post('/params', async (req: any, res: any) => {
  let empresa = req.empresa,
    operador =  req.user,
    dados = req.body;

  if(!empresa.numeroWhatsapp)
    return res.json( Resposta.erro('Configure numero whatsapp da loja'))

  let integracao: IntegracaoUberdirect  = Object.assign(new IntegracaoUberdirect(empresa, operador), dados)

  if(empresa.integracaoUberdirect){
    integracao.id  = empresa.integracaoUberdirect.id;
   // if(integracao.token !== empresa.integracaoUberdirect.token)
    integracao.tokenDataExpiracao = null
  }

  let service: UberDirectService = integracao.obtenhaService(req.empresa);

  let token: any = await service.obtenhaToken().catch((erro) => {
      res.json(Resposta.erro(erro))
  })

  if(token){
    console.log(integracao)
    await new MapeadorDeEmpresa().removaDasCaches(empresa);
    await integracao.salve(true);

    res.json(Resposta.sucesso(integracao))
  }

})


router.post('/ative', async (req: any, res: any) => {
  let empresa: Empresa = req.empresa;

  if(empresa.integracaoDeliveryOpendeliveryAtiva())
    return res.json( Resposta.erro('Integração com opendelivery logistica já esta ativa'))

  if(empresa.integracaoUberdirect){
    await empresa.integracaoUberdirect.ative();
    await new MapeadorDeEmpresa().removaDasCaches(empresa);
  }
  res.json(Resposta.sucesso())
})

router.post('/desative', async (req: any, res: any) => {
  let empresa: Empresa = req.empresa;

  if(empresa.integracaoUberdirect){

    if(empresa.cobraPorApi(EnumTipoDeCobranca.POR_APIUBER))
      return res.json(Resposta.erro('Alteração nao permitida, desative forma de entrega "Receber em casa" por API Uber'))

    await empresa.integracaoUberdirect.desative();
    await new MapeadorDeEmpresa().removaDasCaches(empresa);
  }
  res.json(Resposta.sucesso())
})


router.post('/pedidos/:guid/novaEntrega', async (req: any, res: any) => {
  let integracao: IntegracaoUberdirect = req.empresa.integracaoUberdirect;
  let descartar = req.body.descartar;

  if(integracao){
    let pedido = await new MapeadorDePedido().selecioneSync({ guid: req.params.guid});
    let simulacaoId: any;

    if(pedido.taxaEntregaCalculadaId){
      let taxaEntregaCalculada: TaxaDeEntregaCalculada  =
        await new MapeadorDeTaxaDeEntregaCalculada().selecioneSync(pedido.taxaEntregaCalculadaId);

       simulacaoId =  taxaEntregaCalculada.simulacaoId;
    }

    let novaEntrega: DeliveryPedido =
      await integracao.obtenhaService(req.empresa).notifiqueNovaEntrega(pedido, req.empresa, req.user, simulacaoId, descartar);

    if(novaEntrega){
      res.json(Resposta.sucesso(novaEntrega.toDTO()))
    } else {
      res.json(Resposta.erro(pedido.erroExternoDelivery))
    }
  } else {
    res.json(Resposta.erro('Nenhum integração uber ativa'))
  }
})

router.post('/pedidos/:guid/delivery/cancele', async (req: any, res: any) => {
  let integracao: IntegracaoUberdirect = req.empresa.integracaoUberdirect;

  if(integracao){
    let pedido: Pedido = await new MapeadorDePedido().selecioneSync({ guid: req.params.guid});

    let delivery: any =  await integracao.obtenhaService(req.empresa).canceleEntrega(pedido.deliveryPedido.deliveryId).catch((erro) => {
      res.json({erro : erro})
    })

    if(delivery){
      if(delivery.status !== pedido.deliveryPedido.status)
        await  pedido.deliveryPedido.atualizeRetorno(delivery.status, delivery, 'Entregador cancelado pela loja')

      res.json(Resposta.sucesso(pedido.deliveryPedido.toDTO()))
    }
  } else {
    res.json(Resposta.erro('Nenhum integração ativa'))
  }
})

router.get('/pedidos/:guid/delivery', async (req: any, res: any) => {
  let integracao: IntegracaoUberdirect = req.empresa.integracaoUberdirect;

  let pedido = await new MapeadorDePedido().selecioneSync({ guid: req.params.guid});

  if(integracao && pedido && pedido.deliveryPedido){

    let delivery: any =  await integracao.obtenhaService(req.empresa).obtenhaDelivery(pedido.deliveryPedido.deliveryId)
      .catch((erro) => {
      res.json({erro : erro})
    })

    if(delivery)
     res.json(delivery)

  } else {
    res.json({})
  }
})
router.get('/delivery/:id', async (req: any, res: any) => {
  let integracao: IntegracaoUberdirect = req.empresa.integracaoUberdirect;

  if(integracao){
    let delivery: any =  await integracao.obtenhaService(req.empresa).obtenhaDelivery(req.params.id).catch((erro) => {
      res.json({erro : erro})
    })

    if(delivery)
      res.json(delivery)

  } else {
    res.json({})
  }
})




router.get('/pedidos/:guid/delivery/sincronize', async (req: any, res: any) => {
  let integracao: IntegracaoUberdirect = req.empresa.integracaoUberdirect;

  if(integracao){
    let pedido = await new MapeadorDePedido().selecioneSync({ guid: req.params.guid});

    if(!pedido) return    res.json(Resposta.erro('Pedido inválido'))
    if(!integracao) return    res.json(Resposta.erro('integracao nao configurada'))

    let delivery: any =  await integracao.obtenhaService(req.empresa).obtenhaDelivery(pedido.deliveryPedido.deliveryId).catch((erro) => {
      res.json(Resposta.erro(erro))
    })

    if(delivery ){
      if(delivery.status !== pedido.deliveryPedido.status)
        await  pedido.deliveryPedido.atualizeRetorno(delivery.status, delivery)

      res.json(Resposta.sucesso(pedido.deliveryPedido.toDTO()))
    }
  } else {
    res.json(Resposta.erro('Nenhuma integração ativa'));
  }
})

router.get('/pedidos/:id/notificacoes', async (req: any, res: any) => {
  let eventos = await new MapeadorDeNotificacaoDelivery().listeAsync({ idPedido: req.params.id, origem: 'uber'})

  res.json(Resposta.sucesso(eventos.map((item: NotificacaoDelivery) => item.toDTO())))
})


export const UberDirectController: Router = router;
