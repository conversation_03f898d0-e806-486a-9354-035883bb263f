import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {MapeadorDeMesa} from "../mapeadores/MapeadorDeMesa";
import {MapeadorDeComanda} from "../mapeadores/MapeadorDeComanda";
import {Mesa} from "../domain/Mesa";
import {ComandaService} from "../service/ComandaService";
import {MapeadorDeNotificacaoMesa} from "../mapeadores/MapeadorDeNotificacaoMesa";
import {RotaGuard} from "../lib/permissao/RotaGuard";
import {PedidoService} from "../service/PedidoService";
import {Pedido} from "../domain/delivery/Pedido";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {MapeadorDeAdicionalDeProduto} from "../mapeadores/MapeadorDeAdicionalDeProduto";
import {MapeadorDeCartaoCliente} from "../mapeadores/MapeadorDeCartaoCliente";
import {Comanda} from "../domain/comandas/Comanda";

const router: Router = Router();

router.get('/mesa', async (req, res) => {
  const idMesa = req.query.idm;

  const mapeadorDeMesa = new MapeadorDeMesa();

  mapeadorDeMesa.selecioneSync({
    id: idMesa
  }).then( (mesa) => {
    res.json(Resposta.sucesso(mesa));
  });
});

router.get('/liste', async (req: any, res: any) => {
  const query = {
    filtroComandas: true,
    comandaAberta: true
  };

  let comandas = await new MapeadorDeComanda().selecioneComandas(query);

  if(req.empresa.integradoComEcleticaDWMS()){
    for(let i = 0; i < comandas.length; i++){
      let comanda: any = comandas[i];
      const filtro: any  = { idComanda: comanda.id, comErro: true};
      console.log(filtro)
      comanda.errosNotificacao = await new MapeadorDeNotificacaoMesa().listeAsync(filtro);
    }
  }

  res.json(Resposta.sucesso(comandas));
});

router.get('/obtenha', async (req, res) => {
  const idComanda = req.query.idc;
  if (!idComanda) {
    return res.json(Resposta.erro("Parâmetro idc não informado"));
  }
  const mapeadorDeComanda = new MapeadorDeComanda();
  mapeadorDeComanda.obtenhaComanda({ id: idComanda })
    .then((comanda) => {
      res.json(Resposta.sucesso(comanda));
    })
    .catch((erro) => {
      res.json(Resposta.erro("Erro ao buscar a comanda: " + erro));
    });
});

router.post('/trocar-mesa', async(req, res) => {
  const idComanda = req.body.idc;
  const idNovaMesa = req.body.idnm;

  const mapeadorDeComanda = new MapeadorDeComanda();
  const mapeadorDeMesa = new MapeadorDeMesa();


  mapeadorDeComanda.obtenhaComanda({
    id: idComanda
  }).then( (comanda) => {
    if( !comanda ) {
      return res.json(Resposta.erro('Comanda não encontrada!'));
    }

    mapeadorDeMesa.selecioneSync({id: idNovaMesa}).then( (mesa: Mesa) => {
      if( !mesa ) {
        return res.json(Resposta.erro('Mesa não encontrada!'));
      }

      mapeadorDeComanda.troqueMesa(comanda, mesa).then( () => {
        return res.json(Resposta.sucesso(true));
      })
    });
  });
});


router.post('/:id/reabra', async (req: any, res: any) => {

  let comanda  = await new MapeadorDeComanda().selecioneSync( req.params.id);

  if(!comanda) return res.json(Resposta.erro("Comanda não encontrada: " + req.params.id))

  new ComandaService().reabraComanda(comanda, req.empresa, req.user).then( ( ) => {

    res.json(Resposta.sucesso({}));
  }).catch((erro) => {
    res.json(Resposta.erro(erro))
  })
});

router.post('/:id/marquelida', async (req: any, res: any) => {
  await new MapeadorDeComanda().atualizeComoLida({id: Number(req.params.id)});

  res.json(Resposta.sucesso({}));
});



router.post('/:id/desconto', async (req: any, res: any) => {
  let desconto = req.body.desconto;

  if(desconto != null){
    let comanda  = await new MapeadorDeComanda().selecioneSync( req.params.id);

    comanda.desconto = desconto
    await new MapeadorDeComanda().atualizeValorDesconto(comanda)
    await new MapeadorDeComanda().atualizeValorComanda(comanda)

    res.json(Resposta.sucesso({}));
  } else {
    res.json(Resposta.erro("Parametros inválidos"))
  }

});



router.put('/:id/:guid/item',  RotaGuard.editarPedido, async (req: any, res: any) => {
  let dados = req.body;
  let empresa = req.empresa;

  dados.empresa = req.empresa;
  dados.operador = req.user;


  let pedidoService = new PedidoService();

  let pedidoAntigo: Pedido = await new MapeadorDePedido().selecioneSync({guid: req.params.guid});

  if(!pedidoAntigo.deMesa())
    return res.json(Resposta.erro(String(`Operação não permitida nesse pedido`)))

  if(pedidoAntigo.foiCanceladoOuDevolvido())
    return res.json(Resposta.erro(String(`Pedido #${pedidoAntigo.codigo} está cancelado", alteração não permitida.`)))

  if(pedidoAntigo.multipedido)
    return res.json(Resposta.erroMultipedido)

  if(pedidoAntigo.comanda.id !== Number(req.params.id))
    return res.json(Resposta.erro(String(`Pedidão de outra comanda: ` + pedidoAntigo.comanda.id)))


  let mapeadorDeAtributo = new MapeadorDeAdicionalDeProduto()
  let adicionaisPedido = await mapeadorDeAtributo.listeAsync({ catalogo: req.empresa.catalogo, objeto: req.empresa.catalogo, entidade: 'pedido'})

  dados.definicaoAdicionais = adicionaisPedido

  let resposta = { id: pedidoAntigo.id, codigo: pedidoAntigo.codigo, guid: pedidoAntigo.guid  };

  if(empresa.integracaoPDVParceiroAtiva() && pedidoAntigo.referenciaExterna){
    return res.json(Resposta.erro(String(`Operação não permitida, pedido está integrado com um parceiro`)))
  }

  pedidoAntigo.empresa = empresa;
  pedidoService.atualizeItemPedido(pedidoAntigo, dados, req.user).then( async () => {
    res.json(Resposta.sucesso(resposta));
  }).catch((e: any) => {
    console.error(e)
    res.json(Resposta.erro(e.message ? e.message : e))
  })
});


router.post('/mesa/:codigomesa/cartaocliente', async (req: any, res: any) => {
    const codigoCartao: any = req.body.codigo;
    const mesa: any = req.body.mesa;
    if(!codigoCartao || !mesa) {
      console.log('dados: ', req.body)
      return   res.json(Resposta.erro('Dados inválidos'))
    }

    let cartaoCliente = await new MapeadorDeCartaoCliente().selecioneSync({codigo: codigoCartao})

    if(!cartaoCliente) return res.json(Resposta.erro(`Cartão "${codigoCartao}" inexistente`))

    let comanda: any  = await new ComandaService().crieComandaParaCartao(mesa, cartaoCliente, req.empresa).catch((err: any) => {
      console.error(err)
      res.json(Resposta.erro(err))
    });

    if(comanda)
      res.json(Resposta.sucesso({id: comanda.id}));
})


export const ComandaController: Router = router;
