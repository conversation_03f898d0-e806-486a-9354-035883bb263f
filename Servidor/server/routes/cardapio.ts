import {Router} from "express";
import {Cardapio} from "../domain/delivery/Cardapio";
import {MapeadorDeCardapio} from "../mapeadores/MapeadorDeCardapio";
import {Resposta} from "../utils/Resposta";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {RotaGuard} from "../lib/permissao/RotaGuard";

const router: Router = Router();

function insiraCardapioOuAtualize(cardapio: any) {
  return new Promise<void>( (resolve, reject) => {
    const mapeadorDeCardapio = new MapeadorDeCardapio();
    if( cardapio.id ) {
      mapeadorDeCardapio.atualizeSync(cardapio).then( () => {
        resolve();
      }).catch( (erro: any) => {
        reject(erro);
      });

      return;
    }

    mapeadorDeCardapio.insiraSync(cardapio).then( () => {
      resolve();
    }).catch( (erro: any) => {
      reject(erro);
    });
  });
}

router.post('/atualize', RotaGuard.alterarLoja, async (req: any, res) => {
  const empresa = req.empresa;
  let dadosCardapio = req.body;

  if( !empresa.cardapio )
    empresa.cardapio = new Cardapio(empresa);

  Object.assign(empresa.cardapio, dadosCardapio);

  insiraCardapioOuAtualize(    empresa.cardapio ).then( () => {
    new MapeadorDeEmpresa().removaDasCaches(empresa);

    delete     empresa.cardapio.empresa;
    res.json(Resposta.sucesso(    empresa.cardapio ));
  }).catch( (erro: any) => {
    res.json(Resposta.erro(erro));
  });
});

router.get('/montarCardapio', async (req: any, res) => {
});

export const CardapioController: Router = router;
