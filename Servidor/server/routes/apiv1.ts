import {Router} from "express";
import {Pedido} from "../domain/delivery/Pedido";
import {Resposta} from "../utils/Resposta";
import {DTOPedidoAPI} from "../lib/dto/DTOPedidoAPI";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import * as _ from "underscore";
import moment = require("moment");
import {Contato} from "../domain/Contato";
import {ContatoService} from "../service/ContatoService";
import {PedidoService} from "../service/PedidoService";
import {StatusPedidoApi} from "../lib/emun/EnumStatusPedido";
import {MapeadorDePlano} from "../mapeadores/MapeadorDePlano";
import {CartaoService} from "../service/CartaoService";
import {Cartao} from "../domain/Cartao";
import {MapeadorDeContato} from "../mapeadores/MapeadorDeContato";
import {MapeadorDeProduto} from "../mapeadores/MapeadorDeProduto";
import {DisponibilidadeProdutApiLabel} from "../lib/emun/EnumDisponibilidadeProduto";
import {Produto} from "../domain/Produto";
import {RegistroDeOperacaoService} from "../service/RegistroDeOperacaoService";
import {Ambiente} from "../service/Ambiente";
import {UploadUtils} from "../utils/UploadUtils";
import {ImagemEan} from "../domain/ImagemEan";
import {MapeadorDeImagemCodigoDeBarras} from "../mapeadores/MapeadorDeImagemCodigoDeBarras";
import ErrnoException = NodeJS.ErrnoException;
import {ExecutorAsync} from "../utils/ExecutorAsync";
let path = require('path');
let fs = require('fs');
const uuidv1 = require('uuid/v1');
const router: Router = Router();
import * as async from "async";
import {Empresa} from "../domain/Empresa";
import {IntegracaoFoodyDelivery} from "../domain/integracoes/IntegracaoFoodyDelivery";


router.get('/teste', (req, res) => {
  res.send("Token válido utilizado com sucesso.")
})

router.get('/pedido/:codigo', (req: any, res: any) => {
  Pedido.get({codigo: req.params.codigo }).then((pedido: Pedido) => {
    if(pedido){
      res.json(Resposta.sucesso(new DTOPedidoAPI(pedido, req.empresa.integracaoPedidoFidelidade)))
    } else {
      res.json(Resposta.sucesso({}))
    }
  }).catch((reason: any) => {
    console.error(reason)
    res.json(Resposta.erro(reason.message || reason))
  });

})

router.get('/liste/ultimos/:codigo', (req: any, res: any) => {
  //let formatHU = 'YYYYMMDDHHmmss';
 // console.log('buscar de pedidos: ')
  let dados: any = req.query;
  let query: any = { inicio: dados.i || 0, total: dados.t || 10, orderBy: true };

  query.codigoUltimo = req.params.codigo;


  query.inicio = Number(query.inicio)
  query.total = Number(query.total)

  //console.log(query)

  if(dados.st)
    query.status = StatusPedidoApi.get(dados.st)

  new MapeadorDePedido().listeAsync(query).then((pedidos: Pedido[]) => {
    let resposta: any = { pedidos: pedidos.map( (pedido: any) => new DTOPedidoAPI(pedido, req.empresa.integracaoPedidoFidelidade))}
    let ultimoPedido: Pedido = _.sortBy( pedidos, (pedido: any) => -pedido.id)[0];

    resposta.codigoUltimo =  ultimoPedido ? ultimoPedido.codigo : query.codigoUltimo

    res.json(Resposta.sucesso(resposta))

  });
})

router.put('/pedidos/status', async (req: any, res: any) => {
    let codigo = req.body.codigo,
        status = req.body.status,
        pago = req.body.pago;

    if(!codigo || !status)
      return res.json(Resposta.erro('Parametros inválidos'))

    let pedido: any = await new MapeadorDePedido().selecioneSync({ codigo: codigo})

    if(!pedido)
      return res.json(Resposta.erro('Pedido não econtrado'))

    let novoStatus = StatusPedidoApi.get(status);

    if(novoStatus >= 0 ){
      let client = req.user;

      let marcarPago: boolean = !!pago

      let erroAlterar: any =
        await new PedidoService().altereStatus(pedido, req.empresa, novoStatus, true, marcarPago, null, client);

      if(!erroAlterar){
        await IntegracaoFoodyDelivery.notifiqueSistemaDelivery(pedido, req.empresa, null);

        let reposta: any  = {status: status};

        if(pedido.deliveryPedido)
          reposta.delivery =  pedido.deliveryPedido.toDTO()

        res.json(Resposta.sucesso(reposta));
      } else {
        res.json(Resposta.erro(erroAlterar))
      }

    } else {
      res.json(Resposta.erro('Status não esperado: ' + status))
    }
})

router.put('/produtos/pdv/:codigoPdv/disponibilidade', async (req: any, res: any) => {
  let disponibilidade = req.body.disponibilidade;
  const empresa: Empresa = req.empresa
  let mapeador = new MapeadorDeProduto(empresa.catalogo);
  let codigoPdv = Number(req.params.codigoPdv);

  if(!disponibilidade || !codigoPdv)
    return res.json(Resposta.erro('Parametros inválidos'))

  let produto: Produto =  await new MapeadorDeProduto(empresa.catalogo).selecioneSync({codigoPdv: codigoPdv});

  if(!produto)
    return res.json(Resposta.erro('Codigo produto não encontrado (disponibilidade): ' + codigoPdv));

  let disponibildadeProduto = DisponibilidadeProdutApiLabel.get(disponibilidade)

  if(disponibildadeProduto != null){
    if( produto.disponibilidade === disponibildadeProduto)
      return res.json(Resposta.sucesso({ diponibilidade: produto.disponibilidade, id: produto.id, nome: produto.nome}))

    mapeador.transacao(  async (conexao: any, commit: any) => {
      produto.disponibilidade = disponibildadeProduto;
      await  mapeador.atualizeDisponibilidade(produto);

      let registroDeOperacao = new RegistroDeOperacaoService(null, Ambiente.Instance.ip(), req.user)

      await registroDeOperacao.alterouDisponibilidadeDoProduto(produto)

      commit( () => {
        res.json(Resposta.sucesso({ diponibilidade: produto.disponibilidade, id: produto.id, nome: produto.nome}));
      })
    })
  } else {
    return res.json(Resposta.erro('Valor não aceito: ' + disponibilidade + '. Informe "sempre_disponivel" ou "nao_disponivel"'))
  }
})

router.put('/produtos/:codigoPdv/preco', async(req: any, res: any) => {
  let preco = req.body.preco;
  const empresa: Empresa = req.empresa
  let mapeador = new MapeadorDeProduto(empresa.catalogo);
  let codigoPdv = Number(req.params.codigoPdv);

  if(!preco || !codigoPdv)
    return res.json(Resposta.erro('Parametros inválidos'))

  let produto: Produto =  await new MapeadorDeProduto(empresa.catalogo).selecioneSync({codigoPdv: codigoPdv});

  if(!produto)
    return res.json(Resposta.erro('Codigo produto não encontrado (preco): ' + codigoPdv));

  if( produto.preco === preco)
    return res.json(Resposta.sucesso({ preco: produto.preco, id: produto.id, nome: produto.nome}))

  mapeador.transacao(  async (conexao: any, commit: any) => {
    produto.preco = preco;
    await  mapeador.atualizePreco(produto);

    let registroDeOperacao = new RegistroDeOperacaoService(null, Ambiente.Instance.ip(), req.user)

    await registroDeOperacao.alterouPrecoProduto(produto)

    commit( () => {
      res.json(Resposta.sucesso({ preco: produto.preco, id: produto.id, nome: produto.nome}));
    })
  })
})



router.put('/produtos/:id/disponibilidade', async (req: any, res: any) => {
  let disponibilidade = req.body.disponibilidade;
  const empresa: Empresa = req.empresa
  let mapeador = new MapeadorDeProduto(empresa.catalogo);
  let idProduto = Number(req.params.id);

  if(!disponibilidade || Number.isNaN(idProduto))
    return res.json(Resposta.erro('Parametros inválidos'))

  let produto: Produto =  await new MapeadorDeProduto(empresa.catalogo).selecioneSync(idProduto);

  if(!produto)
    return res.json(Resposta.erro('Id produto não encontrado (disponibilidade):' +  idProduto));

  let disponibildadeProduto = DisponibilidadeProdutApiLabel.get(disponibilidade)

  if(disponibildadeProduto != null){
    if( produto.disponibilidade === disponibildadeProduto)
      return res.json(Resposta.erro('Produto já está como ' + disponibilidade))

    mapeador.transacao(  async (conexao: any, commit: any) => {
      produto.disponibilidade = disponibildadeProduto;
      await  mapeador.atualizeDisponibilidade(produto);

      let registroDeOperacao = new RegistroDeOperacaoService(null, Ambiente.Instance.ip(), req.user)

      await registroDeOperacao.alterouDisponibilidadeDoProduto(produto)

      commit( () => {
        res.json(Resposta.sucesso({ diponibilidade: produto.disponibilidade, id: produto.id, nome: produto.nome}));
      })
    })
  } else {
    return res.json(Resposta.erro('Valor não aceito: ' + disponibilidade))
  }
})

router.post('/contatos', async (req: any, res: any) => {
  let dados = req.body;

  if(dados.dataNascimento) {
    if (/^\d\d\/\d\d\/\d\d\d\d$/.test(dados.dataNascimento.toString())) {
      dados.dataNascimento =  moment(dados.dataNascimento, 'DD/MM/YYYY').toDate()
    } else {
      return res.json(Resposta.erro('Data nascimento deve estar no formato DD/MM/AAAAA'  ))
    }
  }

  let contato = new Contato(null, dados.nome, dados.telefone, dados.sexo, dados.dataNascimento,
                                     dados.cpf, dados.email);
  new ContatoService().salve(contato).then( () => {
    res.json(Resposta.sucesso(contato.obtenhaDTOContatoAPI()))
  }).catch(erro => {
    res.json(Resposta.erro(erro))
  })

});

router.post('/cartoes', async (req: any, res: any) => {
  let dados = req.body;

  let idContato = dados.idContato,
      idPLano = dados.idPlano;

  if(!idContato || !idPLano)
    return res.json(Resposta.erro('Parametros inválidos'))

  let contato = await new MapeadorDeContato().selecioneSync(Number(idContato));

  if(!contato)
    return res.json(Resposta.erro('Contato inválido'))

  let plano = await new MapeadorDePlano().selecioneSync(Number(idPLano));

  if(!plano)
    return res.json(Resposta.erro('Plano inválido'))

  let cartao = new Cartao(null, contato, plano, 0);

  cartao.empresa = req.empresa;

  try{
    await new CartaoService().salve(cartao)

    res.json(Resposta.sucesso({id: cartao.id, pontos: cartao.pontos}));
  }catch (error){
    res.json(Resposta.erro(error.message ? error.message : error))
  }


});

router.get('/planos', async (req: any, res: any) => {
    let empresa = req.empresa;

    let planos = await new MapeadorDePlano().listeAsync({idEmpresa: empresa.id});

    res.json(Resposta.sucesso(planos.map( (plano: any) => plano.toDTOApi())))
});

router.post('/imagens/ean/importe', async (req, res) => {
  //requiring path and fs modules
//joining path of directory
  const directoryPath = req.body.caminho;
  //const eans: any[] = []
  const importados: any = {}
//passsing directoryPath and callback function

  ExecutorAsync.execute( async (callback: Function) => {
    fs.readdir(directoryPath,  (erro: ErrnoException, files: string[]) => {
      //handling error
      if (erro) {
        return console.log('Unable to scan directory: ' + erro);


      }

      let todosErros: any[] = []
      //listing all files using forEach
      async.eachSeries(files, (file: string, cb) => {
        let ean: any = file.split(".")[0]
        let extensao = file.split(".")[1]


        if(!extensao)
        {
          console.log("Não tem extensão para o ean " + ean)
          todosErros.push("Não tem extensão para o ean " + ean)
          return cb()
        }

        if(importados[ean])  {
          todosErros.push("Ean " + ean + " já foi importado anteriormente.")
          console.log("Ean " + ean + " já foi importado anteriormente.")
          return cb();
        }

        let caminhoOriginal = path.join(directoryPath, file)
        let diretorio = Ambiente.Instance.config.caminhoImagens,
          nomeArquivo = String(`${uuidv1()}.${extensao}`),
          arquivo: string = path.join(diretorio, 'produtos', nomeArquivo);

        fs.readFile(caminhoOriginal, (erro2: ErrnoException, conteudoArquivo: Buffer)  => {
          if(erro2) {
            todosErros.push(erro2)
            return cb()
          }


          UploadUtils.redimensioneImagemLocal(caminhoOriginal, conteudoArquivo, 800, arquivo, (err: any) => {
            if(!err){
              let sizeInBytes = fs.statSync(arquivo).size;
              let sizeInMB = (sizeInBytes / (1024 * 1024));
              console.log(String(`'tamanho máximo arquivo: ${sizeInMB}MB`))
              if(!Ambiente.Instance.producao && arquivo.indexOf('/server/') >= 0){
                // destination.txt will be created or overwritten by default.
                fs.copyFile(arquivo, arquivo.replace('/server/', '/distServer/'), ( errCp: any ) => {
                  if (errCp)  console.log(errCp)
                });
              }

              let imagem = new ImagemEan(nomeArquivo, ean)

              let mapeador = new MapeadorDeImagemCodigoDeBarras()

              mapeador.insiraGraph(imagem).then(() => {
                importados[ean] = true
                cb();

              }).catch((reason: any) => {
                console.log("Ocorreu um erro ao importar imagens: " + reason)
                todosErros.push(err)
                cb()
              })

            } else {
              todosErros.push(err)
              cb()
            }
          })
        })
      }, (erroSeries: any) => {
        if(erroSeries)
          return console.log("Ocorreu um erro ao importar imagens: " + erroSeries)
        console.log("*** importação finalizada ***")
        console.log("erros:")
        console.log(todosErros)
        callback();
      })
    });
  }, (erro2: any) => {
    console.log("Ocorreu um erro ao importar: " + erro2)
  }, 100)


  res.json({
    sucesso: true,
    mensagem: "Processo de importação iniciado com sucesso."
  })
})

router.post('/produto/imagem/ean/:ean', (req: any, res: any) => {
  let ean = req.params.ean
  if(!req.files)
    return res.json(Resposta.erro("Não foi localizado nenhum arquivo na chamada."))

  let maxsize = Number(req.params.maxsize)

  const file: any = req.files.file,
    extensao: any = file.mimetype.split('/')[1];

  let diretorio = Ambiente.Instance.config.caminhoImagens,
    nomeArquivo = String(`${uuidv1()}.${extensao}`),
    arquivo: string = path.join(diretorio, 'produtos', nomeArquivo);

  if(!fs.existsSync(diretorio)) {
    console.log(diretorio)
    return res.json(Resposta.erro ("Diretório upload de imagens não existe"));
  }

  console.log(arquivo)
  console.log('mimetype: ' + file.mimetype)

  UploadUtils.redimensioneImagem(file, maxsize, arquivo, (err: any) => {
    if(!err){
      let sizeInBytes = fs.statSync(arquivo).size;
      let sizeInMB = (sizeInBytes / (1024 * 1024));
      console.log(String(`'tamanho máximo upload: ${sizeInMB}MB`))
      if(!Ambiente.Instance.producao && arquivo.indexOf('/server/') >= 0){
        // destination.txt will be created or overwritten by default.
        fs.copyFile(arquivo, arquivo.replace('/server/', '/distServer/'), ( errCp: any ) => {
          if (errCp)  console.log(errCp)
        });
      }

      let imagem = new ImagemEan(nomeArquivo, ean)

      let mapeador = new MapeadorDeImagemCodigoDeBarras()

      mapeador.insiraGraph(imagem).then(() => {
        res.json(Resposta.sucesso({file: nomeArquivo, extensao: extensao, ean: ean  }))

      }).catch((reason: any) => {
        res.json(Resposta.erro(reason))
      })

    } else {
      res.json(Resposta.erro(err))
    }
  })


})

export const ApiV1Controller: Router = router;
