import {Router} from "express";
import {IuguService} from "../service/IuguService";
import {Resposta} from "../utils/Resposta";
import {PlanoEmpresarial} from "../domain/faturamento/PlanoEmpresarial";
import {ContratoService} from "../service/ContratoService";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import * as _ from 'underscore';
import {MapeadorDeFatura} from "../mapeadores/MapeadorDeFatura";
import {DTORecebimento, DTOResumoRecebimentos} from "../lib/dto/DTOResumoRecebimentos";
import moment = require("moment");
import {MapeadorDeAssinatura} from "../mapeadores/MapeadorDeAssinatura";
import {MapeadorDeContato} from "../mapeadores/MapeadorDeContato";
import {MapeadorDeContrato} from "../mapeadores/MapeadorDeContrato";

const router: Router = Router();

router.get('/gatilhos/crie', async (req, res) => {
  let gatilhos = [
    "invoice.created",
    "invoice.status_changed",
    "invoice.refund",
    "invoice.payment_failed",
    "invoice.dunning_action",
    "invoice.due",
    "invoice.installment_released",
    "invoice.released",
    "subscription.suspended",
    "subscription.activated",
    "subscription.created",
    "subscription.renewed",
    "subscription.expired",
    "subscription.changed",
    "referrals.verification",
    "referrals.bank_verification",
    "withdraw_request.created",
    "withdraw_request.status_changed"
  ];

  res.json(Resposta.sucesso(gatilhos))

})

router.get('/planos/atualizeparcelasmaxima', async (req, res) => {

  let  iuguService = new IuguService();
  let  planosAtualizados: any = [], jaProcessados = [], erros: any = []

  let  planosIugu: any = await iuguService.listePlanos(0, 100);

  for( let planoIugu of planosIugu ) {
    if(planoIugu.invoice_max_installments == null){
      planoIugu.invoice_max_installments = planoIugu.interval || 1;

      await iuguService.atualizeParcelaMaximaPlano(planoIugu)

      planosAtualizados.push(planoIugu)
    } else {
      jaProcessados.push(planoIugu)
    }
  }

  res.json({ erros: erros, atualizados: planosAtualizados, jaProcessados: jaProcessados})

})

router.get('/planos/crie', async (req, res) => {
  let  planos: Array<PlanoEmpresarial> = await PlanoEmpresarial.liste();
  let  iuguService = new IuguService();
  let  planosCriados: any = [], erros = [], existentes = [];

  for( let plano of planos ){
    let planoIugu: any =  plano.obtenhaPlanoIugu( iuguService.obtenhaToken() );

    if( !planoIugu ) {
      let erro =  await iuguService.criePlano(plano);

      if( !erro ){
        planosCriados.push(plano);
      } else {
        erros.push(erro);
      }
    } else {
      existentes.push(planoIugu)
    }
  }

  res.json(Resposta.sucesso({ novos: planosCriados, erros: erros, existentes: existentes } ));
})

router.get('/assinaturas', async (req, res ) => {
  let assinaturas =  await new IuguService().listeAssinaturas(0, 100);

  res.json(assinaturas)

})

router.get('/assinaturas/:codigo', async (req, res ) => {
  let assinaturas =  await new IuguService().obtenhaAssinatura(req.params.codigo);

  res.json(assinaturas)
})


router.get('/faturas/:codigo', async (req, res ) => {
  let faturas =  await new IuguService().obtenhaFatura(req.params.codigo);

  res.json(faturas)
})

router.get('/planos', async (req, res ) => {
  let assinaturas =  await new IuguService().listePlanos(0, 100 );

  res.json(assinaturas)
})


router.get('/empresas/:codigo', async (req, res ) => {
  let cliente =  await new IuguService().obtenhaCliente( req.params.codigo );

  res.json(cliente)
})


router.get('/empresas/:codigo/removacartao/:codigocartao', async (req, res ) => {
  let resp =  await new IuguService().removaFormaDePagamento( { codigoCliente: req.params.codigo}, {codigo: req.params.codigocartao} );

  res.json(resp)
})


router.get('/faturas/executecobranca/cartao', async (req, res) => {
  await new ContratoService().executeCobrancasCartao();

  res.json(Resposta.sucesso())
})

router.get('/assinaturas/corrigir/liste', async (req, res) => {
  let temAssinaturas = true, assinaturasComAdesao: any = [], assinaturasExpiradas: any = [];
  let assinaturasAtivas = await new MapeadorDeAssinatura().listeAsync({ ativas: true})

  for(let i = 0;  temAssinaturas;  i = i + 100){
    let assinaturas: any =  await new IuguService().listeAssinaturas( i, 100, true);
    console.log('total assinaturas: ' + i);
    assinaturas.forEach((assinatura: any) => {
      let assinaturaAtiva = assinaturasAtivas.find((a: any) => a.codigo === assinatura.id);

      if(!assinaturaAtiva) return;

      let expirou = moment(assinaturaAtiva.dataUltimoPagamento).isAfter(assinatura.expires_at);
      let jaPagou = assinaturaAtiva.dataPrimeiroPagamento !=  null;

      if(expirou &&  jaPagou)
        assinaturasExpiradas.push(assinatura.id);

      if(assinatura.subitems && assinatura.subitems.length){
        let temAdesao = assinatura.subitems.find((item: any) => item.description.toLowerCase().indexOf('adesão') >= 0) != null;
        if(temAdesao && jaPagou)
          assinaturasComAdesao.push(assinatura.id);
      }
    })

    temAssinaturas = assinaturas.length;

  }

  res.json({assinaturasExpiradas: assinaturasExpiradas, adesaoVerificar: assinaturasComAdesao});
})





// essas duas estavam com cartao salvo
//24D791517EFB4F5B8F458178207C07A5 -> China In Box Cambuí
//******************************** -> China In Box Santos
router.get('/cartaosalvo/empresas', async (req, res) => {
  let clientesComPagamentoPadrao: any = []
  let temCliente = true;

  for(let i = 0;  temCliente;  i = i + 100){
    let clientes: any =  await new IuguService().listeClientes( i, 100);

    if(clientes ){
      console.log('total clientes: ' + clientes.length);
      clientes.forEach( (cliente: any) => {
        if(cliente.default_payment_method_id  != null){
          clientesComPagamentoPadrao.push(cliente)
        }
      })
      temCliente = clientes.length;
    } else {
      temCliente = false;
    }
  }

  console.log('total clientes comp pagamento padrao: ' + clientesComPagamentoPadrao.length);
  let contratos = await new MapeadorDeContrato().listeQuePagaParcelado();
  console.log('total contratos parcelados: ' + contratos.length);

  let resposta: any = { total: 0,
    anual: {total: 0, lista : []},
    semestral: {total: 0, lista : []},
    trimestral: {total: 0, lista : []}}

  clientesComPagamentoPadrao.forEach((cliente: any) => {
    let contrato: any =  contratos.find((c: any) => c.empresa.codigoCliente === cliente.id);

    if(contrato){
      resposta.total++;
      let intervalo = contrato.plano.intervalo;

      switch (intervalo){
        case 12:
          resposta.anual.lista.push(cliente);
          break
        case 6:
          resposta.semestral.lista.push(cliente);
          break
        case 3:
          resposta.trimestral.lista.push(cliente);
          break
      }
    }
  })

  resposta.anual.total = resposta.anual.lista.length;
  resposta.semestral.total = resposta.semestral.lista.length;
  resposta.trimestral.total = resposta.trimestral.lista.length;

  res.json(resposta);
})

router.get('/transacoes/:ano/:mes', async(req: any, res: any) => {
  let transacoes: any = await new IuguService().listeTransacoes(req.params.ano, req.params.mes);

  res.json(transacoes)
})

router.get('/recebimentos/:ano/:mes', async(req: any, res: any) => {
  let transacoes: any = await new IuguService().listeTodasTransacoes(req.params.ano, req.params.mes);

  let transacoesCreditos = transacoes.filter(
    (recebimento: any) => recebimento.type === 'credit' &&  recebimento.reference_type === "Invoice" );

  let transacoesTarifas = transacoes.filter( (recebimento: any) => recebimento.type === 'debit');

  let faturas = await new MapeadorDeFatura().listeAsync({codigos: transacoesCreditos.map(  (t: any) => t.reference)})

  let tipos = transacoes.map( (recebimento: any) => recebimento.transaction_type)

  tipos = _.uniq(tipos);

  let recebimentos: any = { qtde: transacoesCreditos.length, total: 0, totalTarifas: 0, totalBruto: 0, totalAdesao: 0,
    tipos: tipos, trendfoods: [] , ecletica: [], mensalidades: [], outros: [] };


  transacoesCreditos.forEach(  (transacao: any) => {
    let fatura = faturas.find( (f: any) => f.codigo === transacao.reference);
    let transacaoTarifa =  transacoesTarifas.find( (t: any) => t.reference === transacao.reference);
    let dtoRecebimentoTarifa = new DTORecebimento(transacaoTarifa);

    let dtoRecebimento = new DTORecebimento(transacao, fatura);

    dtoRecebimento.calculeTarifas(dtoRecebimentoTarifa);

    recebimentos.totalAdesao += dtoRecebimento.valorAdesao;
    recebimentos.totalMensalidade += dtoRecebimento.valorMensalidade;
    recebimentos.totalBruto += dtoRecebimento.valorBruto;
    recebimentos.totalTarifas += dtoRecebimento.valorTarifas;
    recebimentos.total += dtoRecebimento.total;

    if(fatura){
      if(fatura.empresa.integracaoDelivery && fatura.empresa.integracaoDelivery.sistema === 'ecletica'){
        if(fatura.empresa.rede === 'chinainbox' || fatura.empresa.dominio.indexOf("kohala") >= 0
        || fatura.empresa.dominio.indexOf("gokei") >= 0 || fatura.empresa.dominio.indexOf("owan") >= 0){
          recebimentos.trendfoods.push(dtoRecebimento)
        } else {
          recebimentos.ecletica.push(dtoRecebimento)
        }
      } else {
        recebimentos.mensalidades.push(dtoRecebimento)
      }
    } else {
      recebimentos.outros.push(dtoRecebimento)
    }
  })

  recebimentos.trendfoods = new DTOResumoRecebimentos(  recebimentos.trendfoods);
  recebimentos.ecletica = new DTOResumoRecebimentos(  recebimentos.ecletica);
  recebimentos.mensalidades = new DTOResumoRecebimentos(  recebimentos.mensalidades);
  recebimentos.outros = new DTOResumoRecebimentos(recebimentos.outros);


  res.json(Resposta.sucesso(recebimentos))
})


export const IuguController: Router = router;
