import {Router} from "express";

const sharp = require('sharp');
import * as fs from 'fs';
import * as path from 'path';
import {Ambiente} from "../service/Ambiente";

const router: Router = Router();

router.get('/l/:nome/:largura', async (req, res) => {
  const { nome, largura } = req.params;

  // Remove a extensão do nome do arquivo e cria o nome do arquivo cacheado
  const nomeSemExtensao = nome.replace(/\.[^/.]+$/, "");
  const cacheFile = path.join(Ambiente.Instance.config.caminhoImagens, 'empresa', `${nomeSemExtensao}_${largura}.webp`);

  // Verifica se a imagem já está no cache
  if (fs.existsSync(cacheFile)) {
    res.sendFile(cacheFile);
    return;
  }

  // Caminho para a imagem original
  const originalImagePath = path.join(Ambiente.Instance.config.caminhoImagens, 'empresa', nome);

  // Processa e cacheia a imagem
  try {
    const data = await sharp(originalImagePath)
      .resize(parseInt(largura))
      .toFormat('webp') // Altera o formato para PNG
      .toBuffer();

    fs.writeFileSync(cacheFile, data);
    res.contentType('image/webp');
    res.send(data);
  } catch (err) {
    res.status(404).send('Imagem não encontrada');
  }
});


export const ImagemController: Router = router;
