import {CFOP} from "./CFOP";
import {ModalidadeBaseDeCalculoICMS} from "./ModalidadeBaseDeCalculoICMS";
import {ModalidadeBaseDeCalculoICMSST} from "./ModalidadeBaseDeCalculoICMSST";
import {TipoDeTributacaoICMS} from "./TipoDeTributacaoICMS";
import {TipoDeTributacaoPIS} from "./TipoDeTributacaoPIS";
import {EnumTipoDeCalculo} from "../EnumTipoDeCalculo";
import {TipoDeTributacaoCOFINS} from "./TipoDeTributacaoCOFINS";

export class TributacaoNaturezaOperacao {
  id: number;
  descricao: string;
  tipoDeTributacaoICMS: TipoDeTributacaoICMS;
  cfop: CFOP;
  modalidadeBaseDeCalculoICMS: ModalidadeBaseDeCalculoICMS;
  aliquotaICMS: number;
  aliquotaICMSST: number;


  percentualReducaoBaseCalculoICMS: number;
  modalidadeBaseDeCalculoICMSST: ModalidadeBaseDeCalculoICMSST;
  percentualMargemValorAdicionadoICMSST: number;
  percentualReducaoBaseCalculoICMSST: number;
  percentualFundoCombatePobreza: number;
  percentualFundoCombatePobrezaST: number;

  //campos do icms efetivo
  percentualReducaoBCEfetiva: number;
  aliquotaICMSEfetivo: number;

  //campos do PIS
  tipoDetributacaoPIS: TipoDeTributacaoPIS;
  percentualAliquotaPIS: number; //preenchido quando o tipo de tributação for por alíquota
  aliquotaEmReaisPIS: number; //preenchido quando o tipo de tributação for por quantidade vendida

  //campos do COFINS
  tipoDetributacaoCOFINS: TipoDeTributacaoCOFINS;
  percentualAliquotaCOFINS: number; //preenchido quando o tipo de tributação for por alíquota
  aliquotaEmReaisCOFINS: number; //preenchido quando o tipo de tributação for por quantidade vendida



  ipiCompoeBaseCalculoICMS: boolean;
  ipiCompoeBasePISeCOFINS: boolean;
  excluirICMSbasePISeCOFINS: boolean;
}
