export enum TipoDeStatus {
    Nova = 1,
    Autorizada = 2,
    EmProcessamento = 3,
    Cancelada = 4,
    <PERSON><PERSON>lizada = 5,
    Importada = 6,
    Contigenciada = 7,
    DeContigencia = 8,
    Duplicidade = 9,
    Rejeitada = 10,
    DadosInvalidos = 11,
    Sucesso = 12,
    Falha = 13,
    ConferenciaRejeitou = 14,
    PresaNaSefaz = 15,
    ProblemasNoEnvio = 16,
    EnvioInterrompido = 17,
    ProblemaComunicacaoSefaz = 18,
    Denegada = 19,
    Re<PERSON>bida = 20,
    PendenteDeConciliacao = 21,
    RejeitadaEPendenteDeConciliacao = 22,
    ProblemasNoEnvioEmContingencia = 23,
    ProblemaAoCancelarNFe = 24
}



export class TipoDeStatusHelper {
    static dicNomes: { [key: number]: string } = {
        1: 'Nova',
        2: 'Autorizada',
        3: 'EmProcessamento',
        4: 'Cancelada',
        5: 'Inutilizada',
        6: 'Importada',
        7: 'Contigenciada',
        8: 'DeContigencia',
        9: 'Duplicidade',
        10: 'Rejeitada',
        11: 'DadosInvalidos',
        12: 'Sucesso',
        13: '<PERSON>alha',
        14: 'ConferenciaRejeitou',
        15: 'PresaNaSefaz',
        16: 'ProblemasNoEnvio',
        17: 'EnvioInterrompido',
        18: 'ProblemaComunicacaoSefaz',
        19: 'Denegada',
        20: 'Recebida',
        21: 'PendenteDeConciliacao',
        22: 'RejeitadaEPendenteDeConciliacao',
        23: 'ProblemasNoEnvioEmContingencia',
        24: 'ProblemaAoCancelarNFe'
    };
    static nome(status: TipoDeStatus): string {
        return this.dicNomes[status];
    }

    static obtenhaCodigo(estado: string): TipoDeStatus {
        const dicCodigos: { [key: string]: TipoDeStatus } = {
            'Nova': TipoDeStatus.Nova,
            'Autorizada': TipoDeStatus.Autorizada,
            'EmProcessamento': TipoDeStatus.EmProcessamento,
            'Cancelada': TipoDeStatus.Cancelada,
            'Inutilizada': TipoDeStatus.Inutilizada,
            'Importada': TipoDeStatus.Importada,
            'Contigenciada': TipoDeStatus.Contigenciada,
            'DeContigencia': TipoDeStatus.DeContigencia,
            'Duplicidade': TipoDeStatus.Duplicidade,
            'Rejeitada': TipoDeStatus.Rejeitada,
            'DadosInvalidos': TipoDeStatus.DadosInvalidos,
            'Denegada': TipoDeStatus.Denegada,
            'Recebida': TipoDeStatus.Recebida,
            'PendenteDeConciliacao': TipoDeStatus.PendenteDeConciliacao,
            'RejeitadaEPendenteDeConciliacao': TipoDeStatus.RejeitadaEPendenteDeConciliacao,
            'ProblemasNoEnvioEmContingencia': TipoDeStatus.ProblemasNoEnvioEmContingencia,
            'ProblemaAoCancelarNFe': TipoDeStatus.ProblemaAoCancelarNFe
        };

        return dicCodigos[estado] ?? null;
    }
}
