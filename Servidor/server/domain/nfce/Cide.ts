import {CideXml} from "../../utils/nfce/xml/CideXml";

export class Cide {
  quantidadeBC: number;
  aliquota: number;
  valor: number;

  static obtenhaCide(tag: any): Cide {
    const cide = new Cide();

    cide.quantidadeBC = tag.qBCProd;
    cide.aliquota = tag.vAliqProd;
    cide.valor = tag.vCIDE;

    return cide;
  }

  public gereXml(): string {
    return new CideXml(this).obtenhaXml();
  }

}
