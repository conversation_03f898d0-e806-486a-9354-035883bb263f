import {EventoDeEmitente} from "./EventoDeEmitente";
import {string} from "blockly/core/utils";
import {Evento} from "./Evento";
import {ClasseXml} from "../../utils/nfce/xml/ClasseXml";
import {EventoEPECXml} from "../../utils/nfce/xml/EventoEPECXml";
import {ConfiguracoesNotaFiscal} from "./configuracoes/ConfiguracoesNotaFiscal";

export class EventoEPEC extends EventoDeEmitente {
    id: string;
    tipoDoDocumento: number;
    ieEmitente: string;
    ufDestinatario: string;
    cnpjDestinatario: string;
    cpfDestinatario: string;
    idEstrangeiroDestinatario: string;
    ieDestinatario: string;
    valorTotalNFe: number;
    valorICMS: number;
    valorICMSSt: number;
    numeroDoProtocolo: string;

    constructor(private configuracoes: ConfiguracoesNotaFiscal) {
        super();
        // Inicialização de variáveis, se necessário
    }

    obtenhaTipoDeEvento(): string {
        return "110140";
    }

    obtenhaNumeroDeSequencia(): number {
        return Evento.NUMERO_SEQUENCIA_PADRAO;
    }

    estaValido(): boolean {
        return true
    }

  obtenhaClasseXml(): ClasseXml {
    return new EventoEPECXml(this, this.configuracoes);
  }

  gereXmlAssinado(): string {
    throw new Error("Method not implemented.");
  }

}
