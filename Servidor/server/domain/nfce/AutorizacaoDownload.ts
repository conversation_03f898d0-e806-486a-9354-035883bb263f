import {AutorizacaoDownloadXml} from "../../utils/nfce/xml/AutorizacaoDownloadXml";

export class AutorizacaoDownload {
  public cnpj: string;
  public cpf: string
  static obtenhaAutorizacaoDownload(tag: any) {
    const autorizacaoDownload = new AutorizacaoDownload();

    autorizacaoDownload.cnpj = tag.CNPJ;
    autorizacaoDownload.cpf = tag.CPF;

    return autorizacaoDownload;
  }

  public gereXml(): string {
    return new AutorizacaoDownloadXml(this).obtenhaXml();
  }
}
