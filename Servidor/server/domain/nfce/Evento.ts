import {ClasseXml} from "../../utils/nfce/xml/ClasseXml";
import {Empresa} from "../Empresa";
import {AssinadorDeXml} from "../../utils/nfse/AssinadorDeXml";

export abstract class Evento {
  static NUMERO_SEQUENCIA_PADRAO = 1;

  empresa: Empresa
  ehValido = false;
  chaveDaNFe: string;
  dataEvento: Date;
  erros: any[] = [];
  identificador: string;
  numeroSequencia: number;
  xmlGerado: string;
  motivo: string;
  operacao: number; // Operacao.Enviar
  status: string;
  numeroNFe: number;
  xmlAssinado: string;
  tamanho = 0;

  abstract obtenhaTipoDeEvento(): string;

  abstract obtenhaNumeroDeSequencia(): number;

  abstract estaValido(): boolean;

  abstract gereXmlAssinado(): string;

  abstract obtenhaIdentificacaoDoDono(): string;

  obtenhaIdentificador(): string {
    let numSeq: string = this.obtenhaNumeroDeSequencia().toString().padStart(2, '0');

    return `ID${this.obtenhaTipoDeEvento()}${this.chaveDaNFe}${numSeq}`;
  }

  tamanhoEmBytes(): number {
    if (this.tamanho === 0) {
      const xmlAssinado = this.gereXmlAssinado();
      this.tamanho = new TextEncoder().encode(xmlAssinado).length;
    }
    return this.tamanho;
  }

  gereXmlAssinadoPeloCertificado(caminhoCertificado: string): string {
    let xml = this.gereXml();
    if (!this.xmlAssinado || this.xmlAssinado.trim() === "") {
      console.log(`Assinando evento: ${xml}.`);
      if (!caminhoCertificado) return xml;
      const assinador = new AssinadorDeXml(caminhoCertificado);
      this.xmlAssinado = assinador.assine(xml, { location: {reference: "//*[local-name(.)='infEvento']", action: 'after'}},
        "//*[local-name(.)='infEvento']", false);
      console.log(`Evento assinado: ${this.xmlAssinado}`);
    }

    return this.xmlAssinado;
  }

  gereXml(): string {
    if (!this.xmlGerado || this.xmlGerado.trim() === "") {
      console.log(`Gerando XML do evento numero ${this.numeroSequencia}`);
      let xml = this.obtenhaClasseXml().obtenhaXml()
      xml = xml.replace(/>\\s*</g, '><').replace(/>\\s*/g, '>').replace(/\\s*</g, '<');
      this.xmlGerado = xml;
    }

    return this.xmlGerado;
  }

  abstract obtenhaClasseXml(): ClasseXml;
}
