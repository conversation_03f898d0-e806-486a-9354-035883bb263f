import {ICMSimplesXml} from "../../utils/nfce/xml/ICMSimplesXml";
import {ItemPedido} from "../delivery/ItemPedido";
import {TributacaoNaturezaOperacao} from "./TributacaoNaturezaOperacao";
import {ICMS} from "./ICMS";
import {Ipi} from "./Ipi";
import {ProdutoConfigFiscal} from "../ProdutoConfigFiscal";
import {ItemNFe} from "./ItemNFe";

export class ICMSimples {
  id: string;
  origem: number;
  codigoSitOp: number;
  percentualCredito: number;
  valorCredito: number;
  modBaseDeCalculoICMSSt: number;
  percentualMargemValorICMSSt: number;
  percentualReducaoBaseDeCalculoICMSSt: number;
  valorBaseDeCalculoICMSSt: number;
  aliquotaICMSSt: number;
  valorICMSSt: number;
  modbasecalcIcms: number;
  valorBaseDeCalculoICMS: number;
  aliquotaICMS: number;
  aliqutoAplicavelCalcCred: number;
  percentualReducaoBaseDeCalculoICMS: number;
  valorICMS: number;
  valorBCICMSRet: number;
  valorICMSRet: number;

  // Versão 4.0
  valorBaseDeCalculoFCPST: number;
  percentualFCPST: number;
  valorFCPST: number;

  percentualConsumidorST: number;

  valorBCFCPSTRet: number;
  percentualFCPSTRet: number;
  valorFCPSTRet: number;

  percentualReducaoBCEfetiva: number;
  valorBCEfetiva: number;
  percentualICMSEfetivo: number;
  valorICMSEfetivo: number;

  valorICMSSubstituto: number;

  static ehICMSimples(tipo: string): boolean {
    const icmsSimplesTypes: { [key: string]: string } = {
      'SN101': 'SN101',
      'SN102': 'SN102',
      'SN201': 'SN201',
      'SN202': 'SN202',
      'SN500': 'SN500',
      'SN900': 'SN900'
    };

    const isSimples = icmsSimplesTypes[tipo] !== undefined;

    // Assuming 'loggin.imprima' is a logging function. Replace with actual logging if needed.
    console.log(`${tipo} -> ICMS simples: ${isSimples}`);

    return isSimples;
  }

  static  criePeloItem(itemNFe: ItemNFe, configuracaoFiscal: ProdutoConfigFiscal, tributacaoNaturezaOperacao: TributacaoNaturezaOperacao, ipi: Ipi): ICMSimples {
    if(!tributacaoNaturezaOperacao.tipoDeTributacaoICMS.simplesNacional) return undefined;


    const icms = new ICMSimples();

    icms.codigoSitOp = parseInt(tributacaoNaturezaOperacao.tipoDeTributacaoICMS.codigo, 10);

    icms.origem = configuracaoFiscal.origem.codigo; //102, 103, 300, 400, 500

    if(tributacaoNaturezaOperacao.modalidadeBaseDeCalculoICMS) {
      icms.modbasecalcIcms = parseInt(tributacaoNaturezaOperacao.modalidadeBaseDeCalculoICMS.codigo, 10);
      icms.valorBaseDeCalculoICMS = itemNFe.valorTotalBruto - itemNFe.valorDesconto;

      if(tributacaoNaturezaOperacao.ipiCompoeBaseCalculoICMS) {
        icms.valorBaseDeCalculoICMS = icms.valorBaseDeCalculoICMS + ipi.valor;
      }

      icms.percentualReducaoBaseDeCalculoICMS = tributacaoNaturezaOperacao.percentualReducaoBaseCalculoICMS;

      if(icms.percentualReducaoBaseDeCalculoICMS)
        icms.valorBaseDeCalculoICMS = icms.valorBaseDeCalculoICMS *
          (1 - icms.percentualReducaoBaseDeCalculoICMS / 100);


      icms.aliquotaICMS = tributacaoNaturezaOperacao.aliquotaICMS
      icms.valorICMS = icms.valorBaseDeCalculoICMS * icms.aliquotaICMS / 100;

    }


    if(tributacaoNaturezaOperacao.tipoDeTributacaoICMS.codigo === '500' && tributacaoNaturezaOperacao.aliquotaICMSEfetivo) {
      //incluir campos da aliquota efetiva
      icms.percentualICMSEfetivo = tributacaoNaturezaOperacao.aliquotaICMSEfetivo;
      icms.percentualReducaoBCEfetiva = tributacaoNaturezaOperacao.percentualReducaoBCEfetiva;
      icms.valorBCEfetiva = itemNFe.valorTotalBruto - itemNFe.valorDesconto;

      if(icms.percentualReducaoBCEfetiva)
        icms.valorBCEfetiva = icms.valorBCEfetiva * (1 - icms.percentualReducaoBCEfetiva / 100);

      icms.valorICMSEfetivo = icms.valorBCEfetiva * icms.percentualICMSEfetivo / 100;
    }





  }

  static obtenhaICMS(tagCompleta: any): ICMSimples | null {
    const tipo: string = ICMSimples.trateTipoDeICMS(tagCompleta); // Assuming trateTipoDeICMS is a method in the same class
    if (!ICMSimples.ehICMSimples(tipo)) return null;

    let tagICMS = tagCompleta["ICMS" + tipo];


    console.log("Processando ICMS Simples");
    const icms = new ICMSimples(); // Assuming ICMSimples is a class that exists
    icms.origem = tagICMS.orig;
    icms.codigoSitOp = tagICMS.CSOSN;
    icms.modbasecalcIcms = tagICMS.modBC;
    icms.valorBaseDeCalculoICMS = tagICMS.vBC;
    icms.percentualReducaoBaseDeCalculoICMS = tagICMS.pRedBC;
    icms.aliquotaICMS = tagICMS.pICMS;
    icms.valorICMS = tagICMS.vICMS;
    icms.valorBCICMSRet = tagICMS.vBCSTRet;
    icms.valorICMSRet = tagICMS.vICMSSTRet;
    icms.modBaseDeCalculoICMSSt = tagICMS.modBCST;
    icms.percentualMargemValorICMSSt = tagICMS.pMVAST;
    icms.percentualReducaoBaseDeCalculoICMSSt = tagICMS.pRedBCST;
    icms.valorBaseDeCalculoICMSSt = tagICMS.vBCST;
    icms.aliquotaICMSSt = tagICMS.pICMSST;
    icms.valorICMSSt = tagICMS.vICMSST;
    icms.aliqutoAplicavelCalcCred = tagICMS.pCredSN;
    icms.valorCredito = tagICMS.vCredICMSSN;

    icms.valorBaseDeCalculoFCPST = tagICMS.vBCFCPST;
    icms.percentualFCPST = tagICMS.pFCPST;
    icms.valorFCPST = tagICMS.vFCPST;
    icms.valorBCFCPSTRet = tagICMS.vBCFCPSTRet;
    icms.percentualFCPSTRet = tagICMS.pFCPSTRet;
    icms.valorFCPSTRet = tagICMS.vFCPSTRet;
    icms.percentualConsumidorST = tagICMS.pST;

    icms.percentualReducaoBCEfetiva = tagICMS.pRedBCEfet;
    icms.valorBCEfetiva = tagICMS.vBCEfet;
    icms.percentualICMSEfetivo = tagICMS.pICMSEfet;
    icms.valorICMSEfetivo = tagICMS.vICMSEfet;
    icms.valorICMSSubstituto = tagICMS.vICMSSubstituto;

    return icms;
  }


  private static trateTipoDeICMS(tagICMS: any) {
    if(!tagICMS) return null;
    return  Object.keys(tagICMS)[0].replace("ICMS", "");
  }

  public gereXml(): string {
    return new ICMSimplesXml(this).obtenhaXml()
  }




  obtenhaTipoICMS(): string {
    // Assuming 'loggin.imprima' is a logging function. Replace with actual logging if needed.
    console.log("Buscando Tipo de ICMS Simples");

    const tipoICMSMap: { [key: number]: string } = {
      101: "SN101",
      102: "SN102",
      103: "SN102",
      300: "SN102",
      400: "SN102",
      201: "SN201",
      202: "SN202",
      203: "SN202",
      500: "SN500",
      900: "SN900"
    };

    return tipoICMSMap[this.codigoSitOp] || "Unknown";
  }

}
