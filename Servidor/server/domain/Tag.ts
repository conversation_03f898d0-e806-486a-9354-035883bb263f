import {ObjetoPersistente} from "./ObjetoPersistente";
import {MapeadorDeTag} from "../mapeadores/MapeadorDeTag";
import {Empresa} from "./Empresa";
import {MapeadorBasico} from "../mapeadores/MapeadorBasico";

export class Tag extends ObjetoPersistente{
  public empresa: Empresa;
  constructor(public nome: string) {
    super()
  }


  valide(): Promise<any> {
    return new Promise<any>( async (resolve, reject) => {
      if(! this.nome) return resolve('Nome é obrigatorio')

      let existe = await this.mapeador().existeSync({nome: this.nome})

      if(existe) return resolve('Já existe um tag com esse nome')

      resolve('')
    })
  }


  mapeador(): MapeadorBasico {
    return new MapeadorDeTag();
  }
}
