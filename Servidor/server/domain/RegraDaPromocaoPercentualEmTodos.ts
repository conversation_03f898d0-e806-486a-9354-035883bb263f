import {AplicacaoDeRegra, RegraDaPromocao} from "./RegraDaPromocao";
import {Pedido} from "./delivery/Pedido";
import {Produto} from "./Produto";
import {AdicionalDeProduto} from "./delivery/AdicionalDeProduto";
import {ItemPedido} from "./delivery/ItemPedido";
import {Promocao} from "./Promocao";

export class RegraDaPromocaoPercentualEmTodos extends RegraDaPromocao {

  constructor(public percentual: number, public ativa: boolean = true, public promocao: Promocao,
              valorMinimoPedido: number = 0) {
    super(promocao, 'percentual-todos', valorMinimoPedido);
  }

  aplique(pedido: Pedido): AplicacaoDeRegra {
    if(!this.ativa) return new AplicacaoDeRegra(0);

    let valorDesconto = 0;

    for(let item of pedido.itens){
      item.desconto = this.percentual * item.total / 100;
      valorDesconto += item.desconto
    }

    let totalDesconto = valorDesconto

    if(totalDesconto > 0 && this.valorMinimoPedido > 0) {
      let valorPedido = pedido.valor

      if(valorPedido < this.valorMinimoPedido)
        return new AplicacaoDeRegra(0);
    }

    pedido.desconto += totalDesconto

    return new AplicacaoDeRegra(totalDesconto);
  }

}
