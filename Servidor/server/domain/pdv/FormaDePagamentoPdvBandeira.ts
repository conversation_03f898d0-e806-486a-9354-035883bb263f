import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorDeFormaDePagamentoBandeira} from "../../mapeadores/MapeadorDeFormaDePagamentoBandeira";

export class FormaDePagamentoPdvBandeira extends ObjetoPersistente{
  public ativo: boolean;
  constructor(public formaDePagamentoPdv: any, public bandeira: any) {
     super();
     this.ativo = true;
  }

  mapeador(): any {
    return new MapeadorDeFormaDePagamentoBandeira();
  }
}
