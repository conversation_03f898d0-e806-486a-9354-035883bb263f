import {FormaDePagamento} from "../delivery/FormaDePagamento";
import {MoneyUtils} from "./MoneyUtils";
import {Caixa} from "./Caixa";

export class SaldoDaFormaDePagamento {
  public id: number;

  private ultimaAtualizacao: Date;
  constructor(public caixa: Caixa, public formaDePagamento: FormaDePagamento, public saldoEmCentavos: number) {
    this.ultimaAtualizacao = new Date();
  }
  //retorna o saldo a partir do saldo em centavos usando a classe MoneyUtils
  get saldo(): number {
    return MoneyUtils.deCentavosParaNumber(this.saldoEmCentavos)
  }

  //adiciona um valor ao saldo
  adicione(valorEmCentavos: number) {
    this.saldoEmCentavos += valorEmCentavos;
    this.ultimaAtualizacao = new Date();
  }


  obtenhaDTO() {
    //return all data except caixa reference
    return {
      id: this.id,
      formaDePagamento: this.formaDePagamento,
      saldo: this.saldo,
      ultimaAtualizacao: this.ultimaAtualizacao
    }
  }
}
