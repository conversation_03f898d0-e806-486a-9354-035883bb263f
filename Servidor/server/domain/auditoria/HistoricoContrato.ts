import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorBasico} from "../../mapeadores/MapeadorBasico";
import {MapeadorDeHistoricoContrato} from "../../mapeadores/MapeadorDeHistoricoContrato";

export class HistoricoContrato extends ObjetoPersistente{
  horario: Date;
  constructor(public contrato: any, public descricao: string,
              public dados: any,  public operador: any = null) {
    super();
    this.horario = new Date();

    if(this.dados) this.dados =  JSON.stringify(this.dados)
  }

  mapeador(): MapeadorBasico {
    return new MapeadorDeHistoricoContrato();
  }
}
