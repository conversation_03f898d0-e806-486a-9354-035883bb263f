import {EnumTipoDeCobrancaVariosSabores} from "../delivery/EnumTipoDeCobrancaVariosSabores";
import {ProdutoTemplateTamanho} from "./ProdutoTemplateTamanho";
import {ProdutoTemplateAdicional} from "./ProdutoTemplateAdicional";
import {ProdutoTemplateOpcao} from "./ProdutoTemplateOpcao";
import {Catalogo} from "../catalogo/Catalogo";


export class ProdutoTemplate{
  tamanhos: any = [];
  adicionais: any = [];
  ativo = true;
  vendaPorTamanho = false;
  montarPizza = false;
  taxaExtra: number;
  campoOrdenar: string;
  identificador = 'Pizza'
  exibirPrecosTamanhos = false;
  //templateOriginal: ProdutoTemplate;
  nomeCategoriaMontar = 'MONTE SUA PIZZA'
  ocultarProdutos = false;
  constructor(public id: number,  public nome: string, public tipo: string,
              public   tipoDeCobranca: EnumTipoDeCobrancaVariosSabores,
              public catalogo: Catalogo = null){

  }

  static novoTemplateDePizzaDoModelo(catalogo: Catalogo, produtoTemplateModelo: any){
    let tipoCobranca = produtoTemplateModelo ? produtoTemplateModelo.tipoDeCobranca : EnumTipoDeCobrancaVariosSabores.Maior ;

    let template = new ProdutoTemplate(null, 'Pizzas', 'pizza', tipoCobranca, catalogo)


    if(produtoTemplateModelo){

      template.nomeCategoriaMontar = produtoTemplateModelo.nomeCategoriaMontar;

      produtoTemplateModelo.tamanhos.forEach( (tamanho: any) => {
        template.tamanhos.push(new ProdutoTemplateTamanho(null, tamanho.descricao, tamanho.qtdePedacos, tamanho.qtdeSabores))
      })

      produtoTemplateModelo.adicionais.forEach ( (adicional: any) => {
        let opcoes: any = [];

        adicional.opcoes.forEach( (opcao: any) => {
          opcoes.push(new  ProdutoTemplateOpcao(null, opcao.nome, opcao.valor, opcao.descricao))
        })

        template.adicionais.push(new ProdutoTemplateAdicional(null, adicional.descricao, 'escolha-simples', opcoes))
      })

    } else {
      let opcoesMassa  = [], opcoesBorda = [];

      opcoesMassa.push(new  ProdutoTemplateOpcao(null, 'Tradicional', 0, ''))
      opcoesMassa.push(new  ProdutoTemplateOpcao(null, 'Integral', 5, ''))

      template.adicionais.push(new ProdutoTemplateAdicional(null, 'Massa', 'escolha-simples', opcoesMassa))

      opcoesBorda.push(new  ProdutoTemplateOpcao(null, 'Tradicional', 0, ''))
      opcoesBorda.push(new  ProdutoTemplateOpcao(null, 'Catupiry', 5, ''))

      template.adicionais.push(new ProdutoTemplateAdicional(null, 'Borda', 'escolha-simples', opcoesBorda))

    }

    return template;

  }


  static novoTemplateDePizza(catalogo: Catalogo){
    let template = new ProdutoTemplate(null, 'Pizzas', 'pizza', EnumTipoDeCobrancaVariosSabores.Maior, catalogo)
    template.tamanhos.push(new ProdutoTemplateTamanho(null, 'Pequena', 6, 1))
    template.tamanhos.push(new ProdutoTemplateTamanho(null, 'Média', 8, 2))
    template.tamanhos.push(new ProdutoTemplateTamanho(null, 'Grande', 10, 3))

    let opcoesMassa  = [], opcoesBorda = [];

    opcoesMassa.push(new  ProdutoTemplateOpcao(null, 'Tradicional', 0, ''))
    opcoesMassa.push(new  ProdutoTemplateOpcao(null, 'Integral', 5, ''))

    template.adicionais.push(new ProdutoTemplateAdicional(null, 'Massa', 'escolha-simples', opcoesMassa))

    opcoesBorda.push(new  ProdutoTemplateOpcao(null, 'Tradicional', 0, ''))
    opcoesBorda.push(new  ProdutoTemplateOpcao(null, 'Catupiry', 5, ''))

    template.adicionais.push(new ProdutoTemplateAdicional(null, 'Borda', 'escolha-simples', opcoesBorda))

    return template;
  }

  clone(): ProdutoTemplate {
    let produtoTemplate = new ProdutoTemplate(null, this.nome, this.tipo, this.tipoDeCobranca, this.catalogo)
    produtoTemplate.nomeCategoriaMontar = this.nomeCategoriaMontar;
    produtoTemplate.ocultarProdutos = this.ocultarProdutos;
    //produtoTemplate.templateOriginal = this;
    this.tamanhos.forEach((tamanho: ProdutoTemplateTamanho) => {
      let cloneTamanho = tamanho.clone()

      produtoTemplate.tamanhos.push(cloneTamanho)
    })

    this.adicionais.forEach ((adicional: ProdutoTemplateAdicional) => {
      let cloneAdicional = adicional.clone()
      produtoTemplate.adicionais.push(cloneAdicional)

    })

    return produtoTemplate
  }

}
