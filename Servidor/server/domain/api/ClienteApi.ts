import {GeradorDeIdentificador} from "../../utils/GeradorDeIdentificador";
import {SistemaIntegrado} from "../integracoes/SistemaIntegrado";

export class ClienteApi {
  id: string
  segredo: string
  dataCriacao: Date
  acessoDireto: boolean;
  sistemaIntegrado: SistemaIntegrado;
  empresa: any;
  constructor(private nome: string, private identificador: string,
              private tipo: string, private ip: string,
              public ativo = false) {
    this.dataCriacao = new Date()
    this.acessoDireto = false;
  }

  gereIdESegredo() {
    this.id = GeradorDeIdentificador.gere();
    this.segredo = GeradorDeIdentificador.gere();
  }

  ativar() {
    this.ativo = true;
  }

  desativar() {
    this.ativo = false;
  }
  obtenhaDTO() {
    return {
      clientId: this.id,
      clientSecret: this.segredo,
      dataCriacao: this.dataCriacao,
      mensagem: "Par id-secret criado com sucesso. Entre em contato pelo WhatsApp (62) 98171-2622 para ativar a integração."
    }
  }

}
