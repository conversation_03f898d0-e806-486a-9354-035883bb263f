import {Cupom} from "./Cupom";
import {EnumTipoDeCupom} from "../../lib/emun/EnumTipoDeCupom";
import {Pedido} from "../delivery/Pedido";

export class CupomValorFixo extends Cupom{
  constructor(public nome: string, public codigo: string, public valor: number,
              validade: any, restrito: boolean = false, primeiraCompra: boolean = false,
              quantidade: number, qtdeMaxima: number, valorMinimo: number, categorias: any,
              produtos: any, aplicarNaTaxaDeEntrega: any,
              percentualMaximoDescontoProduto: number, permitirComprarComPromocao: boolean,
              produtoTemplateTamanho: any, naoPontuarFidelidade: any,   minimoApenasNoValorProduto: boolean,
              restritoContatoPerdido: boolean, restritoAniversariantes: boolean,
              produtoNaoComporMinimo: boolean, minimoApenasMesmoTamanho: boolean,
              mensagemMinimo: string) {
    super(nome, codigo, EnumTipoDeCupom.Valor, validade, restrito, primeiraCompra, quantidade, qtdeMaxima,
      valorMinimo, categorias, produtos, aplicarNaTaxaDeEntrega, percentualMaximoDescontoProduto,
      permitirComprarComPromocao,  produtoTemplateTamanho,   naoPontuarFidelidade, minimoApenasNoValorProduto,
      restritoContatoPerdido, restritoAniversariantes,
      produtoNaoComporMinimo, minimoApenasMesmoTamanho, mensagemMinimo);
  }

  static novo(dados: any,  percentualMaximoDescontoProduto: number, permitirComprarComPromocao: boolean) {
    return  new CupomValorFixo(dados.nome, dados.codigo, dados.valor, dados.validade, dados.restrito,
      dados.primeiraCompra, dados.quantidade, dados.qtdeMaxima,
      dados.valorMinimo, dados.categorias, dados.produtos, dados.aplicarNaTaxaDeEntrega,
      percentualMaximoDescontoProduto, permitirComprarComPromocao, dados.produtosTemplateTamanho, dados.naoPontuarFidelidade,
      dados.minimoApenasNoValorProduto, dados.restritoContatoPerdido, dados.restritoAniversariantes,
      dados.produtoNaoComporMinimo, dados.minimoApenasMesmoTamanho,
      dados.mensagemMinimo);
  }

  calculeDesconto(pedido: Pedido) {
    let valor = this.aplicarNaTaxaDeEntrega  ? pedido.taxaEntrega : pedido.obtenhaValorProdutosAplicavelAoCupom(this);

    return valor && valor > this.valor ? this.valor : valor
  }

  gereFraseDescritivaEspecifica(): string {
    const formatter = new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',

      // These options are needed to round to whole numbers if that's what you want.
      //minimumFractionDigits: 0, // (this suffices for whole numbers, but will print 2500.10 as $2,500.1)
      //maximumFractionDigits: 0, // (causes 2500.99 to be printed as $2,501)
    });
    return Cupom.formateComoDinheiro(this.valor) + " de desconto"
  }

}
