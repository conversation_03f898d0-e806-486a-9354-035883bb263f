import {ServicoCob<PERSON>} from "./ServicoCobrado";
import {MapeadorDeLancamento} from "../../mapeadores/MapeadorDeLancamento";

export class Lancamento {
  id: number;
  descricao: string;
  fatura: any;
  valor: number;
  desconto: number;
  qtde: number;
  servicoCobrado: ServicoCobrado;
  tipo: string;

  constructor(fatura: any, descricao: string, tipo: string, qtde: number, servicoCobrado: any, valor: number, desconto: number = 0) {
    this.descricao = descricao;
    this.fatura = fatura;
    this.tipo = tipo;
    this.valor = valor;
    this.qtde = qtde;
    this.servicoCobrado = servicoCobrado;
    this.desconto = desconto;
  }

  async insira(){
    await new MapeadorDeLancamento().insiraGraph(this)
  }

  async remova(){
    await new MapeadorDeLancamento().removaAsync(this)
  }
}
