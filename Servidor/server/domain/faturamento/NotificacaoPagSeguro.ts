import {NotificacaoMeioPagamento} from "./NotificacaoMeioPagamento";
import {EnumStatusPagamento, StatusChargeDeParaPagseguro} from "../../lib/emun/EnumStatusPagamento";

const meio = 'pagseguro';

export class NotificacaoPagSeguro extends NotificacaoMeioPagamento{
  constructor(id: number,  tipo: string, dados: any = null) {
    super(id, meio, tipo, dados ? dados.codigo : null, dados ? dados.status : null, new Date(), dados);
    this.referencia =  dados ? dados.pedido : null;
  }

  static novaDoTipo(tipo: string, obj: any){
    let dados: any = {}, codigo: string, status: any;
    let charge: any;

    if(obj){
      if(tipo === 'charge'){
        charge = obj;
      } else {
        charge = obj.charges.find((item: any) => item.status === 'PAID') || obj.charges[0];
      }

      if(charge){
        dados.codigo =  charge.id;
        dados.status = charge.status;

        let valorReembolso = charge.amount.summary && charge.amount.summary.refunded > 0 ?   charge.amount.summary.refunded : null;

        if(valorReembolso) dados.valorReembolso  = valorReembolso;

        dados.pedido = charge.reference_id;
      }
    }

    return new NotificacaoPagSeguro(new Date().getTime(), tipo, dados)
  }

  getStatus(){
    if(typeof this.dados === 'string') this.dados = JSON.parse(this.dados)

    if(this.dados.valorReembolso > 0) return EnumStatusPagamento.Reembolsado

    return StatusChargeDeParaPagseguro.get(this.status);
  }

}
