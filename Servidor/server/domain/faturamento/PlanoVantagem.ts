import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorDePlanoVantagem} from "../../mapeadores/MapeadorDePlanoVantagem";


export class PlanoVantagem extends ObjetoPersistente{
  disponivel = true;
  constructor(public planoEmpresarial: any,
              public vantagem: any, public ordem: number) {
    super()
  }

  mapeador(): any {
    return new MapeadorDePlanoVantagem();
  }

  // tslint:disable-next-line:member-ordering
  static get(query: any){
    return  new MapeadorDePlanoVantagem().selecioneSync(query);
  }
}
