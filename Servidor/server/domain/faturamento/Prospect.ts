import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorBasico} from "../../mapeadores/MapeadorBasico";
import {MapeadorDeProspect} from "../../mapeadores/MapeadorDeProspect";
// @ts-ignore
import uuid = require("uuid");
import {MapeadorDeEmpresa} from "../../mapeadores/MapeadorDeEmpresa";
import {MapeadorDeCidade} from "../../mapeadores/MapeadorDeCidade";
import {TomtomService} from "../../service/TomtomService";

// @ts-ignore
export class Prospect extends ObjetoPersistente {
  id: number
  horario: Date;
  atualizacao: Date;
  passo: string;
  tipoDeNegocio: string;
  codigo: string;
  dados: any
  empresaId: number;
  telefoneValido: boolean;
  constructor(public nome: string, public email: string,
              public empresa: string, public cnpj: string, public telefone: string,
              public instagram: string) {
    super()
    this.horario = new Date();
    this.atualizacao = new Date();
    this.telefoneValido = false;
  }

  async valide(): Promise<any> {
    return new Promise(async (resolve, reject) => {
      if (!this.nome) return resolve(this.obtenhaErroAmp('nome', 'Nome não informado'))
      if (!this.email) return resolve(this.obtenhaErroAmp('email', 'Email não informada'))
      if (!this.telefone) return resolve(this.obtenhaErroAmp('telefone', 'Telefone não informada'))
      if (!this.instagram) return resolve(this.obtenhaErroAmp('instagram', 'Instagram não informado'))


      let erro = await this.validePreenchidos();

      resolve(erro)
    })
  }

  validePreenchidos(): Promise<any> {
    return new Promise<any>(async (resolve, reject) => {
      if (this.instagram) {
        this.instagram = this.instagram.trim();

        if (!this.instagram.match(/^[a-zA-Z0-9._]+$/))
          return resolve(this.obtenhaErroAmp('instagram',
            'Instagram inválido, verifique os caracteres informados nao são aceitos espaços , acentos.'))

      }

      let empresa: any = this.obtenhaEmpresa();

      if (empresa) {
        if (empresa.cnpj){
           empresa.cnpj = empresa.cnpj.replace(/[\.\/-]/g, '').trim();

           if(empresa.cnpj.length < 14)
            return resolve(this.obtenhaErroAmp('cnpj', 'CNPJ inválido.'));

           let existeCnpj = await new MapeadorDeEmpresa().existeSync({cnpj: empresa.cnpj});

           if(existeCnpj)
             return resolve(this.obtenhaErroAmp('cnpj', 'CNPJ já está em uso'));
        }

        this.dados.empresa = empresa;

        if(!empresa.endereco){
          empresa.endereco = await this.obtenhaEnderecoCompleto();

          const posicoes: any = await new TomtomService().calculeCoordenadasEnderecoString(null, null, empresa.endereco);

          console.log(posicoes);
          if( posicoes && posicoes.posicao )
            empresa.latitudeLongitude = posicoes.posicao.lat + ',' + posicoes.posicao.lon;
        }

      }

      let plano: any = this.obtenhaPlano();
      if (plano && !plano.id) {
        return resolve(this.obtenhaErroAmp('plano', 'Id do plano inválido'))
      }

      let pagamento: any = this.obtenhaPagamento();

      if (pagamento) {
        if (!pagamento.token)
          return resolve(this.obtenhaErroAmp('token', 'Token cartão não informado'))
      }

      if (this.dados) this.dados = JSON.stringify(this.dados)

      resolve('');
    })

  }

  async salve(graph: boolean = false): Promise<any> {
    this.codigo = uuid();
    if (this.dados) this.dados = JSON.stringify(this.dados)

    return super.salve(graph);
  }

  async atualizePasso(passo: string, dados: any) {
    this.dados = dados;
    this.passo = passo;
    this.atualizacao = new Date();
    console.log(String(`Prospect atualizado ${this.codigo} -  passo: ${this.passo}`));
    return new Promise<void>(async (resolve, reject) => {

      let erro = await this.valide();

      if (erro) return reject(erro)

      this.dados = JSON.stringify(this.dados);

      await super.atualize();

      resolve()
    })
  }

  async telefoneFoiValidado() {
    this.passo = 'telefone-validado';
    this.telefoneValido = true;
    this.atualizacao = new Date();

    return new MapeadorDeProspect().atualizeTelefoneValido(this)
  }

  obtenhaEmpresa(): any {
    let dados = this.obtenhaDados();

    return dados ? dados.empresa : null;
  }

  obtenhaPlano(): any {
    let dados = this.obtenhaDados();

    return dados ? dados.plano : null;
  }

  obtenhaPagamento(): any {
    let dados = this.obtenhaDados();

    return dados ? dados.pagamento : null;
  }


  obtenhaUsuario() {
    let dados = this.obtenhaDados();

    return {nome: this.nome, email: this.email, senha: dados.senha}
  }

  obtenhaDados() {
    if (typeof this.dados === 'string')
      this.dados = JSON.parse(this.dados);

    return this.dados;

  }


  obtenhaDominio() {
    let empresa = this.obtenhaEmpresa();

    return empresa ? empresa.dominio : '';
  }


  obtenhaEnderecoCompleto(): Promise<string> {

    return new Promise(async (resolve, reject) => {
      let empresa = this.obtenhaEmpresa();

      let endereco = empresa.logradouro  ;

      if(empresa.numero) endereco += ", numero " + empresa.numero
      if(empresa.bairro) endereco += ", " + empresa.bairro

      if(empresa.cidade){
        let cidade = await new MapeadorDeCidade().selecioneSync(empresa.cidade);

        if(cidade)
          endereco +=  ", " + cidade.nome + " - " + cidade.estado.sigla;
      }


      resolve(endereco)
    })
  }

  mapeador(): MapeadorBasico {
    return new MapeadorDeProspect();
  }

  // tslint:disable-next-line:member-ordering
  static async liste(query: any) {
    return new MapeadorDeProspect().listeAsync(query)
  }

  // tslint:disable-next-line:member-ordering
  static async get(query: any) {
    return new MapeadorDeProspect().selecioneSync(query)
  }

  private obtenhaErroAmp(campo: string, mensagem: string) {
    return {name: campo, message: mensagem};
  }


}
