import {Produto} from "./Produto";
import {ExecutorAsync} from "../utils/ExecutorAsync";
import * as Queue from 'bull'
import {ProdutoEmbeddings} from "./ProdutoEmbeddings";


export class FileProdutoEmbedding {
  private queue: Queue.Queue<Produto>;

  constructor() {
    this.queue = new Queue<Produto>('filaDeProdutos', { defaultJobOptions: {
        attempts: 90,
        backoff: 2000,
        removeOnComplete: true,
        removeOnFail: true
      }
    });

    this.queue.process(this.processarTarefa.bind(this));

    this.queue.on('completed', (job: Queue.Job<Produto>, result: Boolean) => {
      console.log(`Tarefa ${job.id} completada com resultado:`, result);
    });

    this.queue.on('failed', (job: Queue.Job<Produto>, err: Error) => {
      console.error(`Tarefa ${job.id} falhou com erro:`, err);
    });
  }

  public adicionarProduto(produto: Produto): Promise<Queue.Job<Produto>> {
    return this.queue.add(produto, { delay: 200 });
  }

  // Processador de tarefas
  private async processarTarefa(job: Queue.Job<Produto>, done: any) {
    // Aqui você implementa a lógica de processamento do produto
    const produto: Produto = job.data; // Declaração explícita da variável produto
    console.log('Processando produto:', produto);
    ExecutorAsync.execute( async(callback: Function) => {
      console.log('Produto:', produto);
      //seta empresa no contexto
      require('domain').active.contexto.idEmpresa = 403;
      require('domain').active.contexto.empresa = {id : 403};

      await ProdutoEmbeddings.facaEmbeddingDoProduto(produto);

      console.log('Produto processado:', produto);
      done();
    }, (erro: Error) => {
      done(erro);
    }, 0);

    // Implemente a lógica de processamento aqui
    // Retorne o resultado do processamento
    return 'resultado';
  }
}
