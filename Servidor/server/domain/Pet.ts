import {Contato} from "./Contato";
import {Empresa} from "./Empresa";

export class Pet {
  id: number;
  nome: string;
  genero: string;
  tipo: string;
  dataNascimento: Date;
  contato: Contato;
  empresa: Empresa;


  constructor(id: number, nome: string, tipo: string, genero: string, dataNascimento: any,  contato: Contato) {
    this.id = id;
    this.nome = nome;
    this.tipo = tipo;
    this.genero = genero;
    this.contato = contato;
    this.empresa = contato ? contato.empresa : null;
    if ( dataNascimento)
      this.dataNascimento = new Date(dataNascimento);
  }
}
