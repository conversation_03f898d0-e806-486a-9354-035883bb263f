
import {ObjetoPersistente} from "../ObjetoPersistente";
import {IngredienteDaReceita} from "./IngredienteDaReceita";
import {MapeadorDeReceita} from "../../mapeadores/MapeadorDeFichaTecnica";

export class <PERSON>ceita extends ObjetoPersistente{
  public id: number;
  public rendimento: number;
  public ingredientes: Array<IngredienteDaReceita> = [];

  constructor() {
    super()
  }



  async valide(contexto: any = null, catalogo: any = null): Promise<any> {
    return new Promise<string>( async (resolve, reject) => {
      if(!this.ingredientes.length)
        return reject('Informe pelo menos 1 ingrediente')

      let erro: string;

      this.ingredientes.forEach((ingrediente: IngredienteDaReceita ) => {
         if(!ingrediente.insumo){
           erro = 'Nenhum insumo vinculado ao ingrediente da receita'
         } else if(!ingrediente.quantidade){
           erro =  `Quantidade do ingrediente "${ingrediente.insumo.nome}" invalido: ${ingrediente.quantidade}`
         }
      })

      if(erro) return reject(erro)

      return resolve(null)
    })
  }

  mapeador(): any {
    return new MapeadorDeReceita();
  }


}
