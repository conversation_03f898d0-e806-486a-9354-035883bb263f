import {MapeadorDeNotificacaoPedido} from "../../mapeadores/MapeadorDeNotificacaoPedido";
import * as uuid from "uuid";
import {MapeadorDePedidoIntegrado} from "../../mapeadores/MapeadorDePedidoIntegrado";
import {NotificacaoSistemaExterno} from "./NotificacaoSistemaExterno";

export class NotificacaoPedido extends NotificacaoSistemaExterno{
  id: string;
  horarioNotificado: Date;
  constructor(pedido: any, public origem: string, public codigo: string,
              public status: string, public  dados: any) {
    super(pedido)
    this.id =     uuid();
    this.horarioNotificado = new Date();
  }

  mapeador(): any {
    return new MapeadorDeNotificacaoPedido();
  }

  async obtenhaPedido(){
    if(this.dados && this.dados.pedido) return this.dados.pedido

    let pedido = await  new MapeadorDePedidoIntegrado().selecioneSync({ idEmpresa: this.empresaId, id: this.pedidoId});

    return pedido;
  }

  async salve(graph: boolean = false) {
    this.dados = JSON.stringify(this.dados);
    await       super.insira();

  }

}
