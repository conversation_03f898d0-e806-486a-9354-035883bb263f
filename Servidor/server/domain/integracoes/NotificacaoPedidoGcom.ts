import {NotificacaoPedido} from "./NotificacaoPedido";

export class NotificacaoPedidoGcom extends NotificacaoPedido {
  //{“orderId”: “1234567890MCI”, “statusId” : 1, “rede”:24, “loja”: 5 }
  constructor(pedido: any, dados: any = {}) {
    super(pedido, 'gcom' , dados.orderId, dados.status, dados)
  }

  obtenhaCupomFiscal(){
    let dados = this.getDados();

    return dados.numCupomFiscal != null ?  dados.numCupomFiscal : null;
  }

}
