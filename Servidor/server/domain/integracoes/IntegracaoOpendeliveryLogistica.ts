import {ObjetoPersistente} from "../ObjetoPersistente";
import {OpenDeliveryLogisticService} from "../../service/integracoes/OpenDeliveryLogisticService";
import {MapeadorIntegracaoOpenDeliveryLogistica} from "../../mapeadores/MapeadorIntegracaoOpenDeliveryLogistica";
import {EnumStatusPedido} from "../../lib/emun/EnumStatusPedido";

export class IntegracaoOpendeliveryLogistica extends ObjetoPersistente{
  public empresa: any;
  public horarioCadastro: Date;
  public horarioAtivacao: Date;
  public ativa: boolean;

  public authUrl: string;
  public baseUrl: string;
  public clientId: string;
  public clientSecret: string;
  public merchantId: string;
  public appid: string;
  public token: string;
  public tokenDataExpiracao: Date;
  public notificarRetirada: boolean;
  public notificarConclusao: boolean;
  public retornarNaLoja: boolean;
  public veiculoPadrao: any;
  public tempoLimiteRetirada: number;
  public tempoLimiteEntrega: number;
  public formasDePagamento: any = [];
  public automatico: boolean;
  public  naoEnviarLocalizacao: boolean;
  constructor( public operador: any = null) {
    super();
    this.horarioCadastro = new Date();
    this.ativa = true;
    this.automatico = false;
  }

  async salve(graph: boolean = false): Promise<void> {
     this.veiculoPadrao = JSON.stringify(this.veiculoPadrao)
    return super.salve(graph);
  }

  notificarNovo(pedido: any){
    if(pedido.deliveryPedido){
      console.log('Já foi notificado auto open delivery:' + pedido.deliveryPedido.id)
      return  false
    }

    if(this.automatico && pedido.ehDelivery())
      return Number(pedido.status) === EnumStatusPedido.EmPreparacao;

    return false;
  }

  ative(){
    this.ativa = true;
    this.mapeador().atualizeAtiva(this)
  }

  desative(){
    this.ativa = false;
    this.mapeador().atualizeAtiva(this)
  }

  atualizeToken(){
    return this.mapeador().atualizeToken(this)
  }

  getVeiculoPadrao(){

    let veiculo: any =  Object.assign({}, JSON.parse(this.veiculoPadrao));

    if(veiculo.types){ // todo: retirar if assim que corrigir o cadastro
      veiculo.type = veiculo.types
      delete veiculo.types;
    }

    veiculo.instruction = ""

    return veiculo
  }

  setToken(token: string, dataExpiracao: Date){
    this.token = token;
    this.tokenDataExpiracao = dataExpiracao;
  }

  tokenValido(){
    return this.token &&  (this.tokenDataExpiracao && new Date(this.tokenDataExpiracao).getTime() > new Date().getTime())
  }

  mapeador(): any {
    return new MapeadorIntegracaoOpenDeliveryLogistica();
  }

  obtenhaService(): OpenDeliveryLogisticService{
    return new OpenDeliveryLogisticService(this);
  }



}
