import {NotificacaoMesa} from "./NotificacaoMesa";

export class NotificacaoMesaCancelada extends NotificacaoMesa{
  constructor(public empresa: any = null,  numero: string = null,   public operacao: string = null,
              dados: any = null) {
    super(empresa, numero, operacao, dados);
    this.comando  = 'cancelar';
  }

  async valide(contexto: any = null, catalogo: any = null): Promise<any> {
    if(!this.operacao)
      return Promise.resolve('tipo de operacao não informado')

    if(!this.numero)
      return  Promise.resolve(`numero da ${this.operacao} não informado`);

    let dados =  this.getDados();

    if(!dados.rede || !dados.loja)
      return  Promise.resolve('Rede ou Loja não informado')

    if(typeof this.dados !== 'string')
      this.dados = JSON.stringify(this.dados)

    return Promise.resolve();

  }

  getMotivo() {
    let dados: any = this.getDados();

    let motivo = dados.motivo || ''

    return `Comanda cancelada pela parceiro: ${motivo}`;
  }

  carregarPedidos(){
    return true;
  }
}
