import {RespostaFazEntregaKml} from './../../lib/taxaDeEntrega/RespostaFazEntregaKML';
import {Localizacao} from './../../utils/Localizacao';
import {Alcance} from './Alcance';
import {RaioDeCobranca} from "./RaioDeCobranca";
import {FormaDeEntrega} from "./FormaDeEntrega";
import {Empresa} from "../Empresa";
import {Endereco} from "./Endereco";
import {TomtomService} from "../../service/TomtomService";
import {ZonaDeEntrega} from "./ZonaDeEntrega";
import {EnumTipoDeCobranca} from "./EnumTipoDeCobranca";
import * as _ from 'underscore';
import * as moment from 'moment';
import {Cidade} from "./Cidade";
import * as geolib from 'geolib';
import {TaxaDeEntregaCalculada} from './TaxaDeEntregaCalculada';
import {TurfService} from "../../service/TurfService";
import {FormatadorUtils} from "../../lib/FormatadorUtils";
import {OpenDeliveryLogisticService} from "../../service/integracoes/OpenDeliveryLogisticService";
import {UberDirectService} from "../../service/integracoes/UberDirectService";

export class FormaDeEntregaEmpresa {
  public static MSG_NAO_ENTREGA = 'Que pena! Ainda não entregamos na sua região, mas estamos trabalhando para expandir nosso atendimento.';
  public static MSG_LOCALIZACAO_NAO_ENCONTRADA = 'Não conseguimos encontrar sua localização. Verifique seu endereço e tente novamente.';
  id: number;
  empresa: any;
  formaDeEntrega: FormaDeEntrega
  permiteAgendamento: boolean
  agendamentoObrigatorio: boolean;
  intervaloAgendamento: number;
  tempoMinimo: number;
  tempoMaximo: number;
  tempoMinimoRetirada: number;
  tempoMaximoRetirada: number;
  valorMinimoFreteGratis: number;
  valorMinimoPedido: number;
  valorMaximoPedido: number;
  raiosDeCobranca: Array<RaioDeCobranca> = [];
  zonasDeEntrega: Array<ZonaDeEntrega> = [];
  alcances: Array<Alcance> = [];
  ativa: boolean;
  selecionarBairroDaZona: boolean;
  tipoDeCobranca: EnumTipoDeCobranca;
  bairroOpcional = false;
  cidadesQueEntrega: Array<Cidade> = [];
  arquivoKML = '';
  arquivoGeoJson = '';
  agendamentoLimiteMinimo: number;
  agendamentoLimiteMaximo: number;
  limitePedidosAgendados: number;
  perguntarEnderecoInicio: boolean;
  naoPerguntarHorario: boolean;
  permiteComerNoLocal: boolean;
  bloquearBairroAposCEP: boolean;
  taxaFixa: number;
  arredondarDistancias: boolean;
  priorizarLocalizacao: boolean;
  cepObrigatorio: boolean;
  naoUsarCidadePadrao = false;
  naoEnviarBairro = false;
  taxaExtraRetorno: number;
  constructor() {
    this.selecionarBairroDaZona = false;
  }

  ehParaRetirar(){
    return this.formaDeEntrega.id === FormaDeEntrega.RETIRADA;
  }

  ehParaDelivery(){
    return this.formaDeEntrega.id === FormaDeEntrega.ENTREGA;
  }

  cobrancaPorZona(){
    return this.tipoDeCobranca === EnumTipoDeCobranca.POR_ZONA
  }

  obtenhaErroLimiteAgendamento(dataAgendamento: Date, empresa: Empresa){

    if(this.agendamentoLimiteMinimo){
      let agora = moment();

      if(this.naoPerguntarHorario)
        agora = moment().startOf('day');

      if(moment(dataAgendamento).diff(agora, 'h') < this.agendamentoLimiteMinimo)
        return String(`Agendamento deve ser programados com no mínimo ${this.agendamentoLimiteMinimo}hs de antecedência`);
    }


    if(this.agendamentoLimiteMaximo ){
      let dataMaxima = moment().add(this.agendamentoLimiteMaximo, 'h');

      if(moment(dataAgendamento).diff(moment(), 'h') >  this.agendamentoLimiteMaximo)
        return String(`Aceitamos agendamento até no máximo ${moment(dataMaxima).format('DD/MM/YYYY [às] HH:mm')}hs`);
    }

    if(empresa.obtenhaPausaProgramada(dataAgendamento))
      return String ("Não é possível agendar pois o estabelecimento estará numa pausa programada no dia "
        + moment(dataAgendamento).format('DD/MM/YYYY [às] HH:mm') + "hs")


    return  ;
  }

  obtenhaErroValorMaximoMinimo(valor: number){
    if(!this.temValorMinimo(valor))
      return 'As entregas são feitas somente para valores acima  de R$ ' +  FormatadorUtils.numeroParaCurrency(this.valorMinimoPedido);

    if(this.ultrapassouValorMaximo(valor))
     return 'As entregas são feitas somente para valores abaixo de ' +  FormatadorUtils.numeroParaCurrency(this.valorMaximoPedido);

    return null;
  }


  calcule(empresa: Empresa, endereco: Endereco, valor: number, zonaDeEntrega: any = null): Promise<TaxaDeEntregaCalculada> {
    let resposta: any = { taxaDeEntrega: 0.0, localizacao:   ''};

    console.log('[FormaDeEntregaEmpresa] Calculando taxa de entrega: ' + empresa.id + ' ' + endereco.obtenhaEnderecoTomtom());

    return new Promise( async (resolve, reject) => {
      if(!this.temValorMinimo(valor)) {
        return resolve(TaxaDeEntregaCalculada.erro(this.obtenhaErroValorMaximoMinimo(valor),
          this.tipoDeCobranca, 0, endereco));
      }

      if(this.ultrapassouValorMaximo(valor)){
        return resolve(TaxaDeEntregaCalculada.erro(this.obtenhaErroValorMaximoMinimo(valor),
          this.tipoDeCobranca, 0, endereco));
      }

      if( this.ehParaRetirar() )
        return resolve(TaxaDeEntregaCalculada.retirada());

      const atingiuValorFreteGratis = this.conseguiuFreteGratisPorValor(valor);

      const tomtomService = new TomtomService();

      let enderecoEncontrado: any = null;
      const apiExterna = this.tipoDeCobranca === EnumTipoDeCobranca.POR_APIOPENDELIVERY ||
        this.tipoDeCobranca === EnumTipoDeCobranca.POR_APIUBER ;
      const calcularLocalizacao = !apiExterna || (empresa.integracaoOpendeliveryLogistica &&
        !empresa.integracaoOpendeliveryLogistica.naoEnviarLocalizacao);

      if( this.tipoDeCobranca !== EnumTipoDeCobranca.POR_ZONA && this.tipoDeCobranca !== EnumTipoDeCobranca.POR_VALOR_FIXO) {

        if (!endereco.localizacao) {
          if(calcularLocalizacao){
            enderecoEncontrado = await tomtomService.calculeCoordenadas(empresa, endereco);

            if (enderecoEncontrado)
              endereco.localizacao = enderecoEncontrado.localizacao.obtenhaString();
          }
        } else {
          enderecoEncontrado = Object.assign(Endereco.novo(), endereco);
          enderecoEncontrado.localizacao = Localizacao.fromString(endereco.localizacao);
        }
      }


       if(apiExterna){
        if( !enderecoEncontrado && calcularLocalizacao) {
          return resolve(TaxaDeEntregaCalculada.erro(FormaDeEntregaEmpresa.MSG_LOCALIZACAO_NAO_ENCONTRADA,
            this.tipoDeCobranca, 0, endereco));
        }

        if(this.possuiKML() && enderecoEncontrado ){
          const respostaFazEntrega: RespostaFazEntregaKml =
            await new TurfService().fazEntrega(empresa, this, enderecoEncontrado.localizacao);

          if( !respostaFazEntrega.sucesso )
            return resolve(TaxaDeEntregaCalculada.erro(respostaFazEntrega.erro, this.tipoDeCobranca, 0, endereco));

        }

        if(this.tipoDeCobranca === EnumTipoDeCobranca.POR_APIOPENDELIVERY ){
          if( atingiuValorFreteGratis )
            return resolve(TaxaDeEntregaCalculada.freteGratis(this.tipoDeCobranca, endereco));

          let service: OpenDeliveryLogisticService = empresa.integracaoOpendeliveryLogistica.obtenhaService();

          let deliveryPrice: any = await service.simuleEntrega(valor, endereco, empresa).catch((err) => {
            let erro = err && err.message ?  err.message : err;
            resolve(TaxaDeEntregaCalculada.erro(erro, this.tipoDeCobranca, 0, endereco))
          });

          if(deliveryPrice){
            console.log(deliveryPrice)
            let precoCalculado: number = deliveryPrice.price.value;

            resolve(TaxaDeEntregaCalculada.entrega(this.tipoDeCobranca, precoCalculado, this.taxaExtraRetorno, 0.0, endereco, 'api-opendelivery'));
          }
        } else if(this.tipoDeCobranca === EnumTipoDeCobranca.POR_APIUBER){
          if( atingiuValorFreteGratis )
            return resolve(TaxaDeEntregaCalculada.freteGratis(this.tipoDeCobranca, endereco));

          let service: UberDirectService = empresa.integracaoUberdirect.obtenhaService(empresa);

          let deliveryPrice: any =  await service.simuleEntrega(valor, endereco, empresa).catch((err: any) => {
            let erro = err && err.message ?  err.message : err;
            resolve(TaxaDeEntregaCalculada.erro(erro, this.tipoDeCobranca, 0, endereco))
          });

          if(deliveryPrice){
            console.log(deliveryPrice)
            let precoCalculado: number = deliveryPrice.fee / 100;

            resolve(TaxaDeEntregaCalculada.entrega(this.tipoDeCobranca, precoCalculado, this.taxaExtraRetorno,
              0.0, endereco, 'api-uber', deliveryPrice.id));
          }
         }
        return;
      }

      if( this.possuiKML() && this.tipoDeCobranca !== EnumTipoDeCobranca.POR_ZONA ) {
        if( !enderecoEncontrado ) {
          return resolve(TaxaDeEntregaCalculada.erro(FormaDeEntregaEmpresa.MSG_LOCALIZACAO_NAO_ENCONTRADA,
            this.tipoDeCobranca, 0, endereco));
        }

        const respostaFazEntrega: RespostaFazEntregaKml =
          await new TurfService().fazEntrega(empresa, this, enderecoEncontrado.localizacao);
        if( !respostaFazEntrega.sucesso )
          return resolve(TaxaDeEntregaCalculada.erro(respostaFazEntrega.erro, this.tipoDeCobranca, 0, endereco));


        if( atingiuValorFreteGratis )
          return resolve(TaxaDeEntregaCalculada.freteGratis(this.tipoDeCobranca, endereco));

        if( respostaFazEntrega.valor !== null && !isNaN(respostaFazEntrega.valor) )
          return resolve(TaxaDeEntregaCalculada.entrega(this.tipoDeCobranca, respostaFazEntrega.valor,
            this.taxaExtraRetorno, 0.0, endereco, 'kml'));
      }

      if(this.tipoDeCobranca === EnumTipoDeCobranca.POR_RAIO ) {
        const objResposta: TaxaDeEntregaCalculada = this.calculeTaxaEntregaPorAlcance(empresa,
          endereco, enderecoEncontrado.localizacao, atingiuValorFreteGratis);

        if( !objResposta.fazEntrega )
          return resolve(objResposta);

        return resolve(objResposta);
      }

      else if(this.tipoDeCobranca === EnumTipoDeCobranca.POR_ZONA) {
        if(!zonaDeEntrega || !zonaDeEntrega.id)
           return resolve(TaxaDeEntregaCalculada.erro('Zona de entrega não informada', this.tipoDeCobranca, 0, endereco))

        let zona = this.obtenhaZonaDeEntrega(zonaDeEntrega.id);

        if(!zona)
          return resolve(TaxaDeEntregaCalculada.erro('Zona de entrega inválida: ' + zonaDeEntrega.id,
            this.tipoDeCobranca, 0, endereco));

        if(zona.desativada)
          return resolve(TaxaDeEntregaCalculada.erro('Zona de entrega desativada temporariamente: ' + zonaDeEntrega.nome,
            this.tipoDeCobranca, 0, endereco));

        if(zona.permiteFreteGratis && atingiuValorFreteGratis ) {
          return resolve(TaxaDeEntregaCalculada.freteGratis(this.tipoDeCobranca, endereco));
        }

        resposta.taxaDeEntrega = zona.valor;
        endereco.zonaDeEntrega = zona;

        return resolve(TaxaDeEntregaCalculada.entrega(this.tipoDeCobranca, resposta.taxaDeEntrega, this.taxaExtraRetorno,
          0.0, endereco, 'zona'));
      }

      else if(this.tipoDeCobranca === EnumTipoDeCobranca.POR_VALOR_FIXO) {
        if( atingiuValorFreteGratis ) {
          return resolve(TaxaDeEntregaCalculada.freteGratis(this.tipoDeCobranca, endereco));
        }

        resposta.taxaDeEntrega = this.taxaFixa;

        return resolve(TaxaDeEntregaCalculada.entrega(this.tipoDeCobranca, resposta.taxaDeEntrega, this.taxaExtraRetorno,
          0.0, endereco, 'fixo'));
      }

      tomtomService.calculeDistancia(empresa, endereco).then( (respDistancia: any) => {
        if( this.arredondarDistancias ) {
          const dist = respDistancia.distancia;

          if( (dist + 0.5) > Math.ceil(dist) ) {
            respDistancia.distancia = Math.ceil(dist);
            console.log('#Nova distância após arredondamento: ' + respDistancia.distancia + "KM.");
          }
        }

        let raioDeCobranca: RaioDeCobranca = this.obtenhaRadioCobranca(respDistancia.distancia);

        if(!raioDeCobranca)
          return resolve(TaxaDeEntregaCalculada.erro(FormaDeEntregaEmpresa.MSG_NAO_ENTREGA, this.tipoDeCobranca,
            respDistancia.distancia, endereco));

        if(raioDeCobranca.valorMinimoPedido && raioDeCobranca.valorMinimoPedido > valor){
          let valorMinimo = raioDeCobranca.valorMinimoPedido.toFixed(2).replace('.', ',');
          return resolve(TaxaDeEntregaCalculada.erro('As entregas são feitas somente para valores acima  de R$ ' + valorMinimo,
            this.tipoDeCobranca, respDistancia.distancia, endereco));
        }

        if( atingiuValorFreteGratis && raioDeCobranca.permiteFreteGratis)
          return resolve(TaxaDeEntregaCalculada.freteGratis(this.tipoDeCobranca, endereco));

        console.log('**raio escolhido**')
        console.log(raioDeCobranca)
        resposta.localizacao = endereco.localizacao;
        resposta.taxaDeEntrega = raioDeCobranca.calculeTaxaEntrega(respDistancia.distancia)

        return resolve(TaxaDeEntregaCalculada.entrega(this.tipoDeCobranca, resposta.taxaDeEntrega, this.taxaExtraRetorno,
          respDistancia.distancia, endereco, respDistancia.calculadoPor || 'desconhecido'));


      }).catch((erro: Error) => {
        console.error(erro)
        let msgErro = erro  ? erro.message : "Nao foi possivel calcular taxa de entrega";
        resolve(TaxaDeEntregaCalculada.erro(msgErro, this.tipoDeCobranca, 0, endereco))
      });
    });
  }

  private temValorMinimo(valor: number){
    if(!this.valorMinimoPedido) return true;

    return   valor >= this.valorMinimoPedido
  }

  private ultrapassouValorMaximo(valor: number){
    if(!this.valorMaximoPedido) return false;

    return   valor > this.valorMaximoPedido
  }

  private conseguiuFreteGratisPorValor(valor: number) {
    return this.valorMinimoFreteGratis && this.valorMinimoFreteGratis && valor && valor >= this.valorMinimoFreteGratis;
  }

  public obtenhaRadioCobranca(distancia: any) {
     let raiosOrdenados = _.sortBy( this.obtenhaRaiosAtivos(), raio  => raio.alcance);

     return raiosOrdenados.find( radioCobrancao => radioCobrancao.alcance >= Number(distancia));
  }

  public obtenhaRaiosAtivos() {
    return this.raiosDeCobranca.filter((raio: any) => !raio.desativado);
  }

  private alcancesAtivos(){
    return this.alcances.filter((a: any) => !a.desativado);
  }

  // tslint:disable-next-line:member-ordering
  static novaDaCache(forma: any) {
    let formaDeEntregaEmpresa = new FormaDeEntregaEmpresa();
    Object.assign(formaDeEntregaEmpresa, forma);

    formaDeEntregaEmpresa.raiosDeCobranca = [];


    forma.raiosDeCobranca.forEach( (radioDeConbracao: any) => {
      let raioDeCobrancaForma = new RaioDeCobranca(null, null, null, null, null);

      Object.assign(raioDeCobrancaForma, radioDeConbracao);
      formaDeEntregaEmpresa.raiosDeCobranca.push(raioDeCobrancaForma)
    })

    return formaDeEntregaEmpresa;
  }


  obtenhaZonaDeEntrega(id: number): ZonaDeEntrega{
    return this.zonasDeEntrega.find( (zonaDeEngrega: any) => zonaDeEngrega.id === id);
  }

  obtenhaDescricao() {
    if(this.ehParaDelivery()) return 'Delivery'
    if(this.ehParaRetirar()) return 'Retirada'

    return this.formaDeEntrega.nome;
  }

  lojaEntrega(endereco: any): string {
    if( !endereco || this.cidadesQueEntrega.length === 0 ) {
      return '';
    }

    const cidadeExiste = this.cidadesQueEntrega.find( cidade => {
      return endereco.cidade.id === cidade.id;
    });

    if( !cidadeExiste ) {
      return `Que pena! Ainda não entregamos em ${endereco.cidade.nome}, mas estamos trabalhando para expandir nosso atendimento.`;
    }

    return '';
  }

  obtenhaUrlArquivoKML() {
    return `/images/empresa/kml/${this.arquivoKML}`;
  }

  obtenhaUrlArquivoGeoJson() {
    return `/images/empresa/kml/${this.arquivoGeoJson}`;
  }

  possuiKML(): boolean {
    return this.arquivoGeoJson ? true : false;
  }

  possuiAlcance(alcance: Alcance) {
    return this.alcancesAtivos().find( (objAlcance: Alcance) => {
      return objAlcance.alcance === alcance.alcance && alcance.id !== objAlcance.id;
    });
  }

  calculeDistanciaLinear(latitudeLongitude: string, posicao: Localizacao) {
    const latitude1 = latitudeLongitude.split(',')[0];
    const longitude1 = latitudeLongitude.split(',')[1];

    return geolib.getDistance( { latitude: latitude1, longitude: longitude1 },
      { latitude: posicao.latitude, longitude: posicao.longitude }) / 1000.0;
  }

  public obtenhaAlcance(distancia: any) {
    let alcancesOrdenados = _.sortBy( this.alcancesAtivos(), alcance  => alcance.alcance);

    return alcancesOrdenados.find( alcance => alcance.alcance >= Number(distancia));
 }

 calculeTaxaEntregaPorAlcance(empresa: Empresa, endereco: Endereco, posicao: Localizacao,
                              alcancouValorFreteGratis: boolean): TaxaDeEntregaCalculada {
   const distanciaEmKM = this.calculeDistanciaLinear(empresa.latitudeLongitude, posicao);



  if( this.alcances.length > 0 ) {
    const alcance: Alcance = this.obtenhaAlcance(distanciaEmKM);


    if(alcancouValorFreteGratis && alcance.permiteFreteGratis)
      return TaxaDeEntregaCalculada.freteGratis(this.tipoDeCobranca, endereco)

    if(!alcance) {
      return TaxaDeEntregaCalculada.naoEntrega(this.tipoDeCobranca, distanciaEmKM, endereco);
    }



    console.log('**alcance escolhido**')
    console.log(alcance);

    return TaxaDeEntregaCalculada.entrega(this.tipoDeCobranca, alcance.taxa, this.taxaExtraRetorno, distanciaEmKM, endereco, 'linear');
  }

  return TaxaDeEntregaCalculada.erro("Raios de entrega não configurados", this.tipoDeCobranca,
    distanciaEmKM, endereco);
 }

 async fazEntregaGps(empresa: Empresa, localizacao: Localizacao): Promise<RespostaFazEntregaKml> {
    return new Promise( async (resolve, reject) => {
      let distancia = 0;

      if( this.possuiKML() ) {
        const turfService = new TurfService();
        const resposta: RespostaFazEntregaKml =
          await turfService.fazEntrega(empresa, empresa.obtenhaFormaReceberEmCasa(), localizacao);

        distancia = this.calculeDistanciaLinear(
          `${localizacao.latitude}, ${localizacao.longitude}`, empresa.obtenhaLocalizacaoEmpresa());

        resposta.distancia = distancia;

        return resolve(resposta);
      }

      distancia = this.calculeDistanciaLinear(
        `${localizacao.latitude}, ${localizacao.longitude}`, empresa.obtenhaLocalizacaoEmpresa());

      let raioDeCobranca = null;
      if( this.tipoDeCobranca === EnumTipoDeCobranca.POR_DISTANCIA ) {
        raioDeCobranca = this.obtenhaRadioCobranca(distancia);
      } else {
        raioDeCobranca = this.obtenhaAlcance(distancia);
      }

      if( !raioDeCobranca ) {
        return resolve(RespostaFazEntregaKml.erro(
          "Ainda não entregamos na sua região, mas você pode escolher uma loja para retirar seu pedido."));
      }

      return resolve(RespostaFazEntregaKml.entrega(null, distancia));
    });
 }

 valideTempoMiminimoConfigurado(){
   if(this.tempoMinimo || this.tempoMinimoRetirada ) return


   throw Error(String(`Configure na loja o tempo previsão de entrega/retira para forma de entrega: ` + this.formaDeEntrega.nome))

 }

  obtenhaHorarioPrevisaoEntrega(horarioFoiFeito: Date){
    let tempoMedioMinutos: number;

    if(this.tempoMinimo){
      tempoMedioMinutos = (this.tempoMinimo + this.tempoMaximo ) / 2;
    } else if (this.tempoMinimoRetirada ){
      tempoMedioMinutos = (this.tempoMinimoRetirada + this.tempoMaximoRetirada ) / 2;
    }

    return tempoMedioMinutos ?  moment(horarioFoiFeito).add( tempoMedioMinutos, 'm').toDate() : null
  }

  obtenhaHararioPrevisaoPreparo(dataEntregaAgendada: Date){
    let tempoMedioMinutos: number;

    if(this.tempoMinimo){
      tempoMedioMinutos = (this.tempoMinimo + this.tempoMaximo ) / 2;
    } else if (this.tempoMinimoRetirada ){
      tempoMedioMinutos = (this.tempoMinimoRetirada + this.tempoMaximoRetirada ) / 2;
    }

    return tempoMedioMinutos ?  moment(dataEntregaAgendada).add( -tempoMedioMinutos, 'm').toDate() : null
  }
}
