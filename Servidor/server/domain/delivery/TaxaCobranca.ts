import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorDeTaxaCobranca} from "../../mapeadores/MapeadorDeTaxaCobranca";

export class TaxaCobranca extends ObjetoPersistente{
  constructor(public id: number, public percentual: number,
              public valor: number, public  ativa: boolean){
    super()
  }

  calcule(valorPedido: number){
    if(!this.ativa) return 0;

    if(this.percentual)
      return  Number(( (this.percentual / 100) * valorPedido).toFixed(2))

    return this.valor;
  }

  mapeador(): MapeadorDeTaxaCobranca {
    return new MapeadorDeTaxaCobranca();
  }
}
