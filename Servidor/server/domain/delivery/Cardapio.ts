import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorDeCardapio} from "../../mapeadores/MapeadorDeCardapio";
import {Empresa} from "../Empresa";

export class Cardapio extends ObjetoPersistente {
  arquivo: string;
  exibirSelecaoCategorias: boolean;
  constructor( public empresa: any, public botAtivo: boolean = false,
              public modoVisualizacao: boolean = false, public modoTesteBot: boolean = false,
               public modoVisualizacaoQRcode: boolean = false,
              public exibirIndisponiveis: boolean = false, public limiteProdutos: number = null,
               public exibirProdutosValorZeradoMesa: boolean = false) {
    super();
  }

  async valide(): Promise<any> {
    return new Promise<any>( (resolve, reject) => {
      if( !this.arquivo ) {
        return resolve('Arquivo não informado.')
      }

      resolve('');
    })
  }

  mapeador(): any {
    return new MapeadorDeCardapio();
  }

  public obtenhaUrlImagem(empresa: Empresa) {
    return '/images/empresa/' + this.arquivo;
  }
}
