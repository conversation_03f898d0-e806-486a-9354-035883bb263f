import {Estado} from "./Estado";
import {MapeadorDeCidade} from "../../mapeadores/MapeadorDeCidade";

export class Cidade {
  id: Number;
  nome: string;
  estado: Estado;
  codigoNfse: string;
  codigoIbge: string;

  constructor() {
  }

  static async obtenhaCidade(nomeCidade: string, nomeEstado: string){
    return new Promise<any>(   async (resolve) => {
      if(!nomeCidade || !nomeEstado) return resolve(null);

      let cidade = await new MapeadorDeCidade().selecioneSync({nome: nomeCidade.replace(/\s+/mg, ' '), estadoTexto: nomeEstado})
      resolve(cidade)
    });
  }
}
