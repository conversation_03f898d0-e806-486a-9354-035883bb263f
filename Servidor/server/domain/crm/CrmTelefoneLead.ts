export enum TipoTelefoneLead {
  TelefoneFixo = 'Telefone Fixo',
  Celular = 'Celular',
  WhatsApp = 'WhatsApp',
  Comercial = 'Comercial',
  Emergencia = 'Emergência'
}

export class CrmTelefoneLead {
  id?: number;
  crmLeadId: number;
  tipo: TipoTelefoneLead;
  numero: string;
  descricao?: string;
  ativo: boolean = true;
  ordem: number = 0;
  createdAt?: Date;
  updatedAt?: Date;

  constructor(
    crmLeadId: number,
    tipo: TipoTelefoneLead,
    numero: string,
    descricao?: string,
    ordem: number = 0
  ) {
    this.crmLeadId = crmLeadId;
    this.tipo = tipo;
    this.numero = numero;
    this.descricao = descricao;
    this.ordem = ordem;
  }

  // Validações
  isValid(): boolean {
    return !!(this.crmLeadId && this.tipo && this.numero?.trim());
  }

  // Formatação do número
  getNumeroFormatado(): string {
    if (!this.numero) return '';
    
    const numeroLimpo = this.numero.replace(/\D/g, '');
    
    if (numeroLimpo.length === 11) {
      // Celular: (XX) 9XXXX-XXXX
      return numeroLimpo.replace(/^(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
    } else if (numeroLimpo.length === 10) {
      // Fixo: (XX) XXXX-XXXX
      return numeroLimpo.replace(/^(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
    }
    
    return this.numero;
  }

  // Link do WhatsApp (se for tipo WhatsApp)
  getWhatsAppUrl(): string {
    if (this.tipo !== TipoTelefoneLead.WhatsApp && this.tipo !== TipoTelefoneLead.Celular) {
      return '';
    }
    
    const numeroLimpo = this.numero.replace(/\D/g, '');
    if (numeroLimpo.length >= 10) {
      return `https://wa.me/55${numeroLimpo}`;
    }
    
    return '';
  }

  // Ícone baseado no tipo
  getIcone(): string {
    const icones = {
      'Telefone Fixo': 'fa-phone',
      'Celular': 'fa-mobile-alt',
      'WhatsApp': 'fa-whatsapp',
      'Comercial': 'fa-briefcase',
      'Emergência': 'fa-exclamation-triangle'
    };
    return icones[this.tipo] || 'fa-phone';
  }

  // Cor baseada no tipo
  getCor(): string {
    const cores = {
      'Telefone Fixo': '#6c757d',
      'Celular': '#007bff',
      'WhatsApp': '#25d366',
      'Comercial': '#28a745',
      'Emergência': '#dc3545'
    };
    return cores[this.tipo] || '#6c757d';
  }

  // Texto de exibição
  getTextoExibicao(): string {
    if (this.descricao) return this.descricao;
    
    switch (this.tipo) {
      case TipoTelefoneLead.WhatsApp:
        return 'WhatsApp';
      case TipoTelefoneLead.TelefoneFixo:
        return 'Telefone Fixo';
      case TipoTelefoneLead.Celular:
        return 'Celular';
      case TipoTelefoneLead.Comercial:
        return 'Comercial';
      case TipoTelefoneLead.Emergencia:
        return 'Emergência';
      default:
        return this.tipo;
    }
  }

  // Verifica se é um número de WhatsApp
  isWhatsApp(): boolean {
    return this.tipo === TipoTelefoneLead.WhatsApp;
  }

  // Verifica se é um número de celular (pode ser usado para WhatsApp)
  isCelular(): boolean {
    return this.tipo === TipoTelefoneLead.Celular || this.tipo === TipoTelefoneLead.WhatsApp;
  }

  // Verifica se é um número comercial
  isComercial(): boolean {
    return this.tipo === TipoTelefoneLead.Comercial || this.tipo === TipoTelefoneLead.TelefoneFixo;
  }

  // Detecta automaticamente o tipo baseado no número
  static detectarTipo(numero: string, contexto?: string): TipoTelefoneLead {
    if (!numero) return TipoTelefoneLead.Celular;
    
    const numeroLimpo = numero.replace(/\D/g, '');
    const contextoLower = contexto?.toLowerCase() || '';
    
    // Verifica contexto primeiro
    if (contextoLower.includes('whats') || contextoLower.includes('zap') || contextoLower.includes('wa:')) {
      return TipoTelefoneLead.WhatsApp;
    }
    
    if (contextoLower.includes('comercial') || contextoLower.includes('escritório')) {
      return TipoTelefoneLead.Comercial;
    }
    
    if (contextoLower.includes('emergência') || contextoLower.includes('urgência')) {
      return TipoTelefoneLead.Emergencia;
    }
    
    if (contextoLower.includes('fixo') || contextoLower.includes('tel:')) {
      return TipoTelefoneLead.TelefoneFixo;
    }
    
    // Detecta por formato do número
    if (numeroLimpo.length === 11 && numeroLimpo.charAt(2) === '9') {
      // Celular (11 dígitos com 9 na terceira posição)
      return TipoTelefoneLead.Celular;
    } else if (numeroLimpo.length === 10) {
      // Telefone fixo (10 dígitos)
      return TipoTelefoneLead.TelefoneFixo;
    }
    
    return TipoTelefoneLead.Celular; // Padrão
  }

  // Limpa e padroniza o número
  static limparNumero(numero: string): string {
    if (!numero) return '';
    return numero.replace(/\D/g, '');
  }
}