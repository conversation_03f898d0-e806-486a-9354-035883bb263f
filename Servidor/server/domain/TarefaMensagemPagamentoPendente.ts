import {ObjetoPersistente} from "./ObjetoPersistente";
import {MapeadorTarefaMensagemPagamentoPendente} from "../mapeadores/MapeadorTarefaMensagemPagamentoPendente";

export class TarefaMensagemPagamentoPendente extends ObjetoPersistente{
  public horarioCriacao: Date;
  public horarioVencimento: Date;
  public executada: boolean;
  public guidPedido: string;
  public contato: any;
  public empresa: any;
  constructor(public pagamento: any, pedido: any, ) {
    super();
    if(pedido){
      this.guidPedido = pedido.guid;
      this.contato = pedido.contato;
      this.empresa = pedido.empresa;
      this.horarioCriacao = new Date();
      //10min vence o tempo pagar e tem que notificar
      this.horarioVencimento = new Date(this.horarioCriacao.getTime() + 10 * 60 * 1000);
    }
    this.executada = false;
  }

  mapeador(): any {
    return new MapeadorTarefaMensagemPagamentoPendente();
  }
}
