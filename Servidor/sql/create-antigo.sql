create table if not exists empresa (
    id bigint(20) not null auto_increment,
    dominio varchar(255) not null,
    nome varchar(255) not null,
    endereco varchar(255) not null,
    ativar_indicacoes bit(1) not null default 0,
    whatsapp varchar(255) not null,
    descricao varchar(255) not null,
    instagram varchar(255) null,
    titulo_fotos varchar(50) not null,
    titulo_destaques varchar(50) not null,
    link_maps varchar(500) not null,
    qtde_mensagens bigint(20) default 200,
    meio_de_envio varchar(255) not null,
    primary key (id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table campanha(
  id bigint(20) not null auto_increment,
  nome varchar(255) not null,
  mensagem text not null,
  data_criacao datetime not null,
  empresa_id bigint(20) not null,
  filtro_id bigint(20) not null,
  qtde_enviadas bigint(20),
  qtde_lidas bigint(20),
  tipo_de_envio varchar(255) not null default 'Unico',
  horario_envio datetime null,
  status varchar(255) not null,
  qtde_dias_nova_notificacao bigint(20) not null default 1,
  foi_testada bit(1) null default 0,
  primary key (id)
);

create table contato_empresa (
  id bigint(20) not null auto_increment,
  nome varchar(255) not null,
  email varchar(255) not null,
  empresa varchar(255) not null,
  telefone varchar(255) not null,
  primary key (id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


create table link_encurtado (
  id bigint(20) not null auto_increment,
  token varchar(3) not null,
  url varchar(500) not null,
  visitas bigint(20) not null,
  data_criacao datetime not null,
  ultima_visita datetime,
  primary key(id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table if not exists notificacao (
    id bigint(20) not null auto_increment,
    ativada bigint(1) not null,
    mensagem varchar(255) not null,
    empresa_id bigint(20) not null,
    tipo_de_notificacao varchar(255) not null,
    pode_desativar bigint(1) not null,
    qtde_dias_nova_notificacao bigint(20) not null default 1,
    primary key (id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table if not exists mensagem_enviada (
                                   id bigint(20) not null auto_increment,
                                   mensagem varchar(255) not null,
                                   horario datetime not null,
                                   tipo_de_notificacao varchar(255) not null,
                                   status varchar(255) not null,
                                   telefone varchar(255) not null,
                                   contato_id bigint(20) not null,
                                   empresa_id bigint(20) not null,
                                   campanha_id bigint(20)   null,
                                   horario_modificacao datetime not null,
                                   id_sms_externo varchar(50),
                                   meio_de_envio varchar(255) not null,
                                   primary key (id),
                                   foreign key(campanha_id) references campanha(id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table if not exists usuario (
  id bigint(20) not null auto_increment,
  email varchar(255) not null,
  nome varchar(255) not null,
  senha varchar(255) not null,
  ultimo_login datetime not null,
  empresa_id bigint(20) not null,
  ativo bit(1) not null,
  admin bit(1) not null,
  primary key (id),
  foreign key(empresa_id) references empresa(id),
  unique(email,empresa_id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


create table if not exists contato (
  id bigint(20) not null auto_increment,
  nome varchar(255),
  telefone varchar(30),
  sexo varchar(20),
  data_nascimento date null,
  ultima_visita datetime not null,
  quem_indicou_id bigint(20) null,
  empresa_id bigint(20) not null,
  status int unsigned not null default  0,
  token varchar(5) not null,
  data_ativacao datetime  null ,
  data_cadastro datetime  null,
  primary key (id),
  unique key(empresa_id, telefone),
  foreign key(empresa_id) references empresa(id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


create table if not exists tipo_de_pontuacao (
    id bigint(20) not null auto_increment,
    tipo varchar(255) not null,
    valor_por_ponto decimal(9, 3),
    selos_por_atividade bigint(10),
    empresa_id bigint(20) not null, primary key(id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table if not exists plano(
  id bigint(20) not null auto_increment,
  nome varchar(255),
  id_tipo_de_pontuacao bigint(20) not null,
  empresa_id bigint(20) not null,
  tipo_de_acumulo varchar(255) not null,
  primary key (id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

Create table if not exists atividade(
  id bigint(20) not null auto_increment,
  nome varchar(255),
  valor DECIMAL(9,2),
  empresa_id bigint(20) not null,
  plano_id bigint(20) not null,
  primary key (id),
    foreign key(empresa_id) references empresa(id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table if not exists cartao(
    id bigint(20) not null auto_increment,
    pontos decimal(9, 3),
    empresa_id bigint(20) not null,
    contato_id bigint(20) not null,
    plano_id bigint(20) not null,
    codigo_temp varchar(10) null,
    primary key (id),
    foreign key(empresa_id) references empresa(id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table if not exists brinde(
    id bigint(20) not null auto_increment,
    nome varchar(255),
    valor_em_pontos bigint(20),
    empresa_id bigint(20) not null,
    link_imagem varchar(255) not null,
    plano_id bigint(20),
    primary key (id),
      foreign key(empresa_id) references empresa(id),
      foreign key(plano_id) references plano(id)


)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table if not exists pontuacao_registrada(
    id bigint(20) not null auto_increment,
    valor decimal(9, 3) not null,
    cartao_id bigint(20) not null,
    atividade_id bigint(20) not null,
    horario datetime not null,
    pontos bigint(20) not null,
    empresa_id bigint(20) not null,
    primary key (id),
      foreign key(empresa_id) references empresa(id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table if not exists brinde_resgatado(
    id bigint(20) not null auto_increment,
    cartao_id bigint(20) not null,
    id_brinde bigint(20) not null,
    horario datetime not null,
    mensagem varchar(255) not null,
    valor_em_pontos bigint(20) not null,
    empresa_id bigint(20) not null,

    primary key (id),
    foreign key(empresa_id) references empresa(id)

) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table if not exists acao_contato (
    id bigint(20) not null auto_increment,
    contato_id bigint(20) not null,
    horario datetime not null,
    mensagem varchar(255) not null,
    tipo_de_acao varchar(255) not null,
    empresa_id bigint(20) not null,
    pontos int    null,
    primary key (id),
    foreign key(empresa_id) references empresa(id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table if not exists produto(
    id bigint(20) not null auto_increment,
    nome varchar(255) not null,
    descricao varchar(255) not null,
    mensagem_pedido varchar(255) not null,
    preco decimal(9,2) not null,
    link_imagem varchar(255) not null,
    empresa_id bigint(20) not null,

    primary key (id),
    foreign key(empresa_id) references empresa(id)

)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table if not exists foto(
    id bigint(20) not null auto_increment,
    descricao varchar(255) not null,
    titulo varchar(255) not null,
    link varchar(255) not null,
    empresa_id bigint(20) not null,
    primary key (id),
    foreign key(empresa_id) references empresa(id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table if not exists horario_funcionamento (
  id bigint(20) not null auto_increment,
  dia_da_semana bigint(1) not null,
  funciona bit(1) not null,
  horario_abertura int(2),
  horario_fechamento int(2),
  empresa_id bigint(20) not null,
  primary key(id),
   foreign key(empresa_id) references empresa(id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table if not exists links_mensagem_enviada (
    empresa_id bigint(20) not null,
    mensagem_enviada_id bigint(20) not null,
    link_encurtado_id varchar(255) not null,
    primary key (mensagem_enviada_id, link_encurtado_id),
    foreign key(empresa_id) references empresa(id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;



create table if not exists filtro(
  id bigint(20) not null auto_increment,
  nome varchar(255) not null,
  usuario_id bigint(20) not null,
  dados longtext not null,
  excluido bit(1) default 0,
   primary key(id),
     foreign key(usuario_id) references usuario(id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table if not exists codigo_verificacao(
  telefone varchar(255) not null,
  codigo varchar(50) not null,
  empresa_id bigint(20) not   null,
  primary key(telefone),
  foreign key(empresa_id) references empresa(id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


create table if not exists pet(
  id bigint(20) not null auto_increment,
  nome varchar(255) not null,
  tipo varchar(100)   not null,
  genero varchar(50)   null,
  data_nascimento date null,
  empresa_id bigint(20) not   null,
  contato_id bigint(20) not null     ,
  primary key(id),
  foreign key(contato_id) references contato(id),
  foreign key(empresa_id) references empresa(id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


create table if not exists pontuacao_registrada_atividade(
  pontuacao_registrada_id bigint(20) not null  ,
  atividade_id bigint(20) not null  ,
   primary key(pontuacao_registrada_id, atividade_id ),
    foreign key(pontuacao_registrada_id) references pontuacao_registrada(id),
    foreign key(atividade_id) references atividade(id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table if not exists regra_extra (
  id bigint(20) not null auto_increment,
  plano_id bigint(20) not null ,
  descricao varchar(255) not null,
  primary key(id),
  foreign key(plano_id) references plano(id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table if not exists view_contato (
  id bigint(20) not null auto_increment,
  status varchar(255), /* NOVO, VIP ... */
  qtde_dias_como_cliente bigint(20),
  qtde_visitas bigint(20),
  qtde_dias_nao_volta bigint(20),
  ticket_medio decimal(9, 3),
  total_gasto decimal(9, 3),
  tempo_para_retornar decimal(9, 3),
  empresa_id bigint(20) not null,
  contato_id bigint(20) not null,
  primary key (id),
  foreign key(empresa_id) references empresa(id),
  foreign key(contato_id) references contato(id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


alter table add empresa column id;

alter table mensagem_enviada add column campanha_id bigint(20) null;

alter table campanha add column tipo_de_envio varchar(255) not null default 'Unico';
alter table campanha add column horario_envio datetime null;
alter table campanha add column qtde_de_dias_nova_notificacao bigint(20) not null default -1;
alter table produto modify column preco decimal(9,2) null;

alter table horario_funcionamento  modify column horario_abertura time null;
alter table horario_funcionamento  modify column horario_fechamento time null;

alter table atividade add column    plano_id bigint(20) not null;
update atividade , plano, tipo_de_pontuacao  set atividade.plano_id = plano.id where atividade.empresa_id = plano.empresa_id and tipo_de_pontuacao.id = id_tipo_de_pontuacao;
alter table atividade add foreign key(plano_id) references plano(id);
alter table usuario add column operador bit(1) default false;

alter table empresa add column removida bit(1)   null;
alter table empresa add column data_remocao datetime null;
alter table empresa  modify column instagram varchar(255) null;
alter table empresa  modify column endereco varchar(255) null;

alter table tipo_de_pontuacao add column pontos_por_valor int default null;

alter table campanha add column ativa bit(1) null default 1;

alter table contato add column tipo varchar(100) default 'Pessoa';
alter table contato add column responsavel varchar(255) null;
ALTER TABLE cartao ADD FOREIGN KEY (contato_id) REFERENCES contato(id);

alter table contato drop column responsavel;
alter table contato drop column tipo;

alter table campanha add column foi_testada bit(1) null default 0;


update tipo_de_pontuacao set tipo = 'qtde-fixa' where tipo = 'qtd-por-atividade';
alter table atividade add column pontos_ganhos int default null;

alter table filtro add column empresa_id bigint(20) not null;
update filtro join usuario set filtro.empresa_id = usuario.empresa_id where filtro.usuario_id = usuario.id;
ALTER TABLE filtro ADD FOREIGN KEY (empresa_id) REFERENCES empresa(id);

update plano p join tipo_de_pontuacao  tp set tp.empresa_id = p.empresa_id where tp.id = id_tipo_de_pontuacao;
delete from  tipo_de_pontuacao where not  exists (select 1 from plano where id_tipo_de_pontuacao = tipo_de_pontuacao.id);

create index petnome on pet(nome);

insert into pontuacao_registrada_atividade(pontuacao_registrada_id, atividade_id) select  id,atividade_id from pontuacao_registrada;
alter table pontuacao_registrada  modify column atividade_id bigint(20) null;

alter table brinde add column artigo varchar(10) not null default 'um';
alter table plano add column validade int    null ;
alter table mensagem_enviada add column id_sms_externo varchar(50) null;
alter table mensagem_enviada add column meio_de_envio varchar(255) null default ' SMS';

update mensagem_enviada set status = 'ENVIADA' where status = 'ENVIANDO';

alter table empresa add column qtde_mensagens bigint(20) default 200;


alter table notificacao add column qtde_dias_ativa int null;
update notificacao set qtde_dias_ativa = 30 where tipo_de_notificacao = 'Sentimos Sua Falta';
create index contatoacao on acao_contato(contato_id);
update contato set status = 7 where status = 0 and exists (select 1 from acao_contato where contato_id = contato.id and tipo_de_acao = 5);
alter table plano add column excluido bit(1) null;
update notificacao set qtde_dias_ativa = 45 where tipo_de_notificacao = 'Cliente Perdido';
update notificacao set ativada = false where tipo_de_notificacao = 'Sentimos Sua Falta' or tipo_de_notificacao = 'Cliente Perdido';

alter table cartao add column desativado bit(1) null;


/* perdidos */
update contato c join cartao car on(car.contato_id = c.id) set c.status = 8 where c.empresa_id = 2 and token = 'imp' and car.pontos = 1;

/* em perigo */
update contato c set c.status = 6 where c.empresa_id = 2 and c.status <> 8  and c.ultima_visita < curdate() - INTERVAL 60 day;


create table if not exists plano_empresarial (
  id bigint(20) not null auto_increment,
  nome varchar(255) not null ,
  valor decimal(9,2) not null,
  limite_contatos int null,
  ativo bit(1) default null,
  limite_sms int null,
  recomendado bit(1) default false,
  primary key(id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table if not exists contrato (
  id bigint(20) not null auto_increment,
  plano_id  bigint(20) not null,
  empresa_id bigint(20) not null,
  dia_vencimento int not null,
  data_ativacao datetime null,
  dias_gratis int null,
  valor_negociado  decimal(9,2) null,
  encerrado bit(1) default false,
  data_encerramento date null,
  primary key(id),
  foreign key (plano_id) references plano_empresarial(id),
  foreign key (empresa_id) references empresa(id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


CREATE TABLE cartao_credito (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  numero varchar(255) NOT NULL,
  validade int(11) DEFAULT NULL,
  cpf varchar(255)   NULL,
  nome varchar(255)   NULL,
  token varchar(255)   NULL,
  empresa_id bigint(20)   NULL,
  data_nascimento varchar(255)   NULL,
  telefone varchar(255)   NULL,
  bandeira varchar(20) NOT NULL,
  hash varchar(255)   NULL,

  PRIMARY KEY (id),
  CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id),
  CONSTRAINT   FOREIGN KEY (contato_id) REFERENCES contato (id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE fatura (
  id bigint(20) NOT NULL auto_increment,
  contrato_id bigint(20) NOT NULL,
  referencia int(11) NOT NULL,
  status int(11) NOT NULL,
  data_vencimento date NOT NULL,
  data_pagamento datetime DEFAULT NULL,
  valor_pago decimal(9,2) DEFAULT NULL,
  observacao longtext,
  pagamento_id bigint(20) DEFAULT NULL,
  acerto bit(1) DEFAULT  null,
  fatura_acerto_id bigint(20) DEFAULT NULL,
  PRIMARY KEY (id),
  CONSTRAINT  FOREIGN KEY (contrato_id) REFERENCES contrato (id),
  CONSTRAINT  FOREIGN KEY (fatura_acerto_id) REFERENCES fatura (id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE pagamento (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  codigo varchar(100) DEFAULT NULL,
  status varchar(50) NOT NULL,
  tipo varchar(50) NOT NULL,
  fatura_id bigint(20) not null,
  horario datetime NOT NULL,
  ultima_atualizacao datetime DEFAULT NULL,
  cartao_credito_id bigint(20) DEFAULT NULL,
  url varchar(255) DEFAULT NULL,
  data_vencimento date DEFAULT NULL,
  numero_transacao varchar(50) DEFAULT NULL,
  numero_parcelas int(11) DEFAULT NULL,
  valor decimal(9,2) DEFAULT NULL,
  data_pagamento datetime DEFAULT NULL,
  valor_parcela decimal(9,2) DEFAULT NULL,
  total_parcelado decimal(9,2) DEFAULT NULL,
  codigo_de_barras varchar(255) DEFAULT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY codigo (codigo),
  CONSTRAINT  FOREIGN KEY (fatura_id) REFERENCES fatura(id),
  CONSTRAINT  FOREIGN KEY (cartao_credito_id) REFERENCES cartao_credito (id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


CREATE TABLE servico_cobrado (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  nome varchar(100) NOT NULL,
  valor decimal(9,2) NOT NULL,
  desativado bit(1)   null,
  PRIMARY KEY (id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE lancamento (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  descricao varchar(255) not null,
  fatura_id bigint(20) NOT NULL,
  servico_cobrado_id bigint(20) NOT NULL,
  tipo varchar(100) NOT NULL,
  qtde int(11) NOT NULL DEFAULT '1',
  valor decimal(9,2) NOT NULL,
  desconto decimal(9,2) NOT NULL DEFAULT '0.00',
  PRIMARY KEY (id),
  KEY servico_cobrado_id (servico_cobrado_id),
  CONSTRAINT  FOREIGN KEY (fatura_id) REFERENCES fatura (id),
  CONSTRAINT  FOREIGN KEY (servico_cobrado_id) REFERENCES servico_cobrado (id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


CREATE TABLE notificacao_meio_pagamento (
  id varchar(255) NOT NULL,
  meio varchar(255) NOT NULL,
  tipo varchar(255) NOT NULL,
  status varchar(255) NOT NULL,
  codigo varchar(255) DEFAULT NULL,
  referencia bigint(20) NOT NULL,
  horario datetime NOT NULL,
  horario_notificado datetime NOT NULL,
  dados longtext NULL,
  executada bit(1) NOT NULL,
  erro varchar(255) DEFAULT NULL,
  ignorar bit(1) DEFAULT NULL,
  PRIMARY KEY (id),
  KEY referencia (referencia)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table plano_iugu (
  id varchar(255) not null,
  token varchar(255) not null,
  identificador varchar(255) not null,
  plano_empresarial_id bigint(20),
  data_criacao datetime not null,
  ativo bit(1) not null,
  primary key (id),
  CONSTRAINT   FOREIGN KEY ( plano_empresarial_id) REFERENCES plano_empresarial (id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table assinatura (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  codigo varchar(255) not null,
  identificador_plano varchar(255) not null,
  forma_de_pagamento varchar(50) not  null,
  data_criacao datetime not null,
  data_atualizacao datetime not null,
  ativo bit(1) not null,
  suspensa bit(1) not null,
  ativa bit(1) not null,
  dados longtext null,
  empresa_id bigint(20) not null  ,
   primary key (id),
   unique(codigo),
   CONSTRAINT  FOREIGN KEY (empresa_id) REFERENCES empresa(id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

<!-- viees -->
create or replace view resumo_compras as
  select contato_id, cartao_id, plano_id ,count(*) qtde, count(distinct DATE_FORMAT(pr.horario, "%M %d %Y")) qtde_visitas,
   sum(pr.valor) valor , sum(pr.valor)/count(distinct DATE_FORMAT(pr.horario, "%M %d %Y")) media,   sum(pr.pontos) pontos_acumulados, max(horario) ultima
    from pontuacao_registrada pr join cartao c on c.id = cartao_id  group by cartao_id;

create or replace view resumo_trocas as
  select c.contato_id , cartao_id,  plano_id, count(*) qtde, sum(ac.pontos) pontos_resgatados, sum(ac.pontos)/count(*) media, max(horario) ultima
    from   acao_contato ac join cartao c on c.id = cartao_id where tipo_de_acao = 3 group by cartao_id;


create or replace view resumo_contato as
  select rc.*, rt.pontos_resgatados
      from resumo_compras rc left join resumo_trocas  rt on rt.cartao_id = rc.cartao_id;



create or replace view empresa_contatos_pontuados as
  select empresa.id,empresa.nome,  count(distinct contato.id) qtde_pontuados,
       if(limite_contatos_negociado is null,  plano.limite_contatos, limite_contatos_negociado) qtde_contratado
      from empresa join contrato on empresa.id = contrato.empresa_id
                   join plano_empresarial plano on plano.id = plano_id
                   join contato on contato.empresa_id = empresa.id
                   join cartao on cartao.contato_id = contato.id and cartao.pontuou is true
                    where empresa.id =  0
                                  group by empresa.id;

alter table empresa add column cnpj varchar(20) null;
alter table empresa add column email varchar(100) null;
alter table empresa add column responsavel_id  bigint(20) null;
alter table empresa add FOREIGN KEY (responsavel_id) REFERENCES usuario(id);

alter table usuario add column cpf varchar(20) null;
update plano_empresarial set ativo = true;

alter table empresa add bloqueada bit(1) default null;

alter table acao_contato add column cartao_id bigint(20)   null ;
alter table acao_contato add  foreign key (cartao_id) references cartao(id);
update acao_contato , contato, cartao
 set acao_contato.cartao_id = cartao.id
    where acao_contato.contato_id = contato.id and contato.id = cartao.contato_id ;

update acao_contato set cartao_id = 17926  where contato_id = 18113 and pontos > 5;
update acao_contato set cartao_id = 6700 where contato_id = 6916 and pontos > 5;
update acao_contato set cartao_id = 20956 where contato_id = 20138 and pontos > 5;

alter table plano add column vencimento datetime null;

<!-- cashback -->
alter table atividade add column cashback  decimal(9,2)   null;
alter table pontuacao_registrada modify column pontos   decimal(9,2) not null;
alter table brinde_resgatado modify column valor_em_pontos   decimal(9,2) null;
alter table acao_contato modify column pontos   decimal(9,2) null;
alter table brinde modify link_imagem varchar(255) null;

alter table contato modify column status varchar(255) not null;

<!-- comprovante troca -->

alter table brinde_resgatado add column saldo decimal(9, 2) null;
alter table brinde_resgatado add column operador_id bigint(20) null;
alter table brinde_resgatado add FOREIGN KEY (operador_id) REFERENCES usuario(id);
alter table brinde_resgatado add column codigo varchar(255) null;

alter table pontuacao_registrada add column referencia_externa varchar(255) null;
alter table pontuacao_registrada add column operador_id bigint(20) null;
alter table pontuacao_registrada add FOREIGN KEY (operador_id) REFERENCES usuario(id);
alter table pontuacao_registrada add column codigo varchar(255) null;

alter table plano add column  referencia_externa bit (1) default null;


alter table empresa add column qtde_visitas_recorrente int(11) not null default 2;
alter table empresa add column qtde_dias_em_risco int(11) not null default 30;
alter table empresa add column qtde_dias_perdido int(11) not null default 60;
alter table empresa add column qtde_compras_vip int(11) not null default 3;
alter table empresa add column ticket_medio_vip decimal(9, 2) not null default 30;

alter table empresa add column qtde_dias_periodo int(11) not null default 60;

-----------------

alter table   pontuacao_registrada drop column atividade_id;

alter table acao_contato add column pontuacao_registrada_id bigint(20) null;

delete from acao_contato where tipo_de_acao = 2;

insert into acao_contato(contato_id, horario, mensagem, tipo_de_acao, empresa_id, pontos, cartao_id, pontuacao_registrada_id)
select c.contato_id, pr.horario, group_concat(a.nome SEPARATOR ', ') mensagem, 2 tipo_de_acao, pr.empresa_id, pr.pontos, pr.cartao_id, pr.id from pontuacao_registrada pr
left join pontuacao_registrada_atividade pra on (pr.id = pra.pontuacao_registrada_id)
inner join atividade a on(pra.atividade_id = a.id)
inner join cartao c on(c.id = pr.cartao_id)
group by pr.id;

alter table acao_contato add column brinde_resgatado_id bigint(20) null;

update acao_contato ac, brinde_resgatado br set ac.brinde_resgatado_id = br.id
	where
    timestampdiff(SECOND, ac.horario, br.horario) = 3600;

------------------

<!-- add column pra indicar contato/cartao ja pontuarao -->
alter table cartao   add column  pontuou bit(1) default null;
alter table contrato add column  limite_contatos_negociado int null;
alter table empresa add column cep varchar(10) null;

alter table contrato add column assinatura_id bigint(20) null;
alter table contrato add FOREIGN KEY (assinatura_id) REFERENCES assinatura(id);

alter table fatura modify column codigo varchar(100) null;
alter table fatura add unique(codigo);
alter table fatura add column data_criacao datetime   null;
alter table fatura add column data_atualizacao datetime   null;
alter table fatura add column dados longtext null;
alter table plano_empresarial add column desconto_cartao   decimal(9,2) default 0;

alter table assinatura drop column ativo;

--> Alterações RAFAEL ticket #96
alter table contato add column removido bit(1)   null;
alter table cartao add column removido bit(1)  null;
alter table brinde_resgatado add column removido bit(1)  null;
alter table pontuacao_registrada add column removida bit(1)  null;
alter table acao_contato add column removida bit(1)  null;


--> Alterações RAFAEL ticket #128
alter table atividade add column removida bit(1)  null;
alter table brinde add column removido bit(1) null;
alter table produto add column removido bit(1) null;
alter table foto add column removida bit(1) null;


-->
alter table empresa add column codigo_cliente varchar (255) null;
alter table notificacao_meio_pagamento modify column referencia bigint(20) null;

alter table plano add column valor_minimo_pontuar  decimal(9,2)  default 0;

alter table cartao_credito add column codigo varchar(255) null;
alter table cartao_credito add column padrao bit(1) default null;
alter table cartao_credito modify column cpf varchar(255) null;
alter table cartao_credito modify column nome varchar(255) null;
alter table cartao_credito modify column data_nascimento varchar(255) null;
alter table cartao_credito modify column telefone varchar(255) null;
alter table cartao_credito add unique(codigo);

alter table assinatura add column data_vencimento  date null;
alter table assinatura add column cartao_credito_id  bigint(20) null;
alter table assinatura add foreign key(cartao_credito_id) references cartao_credito(id);

alter table plano add column cartao_consumo bit(1) null;
alter table plano add column titulo_cartao varchar(255) null;

update plano set titulo_cartao = concat('Cartão ',nome);

<!-- migração pontuação vencida -->
alter table pontuacao_registrada add column data_vencimento date null;
alter table pontuacao_registrada add column pontos_usados decimal(9,2) default 0;
alter table pontuacao_registrada add column pontos_vencidos decimal(9,2) default 0;


update pontuacao_registrada set pontos_usados = 0;

update  pontuacao_registrada pr join cartao on cartao.id = cartao_id join  plano on plano_id = plano.id
  set pr.data_vencimento =  if(vencimento is not null, vencimento, date_add(horario, interval validade day) )
    where validade is not null or vencimento is not null;


update pontuacao_registrada
   set data_vencimento = '2020-03-24 12:00:00'
    where datediff('2020-03-23 23:59:59',data_vencimento) >= 0  ;


insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
    select  true, empresa_id, 'Pontos Expirar',  '[NomeContato], você tem [PontosExpirar] expirando em [TempoRestante]. Aproveite enquanto ainda há tempo!  [LinkCartao]', true, 7,-1
        from plano where excluido is not true and vencimento is not null or validade  is not null group by empresa_id;


create table modulo(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  nome varchar(255) not null,
  primary key(id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table empresa_modulo(
  empresa_id bigint(20) not null,
  modulo_id bigint(20) not null,
  PRIMARY KEY (empresa_id, modulo_id),
    CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id),
    CONSTRAINT   FOREIGN KEY (modulo_id) REFERENCES modulo (id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


alter table contato add column data_ultimo_pedido datetime null;
insert into modulo (nome) values ('fidelidade'), ('pedidos');

<!-- delivery -->
<!-- importar primeiro  sql/cidades.dump.sql  -->

CREATE TABLE endereco (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  descricao varchar(255) DEFAULT NULL,
  bairro varchar(255) DEFAULT NULL,
  cep varchar(255) DEFAULT NULL,
  complemento varchar(255) DEFAULT NULL,
  logradouro varchar(255) DEFAULT NULL,
  localidade varchar(255) DEFAULT NULL,
  numero varchar(255) DEFAULT NULL,
  cidade_id bigint(20) DEFAULT NULL,
  contato_id bigint(20) DEFAULT NULL,
  PRIMARY KEY (id),
  CONSTRAINT   FOREIGN KEY (contato_id) REFERENCES contato (id),
  CONSTRAINT   FOREIGN KEY (cidade_id) REFERENCES cidade (id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE pedido (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  codigo varchar(100) not null,
  status varchar(25) not null,
  valor decimal(9,2) not null,
  taxa_entrega decimal(9,2) not null,
  desconto decimal(9,2) not null,
  horario datetime not null,
  horario_atualizacao datetime not null,
  empresa_id  bigint(20)  not null,
  contato_id  bigint(20)  not null,
  endereco_id  bigint(20) not null,
  operador_id bigint(20) null,
  pago bit(1) default false,
  observacoes varchar(500) null,
  unique(empresa_id, codigo),
   PRIMARY KEY (id),
    CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id) ,
    CONSTRAINT   FOREIGN KEY (contato_id) REFERENCES contato (id) ,
    CONSTRAINT   FOREIGN KEY (endereco_id) REFERENCES endereco (id),
    CONSTRAINT   FOREIGN KEY (operador_id) REFERENCES usuario (id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


CREATE TABLE pagamento_pedido (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  valor decimal(9,2) not null,
  troco_para decimal(9,2) not null,
  forma_de_pagamento varchar(25) not null,
  status varchar(25) not null,
  pedido_id  bigint(20) not null,
   PRIMARY KEY (id),
   CONSTRAINT   FOREIGN KEY (pedido_id) REFERENCES pedido (id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE item_pedido (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  codigo varchar(25)   null,
  qtde int not null,
  valor decimal(9,2) not null,
  desconto decimal(9,2) not null,
  pedido_id  bigint(20) NOT NULL,
  produto_id  bigint(20) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT   FOREIGN KEY (pedido_id) REFERENCES pedido (id),
  CONSTRAINT   FOREIGN KEY (produto_id) REFERENCES produto (id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


create table cardapio(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  arquivo varchar(255) not null,
  empresa_id bigint(20) not null,
  primary key(id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;



alter table notificacao add unique(empresa_id, tipo_de_notificacao);
alter table mensagem_enviada add column imagem varchar(255) null;
alter table produto add column exibir_no_site bit(1) default true;
alter table produto modify column   link_imagem  varchar(255)  null;
alter table produto modify column   mensagem_pedido   varchar(255)  null;

insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
    select  true, empresa.id, 'Cardapio',  '[NomeContato], Segue nosso cardápio!', true, -1,-1
        from empresa;

insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
    select  true, empresa.id, 'ConfirmacaoPedido',  '[ConfirmacaoPedido]', true, -1,-1
        from empresa;


insert into empresa_modulo(empresa_id, modulo_id)
    select id, 1 from empresa where not exists (select 1 from empresa_modulo em where em.empresa_id = empresa.id);


alter table acao_contato add column pedido_id bigint(20) null;
alter table acao_contato add FOREIGN KEY (pedido_id) REFERENCES pedido(id);


<!-- campos extras -->
alter table contato add column cpf varchar(11) null;
alter table contato add column email varchar(255) null;

create table  campo_extra(
  id  bigint(20) not null AUTO_INCREMENT,
  nome varchar(100) not null,
  primary key(id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table empresa_campo_extra(
    empresa_id bigint(20) not null ,
    campo_extra_id bigint(20) not null,
    primary key(empresa_id, campo_extra_id),
       CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id),
       CONSTRAINT   FOREIGN KEY (campo_extra_id) REFERENCES campo_extra (id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


insert into campo_extra(nome) values ('cpf'), ('email');

alter table contato add unique(empresa_id, cpf);


alter table produto add column tem_estoque bit(1) default true;
alter table produto add column exibir_preco_site bit(1) default true;


alter table contato_empresa add column horario timestamp  DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Lista de leads - alteraçãao rafael

alter table contato_empresa add column cadastrado bit(1) default false;



<!-- formas de entrega-->
create table forma_de_entrega (
  id  bigint(20) not null AUTO_INCREMENT,
  nome varchar(100) not null,
  primary key(id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table empresa_forma_de_entrega (
    empresa_id bigint(20) not null ,
    forma_de_entrega_id bigint(20) not null,
    frete_gratis_por_valor bit(1) null,
    valor_minimo_gratis decimal(9,2) null,
    primary key(empresa_id, forma_de_entrega_id),
       CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id),
       CONSTRAINT   FOREIGN KEY (forma_de_entrega_id) REFERENCES forma_de_entrega (id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

insert into forma_de_entrega(nome) values ('Retirar'), ('Receber em casa');

alter table pedido add column forma_de_entrega_id bigint(20) null;
alter table pedido add FOREIGN KEY (forma_de_entrega_id) REFERENCES forma_de_entrega(id);
alter table pedido add column origem varchar(100)  null;
alter table pedido modify column endereco_id bigint(20) null;

alter table item_pedido add column observacao varchar(255) null;

create table categoria (
  id  bigint(20) not null AUTO_INCREMENT,
  nome varchar(100) not null,
  empresa_id bigint(20) not null ,
  primary key(id),
    CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

alter table produto add column categoria_id bigint(20) null;
alter table produto add FOREIGN KEY (categoria_id) REFERENCES categoria(id);


insert into modulo (nome) values ('cardápio');
alter table categoria add column posicao int null;


  ----- Alterações novos campos landing page

alter table contato_empresa add column instagram varchar(255) null;
alter table contato_empresa add column qtde_pedidos varchar(255) null;
alter table contato_empresa add column ticket_medio varchar(255) null;

alter table empresa_forma_de_entrega add column taxa_tipo varchar(255);
alter table empresa_forma_de_entrega add column taxa_valor_fixo decimal(9, 2) default 0 ;
alter table empresa_forma_de_entrega add column taxa_valor_km_taxa decimal(9, 2) default 0;
alter table empresa_forma_de_entrega add column taxa_valor_minimo_taxa decimal(9, 2) default 0;

alter table empresa add column lat_long varchar(255) null;

<!-- troca junto com pedido -->
alter table brinde_resgatado modify column mensagem varchar(255) null;
alter table brinde_resgatado add column pedido_id bigint(20) null;
alter table brinde_resgatado add FOREIGN KEY (pedido_id) REFERENCES pedido(id);


insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
    select  true, empresa.id, 'Link Extrato Cartao',  '[NomeContato], segue o link para conferir seus pontos: [LinkExtratoCartao]', true, -1,-1
        from empresa  join empresa_modulo on empresa_modulo.empresa_id = empresa.id and modulo_id = 2;


alter table empresa_forma_de_entrega add column taxa_maximo_km decimal(9, 2) default 0;

<!-- ampliando planos empresas
 CREATE TABLE vantagem (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  descricao varchar(255) NOT NULL,
  PRIMARY KEY (id)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

CREATE TABLE plano_vantagem (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  plano_empresarial_id bigint(20) NOT NULL,
  vantagem_id bigint(20) NOT NULL,
  ordem int(11) NOT NULL,
  PRIMARY KEY (id),
  KEY vantagem_id (vantagem_id),
  CONSTRAINT  FOREIGN KEY (vantagem_id) REFERENCES vantagem (id),
  CONSTRAINT  FOREIGN KEY (plano_empresarial_id) REFERENCES plano_empresarial (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


 CREATE TABLE prospect (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  nome varchar(255) NOT NULL,
  instagram varchar(100) NOT NULL,
  email varchar(100) NOT NULL,
  telefone varchar(20) NOT NULL,
  passo varchar(255) not null,
  empresa varchar(255)   NULL,
  cnpj varchar(20)   NULL,
  dados longtext null,
  horario datetime not null,
  atualizacao datetime not null,
  codigo varchar(255) not null,
  PRIMARY KEY (id)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

--> distancia minima para comecar a adicionar taxa na entrega

alter table empresa_forma_de_entrega add column distancia_minima_km decimal(9, 2) default 0;


<!-- criar empresa do prospect ->

CREATE TABLE plano_modulo (
  plano_empresarial_id bigint(20) not null,
  modulo_id bigint(20) not null,
  primary key (plano_empresarial_id, modulo_id),
  foreign key(plano_empresarial_id) references plano_empresarial(id),
  foreign key(modulo_id) references modulo(id)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

alter table plano_empresarial add column publico bit(1) default false;
alter table plano_empresarial add column recomendado bit(1) default false;
alter table plano_empresarial add column limite_pedidos int null;

alter table plano_vantagem add disponivel bit(1) default true;

alter table empresa modify column descricao varchar(255) null;
alter table empresa modify column titulo_fotos varchar(50) null;
alter table empresa modify column titulo_destaques varchar(50) null;
alter table empresa modify column link_maps varchar(500) null;
alter table contrato add column data_fim_trial date;


alter table notificacao modify column mensagem text not null;

alter table pedido add column guid varchar(255) default null;
update pedido set guid  = uuid() where guid is null;

alter table plano_empresarial add column identificador varchar(255) null;
alter table plano_empresarial add column codigo varchar(255) null;

alter table prospect add column empresa_id bigint(20) null;
alter table prospect add FOREIGN KEY (empresa_id) REFERENCES empresa(id);

alter table empresa add column sempre_receber_pedidos bit(1) null;

--> Tabela de adicionais
CREATE TABLE adicional_produto (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  nome varchar(255) NOT NULL,
  obrigatorio bit(1) NOT NULL default false,
  qtd_minima int null,
  qtd_maxima int null,
  pode_repetir_item bit(1) null,
  tipo varchar(255) not null,
  produto_id bigint(20) not null,
  excluido bigint(1) null,
  primary key(id),
  foreign key(produto_id) references produto(id)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

CREATE TABLE opcao_adicional_produto (
  id bigint(20) not null auto_increment,
  nome varchar(255) not null,
  valor decimal(9,2) null,
  adicional_produto_id bigint(20) not null,
  excluido bigint(1) null,
  primary key (id),
  foreign key(adicional_produto_id) references adicional_produto(id)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

create table valor_de_adicionais_escolha_simples(
  id bigint(20) not null auto_increment,

  campo0_id bigint(20) null,
  campo1_id bigint(20) null,
  campo2_id bigint(20) null,
  campo3_id bigint(20) null,
  campo4_id bigint(20) null,
  campo5_id bigint(20) null,
  campo6_id bigint(20) null,
  campo7_id bigint(20) null,
  campo8_id bigint(20) null,
  campo9_id bigint(20) null,

  produto_id bigint(20) not null,
  item_id bigint(20) not null,
  primary key(id),
  foreign key(produto_id) references produto(id),
  foreign key(item_id) references item_pedido(id),
  foreign key(campo0_id) references opcao_adicional_produto(id),
  foreign key(campo1_id) references opcao_adicional_produto(id),
  foreign key(campo2_id) references opcao_adicional_produto(id),
  foreign key(campo3_id) references opcao_adicional_produto(id),
  foreign key(campo4_id) references opcao_adicional_produto(id),
  foreign key(campo5_id) references opcao_adicional_produto(id),
  foreign key(campo6_id) references opcao_adicional_produto(id),
  foreign key(campo7_id) references opcao_adicional_produto(id),
  foreign key(campo8_id) references opcao_adicional_produto(id),
  foreign key(campo9_id) references opcao_adicional_produto(id)
);

create table lista_opcoes_escolhidas(
    id bigint(20) not null auto_increment,

    primary key (id)
);


create table valor_de_opcao_multipla_escolha(
    id bigint(20) not null auto_increment,

    qtde bigint(20) not null,
    opcao_id bigint(20) not null,
    lista_id bigint(20) not null,

    primary key(id),
    foreign key(opcao_id) references opcao_adicional_produto(id),
    foreign key(lista_id) references lista_opcoes_escolhidas(id)
);


create table valor_de_adicionais_multipla_escolha(
    id bigint(20) not null auto_increment,

    lista0_id bigint(20) null,
    lista1_id bigint(20) null,
    lista2_id bigint(20) null,
    lista3_id bigint(20) null,
    lista4_id bigint(20) null,
    lista5_id bigint(20) null,
    lista6_id bigint(20) null,
    lista7_id bigint(20) null,
    lista8_id bigint(20) null,
    lista9_id bigint(20) null,

    produto_id bigint(20) not null,
    item_id bigint(20) not null,
    primary key(id),
    foreign key(produto_id) references produto(id),
    foreign key(item_id) references item_pedido(id),
    foreign key(lista0_id) references lista_opcoes_escolhidas(id),
    foreign key(lista1_id) references lista_opcoes_escolhidas(id),
    foreign key(lista2_id) references lista_opcoes_escolhidas(id),
    foreign key(lista3_id) references lista_opcoes_escolhidas(id),
    foreign key(lista4_id) references lista_opcoes_escolhidas(id),
    foreign key(lista5_id) references lista_opcoes_escolhidas(id),
    foreign key(lista6_id) references lista_opcoes_escolhidas(id),
    foreign key(lista7_id) references lista_opcoes_escolhidas(id),
    foreign key(lista8_id) references lista_opcoes_escolhidas(id),
    foreign key(lista9_id) references lista_opcoes_escolhidas(id)
);

alter table prospect add column cadastrado bit(1) default false;

create index idx_msg_enviada_empresa on mensagem_enviada(empresa_id, id);

create index idx_produto_empresa on produto(empresa_id, id);


<!-- login loja contato ->
alter table contato add column senha varchar(100)  null;
alter table contato add unique(empresa_id, email);


alter table empresa_forma_de_entrega add column frete_gratis_por_valor bit(1) null;
alter table empresa_forma_de_entrega add column valor_minimo_gratis decimal(9,2) null;

alter table endereco add column localizacao varchar(255) null;

alter table prospect add column telefone_valido bit(1) null;

CREATE TABLE token (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  horario datetime NOT NULL,
  token varchar(255) NOT NULL,
  codigo varchar(10) NOT NULL,
  validade int NOT NULL,
  contato_id bigint(20) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT FOREIGN KEY (contato_id) REFERENCES contato (id)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;


CREATE TABLE envio_de_email (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  destinatario_email varchar(255) NOT NULL,
  destinatario_nome varchar(255) NOT NULL,
  guid varchar(255) NOT NULL,
  horario datetime NOT NULL,
  horario_envio datetime   NULL,
  status varchar(255) NOT NULL,
  tipo varchar(255) NOT NULL,
  empresa_id bigint(20) not NULL,
  usuario_id bigint(20) DEFAULT NULL,
  contato_id bigint(20) DEFAULT NULL,
  dados longtext NOT NULL,
  enviado bit(1) NOT NULL DEFAULT b'0',
  id_envio varchar(255) DEFAULT NULL,
  mensagem_falha varchar(255) DEFAULT NULL,
  tentar_reenviar bit(1) DEFAULT NULL,
  tentativas_reenvio int(11) DEFAULT NULL,
  PRIMARY KEY (id),
  KEY usuario_id (usuario_id),
  KEY contato_id (usuario_id),
  CONSTRAINT  FOREIGN KEY (empresa_id) REFERENCES empresa (id),
  CONSTRAINT  FOREIGN KEY (usuario_id) REFERENCES usuario (id),
  CONSTRAINT  FOREIGN KEY (contato_id) REFERENCES contato (id)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

alter table produto modify column descricao longtext not null;
alter table empresa_forma_de_entrega add column valor_minimo_pedido decimal(9,2)   null;

alter table contrato add column taxa_adesao decimal(9,2)  null;
alter table usuario modify column ultimo_login datetime null;
alter table usuario modify column senha varchar(255) null;

alter table fatura modify column contrato_id bigint(20) null;
alter table fatura add column   empresa_id bigint(20) null;
alter table fatura add foreign key(empresa_id) references empresa(id);

alter table opcao_adicional_produto add column disponivel bit(1) null default true;

alter table produto add column ordem int(11) null;


//aceitar emoticons
ALTER TABLE mensagem_enviada CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;


alter table cardapio add column bot_ativo bit(1) default false;

insert into modulo (nome) values ('chatbot');

insert into notificacao(ativada,mensagem,empresa_id,tipo_de_notificacao,pode_desativar,qtde_dias_nova_notificacao,qtde_dias_ativa)
  select 1,'[NomeContato], segue o link para conferir seus pontos: [LinkExtratoCartao]',id, 'Link Extrato Cartao',1,-1,-1 from empresa
      where removida is not true and  not exists (select 1 from notificacao where tipo_de_notificacao = 'Link Extrato Cartao' and empresa_id = empresa.id);


ALTER TABLE campanha CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;

alter table empresa add column data_bloqueio_auto date null;

update empresa join contrato on contrato.empresa_id = empresa.id join assinatura on assinatura.id = assinatura_id
   set data_bloqueio_auto = date_add(data_vencimento, INTERVAL 7 DAY)
   where data_vencimento is not null;


<!-- migração forma de entrega por raio de distancia -->

create table empresa_formas_de_entrega (
    id bigint(20) not null auto_increment,
    empresa_id bigint(20) not null ,
    forma_de_entrega_id bigint(20) not null,
    valor_minimo_frete_gratis decimal(9,2) null,
    valor_minimo_pedido  decimal(9,2) DEFAULT NULL,
    ativa bit(1) default true,
    primary key(id), unique(empresa_id, forma_de_entrega_id),
       CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id),
       CONSTRAINT   FOREIGN KEY (forma_de_entrega_id) REFERENCES forma_de_entrega (id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table raio_de_cobranca(
    id bigint(20) not null auto_increment,
    tipo varchar(100) not null,
    alcance decimal(9,2) not null,
    valor_fixo  decimal(9,2)    null,
    valor_km_taxa  decimal(9,2)    null,
    valor_minimo_taxa decimal(9,2)    null,
    empresa_forma_de_entrega_id bigint(20) not null,
    primary key (id),
    foreign key(empresa_forma_de_entrega_id) references empresa_formas_de_entrega(id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

insert into empresa_formas_de_entrega(empresa_id, forma_de_entrega_id, valor_minimo_frete_gratis, valor_minimo_pedido)
 select empresa_id,forma_de_entrega_id,  if(frete_gratis_por_valor,valor_minimo_gratis, 0 ) , valor_minimo_pedido from empresa_forma_de_entrega;

insert into raio_de_cobranca (empresa_forma_de_entrega_id, tipo, alcance, valor_fixo, valor_km_taxa, valor_minimo_taxa)
  select id,taxa_tipo, taxa_maximo_km, taxa_valor_fixo, 0, 0
      from empresa_formas_de_entrega efes join empresa_forma_de_entrega efe
              on  efes.empresa_id = efe.empresa_id and efes.forma_de_entrega_id = efe.forma_de_entrega_id
         where taxa_tipo = 'VALOR_FIXO' union
  select id,taxa_tipo, taxa_maximo_km, 0, 0, 0
                from empresa_formas_de_entrega efes join empresa_forma_de_entrega efe
                  on  efes.empresa_id = efe.empresa_id and efes.forma_de_entrega_id = efe.forma_de_entrega_id
                   where taxa_tipo = 'FRETE_GRATIS' union

  select id,'VALOR_FIXO', distancia_minima_km, taxa_valor_minimo_taxa, 0, 0
        from empresa_formas_de_entrega efes join empresa_forma_de_entrega efe   on  efes.empresa_id = efe.empresa_id and efes.forma_de_entrega_id = efe.forma_de_entrega_id
           where taxa_tipo = 'VALOR_POR_DISTANCIA' and distancia_minima_km > 0  union

  select id,taxa_tipo, if(taxa_maximo_km > 0, taxa_maximo_km, 2500), 0, taxa_valor_km_taxa, taxa_valor_minimo_taxa
                from empresa_formas_de_entrega efes join empresa_forma_de_entrega efe
                      on  efes.empresa_id = efe.empresa_id and efes.forma_de_entrega_id = efe.forma_de_entrega_id
                                      where taxa_tipo = 'VALOR_POR_DISTANCIA';

ALTER TABLE contrato ADD CONSTRAINT empresa_unica  UNIQUE (empresa_id);


<!-- scripts de marketing configuráveis -->
alter table empresa add column pixel_facebook varchar(255) null;
alter table empresa add column analytics varchar(255) null;


alter table empresa add column endereco_id bigint(20) null;
alter table empresa add foreign key(endereco_id) references endereco(id);


<!-- novas formas de pagamento -->

CREATE TABLE forma_de_pagamento (
  id bigint(20) not null auto_increment,
  nome varchar(255) not null,
  descricao varchar(255) not null,
  empresa_id  bigint(20)  not null,
  exibir_cardapio bit(1) not null default true,
  foreign key(empresa_id) references empresa(id),
   PRIMARY KEY (id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

insert into forma_de_pagamento(nome, descricao, empresa_id, exibir_cardapio)
  select 'dinheiro', 'Dinheiro', id, true from empresa where empresa_id = 270
  union select 'cartao', 'Cartão', id, false from empresa where empresa_id = 270
  union select 'cartao-credito', 'Cartão de Crédito', id, true from empresa where empresa_id = 270
  union select 'cartao-debito', 'Cartão de Débito', id, true from empresa where empresa_id = 270
  union select 'cashback', 'Cashback', id, false from empresa where empresa_id = 270
  union select 'transferencia', 'Transferência', id, true from empresa where empresa_id = 270;


alter table pagamento_pedido add column forma_de_pagamento_id bigint(20) null;

update pagamento_pedido inner join pedido on(pedido.id = pagamento_pedido.pedido_id)
 inner join forma_de_pagamento on (pedido.empresa_id = forma_de_pagamento.empresa_id and pagamento_pedido.forma_de_pagamento = forma_de_pagamento.nome)
 set forma_de_pagamento_id = forma_de_pagamento.id;

alter table pagamento_pedido add foreign key(forma_de_pagamento_id) references forma_de_pagamento(id);
alter table pagamento_pedido modify column forma_de_pagamento_id bigint(20) not null;

alter table pagamento_pedido modify column forma_de_pagamento varchar(25) null;

alter table pedido add column  visualizado bit(1) default true;


alter table empresa_formas_de_entrega add column tipo_de_cobranca varchar(50)  null;
update empresa_formas_de_entrega set tipo_de_cobranca = 'distancia';


create table zona_de_entrega(
    id bigint(20) not null auto_increment,
    nome varchar(100) not null,
    valor  decimal(9,2)    not null,
    empresa_forma_de_entrega_id bigint(20) not null,
    primary key (id),
    foreign key(empresa_forma_de_entrega_id) references empresa_formas_de_entrega(id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


alter table endereco add column zona_de_entrega_id  bigint(20)  null;
alter table endereco add foreign key(zona_de_entrega_id) references zona_de_entrega(id);


alter table produto add column qtd_maxima bigint(20) null;




<!-- Novas notificações -->

  insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
      select  true, empresa.id, 'Pedido Saiu Para Entrega',  'Seu pedido [StatusPedido]', true, -1,-1
          from empresa;

  insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
      select  true, empresa.id, 'Pedido Em Preparação',  'Seu pedido [StatusPedido]', true, -1,-1
          from empresa;

  insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
      select  true, empresa.id, 'Pedido Pronto',  'Seu pedido [StatusPedido]', true, -1,-1
          from empresa;

  insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
    select  true, empresa.id, 'Pedido Entregue',  'Seu pedido [StatusPedido]', true, -1,-1
        from empresa;

  insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
      select  true, empresa.id, 'Pedido Cancelado',  'Seu pedido [StatusPedido]', true, -1,-1
          from empresa;


insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
    select  true, empresa.id, 'Pedido Realizado Cardapio Online',  'Acabei de fazer o pedido *[CodigoPedido]*.', false, -1,-1
        from empresa where exists (select 1 from empresa_modulo where empresa_id = empresa.id and modulo_id  = 2);

insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
    select  true, empresa.id, 'Pedido Realizado Cardapio Online',  'Eu quero pedir: \n\n [DadosPedido]', false, -1,-1
        from empresa where exists (select 1 from empresa_modulo where empresa_id = empresa.id and modulo_id = 3)
          and not exists (select 1 from empresa_modulo where empresa_id = empresa.id and modulo_id  = 2);

alter table contato modify column data_nascimento datetime null;

<!-- Mensagem de saudação -->

insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
    select  true, empresa.id, 'Pedido Realizado Cardapio Online',  'Acabei de fazer o pedido *[CodigoPedido]*.', false, -1,-1
        from empresa where exists (select 1 from empresa_modulo where empresa_id = empresa.id and modulo_id  = 2);


insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
    select  false, empresa.id, 'Mensagem Saudação Whatsapp Pedido',  'Olá [NomeContato], Seja bem-vindo à [Empresa]

❗ Clique no link Abaixo Para Conferir Nosso Cardápio Completo e Fazer Seu Pedido❗

⬇⬇⬇⬇⬇⬇⬇⬇⬇

[Link_Cardapio]

⬆⬆⬆⬆⬆⬆⬆⬆⬆

Siga os Passos Abaixo Para Fazer Seu Pedido

1⃣ Escolha os Produtos e Quantidades Desejadas
2⃣ Abra o Carrinho
3⃣ Endereço e Forma de Pagamento
4⃣ Confirme o Pedido
5⃣ Clique em Enviar Pedido para Whatsapp [Empresa].

Super Fácil e Rápido.', true, -1,-1
from empresa where exists (select 1 from empresa_modulo where empresa_id = empresa.id and modulo_id  = 2);

alter table notificacao modify column ativada bit(1) not null default true;


<!-- notificações com emoticons -->
ALTER TABLE notificacao CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;

alter table empresa add column imprimir_txt bit(1) null;


<!--Cadastro de formas de pagamento-->
alter table forma_de_pagamento add constraint unique(empresa_id, descricao);

alter table forma_de_pagamento add column removida bit(1) default false;


<!-- multiplos whatsapps -->

create table numero_whatsapp(
    id bigint(20) not null auto_increment,
    empresa_id bigint(20) not null,
    whatsapp varchar(255) not null,
    principal bit(1) not null default false,
    removido bit(1) not null default false,
    primary key (id),
    foreign key(empresa_id) references empresa(id)
)engine=innodb default charset=utf8mb4 COLLATE=utf8mb4_bin;

alter table mensagem_enviada add column numero_whatsapp_id bigint(20) not null;
insert into numero_whatsapp(empresa_id, whatsapp) select id, whatsapp from empresa;

update mensagem_enviada join numero_whatsapp on(mensagem_enviada.empresa_id = numero_whatsapp.empresa_id)
	set mensagem_enviada.numero_whatsapp_id = numero_whatsapp.id;
alter table mensagem_enviada add foreign key(numero_whatsapp_id) references numero_whatsapp(id);

alter table usuario add column numero_whatsapp_id bigint(20) null null;
update usuario join numero_whatsapp on(usuario.empresa_id = numero_whatsapp.empresa_id)
	set usuario.numero_whatsapp_id = numero_whatsapp.id;
alter table usuario add foreign key(numero_whatsapp_id) references numero_whatsapp(id);
alter table empresa modify column whatsapp varchar(255) null;


<!-- Config impressao -->
create table impressora(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  nome varchar(255) not null,
  tamanho varchar(255) not null,
  primary key(id)
);

create table config_impressao(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  imprimir_txt bit(1) null,
  imprimir_automatico bit(1) null,
  impressora_id bigint(20) null,
  primary key(id),
  foreign key(impressora_id) references impressora(id)
);

alter table empresa add column config_impressao_id bigint(20) null;
alter table empresa add foreign key(config_impressao_id ) references config_impressao(id);

<!-- produtos por peso -->
create table unidade_medida(
  id bigint(20) not null auto_increment,
  nome varchar(100) not null,
  sigla varchar(10) not null,
  valor_inicial_padrao decimal(9, 2) null,
  incremento_padrao decimal(9, 2) null,
  primary key(id)
)engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

alter table produto add column tipo_de_venda varchar(50) null;
alter table produto add column valor_inicial decimal(9, 2) null;
alter table produto add column incremento decimal(9, 2) null;
alter table produto add column unidade_medida_id   bigint(20) null;

alter table produto add foreign key(unidade_medida_id) references unidade_medida(id);

insert into unidade_medida(nome, sigla, valor_inicial_padrao, incremento_padrao)
    values ('Quilo', 'Kg', 1,1), ('Grama', 'g', 500, 100);


alter table item_pedido modify column qtde  decimal(9,2) not null;
alter table item_pedido add column total decimal(9,2) default null;
update item_pedido set total = qtde * valor;


<-- creates mesa -->

create table mesa(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  nome varchar(255) not null,
  empresa_id bigint(20) not null,
  removida bit(1) not null default false,
  primary key (id),
  foreign key(empresa_id) references empresa(id)
);

-- config impressao 2 --

alter table config_impressao add column layout_pedido varchar(255) default 'PedidoCompleto';


alter table pedido add column mesa_id     bigint(20) null;
alter table pedido add foreign key(mesa_id) references mesa(id);

-- multiplas impressoras --
alter table config_impressao add column multiplas_impressoras bit(1) default false;
alter table config_impressao add column impressora_resumido_id bigint(20) default null;
alter table config_impressao add foreign key(impressora_resumido_id) references impressora(id);


alter table item_pedido add column unidade_medida_id   bigint(20)  null;
update item_pedido join produto on produto.id = produto_id set  item_pedido.unidade_medida_id = produto.unidade_medida_id;


alter table forma_de_pagamento add column online bit(1) default false;

create table config_meio_pagamento(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  client_id varchar(255) not null,
  client_secret varchar(255) not null,
  primary key (id)
);

alter table forma_de_pagamento add column config_meio_de_pagamento_id bigint(20);
alter table forma_de_pagamento add foreign key(config_meio_de_pagamento_id) references config_meio_pagamento(id);


alter table pedido add column horario_entrega_agendada datetime default null;
alter table empresa_formas_de_entrega add column permite_agendamento bit(1) default false;

alter table config_meio_pagamento add column nome_fatura_cartao varchar(13);

alter table produto add column disponivel_na_mesa bit(1) default true;
alter table produto add column disponivel_para_delivery bit(1) default true;

alter table pagamento_pedido add column link varchar(255) null;
alter table pagamento_pedido add column codigo varchar(255) null;
alter table pagamento_pedido add column id_link varchar(255) null;
alter table pagamento_pedido add column tipo_de_pagamento_externo varchar(255) null;


CREATE TABLE cupom (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  nome varchar(255) DEFAULT NULL,
  codigo varchar(255) NOT NULL,
  valor decimal(9,2) DEFAULT NULL,
  percentual decimal(9,2) DEFAULT NULL,
  tipo varchar(255) NOT NULL,
  ativo bit(1) NOT NULL,
  validade datetime NULL,
  utilizado bit(1) NOT NULL,
  valor_minimo decimal(9,2) DEFAULT NULL,
  qtde_maxima int(11) DEFAULT NULL,
  qtde_utilizado int(11) DEFAULT NULL,
  empresa_id bigint(20) not  NULL,
  PRIMARY KEY (id),
  CONSTRAINT  FOREIGN KEY (empresa_id) REFERENCES empresa (id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


 CREATE TABLE cupom_contatos(
    cupom_id bigint(20) NOT NULL,
    contato_id  bigint(20) NOT NULL,
    CONSTRAINT   FOREIGN KEY (cupom_id) REFERENCES cupom(id),
    CONSTRAINT   FOREIGN KEY (contato_id) REFERENCES contato(id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

alter table pedido add column cupom_id  bigint(20) NULL;
alter table pedido add foreign key(cupom_id) references cupom(id);
alter table empresa add column tipo_de_loja varchar(255) null default 'CARDAPIO';
alter table adicional_produto add column tipo_de_cobranca varchar(255) null default 'SOMA';
alter table lista_opcoes_escolhidas add column tipo_de_cobranca varchar(255) default 'SOMA';
alter table impressora add column comandos_fim_impressao varchar(255) null;
alter table campanha add column link_imagem varchar(255) not null;

CREATE TABLE sessao_mesa (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  hash varchar(255) NOT NULL,
  horario datetime NOT NULL,
  expirada bit(1) NOT NULL default false,
  mesa_id bigint(20) not null,
  PRIMARY KEY(id),
  constraint foreign key (mesa_id) references mesa(id)
);

alter table cupom add column restrito bit(1) default null;
alter table empresa_formas_de_entrega add column exibir_tela_busca bit(1) default true;
alter table empresa_formas_de_entrega add column tempo_minimo int null;
alter table empresa_formas_de_entrega add column tempo_maximo int null;

alter table opcao_adicional_produto add column descricao varchar(255) null;


alter table cupom add column primeira_compra bit(1) default null;

alter table campanha add column origem_contatos varchar(255) default null;
alter table campanha modify column filtro_id  bigint(20) null;

CREATE TABLE campanha_contatos (
    campanha_id bigint(20) NOT NULL,
    contato_id  bigint(20) NOT NULL,
    CONSTRAINT   FOREIGN KEY (campanha_id) REFERENCES campanha(id),
    CONSTRAINT   FOREIGN KEY (contato_id) REFERENCES contato(id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE produto_horario (
 id varchar(255) not null primary key,
  produto_id bigint(20) NOT NULL,
  dia int not null,
      CONSTRAINT   FOREIGN KEY (produto_id) REFERENCES produto(id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

alter table produto add column disponibilidade int null;


update produto set disponibilidade= 0;
update produto set disponibilidade= 2 where tem_estoque is not true;

alter table contrato add column numero_parcelas int null;


alter table notificacao add column encurtar_links bit(1) default true;

<!--- integraçao modulo pedido com programa de fidelidade -->
CREATE TABLE integracao_pedido_fidelidade (
  id bigint(20)  not null primary key,
  empresa_id bigint(20) NOT NULL,
  plano_id bigint(20) NOT NULL,
  atividade_id bigint(20) NOT NULL,
  data datetime not null ,
  ativa bit(1) default true,

  unique(empresa_id),
      CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa(id),
      CONSTRAINT   FOREIGN KEY (atividade_id) REFERENCES atividade(id),
      CONSTRAINT   FOREIGN KEY (plano_id) REFERENCES plano(id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

alter table atividade add column integrada bit(1) default false;
alter table pedido add column pontos_ganhos decimal(9,2) null;
alter table pontuacao_registrada add column pedido_id bigint(20) null;
alter table pontuacao_registrada add unique(pedido_id);
update pedido set pago = false  where status >  4 and pago is true;


CREATE TABLE mensagem_bot (
   id bigint(20) NOT NULL AUTO_INCREMENT,
   mensagem mediumtext NOT NULL,
   resposta mediumtext NOT NULL,
   horario datetime NOT NULL,
   telefone varchar(255) NOT NULL,
   contato_id bigint(20) NULL,
   empresa_id bigint(20) NOT NULL,
   imagem varchar(255) DEFAULT NULL,
   intent varchar(255) DEFAULT NULL,
   sessao_id varchar(255) DEFAULT NULL,
   contextos text DEFAULT NULL,
   numero_whatsapp_id bigint(20) DEFAULT NULL,
   PRIMARY KEY (id),
   KEY idx_msg_bot_empresa (empresa_id, id),
   KEY numero_whatsapp_id(numero_whatsapp_id),
   CONSTRAINT mensagem_bot_ibfk_1 FOREIGN KEY (numero_whatsapp_id) REFERENCES numero_whatsapp(id)
 ) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


alter table empresa add column descricao_endereco varchar(255) COLLATE utf8mb4_unicode_ci default null;

alter table empresa_formas_de_entrega add column selecionar_bairro_da_zona bit(1) default false;

alter table zona_de_entrega add column permite_frete_gratis bit(1) default true;

alter table contato add column desativar_msg_mkt bit(1) default false;

alter table cardapio add column modo_visualizacao bit(1) default false;
alter table cardapio modify column arquivo varchar(255) NULL;

alter table produto add column qtd_minima int null default 1;

alter table config_meio_pagamento add column meio_de_pagamento varchar(255) not null;
alter table config_meio_pagamento add column token varchar(255) null;
alter table config_meio_pagamento add column email varchar(255) null;
update config_meio_pagamento set meio_de_pagamento = 'cielo';
alter table config_meio_pagamento modify column client_id varchar(255) null;
alter table config_meio_pagamento modify column client_secret varchar(255) null;


-- Implementação da API
CREATE TABLE cliente_api (
  id varchar(255) NOT NULL,
  acesso_direto bit(1) NOT NULL,
  ativo bit(1) NOT NULL,
  nome varchar(255) NOT NULL,
  segredo varchar(255) NOT NULL,
  tipo varchar(255) DEFAULT NULL,
  data_criacao datetime DEFAULT NULL,
  identificador varchar(255) DEFAULT NULL,
  ip varchar(255) DEFAULT NULL,
  empresa_id bigint(20) not null,
  PRIMARY KEY (id),
  foreign key(empresa_id) references empresa(id)
);

 CREATE TABLE bearer_token (
   id bigint(20) NOT NULL auto_increment,
  token varchar(255) NOT NULL,
  cliente_id varchar(255) NOT NULL,
  usuario_id bigint(20) DEFAULT NULL,
  empresa_id bigint(20) not null,
  data_criacao datetime NOT NULL,
  PRIMARY KEY (id),
  KEY (token),
  KEY FK7999539B841AC73C (usuario_id),
  KEY FK7999539BD0F7A7BE (cliente_id),
  CONSTRAINT FK7999539B841AC73C FOREIGN KEY (usuario_id) REFERENCES usuario (id),
  CONSTRAINT FK7999539BD0F7A7BE FOREIGN KEY (cliente_id) REFERENCES cliente_api (id),
  foreign key(empresa_id) references empresa(id)
);


alter table pagamento_pedido add column codigo_transacao varchar(255) null;



<!--- add template pizza -->
create table produto_template(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  empresa_id bigint(20) NOT NULL,
  tipo varchar(255) not null,
  tipo_de_cobranca varchar(20) not null,
  ativo bit(1) default true,
  PRIMARY KEY (id),
  CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa(id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table produto_template_tamanho(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  descricao varchar(255) not null,
  qtde_pedacos int null,
  qtde_sabores int null,
  produto_template_id bigint(20) NOT NULL,
  disponivel bit(1) default true,
  PRIMARY KEY (id),
  CONSTRAINT   FOREIGN KEY (produto_template_id) REFERENCES produto_template(id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table produto_template_adicional(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  descricao varchar(255) not null,
  tipo varchar(255) not null,
  obrigatorio bit(1) default true,
  disponivel bit(1) default true,
  produto_template_id bigint(20) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT   FOREIGN KEY (produto_template_id) REFERENCES produto_template(id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table produto_template_opcao(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  nome varchar(255) not null,
  descricao varchar(255)   null,
  disponivel bit(1) default true,
  produto_template_adicional_id bigint(20) NOT NULL,
    PRIMARY KEY (id),
  CONSTRAINT   FOREIGN KEY (produto_template_adicional_id) REFERENCES produto_template_adicional(id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


create table produto_tamanho(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  preco decimal(9,2) DEFAULT 0,
  produto_id bigint(20) NOT NULL,
  produto_template_tamanho_id bigint(20) NOT NULL ,
    disponivel bit(1) default true,
  PRIMARY KEY (id),
  CONSTRAINT   FOREIGN KEY (produto_id) REFERENCES produto(id),
  CONSTRAINT   FOREIGN KEY (produto_template_tamanho_id) REFERENCES produto_template_tamanho(id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

create table segmento(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  nome varchar(255)  not null,
    PRIMARY KEY (id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

alter table empresa add column segmento_id     bigint(20)  null;
alter table empresa add  CONSTRAINT   FOREIGN KEY (segmento_id) REFERENCES segmento(id);

alter table item_pedido add column descricao varchar(255) null;
alter table item_pedido add column produto_tamanho_id    bigint(20) null;
alter table item_pedido add column item_do_sabor_id     bigint(20) null;

alter table item_pedido add  CONSTRAINT   FOREIGN KEY (item_do_sabor_id) REFERENCES item_pedido(id);
alter table item_pedido add  CONSTRAINT   FOREIGN KEY (produto_tamanho_id) REFERENCES produto_tamanho(id);


alter table produto add column tipo varchar(50) default  'normal';
alter table produto add column template_id  bigint(20) null;
alter table adicional_produto add column produto_template_adicional_id bigint(20) null;
alter table opcao_adicional_produto add column produto_template_opcao_id bigint(20) null;
alter table produto add  CONSTRAINT   FOREIGN KEY (template_id) REFERENCES produto_template(id);
alter table adicional_produto add  CONSTRAINT   FOREIGN KEY (produto_template_adicional_id) REFERENCES produto_template_adicional(id);
alter table opcao_adicional_produto add  CONSTRAINT   FOREIGN KEY (produto_template_opcao_id) REFERENCES produto_template_opcao(id);

update item_pedido join produto on produto_id = produto.id
  set item_pedido.descricao  = produto.nome
      where item_pedido.descricao is null;


insert into segmento(nome) values ('Pizzaria'), ('Hamburgueria'), ('Restaurante');


<-- valor mínimo raio de cobrança -->

alter table raio_de_cobranca add column valor_minimo_pedido decimal(9,2) DEFAULT NULL;
alter table produto_template_opcao add column   valor decimal(9,2) DEFAULT 0;

alter table produto_template add column montar_pizza bit(1) default false;
alter table produto_template add column venda_por_tamanho bit(1) default false;

alter table cupom add column removido bit(1) default null;


alter table cardapio add column modo_teste_bot bit(1) default false;

CREATE TABLE sessao_link_saudacao (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  hash varchar(255) NOT NULL,
  horario datetime NOT NULL,
  expirada bit(1) NOT NULL default false,
  contato_id bigint(20) not null,
  PRIMARY KEY(id),
  CONSTRAINT   FOREIGN KEY (contato_id) REFERENCES contato(id)
);


alter table token add column usuario_id bigint(20)  NULL;
alter table token add  CONSTRAINT   FOREIGN KEY (usuario_id) REFERENCES usuario(id);

alter table token modify contato_id bigint(20)  NULL;

<!-- migração configurar impressora por setor -->
alter table impressora add column  setor  varchar(255)   null;
alter table impressora add column  layout   varchar(255)   null;
alter table impressora add column  config_impressao_id bigint(20)   null;

update impressora join config_impressao on config_impressao.impressora_id = impressora.id
  set setor = 'Completo', layout = 'PedidoCompleto',  config_impressao_id =  config_impressao.id
      where multiplas_impressoras is true;

  update impressora join config_impressao on config_impressao.impressora_resumido_id = impressora.id
    set setor = 'Resumido',  layout = 'PedidoResumido', config_impressao_id =  config_impressao.id
         where multiplas_impressoras is true;

update impressora join config_impressao on config_impressao.impressora_id = impressora.id
  set   config_impressao_id =  config_impressao.id
      where multiplas_impressoras is not true;


create table categoria_impressora (
   categoria_id bigint(20) not null,
   impressora_id bigint(20) not null,
   primary key(categoria_id, impressora_id),
     CONSTRAINT   FOREIGN KEY (categoria_id) REFERENCES categoria(id),
     CONSTRAINT   FOREIGN KEY (impressora_id) REFERENCES impressora(id)

)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

alter table impressora add column imprimir_automatico bit(1) default false;

<!-- pause de categoria -->
alter table categoria add column disponivel bit(1) default true;

<!-- desconto produto ->
alter table produto add column novo_preco decimal(9,2) null;
alter table produto add column percentual_desconto   decimal(4,2) null;
alter table produto add column destaque   bit(1) default null;

alter table empresa_formas_de_entrega add column agendamento_obrigatorio bit(1) default false;


<!-- Mensagem de bot dinâmica ->
create table if not exists config_mensagem_de_bot (
    id bigint(20) not null auto_increment,
    mensagem mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin not null,
    empresa_id bigint(20) not null,
    tipo_de_mensagem varchar(255) not null,
    encurtar_links bit(1) default true,
    primary key (id)
)engine=innodb default charset=utf8mb4 COLLATE=utf8mb4_bin;

<!-- desconto pizza ->
alter table produto_tamanho add column novo_preco decimal(9,2) null;
alter table produto_tamanho add column percentual_desconto   decimal(4,2) null;
alter table produto_tamanho add column destaque   bit(1) default null;

alter table sessao_link_saudacao modify column contato_id bigint(20)   NULL;
alter table sessao_link_saudacao add column telefone varchar(20)  NULL;


<!-- atributo adicional de pedido -->

alter table adicional_produto add column classe varchar(255) not null;

alter table adicional_produto add column entidade varchar(255) not null default 'produto';
alter table adicional_produto add column empresa_id bigint(20) null;
alter table adicional_produto add FOREIGN KEY (empresa_id) REFERENCES empresa(id);
alter table adicional_produto modify column produto_id bigint(20) null;

alter table valor_de_adicionais_escolha_simples add column pedido_id bigint(20) null;
alter table valor_de_adicionais_escolha_simples add FOREIGN KEY (pedido_id) REFERENCES pedido(id);
alter table valor_de_adicionais_escolha_simples modify column produto_id bigint(20) null;
alter table valor_de_adicionais_escolha_simples modify column item_id bigint(20) null;

alter table valor_de_adicionais_multipla_escolha add column pedido_id bigint(20) null;
alter table valor_de_adicionais_multipla_escolha add FOREIGN KEY (pedido_id) REFERENCES pedido(id);
alter table valor_de_adicionais_multipla_escolha modify column produto_id bigint(20) null;
alter table valor_de_adicionais_multipla_escolha modify column item_id bigint(20) null;




<!-- múltiplas imagens por produjto -->
create table if not exists imagem_do_produto (
      id bigint(20) not null auto_increment,
    link_imagem varchar(255) not null,
    ordem int(11) not null,
    produto_id  bigint(20) NOT NULL,
    primary key (id),
    CONSTRAINT   FOREIGN KEY (produto_id) REFERENCES produto (id)
);

insert into imagem_do_produto(produto_id, link_imagem, ordem) select id, link_imagem, 0 from produto where link_imagem is not null;


alter table empresa add column pedido_mesa_nao_identificado bit(1) default false;
alter table plano_empresarial add column intervalo int(11) null;

update  plano_empresarial set intervalo = 6 where nome like '%Semestral%';
update  plano_empresarial set intervalo = 3 where nome like '%Trimestral%';
update  plano_empresarial set intervalo = 1 where  intervalo is null;

<!-- atualizar financeiro assinaturas/fatura  ->
alter table assinatura add column data_primeiro_pagamento datetime null;
alter table assinatura add column data_ultimo_pagamento datetime null;
alter table fatura add column primeira bit(1) null;

 update assinatura ,
  (select assinatura.id, min(data_pagamento) data_pagamento
        from  contrato join assinatura on assinatura.id = assinatura_id join  fatura on fatura.contrato_id = contrato.id
            where fatura.status = 2  group by assinatura.id  )  pagamento
    set assinatura.data_primeiro_pagamento = pagamento.data_pagamento  where assinatura.id = pagamento.id;


 update assinatura ,
  (select assinatura.id, max(data_pagamento) data_pagamento
        from  contrato join assinatura on assinatura.id = assinatura_id join  fatura on fatura.contrato_id = contrato.id
            where fatura.status = 2  group by assinatura.id  ) pagamento
    set assinatura.data_ultimo_pagamento = pagamento.data_pagamento where assinatura.id = pagamento.id;



  update fatura ,
        (select fatura.id, min(data_pagamento) data_pagamento
              from  contrato join assinatura on assinatura.id = assinatura_id join  fatura on fatura.contrato_id = contrato.id
                  where fatura.status = 2  group by assinatura.id  )  pagamento
       set fatura.primeira = true  where fatura.id = pagamento.id;


update fatura join contrato on fatura.contrato_id = contrato.id
      set fatura.empresa_id = contrato.empresa_id where fatura.empresa_id is null;


<!--- add integracao sistema delivery  -->

alter table pedido add column referencia_externa varchar(100) null;
alter table forma_de_pagamento add column referencia_externa  varchar(255)  null;

CREATE TABLE  integracao_delivery  (
  id bigint(20) not null auto_increment,
  token varchar(255)  NOT NULL,
  sistema varchar(255)  NOT NULL,
  empresa_id bigint(20) NOT NULL,
  data datetime not null,
  ativa bit(1) DEFAULT b'1',
  PRIMARY KEY (id),
  CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE notificacao_pedido (
  id varchar(255)  NOT NULL,
  origem varchar(255)  NOT NULL,
  status varchar(255)  DEFAULT NULL,
  codigo varchar(255)  DEFAULT NULL,
  horario datetime NOT NULL,
  horario_notificado datetime NOT NULL,
  dados longtext ,
  executada bit(1) DEFAULT NULL,
  erro varchar(255)  DEFAULT NULL,
  ignorar bit(1) DEFAULT NULL,
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

<!-- ordem no adicional do produto -->
alter table adicional_produto add column ordem int(11) NULL;
alter table  lista_opcoes_escolhidas add column ordem int(11) null;

<!-- comandas -->

create table if not exists comanda (
    id bigint(20) not null auto_increment,
    mesa_id  bigint(20) NOT NULL,
    empresa_id bigint(20) NOT NULL,
    horario_abertura datetime NOT NULL,
    horario_fechamento datetime NULL,
    horario_atualizacao datetime NOT NULL,
    contato_id bigint(20) NULL,
    valor decimal(9,2) NOT NULL,
    status varchar(255) NOT NULL,
    primary key (id),
    CONSTRAINT   FOREIGN KEY (mesa_id) REFERENCES mesa (id),
    CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id),
    CONSTRAINT   FOREIGN KEY (contato_id) REFERENCES contato (id)
);

alter table pedido add column comanda_id     bigint(20) null;
alter table pedido add foreign key(comanda_id) references comanda(id);

CREATE TABLE pagamento_comanda (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  valor decimal(9,2) not null,
  troco_para decimal(9,2) not null,
  status varchar(25) not null,
  comanda_id  bigint(20) not null,
   PRIMARY KEY (id),
   CONSTRAINT   FOREIGN KEY (comanda_id) REFERENCES comanda (id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

alter table pagamento_comanda add column forma_de_pagamento_id bigint(20) not null;
alter table pagamento_comanda add foreign key(forma_de_pagamento_id) references forma_de_pagamento(id);


<-- nome categoria destaque -->

alter table empresa add column nome_categoria_destaques varchar(255) not null default 'DESTAQUES';
alter table empresa add column facebook varchar(255) null;

<!-- integração com ERP ecletica -->
alter table integracao_delivery add column tipo varchar(50) null;
alter table integracao_delivery add column rede int null;
alter table integracao_delivery add column loja int null;
alter table integracao_delivery add column identificacao varchar(25)  null;

update integracao_delivery set tipo = 'delivery';

alter table produto add column codigo_pdv varchar(100) null;

create table forma_de_pagamento_integrada(
  sistema varchar(50) not null,
  descricao varchar(100) not null,
  key (sistema)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

insert into forma_de_pagamento_integrada (sistema, descricao)
  values ('foodydelivery','money'),('foodydelivery', 'card'),
         ('foodydelivery', 'online'),('foodydelivery','on_credit');

insert into forma_de_pagamento_integrada (sistema, descricao)
  values ('ecletica','dinheiro'),('ecletica', 'cartao'),('ecletica', 'ticket'), ('ecletica','cheque'), ('ecletica','outras');

alter table prospect add column operador_id bigint(20) null;

alter table contato add column qtde_pedidos int null default 0;

update contato , (select count(*) total, contato_id from pedido where status <= 4 group by contato_id ) pedidos
  set qtde_pedidos  = pedidos.total where contato.id = pedidos.contato_id;

alter table  empresa_formas_de_entrega add column bairro_opcional bit(1) default false;

<!-- peso mínimo -->
alter table produto add column peso_minimo DECIMAL(9,2) null;
alter table produto add column peso_maximo DECIMAL(9,2) null;

alter table fatura add column tentativa_pagamento int default 0;

create table log_fatura(
  id   varchar(255) not  null,
  criado_em  varchar(25) not null,
  descricao   varchar(100) not null,
  notas   varchar(255) not null,
  fatura_id bigint(20) not null,
  PRIMARY KEY (id),
  CONSTRAINT   FOREIGN KEY (fatura_id) REFERENCES fatura (id)
);



<-- suporte a modo html na impressão nativa -->

  alter table config_impressao add column modo_html bit(1) null default false;


<-- imagem na opção do adicional -->

  alter table opcao_adicional_produto add column link_imagem varchar(255) null;


create table historico_pedido(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  horario datetime not null,
  descricao varchar(255) not null,
  pedido_id bigint(20) not null,
  operador_id  bigint(20) null,
  cliente_api_id   varchar(255) null,
  PRIMARY KEY (id),
  CONSTRAINT   FOREIGN KEY (pedido_id) REFERENCES pedido (id),
  CONSTRAINT   FOREIGN KEY (operador_id) REFERENCES usuario (id),
  CONSTRAINT   FOREIGN KEY (cliente_api_id) REFERENCES cliente_api (id)
);


<!-- identificador de mesa -->
alter table empresa add column identificador_mesa varchar(255) null default 'Mesa';

<!-- criação banners -->
<!-- Possíveis valores de comando:
BUSCA
LINK
NADA
-->
CREATE TABLE banner (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  nome varchar(255) not null,
  comando varchar(255) not null,
  extra text NULL,
  disponibilidade int null,
  link_imagem varchar(255) not null,
  removido bit(1)  null default false,
  ordem int not null,
  empresa_id bigint(20) not null,
  PRIMARY KEY (id),
  foreign key(empresa_id) references empresa(id)
);

CREATE TABLE banner_horario (
 id bigint(20) NOT NULL AUTO_INCREMENT,banner_id bigint(20) NOT NULL,
 dia int not null,
 PRIMARY KEY (id),
 CONSTRAINT   FOREIGN KEY (banner_id) REFERENCES banner(id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


<!-- add integração ecletica bandeira cartao -->

alter table forma_de_pagamento_integrada add column tipo_bandeira varchar(10) null;

update forma_de_pagamento_integrada set tipo_bandeira = 'C' where sistema = 'ecletica' and descricao = 'cartao';
update forma_de_pagamento_integrada set tipo_bandeira = 'T' where sistema = 'ecletica' and descricao = 'ticket';
update forma_de_pagamento_integrada set tipo_bandeira = 'O' where sistema = 'ecletica' and descricao = 'outras';

create table bandeira_cartao_integrada(
    id bigint(20) NOT NULL,
    nome varchar(255) not null,
    tipo varchar(10) null,
      PRIMARY KEY (id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

alter table forma_de_pagamento add column bandeira_cartao_integrada_id bigint(20) null;
alter table forma_de_pagamento add FOREIGN KEY (bandeira_cartao_integrada_id) REFERENCES bandeira_cartao_integrada(id);


alter table sessao_link_saudacao add column qtde_acessos int not null default 0;

alter table produto_template_opcao add column tamanho_id bigint(20) null;
alter table produto_template_opcao add  foreign key (tamanho_id) references produto_template_tamanho(id);


alter table  produto_template add column taxa_extra DECIMAL(9,2) null;


alter table opcao_adicional_produto add column codigo_pdv varchar(100) null;

alter table empresa add column rede varchar(255) null;
alter table adicional_produto add column campo_ordenar varchar(50) null ;


create table pausa_programada(
    id bigint(20) NOT NULL auto_increment,
    descricao varchar(255) not null,
    data_inicio datetime not null,
    data_fim datetime not null,
    empresa_id bigint(20) not null,
      PRIMARY KEY (id),
       CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa(id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


alter table campanha add column qtde_mensagens bigint(20) default 0;
alter table mensagem_enviada modify column contato_id bigint(20) null;


alter table notificacao add column fazer_preview bit(1) default 1;
alter table mensagem_enviada add column fazer_preview bit(1) default 1;

alter table produto_template add column campo_ordenar varchar(50) null ;

alter table empresa add column saldo_mensagens bigint(20) default 0;

create table historico_creditos_mensagens (
    id bigint(20) NOT NULL auto_increment,
    data datetime not null,
    referencia int(11) NOT NULL,
    qtde bigint(20) not null,
    empresa_id bigint(20) not null,
      PRIMARY KEY (id),
       CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa(id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

alter table contrato add column qtde_mensagens_mes bigint(20) not null default 1000;

  CREATE TABLE produto_turno (
    id bigint(20) NOT NULL auto_increment,
  produto_id bigint(20) NOT NULL,
  hora_inicio time not null,
  hora_fim time not null,
        PRIMARY KEY (id),
      CONSTRAINT   FOREIGN KEY (produto_id) REFERENCES produto(id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


alter table mesa add column nao_gerar_comanda bit(1) default 0;

alter table empresa add column fuso_horario int default -3;


alter table comanda add column referencia_externa varchar(100) null;


alter table empresa add column cobrar_taxa_servico bit(1) null default false;
alter table empresa add column valor_taxa_servico DECIMAL(4,2) null default 10;

alter table comanda add column cobrar_taxa_servico bit(1) null default true;
alter table comanda add column taxa_servico decimal(9,2) null;
alter table comanda add column total_com_taxa decimal(9,2) null;

alter table cardapio drop column modo_visualizacao_qrcode;
alter table cardapio add column modo_visualizacao_qr_code bit(1) default false;



alter table integracao_delivery add column ultima_sincronizacao_disponivel datetime null;


alter table empresa_formas_de_entrega add column cep_obrigatorio bit(1) null default false;

<!-- aceitar pedidos ->
alter table pedido add column aceito bit(1) default false;
alter table empresa add column aceitar_pedido_automatico bit(1) default true;
update pedido set aceito = true  ;

alter table cardapio add column exibir_indisponiveis   bit(1)  default false;

alter table produto_template add column nome varchar(255) not null default 'Pizzas'

create table registro_de_operacao (
  id bigint(20) NOT NULL auto_increment,

  descricao varchar(255) not null,
  operacao varchar(255) not null,
  horario datetime  not null,
  ip varchar(255) not null,
  tipo_objeto varchar(255) null,
  id_objeto varchar(255) null,
  valor_novo text null,
  usuario_id bigint(20) NOT NULL,
  empresa_id bigint(20) NOT NULL,

  primary key(id),
  foreign key(usuario_id) references usuario(id),
  foreign key(empresa_id) references empresa(id)
);

alter table usuario add column garcom bit(1) default false;
alter table pedido add column garcom_id bigint(20) null;
alter table pedido  add foreign key(garcom_id) references usuario(id);
alter table comanda add column garcom_id bigint(20) null;
alter table comanda add foreign key(garcom_id) references usuario(id);


alter table cupom add column produto_id bigint(20)   null;
ALTER TABLE cupom ADD FOREIGN KEY (produto_id) REFERENCES produto(id);

alter table cupom add column aplicar_na_taxa_de_entrega bit(1) default false;
alter table pedido add column desconto_taxa_entrega  decimal(9,2) not null  default 0;


insert into forma_de_pagamento_integrada (sistema, descricao)
  values ('tiny','multiplas'),('tiny', 'dinheiro'),('tiny', 'credito'), ('tiny','debito'), ('tiny','boleto'),
  ('tiny','deposito'),('tiny','cheque'),('tiny','crediario'), ('tiny','duplicata_mercantil');


<!-- aprovar campanhas >

alter table campanha add column status_aprovacao varchar(255) default 'Aprovada';


alter table cupom add column percentual_maximo_desconto_produto decimal(9, 2) null;
update cupom set percentual = percentual * 100 where percentual >= 0 and percentual <= 1;

alter table cupom add column produto_template_tamanho_id bigint(20)   null;
ALTER TABLE cupom ADD FOREIGN KEY (produto_template_tamanho_id) REFERENCES produto_template_tamanho(id);


alter table registro_de_operacao modify column usuario_id bigint null;
alter table registro_de_operacao add column cliente_api_id varchar(255) null;
alter table registro_de_operacao add FOREIGN KEY (cliente_api_id) REFERENCES cliente_api (id);


<!-- link de empresas -->
create table dominio_da_empresa (
    id bigint(20) NOT NULL auto_increment,
    hostname varchar(255) not null,
    url_cardapio bit(1) not null default true,
    url_catalogo bit(1) not null default false,
    empresa_id bigint(20) NOT NULL,

    primary key(id),
    foreign key(empresa_id) references empresa(id)
);


create table cidades_entrega (
  cidade_id bigint(20) not null,
  empresa_forma_de_entrega_id bigint(20) not null,
  empresa_id bigint(20) not null,
  primary key(cidade_id, empresa_forma_de_entrega_id, empresa_id),
  constraint foreign key(empresa_forma_de_entrega_id) references empresa_formas_de_entrega(id),
  CONSTRAINT foreign key(empresa_id) references empresa(id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

<!-- taxa cobrança forma de pagamento
create table taxa_cobranca(
  id bigint(20) NOT NULL auto_increment,
  valor decimal(9,2) DEFAULT 0,
  percentual decimal(9,2) DEFAULT 0,
  ativa bit(1) default true,
  primary key(id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

alter table forma_de_pagamento add column taxa_cobranca_id bigint(20) null;
ALTER TABLE forma_de_pagamento ADD FOREIGN KEY (taxa_cobranca_id) REFERENCES taxa_cobranca(id);
alter table pedido add column taxa_forma_de_pagamento decimal(9,2) default 0;
alter table pedido add column erro_externo longtext null;
alter table pagamento_pedido add column taxa decimal(9,2) default 0;

alter  table endereco add column removido bit(1) null;

<!-- notificacao -->
create table notificacao_app (
     id bigint(20) not null auto_increment,
     nome varchar(255) not null,
     mensagem text not null,
     titulo varchar(255) not null,
     link varchar(255) not null,
     url_imagem varchar(255) not null,
     data_criacao datetime not null,
     empresa_id bigint(20) not null,
     qtde_enviadas bigint(20),
     qtde_mensagens bigint(20),
     qtde_lidas bigint(20),
     foi_testada bit(1) not null default 0,
     horario_envio datetime null,
     status varchar(255) not null,
     status_aprovacao varchar(255) default 'Pendente',
     primary key (id)
);

alter table mesa add column codigo_pdv varchar(50) null;


create table token_gcm (
   id bigint(20) not null auto_increment,
   data_criacao datetime not null,
   token varchar(300) not null,
   contato_id bigint(20) null,
   empresa_id bigint(20) not null,
   primary key (id),
   foreign key(empresa_id) references empresa(id),
   foreign key(contato_id) references contato(id),
   unique key(empresa_id, token)
);


insert into modulo (nome) values ('garcom'), ('app');



insert into notificacao (ativada,empresa_id,tipo_de_notificacao,mensagem,pode_desativar, qtde_dias_ativa, qtde_dias_nova_notificacao)
    select  true, id, 'Bem Vindo',  '[NomeContato], seja bem vindo ao plano de fidelidade da [Empresa].' , true, -1,-1
         from empresa;


alter table config_meio_pagamento add column public_key varchar(100) null;
alter table pagamento_pedido add column motivo_reprovacao varchar(255) null;
alter table cupom add column nao_pontuar_fidelidade bit(1) default null;

<!-- migracao bandeiras eclectica mesmo id, tipos diferentes -->

CREATE TABLE bandeira_cartao_integrada_nova (
  id bigint(20) not null auto_increment,
  nome varchar(255)   NOT NULL,
  tipo varchar(10)   DEFAULT NULL,
  codigo_pdv int not null,
  empresa_id bigint(20) not null,
  PRIMARY KEY (id),
  unique(codigo_pdv, tipo, empresa_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

alter table forma_de_pagamento add column bandeira_cartao_integrada_nova_id    bigint(20) null;
alter table forma_de_pagamento add foreign key(bandeira_cartao_integrada_nova_id) references bandeira_cartao_integrada_nova(id);


insert into bandeira_cartao_integrada_nova(empresa_id, codigo_pdv,nome, tipo)
select empresa_id, bandeira_cartao_integrada.*
    from  bandeira_cartao_integrada join forma_de_pagamento on bandeira_cartao_integrada.id = bandeira_cartao_integrada_id
        group  by empresa_id,bandeira_cartao_integrada.id  order by empresa_id;

update forma_de_pagamento , bandeira_cartao_integrada_nova
   set bandeira_cartao_integrada_nova_id = bandeira_cartao_integrada_nova.id
      where forma_de_pagamento.empresa_id = bandeira_cartao_integrada_nova.empresa_id and bandeira_cartao_integrada_id = codigo_pdv;

/* arquivo kml */
create table projeto_tomtom (
    id bigint(20) not null auto_increment,
    data datetime not null,
    id_tomtom varchar(300) not null,
    empresa_id bigint(20) not null,
    qtde_regioes int not null,
    completou bit(1) not null default 0,
    primary key (id),
    foreign key(empresa_id) references empresa(id)
);

alter table empresa_formas_de_entrega add column arquivo_kml varchar(255)  null;
alter table empresa_formas_de_entrega add column arquivo_geo_json varchar(255)  null;



alter table usuario add column garcom_padrao bit(1) null;
alter table usuario modify column email varchar(100) null;


<!--  permitir copiar adicional -->
update adicional_produto set excluido = 0 where excluido is null;
alter table adicional_produto modify column excluido bigint(1) not null default 0;
alter table adicional_produto add column compartilhado bit(1) not null default 0;

create table produto_adicional_produto (
                                           objeto_id bigint(20) null,
                                           adicional_produto_id bigint(20) not null,
                                           empresa_id bigint(20) not null,
                                           ordem int(11) not null,
                                           CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa(id),
                                           CONSTRAINT   FOREIGN KEY (adicional_produto_id) REFERENCES adicional_produto(id)
)  engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

<!-- inserir adicionais de produto -->
insert into produto_adicional_produto(adicional_produto_id, objeto_id, empresa_id, ordem)
select adicional_produto.id, adicional_produto.produto_id, produto.empresa_id, ROW_NUMBER() over w -1 as ordem from adicional_produto
                                                                                                                        join produto on (adicional_produto.produto_id = produto.id)
where adicional_produto.excluido = 0
    window w as (PARTITION BY adicional_produto.produto_id order by adicional_produto.ordem);
<!-- inserir adicionais de pedido -->
insert into produto_adicional_produto(adicional_produto_id, objeto_id, empresa_id, ordem)
select adicional_produto.id, adicional_produto.empresa_id, adicional_produto.empresa_id, ROW_NUMBER() over w -1 as ordem from adicional_produto
where
    produto_id is null
  and adicional_produto.excluido = 0
    window w as (PARTITION BY adicional_produto.empresa_id order by adicional_produto.ordem);

create index idx_produto_acional_produto on produto_adicional_produto(objeto_id);


<!-- importar ifood com codigos do ifodd -->


alter table produto add column id_ifood varchar(100) null;
alter table adicional_produto add column codigo_ifood varchar(50) null;
alter table opcao_adicional_produto add column id_ifood varchar(100) null;
alter table categoria add column codigo_ifood varchar(50) null;

alter table empresa add column app_ios varchar(255) null;


alter table categoria add column codigo_pdv varchar(50) null;

alter table notificacao_pedido add column pedido_id bigint(20) null;
alter table notificacao_pedido add column empresa_id bigint(20) null;

update   notificacao_pedido join pedido on notificacao_pedido.codigo = referencia_externa
   set  notificacao_pedido.empresa_id = pedido.empresa_id, notificacao_pedido.pedido_id = pedido.id
  where pedido_id is null and executada is not true;

alter table produto_tamanho add column codigo_pdv varchar(100) null;
alter table produto_template_opcao add column codigo_pdv varchar(100) null;

<-- raio de entrega -->
create table alcance(
    id bigint(20) not null auto_increment,
    alcance decimal(9,2) not null,
    taxa  decimal(9,2)    null,
    empresa_id bigint(20) not null,
    forma_de_entrega_empresa_id bigint(20) not null,
    primary key (id),
    foreign key(empresa_id) references empresa(id),
    foreign key(forma_de_entrega_empresa_id) references empresa_formas_de_entrega(id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

alter table empresa_formas_de_entrega add column agendamento_limite_minimo int unsigned null ;

alter table empresa_formas_de_entrega add column agendamento_limite_maximo int unsigned null ;


alter table config_impressao add column momento_imprimir_auto varchar(25)  null;
alter table pedido add column impresso bit(1) default false;

CREATE table dependencia_opcao_adicional (
     id bigint(20) not null auto_increment,
     opcao_id bigint(20) not null,
     adicional_id bigint(20) not null,
     opcao_dependente_id  bigint(20) not null,
     primary key (id),
     foreign key(opcao_id) references opcao_adicional_produto(id),
     foreign key(adicional_id) references adicional_produto(id),
     foreign key(opcao_dependente_id) references opcao_adicional_produto(id)
);



create table taxa_de_entrega_calculada(
  id bigint(20) not null auto_increment,
  erro varchar(255) null,
  valor  decimal(9,2) null,
  tipo_de_cobranca varchar(255) not null,
  distancia decimal(9, 2) null,
  localizacao varchar(255) null,
  endereco varchar(255) null,
  empresa_id bigint(20) not null,
  sucesso bit(1) not null default 0,
  faz_entrega bit(1) not null default 0,
  zona_de_entrega_id  bigint(20)  null,
  horario datetime null,
  foreign key(empresa_id) references empresa(id),
  primary key (id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

alter table taxa_de_entrega_calculada add column hash varchar(255) null;


alter table contrato add column qtde_operadores bigint(20) null default 1;


alter table contato add column codigo_pdv varchar(50) null;
alter table bandeira_cartao_integrada add column codigo bigint(20) null;
alter table integracao_delivery add column validade_token datetime null;


alter table integracao_delivery add column configuracoes_especificas json null;

alter table produto add column codigo_de_barras varchar(255) null;

create table if not exists imagem_ean (
    codigo_de_barras varchar(255),
    link_imagem varchar(255)  null,
    primary key (codigo_de_barras)
) DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


ALTER TABLE produto ADD CONSTRAINT fk_imagem_ean FOREIGN KEY(codigo_de_barras) REFERENCES imagem_ean(codigo_de_barras);

alter table imagem_ean add index  imagem_ean (codigo_de_barras);
alter table produto add index produto_codigo_de_barras (codigo_de_barras);

create table if not exists config_whatsapp (
    id bigint(20) not null auto_increment,
    empresa_id bigint(20),
    tempo_msg_saudacao bigint(20)  null default 43200000,
    primary key (id)
) DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

alter table usuario add column admin_rede varchar(255) null;

create table filial_china(
  nome varchar(100) not null,
  primary KEY(nome)
);

alter table integracao_delivery rename COLUMN  identificacao to   unidade_china ;
update integracao_delivery set unidade_china = null;

alter table integracao_delivery add column unidade_gendai varchar(50) null;
insert into filial_china(nome) values ('itajai'),('morumbi'),('butanta'),('centrolondrina'),('brooklin'),('goiania'),('saobernardo1'),('atibaia'),('indaiatuba'),('jabaquara'),('saomiguel'),('bancarioslondrina'),('carapicuiba'),('meier'),('barradatijuca'),('maua'),('lapa'),('saomatheus'),('estancia'),('souzabelem'),('limeira'),('jau'),('mooca'),('catanduva'),('santanadeparnaiba'),('ahu'),('olinda'),('aracaju'),('ribeiraopreto'),('tijuca'),('vilamaria'),('gruta'),('santoandre'),('campolimpo'),('vitoria'),('nazarebelem'),('alphaville'),('guara'),('cidadenova'),('bigorrilho'),('saocarlos'),('pompeia'),('jundiai'),('piratininga'),('pontagrossa'),('laranjeiras'),('bauru'),('ilhadogovernador'),('mogidascruzes'),('juizdefora'),('boaviagem'),('cuiaba'),('jacarepagua'),('americana'),('balneariocamboriu'),('portovelho'),('bixiga'),('copacabana'),('suzano'),('taguatinga'),('pinheiros'),('shoppingtaboao'),('savassi'),('ceasa'),('asasul'),('asanorte'),('rosarinho'),('manaira'),('itaquera'),('lagonorte'),('vilamariana'),('dunlopcampinas'),('aclimacao'),('aguasclaras'),('freguesiadoo'),('viladapenha'),('aguaverde'),('aldeota'),('cachoeirinha'),('lagosul'),('penha'),('mandaqui'),('parquesaodomingos'),('altodaxv'),('campogrande'),('epitacio'),('botafogo'),('saoluis'),('araraquara'),('higienopolis'),('vilacarrao'),('tijuca2'),('fatima'),('vilamascote'),('recreio'),('saojosedoscampos'),('joinville'),('praia'),('parquesaolucas'),('guarulhos2'),('contagem'),('guarulhos1'),('tirol'),('zonanorte'),('chacarasantoantonio'),('cambuicampinas'),('saojosedoriopreto'),('guaruja'),('santos'),('riopequeno'),('chapada'),('teresina'),('itaim'),('valinhos'),('blumenau'),('buritis'),('saocaetano'),('tatuape'),('jardimbotanico'),('marilia'),('caxiasdosul'),('caicara'),('granjaviana'),('moema'),('pontanegra'),('florianopolis'),('saudeipiranga'),('osasco2'),('praiagrande'),('taubate'),('diadema'),('piracicaba'),('osasco1'),('jardins'),('belavista'),('caxanga'),('santana'),('saojose'),('saocristovaoteresina'),('sorocaba'),('floresta'),('zonasul'),('icarai'),('pontaverde'),('rioclaro');

create table filial_gendai(
  nome varchar(100) not null,
  primary KEY(nome)
);

insert into filial_gendai(nome) values ('iguatemibrasilia'),('itajai'),('aguasclaras'),('freguesiadoo'),('viladapenha'),('parqueshoppingmaia'),('shoppingcampogrande'),('aguaverde'),('aldeota'),('shoppingwestplaza'),('genboavista'),('shoppingrecife'),('saomiguel'),('shoppinganaliafranco'),('parkshoppingsaocaetano'),('parquesaodomingos'),('shoppingtambore'),('shoppingmetrotucuruvi'),('carapicuiba'),('altodaxv'),('novoshoppingcenterribeiraopreto'),('campogrande'),('epitacio'),('saomatheus'),('estancia'),('souzabelem'),('araraquara'),('iguatemiribeiraopreto'),('supershoppingosasco'),('fatima'),('catanduva'),('shoppingpracadamoca'),('recreio'),('saojosedoscampos'),('joinville'),('shoppingitaquera'),('shoppingdd'),('contagem'),('praiamarshopping'),('shoppingpatiohigienopolis'),('tirol'),('ahu'),('olinda'),('shoppingplazasul'),('shoppingcittaamerica'),('guaruja'),('shoppingboulevardtatuape'),('shoppingjardimsul'),('nazarebelem'),('riopequeno'),('cidadenova'),('saocarlos'),('teresina'),('shoppingmorumbi'),('jundiai'),('mogidascruzes'),('moocaplazashopping'),('shoppingpiracicaba'),('iguatemicampinas'),('buritis'),('boaviagem'),('senamadureira'),('cuiaba'),('galleriashopping'),('shoppingibirapuera'),('jacarepagua'),('caicara'),('granjaviana'),('shoppingiguatemisorocaba'),('balneariocamboriu'),('shoppingeldorado'),('pontanegra'),('portovelho'),('shoppingfreicaneca'),('copacabana'),('suzano'),('saobernardoplazashopping'),('shoppingtaboao'),('savassi'),('taubate'),('grandplazashopping'),('shoppingspmarket'),('gendai-BRL'),('rosarinho'),('caxanga'),('shoppinginterlagos'),('manaira'),('shoppingbonsucesso'),('floresta'),('shoppingded'),('goldensquareshopping'),('pontaverde'),('rioclaro');

alter table endereco add column ponto_de_referencia varchar(255) null;

alter table produto add column nao_aceita_cupom bit(1) default  false;


alter table empresa add column gtm varchar(255) null;

create table nota_fiscal_de_servico (
  id bigint(20) not null auto_increment,
  numero_rps bigint(20) not null,
  numero_da_nota bigint(20),
  codigo_verificacao varchar(255),
  aprovada bit(1) not null default false,
  data_emissao datetime not null,
  xml text null,
  xml_resposta text,
  valor_servicos decimal(9,2)  null,
  discriminacao varchar(1000)  null,
  tipo_documento_tomador varchar(100)  null,
  cnpj_tomador varchar(20),
  cpf_tomador varchar(20),
  nome_tomador varchar(255)  null,

  fatura_id bigint(20) not null,
  endereco_tomador_id bigint(20) null,
  empresa_tomador_id bigint(20) not null,

  primary key(id),
  constraint foreign key(empresa_tomador_id) references empresa(id),
  constraint foreign key(endereco_tomador_id) references endereco(id),
  CONSTRAINT  FOREIGN KEY (fatura_id) REFERENCES fatura(id)
);

create table tarefa_de_envio_de_nfse (
  id bigint(20) not null auto_increment,
  nota_id bigint(2) not null,
  status varchar(20) not null,
  mensagem varchar(1000),
  tentativas bigint(10) not null default 0,

  primary key(id),
  constraint foreign key(nota_id)  references nota_fiscal_de_servico(id)
);

alter table cidade add column codigo_nfse varchar(6) null;

-- necessário adiciar tabela de cidades antes de executar o código abaixo, arquivo cidades_nfse.sql
update cidade, cidades_nfse, estado set cidade.codigo_nfse = cidades_nfse.Codigo where cidade.estado_id = estado.id and cidade.nome = cidades_nfse.Cidade and cidades_nfse.Estado = estado.sigla;

create table grupo_de_lojas (
    id bigint(20) NOT NULL auto_increment,
    hostname varchar(255) not null,
    nome varchar(255) not null,
    descricao varchar(255) not null,
    primary key(id)
);

alter table empresa add column grupo_de_lojas_id bigint(20);
alter table empresa add foreign key(grupo_de_lojas_id) references grupo_de_lojas(id);

create table promocao(
    id bigint(20) not null auto_increment,
    descricao varchar(255) not null,
    data_inicio datetime not null,
    data_fim datetime not null,
    ativa bit(1) not null,
    empresa_id bigint(20) not null,
    excluido bit(1) null default false,

    primary key(id),
    constraint foreign key (empresa_id) references empresa(id)

);


create table regra_da_promocao (
    id bigint(20) not null auto_increment,
    percentual bigint(20) null,
    maximo_aplicacoes bigint(10) null,
    ativa bit(1) not null,
    tipo varchar(255) not null,
    produto_id bigint(20) null,
    adicional_id bigint(20) null,
    promocao_id bigint(20) null,
    empresa_id bigint(20) null,
    excluido bit(1) null default false,
    primary key(id),

    constraint foreign key(produto_id) references produto(id),
    constraint foreign key (adicional_id) references adicional_produto(id),
    constraint foreign key (promocao_id) references promocao(id),
    constraint foreign key (empresa_id) references empresa(id)
);

create table promocao_aplicada (
    id bigint(20) not null auto_increment,
    desconto decimal(9,2) not null,
    pedido_id bigint(20) not null,
    promocao_id bigint(20) not null,
    empresa_id bigint(20) not null,

    primary key(id),
    constraint foreign key (pedido_id) references pedido(id),
    constraint foreign key (promocao_id) references promocao(id),
    constraint foreign key (empresa_id) references empresa(id)
);

alter table produto add column exibir_preco_no_cardapio bit(1) default true;
alter table produto_template add column identificador varchar(100) not null default 'Pizza';

alter table cupom add column aplicar_desconto_valor_produtos bit(1) default null;

alter table promocao add column cumulativa bit(1) not null default false;
alter table regra_da_promocao add column valor_minimo_pedido decimal(9,2) null;
alter table regra_da_promocao add column produto_compoe_minimo bit(1) null;


<-- campanha rede -->
create table campanha_rede (
  id bigint(20) not null auto_increment,
  nome varchar(255) not null,
  mensagem text not null,
  data_criacao datetime not null,
  empresa_id bigint(20) not null,
  tipo_de_envio varchar(255) not null default 'Unico',
  horario_envio datetime null,
  status varchar(255) not null,
  qtde_dias_nova_notificacao bigint(20) not null default 1,
  foi_testada bit(1) null default 0,
  foi_replicada bit(1) null default 0,
  ativa bit(1) null default 1,
  origem_contatos varchar(255) default null,
  primary key (id)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


alter table campanha add column campanha_rede_id bigint(20) null;
alter table campanha add foreign key(campanha_rede_id) references campanha_rede(id);
alter table campanha add column foi_aceita bigint(20) not null default 1;

alter table  integracao_delivery add column nao_sincronizar_global bit(1) default null;


create table rede(
  id bigint(20) not null auto_increment,
  nome varchar(255) not null,
  primary key (id)
);

alter table empresa add column rede_id bigint(20) null;
create table usuario_rede(
  usuario_id bigint(20) not null,
  rede_id bigint(20) not null,
  constraint foreign key (usuario_id) references usuario(id),
  constraint foreign key (rede_id) references rede(id)
);
create table campanharede_rede(
  campanha_rede_id bigint(20) not null,
  rede_id bigint(20) not null,
  constraint foreign key (campanha_rede_id) references campanha_rede(id),
  constraint foreign key (rede_id) references rede(id)
);

alter table plano add column data_fim_acumulo date null;

alter table integracao_delivery add column nao_sincronizar_disponiveis bit(1) null;

alter table campanha add column inserir_link_desinscrever bit(1) default false;

alter table adicional_produto add column compartilhado bit(1) null default 0;


<-- novas colunas -->
alter table empresa add column dark bit(1) null default 0;
alter table grupo_de_lojas add column capa varchar(255) null;
alter table grupo_de_lojas add column logo varchar(255) null;
alter table grupo_de_lojas add column fundo varchar(255) null;

create table pizza_tamanho_sabores_ecletica(
  id bigint(20) not null auto_increment,
  nome varchar(255) not null,
  qtde_sabores int not null,
  produto_template_tamanho_id bigint(20) not null,
  codigo_pdv varchar(100) not null,
  empresa_id bigint(20) not null,
  foreign key(produto_template_tamanho_id) references produto_template_tamanho(id),
  foreign key(empresa_id) references empresa(id),
  primary key (id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

insert into  pizza_tamanho_sabores_ecletica(nome,qtde_sabores,codigo_pdv, produto_template_tamanho_id, empresa_id)
 values ('GIGANTE 1 SABOR', 1, '262', 285, 753),
        ('GIGANTE 2 SABORES', 2, '263', 285, 753),
        ('GIGANTE 3 SABORES', 3, '264', 285, 753),
        ('GIGANTE 4 SABORES', 4, '265', 285, 753),
        ('GRANDE 1 SABOR', 1, '266',284 , 753),
        ('GRANDE 2 SABORES', 2, '267',284 , 753),
        ('GRANDE 3 SABORES', 3, '268',284 , 753),
        ('MEDIA 1 SABOR', 1, '269',283 , 753),
        ('MEDIA 2 SABORES', 2, '270', 283, 753),
        ('PEQUENA 1 SABOR', 1, '271',282 , 753);

create table grupo_de_lojas_empresa (
  empresa_id bigint(20) not null,
  grupo_de_lojas_id bigint(20) not null,
  foreign key(empresa_id) references empresa(id),
  foreign key(grupo_de_lojas_id) references grupo_de_lojas(id)
);

alter table plano add column ativo bit(1) default true;
alter table usuario add column admin_grupo varchar(255) null;

<-- Indica qtde de mensagens que vai ser enviada -->
alter table campanha add column limitar_qtde_contatos bit(1) default false;
alter table campanha add column qtde_contatos_enviar_msg bigint(20) not null default 0;
alter table campanha add column nao_enviar_msg_para_quem_recebeu_recente bit(1) default false;
alter table campanha add column qtde_dias_ultima_notificacao bigint(20) not null default 7;

alter table config_impressao add column cortar_automatico bit(1) not null default true;

alter table empresa add column empresa_principal_id bigint(20) null;
alter table grupo_de_lojas add column empresa_principal_id bigint(20) null;


alter table empresa_formas_de_entrega add column tempo_minimo_retirada int null;
alter table empresa_formas_de_entrega add column tempo_maximo_retirada int null;


alter table mensagem_bot add column nome varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL;

alter table pagamento_pedido add column bandeira varchar(50) null;
alter table pagamento_pedido add column metodo_pagamento varchar(50) null;
insert into forma_de_pagamento_integrada(sistema, descricao)
    values ('menew', '1# Dinheiro '),
           ('menew', '3# Maquineta p/ crédito'),
           ('menew', '30# Maquineta p/ débito'),
           ('menew', '77# Cartão online');


alter table produto add column nao_sincronizar bit(1) default false;

create table dados_instagram(
  id bigint(20) not null auto_increment,
  empresa_id bigint(20) not null,
  nome_instagram varchar(255) not null,
  image_profile_url text not null,
  access_token varchar(255) null,
  id_pagina_face varchar(255) null,
  access_token_pagina varchar(255) null,
  user_id_insta varchar(255) null,
  data_criacao datetime not null,
  primary key (id),
  foreign key(empresa_id) references empresa(id)
);

create table cancelamento_empresa(
  id bigint(20) not null auto_increment,
  motivo varchar(255) not null,
  horario datetime not null,
  operador_id bigint(20) not null ,
  empresa_id bigint(20) not null ,
  ativo bit(1) default true,
  operador_desativou_id bigint(20) null,
  horario_desativacao datetime null,
  foreign key(operador_id) references usuario(id),
  foreign key(empresa_id) references empresa(id),
    primary key (id)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

insert into modulo (nome) values ('chatbot instagram');

alter table cupom add column  restrito_contato_perdido bit(1) null;

<!--  mapear codigo tipo pagamento cielo para sistema integrado. ->>
alter table pagamento_pedido add column codigo_tipo_pagamento varchar(10)   null;
alter table forma_de_pagamento_integrada add column codigo varchar(10) null;

insert into forma_de_pagamento_integrada(sistema, descricao, codigo)
    values ('cielo', 'Cartão de Crédito', '1'),
           ('cielo', 'Cartão de Débito', '4' );

alter table bandeira_cartao_integrada_nova add column codigo_pdv_online varchar(50) null;
alter table bandeira_cartao_integrada_nova add column descricao_pdv_online varchar(50) null;
alter table bandeira_cartao_integrada_nova add column forma_de_pagamento_id bigint(20) null;
alter table bandeira_cartao_integrada_nova add foreign key(forma_de_pagamento_id) references forma_de_pagamento(id);

update bandeira_cartao_integrada_nova join forma_de_pagamento on bandeira_cartao_integrada_nova_id = bandeira_cartao_integrada_nova.id
    set forma_de_pagamento_id = forma_de_pagamento.id;


alter table produto_template add column  exibir_precos_tamanhos bit(1) default null;


create table if not exists mensagem_whatsapp(
                                   id bigint(20) not null auto_increment,
                                   mensagem longtext not null,
                                   versao int(11) not null,
                                   id_mensagem varchar(255) not null,
                                   id_mensagem_reply varchar(255) not null,
                                   id_mensagem_gs_reply varchar(255) not null,
                                   horario bigint(20) not null,
                                   tipo varchar(255) not null,
                                   tipo_payload varchar(255) not null,
                                   status varchar(255) not null,
                                   country_code char(2) not null,
                                   telefone varchar(255) not null,
                                   dial_code varchar(255) not null,
                                   nome varchar(255) not null,
                                   empresa_id bigint(20) not null,
                                   horario_modificacao bigint(20) not null,
                                   conversa_id bigint(20) not null,
                                   mensagem_completa JSON,
                                   primary key (id),
                                   foreign key(conversa_id) references conversa(id)
)engine=innodb default charset=utf8mb4 COLLATE=utf8mb4_bin;

create table if not exists conversa(
  id bigint(20) not null auto_increment,
  chat_id varchar(255) not null,
  empresa_id bigint(20) not null,
  primary key (id)
);


alter table grupo_de_lojas add column nao_exibir_busca_de_lojas_por_cep bit(1) default false;

alter table forma_de_pagamento add column pix bit(1) not null default false;
alter table pagamento_pedido add column codigo_qr_code text null;

alter table empresa add column agrupar_adicionais bit(1) default false;

alter table produto add column eh_brinde bit(1) default false;

alter table categoria add column nivel int default 1;
alter table categoria add column categoria_pai_id bigint(20)  null;



alter table integracao_delivery add column ultima_sincronizacao_produtos datetime null;
alter table integracao_delivery add column ultima_sincronizacao_precos datetime null;


create or replace  view produto_categorias
  as select produto.id , c1.id categoria1, c2.id categoria2, c3.id categoria3
    from produto join categoria c1 on c1.id = produto.categoria_id left join categoria c2 on c2.id = c1.categoria_pai_id left join categoria c3 on c3.id = c2.categoria_pai_id
        where   removido is not true group by produto.id;

 CREATE TABLE municipio(
   estado varchar(100)  NOT NULL,
   codigo_ibge int not null,
   nome varchar(100)  NOT NULL
  ) ENGINE=InnoDB ;

update cidade join estado on estado.id = estado_id join municipio on municipio.nome = cidade.nome and estado.nome = estado
    set cidade.codigo_ibge = municipio.codigo_ibge;


 CREATE TABLE vitrine (
  id bigint NOT NULL AUTO_INCREMENT,
  nome varchar(100)  NOT NULL,
  empresa_id bigint NOT NULL,
  ordem int DEFAULT NULL,
  disponivel bit(1) DEFAULT b'0',
  PRIMARY KEY (id),
  KEY empresa_id (empresa_id),
  CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id)
 ) ENGINE=InnoDB ;

create table vitrine_produtos(
    vitrine_id bigint NOT NULL,
    produto_id bigint NOT NULL,
    posicao int not null,
      CONSTRAINT   FOREIGN KEY (vitrine_id) REFERENCES vitrine (id),
      CONSTRAINT   FOREIGN KEY (produto_id) REFERENCES produto (id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


alter table plano add column valor_minimo_resgate decimal(9,2) DEFAULT 0.00;

alter table produto_template_adicional add column dados_json longtext null;
alter table adicional_produto add column dados_json longtext null;

alter table pedido add column referencia_externa_delivery varchar(100) null;

create or replace view  ultima_acao as select max(acao_contato.id) id, empresa_id from acao_contato     group by empresa_id;

 alter table config_meio_pagamento add column   merchant_key varchar(100) null;
 alter table config_meio_pagamento add column   merchant_id varchar(100) null;

 alter table pagamento_pedido add column nsu varchar(50) null;
 alter table pagamento_pedido add column codigo_autorizacao varchar(50) null;
 alter table pagamento_pedido add column codigo_adquirente varchar(50) null;


CREATE TABLE forma_de_pagamento_integrada_nova (
  id bigint auto_increment not NULL,
  sistema varchar(50)   NOT NULL,
  descricao varchar(100)  NOT NULL,
  tipo_bandeira varchar(10)   NULL,
  codigo varchar(50)   NULL,
  primary key(id)
) ENGINE=InnoDB;


insert into forma_de_pagamento_integrada_nova (sistema, descricao, tipo_bandeira, codigo)
    select sistema, descricao, tipo_bandeira, descricao from  forma_de_pagamento_integrada;

insert into forma_de_pagamento_integrada_nova(sistema, descricao,codigo)
    values ('saipos', 'Dinheiro', 'DIN'),
           ('saipos', 'Cartão',  'CARD'),
           ('saipos', 'Cartão Crédito',  'CRE'),
           ('saipos', 'Cartão Débito',  'DEB'),
           ('saipos', 'Vale',  'VALE'),
           ('saipos', 'Pagamento Online','PARTNER_PAYMENT'),
           ('saipos', 'Outro', 'OTHER');

alter table forma_de_pagamento add column forma_de_pagamento_integrada_id bigint null;
alter table forma_de_pagamento add foreign key(forma_de_pagamento_integrada_id) references forma_de_pagamento_integrada_nova(id);

update forma_de_pagamento , integracao_delivery,   forma_de_pagamento_integrada_nova set forma_de_pagamento_integrada_id = forma_de_pagamento_integrada_nova.id
          where forma_de_pagamento_integrada_nova.descricao = referencia_externa and integracao_delivery.sistema = forma_de_pagamento_integrada_nova.sistema;

alter table produto add column origem varchar(255) null;
alter table produto add column data_cadastro datetime null;

alter table integracao_delivery DROP FOREIGN KEY fk_integracao_sistema;
alter table empresa_formas_de_entrega add column limite_pedidos_agendados int null;


alter table empresa add column tema varchar(50) null;
alter table empresa_formas_de_entrega add column nao_perguntar_horario bit(1) default 0;


alter table contato add column bloqueado bit(1) not null default 0;
alter table pagamento_pedido add column email varchar (50) null;

create table contato_bloqueado(
  id bigint NOT NULL AUTO_INCREMENT,
  telefone varchar(50) not null,
  empresa_id bigint(20) not null,
  contato_id bigint(20) not null,
  unique key(telefone),
  primary key (id)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

alter table empresa_formas_de_entrega add column complemento_obrigatorio bit(1) default 0;

alter table empresa_formas_de_entrega add column permite_usar_gps bit(1) default 0;

alter table empresa_formas_de_entrega add column complemento_obrigatorio bit(1) default 0;

alter table plano add column renovar_ao_pontuar bit(1) not null default false;

alter table forma_de_pagamento_integrada_nova modify column  codigo varchar(50);
insert into forma_de_pagamento_integrada_nova(sistema, descricao, codigo)
values ('bluesoft', 'Dinheiro','DINHEIRO'),
       ('bluesoft', 'Cartão de Crédito', 'CARTAO_DE_CREDITO'),
       ('bluesoft', 'Pix', 'PAGAMENTO_INSTANTEO_PIX'),
       ('bluesoft', 'Cartão de débito', 'CARTAO_DE_DEBITO'),
       ('bluesoft', 'Boleto' ,'BOLETO_BANCARIO'),
       ('bluesoft', 'Programa Fidelidade', 'PROGRAMA_FIDELIDADE_CASHBACK_CREDITO_VIRTUAL'),
       ('bluesoft', 'Deposito Bancario', 'DEPOSITO_BANCARIO'),
       ('bluesoft', 'Outros', 'OUTROS');



alter table forma_de_pagamento add column enviar_como_desconto bit(1) null;


alter table empresa_formas_de_entrega add column perguntar_endereco_inicio bit(1) default 0;

alter table empresa_formas_de_entrega add column permite_comer_no_local bit(1) default null ;
alter table pedido add column comer_no_local bit(1) default null ;

alter table grupo_de_lojas_empresa add column id bigint auto_increment not NULL primary key;
alter table grupo_de_lojas_empresa add column listar_loja bit(1) default 1 not null;

alter table assinatura add column formas_de_pagamento varchar (150) null;
alter table assinatura modify column forma_de_pagamento varchar(50) null;
update assinatura set formas_de_pagamento = forma_de_pagamento;

alter table grupo_de_lojas add column pagina_direcionar_para varchar(50) not null;
update grupo_de_lojas set pagina_direcionar_para = 'cardapio';
alter table grupo_de_lojas add column msg_conversa_whatsapp varchar(255) null;



alter table pagamento_pedido add  column final_cartao varchar(50) null;
create index pedido_guid on pedido(guid);


alter table grupo_de_lojas add column pixel_facebook varchar(255) null;



alter table cupom   add column categoria_id bigint(20) null;
alter table cupom add FOREIGN KEY (categoria_id) REFERENCES categoria(id);
alter table cupom   add column permitir_comprar_com_promocao bit(1) default null;

alter table grupo_de_lojas add column codigo_gtm varchar(255) null;

alter table grupo_de_lojas add column analytics varchar(255) null;


CREATE TABLE notificacao_venda (
   id bigint NOT NULL AUTO_INCREMENT,
   origem varchar(255)  NOT NULL,
   codigo varchar(255)  not NULL,
   rede varchar(50)    NULL,
   loja varchar(50)    NULL,
   valor  decimal(9,2) null,
   horario_notificacao datetime not null,
   horario_venda datetime   NULL,
   dados longtext   null ,
   executada bit(1) DEFAULT 0,
   ignorar bit(1) DEFAULT 0,
   estorno bit(1) DEFAULT 0,
   erro varchar(255)  DEFAULT NULL,
   empresa_id bigint DEFAULT NULL,
  PRIMARY KEY (id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


alter table contato modify token varchar(10) not null;

alter table produto add column sku varchar(20) null;
alter table produto add column pontos_ganhos int null;
alter table produto add column cashback decimal(9,3) null;


alter table empresa_formas_de_entrega add  valor_maximo_pedido  decimal(9,2)   null;
alter table forma_de_pagamento add column voucher bit(1) null;

alter table produto ADD FULLTEXT(nome);
alter table produto ADD FULLTEXT(descricao);
alter table categoria ADD FULLTEXT(nome);
alter table opcao_adicional_produto ADD FULLTEXT(nome);


alter table pedido add column num_cupom_fiscal varchar (100) null;

alter table  assinatura add column codigo_pai varchar(255) null;
alter table  assinatura modify column codigo varchar(255)   null;


alter table forma_de_pagamento add column numero_parcelas_fixas int null;
alter table pagamento_pedido add column parcela int null;

alter table  cupom add column aplicar_na_fidelidade bit(1) null;

alter table notificacao add column  marcar_como_lida bit(1) null default 1;


create table empresa_pedidos_resumo(
  empresa_id bigint not null ,
  qtd_24_horas int not null default  0,
  qtd_7_dias int not null default  0,
  qtd_mes int not null default  0,
  primary  key (empresa_id)
);


alter table integracao_delivery add column  ultima_sincronizacao_estoque datetime null;


alter table cupom add column produto_nao_compor_minimo bit(1) default null;
update cupom set produto_nao_compor_minimo = true  where produto_id is not null ;



create table pontuacao_estornada(
  id bigint(20) not null auto_increment,
  motivo varchar(255) not null,
  horario datetime not null,
  empresa_id bigint(20) not null ,
  operador_id bigint(20) null,
  pontuacao_registrada_id bigint(20) null,
  primary key (id),
  unique(pontuacao_registrada_id),
  foreign key(operador_id) references usuario(id),
  foreign key(empresa_id) references empresa(id),
  foreign key(pontuacao_registrada_id) references pontuacao_registrada(id)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

alter table grupo_de_lojas add column tela_multi_loja bit(1) default 0;

alter table bandeira_cartao_integrada_nova modify codigo_pdv varchar(100) not null;

insert into forma_de_pagamento_integrada_nova(sistema,codigo, descricao,tipo_bandeira)
    values('gcom', 'CREDIT', 'Crédito', 'C'),
          ('gcom', 'DEBIT', 'Débito', 'C'),
           ('gcom', 'MEAL_VOUCHER', 'Vale refeição', null),
           ('gcom', 'CASH', 'Dinheiro', null),
           ('gcom', 'PIX', 'Pix', null);


alter table produto_template  add column removido bit(1)   null;

/* migrador sessao_link_saudacao empresa_id */

update sessao_link_saudacao set empresa_id =
(select empresa_id from contato where sessao_link_saudacao.contato_id = contato.id);
alter table sessao_link_saudacao add column empresa_id bigint(20) null;

create index idx_msg_enviada_horario_tipo on mensagem_enviada(empresa_id, tipo_de_notificacao, horario);
create index idx_empresa_rede2 on empresa(id, rede);
create index idx_sessao_link_saudacao_empresa on sessao_link_saudacao(empresa_id);
create index idx_sessao_link_saudacao_empresa_horario on sessao_link_saudacao(empresa_id, horario);
alter table sessao_link_saudacao add foreign key(contato_id) references contato(id);

alter table sessao_link_saudacao add column empresa_id bigint(20) null;

insert into rede(nome) values('flyingsushi');

update empresa set rede_id = 4 where rede = 'flyingsushi';


alter table empresa add column gtag varchar(255) null;


alter table contato add column data_primeira_compra datetime null;
 update contato , (select min(horario) primeira_compra, contato_id  from pedido group by contato_id ) pedido
    set data_primeira_compra = pedido.primeira_compra where contato.id = pedido.contato_id;

alter table integracao_pedido_fidelidade add column pontuar_so_loja bit(1) null;

alter table item_pedido modify column qtde decimal(9,3) NOT NULL;

alter table notificacao drop column  marcar_como_lida;
alter table notificacao add column  marcar_como_lida bit(1) null default 1;



alter table sessao_link_saudacao add column chave varchar(255) null;
alter table sessao_link_saudacao add column dia date null;
update sessao_link_saudacao set dia = horario;
create index idx_sessao_link_saudacao_empresa_dia on sessao_link_saudacao(empresa_id, dia);

/* add papeis no sistema */

CREATE TABLE papel (
  id bigint NOT NULL AUTO_INCREMENT,
  nome varchar(255) NOT NULL,
  escopo varchar(100) null,
    empresa_id  bigint NOT NULL,
  PRIMARY KEY (id),
       CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa(id)
) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE operacao_do_sistema (
  id bigint NOT NULL AUTO_INCREMENT,
  descricao varchar(255) NOT NULL,
  nome varchar(255) NOT NULL,
  PRIMARY KEY (id)
 ) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE papel_operacao (
   papel_id bigint NOT NULL,
   operacao_id bigint DEFAULT NULL,
   KEY   (papel_id),
   KEY   (operacao_id),
   CONSTRAINT   FOREIGN KEY (papel_id) REFERENCES papel (id),
   CONSTRAINT   FOREIGN KEY (operacao_id) REFERENCES operacao_do_sistema (id)
 ) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


CREATE TABLE usuario_papel (
   usuario_id bigint DEFAULT NULL,
   papel_id bigint NOT NULL,
   KEY   (papel_id),
   KEY   (usuario_id),
      CONSTRAINT   FOREIGN KEY (papel_id) REFERENCES papel (id),
      CONSTRAINT   FOREIGN KEY (usuario_id) REFERENCES usuario(id)
 ) engine=innodb default charset=utf8 COLLATE=utf8_unicode_ci;


insert into operacao_do_sistema(nome,descricao) values ('Gerenciar loja', 'Permite gerenciar loja: formas de entrega, formas de pagamento, horários e outros');
insert into operacao_do_sistema(nome,descricao) values ('Cadastrar produtos', 'Permite criar, editar e remover produtos');
insert into operacao_do_sistema(nome,descricao) values ('Cadastrar contatos', 'Permite criar, editar e remover contatos');
insert into operacao_do_sistema(nome,descricao) values ('Editar pedido', 'Permite editar pedidos');
insert into operacao_do_sistema(nome,descricao) values ('Alterar status pedido', 'Permite mudar status do pedido, exceto cancelar');
insert into operacao_do_sistema(nome,descricao) values ('Cancelar pedido', 'Permite cancelar pedidos');
insert into operacao_do_sistema(nome,descricao) values ('Editar notificações', 'Permite ativar, desativar e alterar mensagens de notificações');
insert into operacao_do_sistema(nome,descricao) values ('Visualizar relatorio vendas', 'Permite visualizar relatorio pedidos, pontos fidadelidade');


insert into papel(empresa_id, nome, escopo) select id,'Gerenciar Loja', 'loja' from empresa;
insert into papel(empresa_id, nome, escopo) select id,'Operador', 'loja' from empresa;


insert into papel_operacao(papel_id,operacao_id)  select id, 1 from papel where nome = 'Gerenciar Loja';
insert into papel_operacao(papel_id,operacao_id)  select id, 8 from papel where nome = 'Gerenciar Loja';

insert into papel_operacao(papel_id,operacao_id)  select id, 2 from papel ;
insert into papel_operacao(papel_id,operacao_id)  select id, 3 from papel ;
insert into papel_operacao(papel_id,operacao_id)  select id, 4 from papel;
insert into papel_operacao(papel_id,operacao_id)  select id, 5 from papel ;
insert into papel_operacao(papel_id,operacao_id)  select id, 6 from papel ;
insert into papel_operacao(papel_id,operacao_id)  select id, 7 from papel ;


insert into usuario_papel(usuario_id, papel_id)
  select usuario.id, papel.id from usuario join papel on papel.empresa_id = usuario.empresa_id
    where operador is true and papel.nome = 'Operador';

insert into usuario_papel(usuario_id, papel_id)
      select usuario.id, papel.id from usuario join papel on papel.empresa_id = usuario.empresa_id
        where operador is not true and garcom is not true and papel.nome = 'Gerenciar Loja';

create table telefone_cloud_whatsapp(
  id bigint(20) not null auto_increment,
  empresa_id bigint(20) not null,
  nome varchar(255) not null,
  telefone varchar(50) not null,
  access_token_usuario_de_sistema varchar(255) null,
  status varchar(255) null,
  rating varchar(255) null,
  waba_id varchar(255) null,
  whatsapp_id varchar(255) null,
  data_criacao datetime not null,
  primary key (id),
  foreign key(empresa_id) references empresa(id)
);

alter table config_meio_pagamento add column establishment_code varchar(50) null;
alter table config_meio_pagamento add column merchant_name varchar(100) null;
alter table config_meio_pagamento add column mcc varchar(20) null;

update config_meio_pagamento set
   establishment_code = '1118783864',merchant_name = 'COMERCIO DE ALIMENTOS SANTA CR',
   mcc = '5411', client_id = '2714c370-d8cb-445f-a1a1-57f69eff622d', client_secret = 'AwepMqCszj6LKfpV+jtNuJH0syCVXyJAmgPs+rifjP8='
  where id = 89 and meio_de_pagamento = 'cielocheckout';

