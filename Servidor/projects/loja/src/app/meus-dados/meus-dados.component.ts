import { Component, OnInit } from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {DominiosService} from "../../services/dominios.service";
import {ITela} from "../../objeto/ITela";
import {AutorizacaoLojaService} from "../../services/autorizacao-loja.service";
import {CarrinhoService} from "../../services/carrinho.service";
import {CadContatoLojaComponent} from "../cad-contato/cad-contato-loja.component";
import {DialogService} from "@progress/kendo-angular-dialog";
import {Location} from "@angular/common";

@Component({
  selector: 'app-meus-dados',
  templateUrl: './meus-dados.component.html',
  styleUrls: ['./meus-dados.component.scss']
})
export class MeusDadosComponent implements OnInit, ITela {
  usuario: any = {};
  enderecos: any = [];

  constructor(private autorizacao: AutorizacaoLojaService ,
              private router: Router, private dominiosService: DominiosService,
              private dialogService: DialogService, private location: Location,
              private activatedRoute: ActivatedRoute,
              private carrinhoService: CarrinhoService) {
    this.usuario = autorizacao.getUsuario();
  }


  ngOnInit(): void {
  }

  deveExibirMenu() {
    return true;
  }

  deveExibirTopo() {
    return false;
  }

  deveTerBordas() {
    return true;
  }

  deveExibirBannerTema() {
    return false;
  }

  removerConta() {
    this.autorizacao.removaConta(this.usuario).then( (repsosta) => {
      this.autorizacao.logout().then( (erro) => {
        if(!erro){
          this.carrinhoService.limpeContatoPedido();
          window.location.href = this.dominiosService.obtenhaUrlHome();
        } else {
          alert(erro)
        }
      })
    });
  }

  abraModalAtualizarCadastro(){
    this.usuario.atualizarCadastro = true;
    CadContatoLojaComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
      this.dialogService, this.usuario, (result: any) => {
        if(result && result.id){
          this.usuario = this.autorizacao.getUsuario();
        }
      }) ;

    return false;
  }

  nomeIncomplento() {
    return !this.usuario.nome || this.usuario.nome.split(' ').length === 1
  }
}
