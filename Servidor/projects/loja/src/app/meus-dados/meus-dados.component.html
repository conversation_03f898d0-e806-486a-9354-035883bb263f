<app-header-loja [titulo]="'Meus Dados'"></app-header-loja>

<div class="linha" *ngIf="usuario.nome">
  <label>Nome completo</label>
  <p>{{usuario.nome}}


    <button type="button" class="btn btn-sm btn-outline-blue" *ngIf="nomeIncomplento()"
            (click)="abraModalAtualizarCadastro()">
      <i class="fa fa-edit"></i>
    </button>
  </p>

</div>

<div class="linha" *ngIf="usuario.email">
  <label>Email</label>
  <p>
    {{usuario.email}}
  </p>
</div>


<div class="linha">
  <label>Telefone</label>

  <p>
    {{usuario.telefone | telefone}}
  </p>

</div>

<div class="linha" *ngIf="usuario.cpf">
  <label>CPF</label>
  <p>{{usuario.cpf | cpf}}</p>

</div>

<div class="linha" *ngIf="usuario.dataNascimento">
  <label>Data De Nascimento</label>
  <p>{{usuario.dataNascimento | date: 'dd/MMM/yyyy'}}</p>

</div>


<div class="linha" [hidden]="!enderecos ||  !enderecos.length">
  <label>Endereços</label>

  <p *ngFor="let endereco of enderecos">
     {{endereco | json}}
  </p>

</div>

<form class="k-form mt-2" [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
      novalidate #frm="ngForm" (ngSubmit)="removerConta()">
  <button type="submit" class="btn btn-outline-danger ">Remover Minha Conta</button>
</form>
