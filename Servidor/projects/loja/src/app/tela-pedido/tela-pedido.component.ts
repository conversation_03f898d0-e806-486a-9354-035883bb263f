import { Component, OnInit } from '@angular/core';
import {ITela} from "../../objeto/ITela";
import {PedidosService} from "../../services/pedidos.service";
import {ActivatedRoute} from "@angular/router";
import {CarrinhoService} from "../../services/carrinho.service";
import {DominiosService} from "../../services/dominios.service";
import {EnderecoService} from "../../services/endereco.service";


@Component({
  selector: 'app-tela-pedido',
  templateUrl: './tela-pedido.component.html',
  styleUrls: ['./tela-pedido.component.scss']
})
export class TelaPedidoComponent implements OnInit, ITela {
  guid: string;
  pedido: any;
  exibirSomenteItens: boolean;
  msgErro: any;
  msg: any;
  carregou: boolean;
  validandoPedido: boolean;
  constructor(private activatedRoute: ActivatedRoute, private pedidosService: PedidosService,
              private enderecoService: EnderecoService,
              private carrinhoService: CarrinhoService, private dominiosService: DominiosService) {
    this.activatedRoute.params.subscribe(params => {
      this.guid = params['guid'];

      this.pedidosService.obtenhaPorGuid(this.guid).then( (pedido) => {
        this.carregou = true;
        this.pedido = pedido;
      }).catch( (erro) => {
        this.carregou = true;
      });
    });
  }

  ngOnInit(): void {
  }

  deveExibirTopo() {
    return false;
  }

  deveExibirMenu() {
    return false;
  }

  deveTerBordas() {
    return true;
  }

  deveExibirBannerTema() {
    return false;
  }

   pedirNovamente() {
    if( this.validandoPedido) return;

    delete   this.msgErro;
    this.validandoPedido = true;

    this.pedidosService.validePedirNovamente(this.pedido.guid).then( async (pedidoServer: any) => {
      this.carrinhoService.limpePedido();
      if(pedidoServer.endereco) {
        this.enderecoService.calculeTaxaDeEntrega(this.pedido.formaDeEntrega, pedidoServer.endereco,
          this.pedido.subvalor).then( async (resposta: any) => {
          await this.carrinhoService.refazerPedido(pedidoServer, resposta)
          this.validandoPedido = false;
          this.dominiosService.vaParaFinalizarPedido({ pedido: this.pedido});
        }).catch( erro => {
          console.error(erro)
          this.validandoPedido = false;
          this.msgErro = erro;
        })
      } else {
        await this.carrinhoService.refazerPedido(pedidoServer)
        this.validandoPedido = false;
        this.dominiosService.vaParaFinalizarPedido({ pedido: this.pedido});
      }
    }).catch((erro) => {
      console.error(erro)
      this.msgErro = erro;
      this.validandoPedido = false;
    });
  }
}
