import {
  Component,
  OnInit,
  ComponentFactoryResolver,
  ViewContainerRef,
  ViewChild,
  ElementRef,
  ViewChildren,
  QueryList,
  OnDestroy
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CarrinhoService } from '../../services/carrinho.service';
import { ConstantsService } from '../../services/ConstantsService';
import { ClienteService } from '../../services/cliente.service';
import { DominiosService } from '../../services/dominios.service';
import { ProdutoService } from '../../services/produto.service';
import { ITela } from '../../objeto/ITela';
import { DialogService } from "@progress/kendo-angular-dialog";
import { Location } from "@angular/common";
import { SiteProdutoComponent } from "../../site-produto/site-produto.component";
import { MyDetectorDevice } from "../shared/MyDetectorDevice";
import { ItemPedido } from "../../objeto/ItemPedido";

import { SiteProdutoTabletComponent } from '../../site-produto-tablet/site-produto-tablet.component';
import { AutorizacaoLojaService } from "../../services/autorizacao-loja.service";
import { SiteCampoAdicionalComponent } from "../site-campo-adicional/site-campo-adicional.component";
import { AdicionaisCustomizadosComponent } from "../adicionais-customizados/adicionais-customizados.component";
import { PedidosService } from "../../services/pedidos.service";
import {TabletService} from "./tablet.service";

declare var $;


@Component({
  selector: 'app-tablet-pedidos',
  templateUrl: './tablet-pedidos.component.html',
  styleUrls: ['./tablet-pedidos.component.scss']
})
export class TabletPedidosComponent implements OnInit, ITela, OnDestroy {
  mesaValida = false;
  pedido: any;
  empresa: any;
  idMesa: string;
  currentYear: number = new Date().getFullYear();
  erroValidarMesa = 'ID da mesa não informado.';
  notificacao = false;
  notificacaoTipo: 'success' | 'error' = 'success';
  notificacaoMensagem = '';
  notificacaoTimeout: any;

  pedidoConfirmado = false;
  erroPedido = false;
  mensagemErroPedido = '';

  // Propriedades para o cardápio
  categorias: any[] = [];
  produtos: any[] = [];
  produtosPorCategoria: any = {};
  produtosDestaque: any[] = [];
  carrinhoItens = 0;
  carregando = true;
  categoriaAtiva: any = null;
  produtosFiltrados: any[] = [];
  termoBusca = '';
  menuAberto = false;
  secaoAtiva = 'cardapio';

  // Propriedades para a seção de pedidos/conta
  abaAtiva = 'carrinho';
  historicoCarregado = false;
  carregandoHistorico = false;
  pedidosHistorico: any[] = [];

  // Adicionar propriedade para controlar exibição de produtos com valor zerado
  exibirProdutosValorZerado = false;

  // Adicionar propriedade para controlar exibição de preços
  exibirPrecos = true;

  // Adicionar ViewChild para o contêiner do painel lateral
  @ViewChild('painelLateral', { static: false }) painelLateral: ElementRef;
  @ViewChild('painelLateralContainer', { read: ViewContainerRef, static: false }) painelLateralContainer: ViewContainerRef;

  // Adicionar propriedades para controlar o painel lateral
  painelLateralAberto = false;
  produtoSelecionado: any = null;
  componenteRef: any = null;

  // Propriedades para o carrinho lateral
  carrinhoLateralAberto = false;

  categoriasParaExibicao: any[] = [];

  @ViewChildren(SiteCampoAdicionalComponent) ctrlAdicionais: QueryList<SiteCampoAdicionalComponent>;
  @ViewChild(AdicionaisCustomizadosComponent) adicionaisCustomizados: AdicionaisCustomizadosComponent;

  window: any;

  // Propriedades para o painel de adicionar produto
  itemPedidoAtual: ItemPedido;
  erro: any;
  estaRecebendoPedidos: boolean;
  mensagemAbrirPedidos: string;
  modoVisualizacao: boolean;
  modoVisualizacaoQRcode: boolean;
  permiteAgendamento: boolean;
  adicionandoProduto = false;
  painelAdicionarProdutoAberto = false;

  // Propriedades relacionadas ao carrinho
  aplicarCupom = false;
  calculandoTaxa = false;
  eviandoPedido = false;
  aplicandoCupom = false;
  erroCupom: string;

  // Propriedades para leitura de cartão do cliente
  leituraCartaoAberta = false;
  codigoCartao = '';
  videoElement: HTMLVideoElement;
  barcodeDetector: any;
  streamAtivo: MediaStream | null = null;
  dispositivosDisponiveis: MediaDeviceInfo[] = [];
  dispositivoAtual: string = '';
  leituraEmProgresso = false;
  numeroTablet: string;
  tablet: any;
  constructor(
    private carrinhoService: CarrinhoService,
    private constantsService: ConstantsService,
    private clienteService: ClienteService,
    public dominiosService: DominiosService,
    private produtoService: ProdutoService,
    private router: Router,
    private componentFactoryResolver: ComponentFactoryResolver,
    private autorizacaoLojaService: AutorizacaoLojaService,
    private dialogService: DialogService,
    private location: Location,
    private activatedRoute: ActivatedRoute,
    private deviceDetectorService: MyDetectorDevice,
    private pedidosService: PedidosService,
    private tabletService: TabletService
  ) {
    this.numeroTablet = tabletService.getNumeroTablet();

  }



  async ngOnInit(){
    // Ocultar o cabeçalho do site
    this.ocultarCabecalhoSite();

    this.carregando = true;

    await this.carregueInfoTablet();


    // Definir seção ativa inicial
    this.secaoAtiva = 'cardapio';

    // Carregar informações da empresa
    this.constantsService.empresa$.subscribe((empresa) => {
      if (!empresa || !empresa.id) return;

      this.empresa = empresa;
      // Verificar configuração para exibir produtos com valor zerado
      if (this.empresa.cardapio) {
        const tipoDeCardapio = this.obtenhaTipoCardapio();
        this.exibirProdutosValorZerado = this.empresa.cardapio?.exibirProdutosValorZeradoMesa && tipoDeCardapio === 'MESA';

        // Configurar exibição de preços com base na configuração da empresa
        this.exibirPrecos = this.empresa.cardapio?.exibirPrecos !== false; // Se não estiver definido, exibir por padrão
      }


      this.estaRecebendoPedidos = empresa.estaRecebendoPedidos;
      this.permiteAgendamento = empresa.permiteAgendamento;
      this.mensagemAbrirPedidos = empresa.mensagemAbrirPedidos;
      this.modoVisualizacao = empresa.cardapio && empresa.cardapio.modoVisualizacao;
      this.modoVisualizacaoQRcode = (empresa.cardapio && empresa.cardapio.modoVisualizacaoQRcode) ||
        (this.pedido && this.pedido.mesa && this.pedido.mesa.somenteLeitura);

      this.carregarProdutosECategorias();
    });

    // Inscrever-se nos eventos de alteração do pedido
    this.carrinhoService.alterouPedido.subscribe((pedidoAtualizado: any) => {
      if (pedidoAtualizado) {
        this.pedido = pedidoAtualizado;
        this.carrinhoItens = pedidoAtualizado.itens ? pedidoAtualizado.itens.length : 0;
      }
    });
  }

  async carregueInfoTablet(){
    this.numeroTablet = this.tabletService.getNumeroTablet();

    let tablet: any =  await this.tabletService.obterInfoTablet(this.numeroTablet).catch((err) => {
        this.erroValidarMesa = err;
    });

    if(tablet){
      this.tablet = tablet;

      if(tablet && tablet.mesa){
        this.idMesa =  tablet.mesa.id;
        await this.valideMesa();
      } else {
        this.vaParaConfigurarTablet();
      }
    }
  }

   vaParaConfigurarTablet(): void {
    // Redirecionar para o cardápio ou fechar a janela de configuração
    this.router.navigateByUrl(`/pedido-tablet/configurar`, { state: {table: this.tablet}}).then( () => { });
  }

  ngOnDestroy(): void {
    // Restaurar o cabeçalho do site quando o componente for destruído
    this.mostrarCabecalhoSite();
  }

  async valideMesa() {
    if (this.idMesa) {
      let mesa = await this.clienteService.obtenhaMesa(this.idMesa);

      if (mesa) {
        this.mesaValida = true;
        this.pedido = this.carrinhoService.obtenhaPedido(mesa);
      } else {
        this.mesaValida = false;
        this.pedido = this.carrinhoService.obtenhaPedido();
      }
    } else {
      this.mesaValida = false;
      this.pedido = this.carrinhoService.obtenhaPedido();
    }
  }

  // Método para ocultar o cabeçalho do site
  ocultarCabecalhoSite(): void {
    const cabecalho = document.querySelector('app-header');
    if (cabecalho) {
      cabecalho.classList.add('oculto-tablet-pedidos');
    }

    // Adicionar classe ao body para ajustar o layout
    document.body.classList.add('tablet-pedidos-ativo');
  }

  // Método para mostrar o cabeçalho do site
  mostrarCabecalhoSite(): void {
    const cabecalho = document.querySelector('app-header');
    if (cabecalho) {
      cabecalho.classList.remove('oculto-tablet-pedidos');
    }

    // Remover classe do body
    document.body.classList.remove('tablet-pedidos-ativo');
  }

  carregarProdutosECategorias(): void {
    const tipoCardapio = this.obtenhaTipoCardapio();

    this.produtoService.listeAhVenda(this.empresa, tipoCardapio, false).subscribe((resposta: any) => {
      console.log('Resposta do servidor:', resposta);
      this.produtos = resposta.produtos || [];
      this.categorias = resposta.categorias || [];

      console.log('Produtos carregados:', this.produtos.length);
      console.log('Categorias carregadas:', this.categorias.length);

      // Verificar estrutura dos produtos
      if (this.produtos.length > 0) {
        console.log('Exemplo de produto:', this.produtos[0]);
      }

      // Adicionar ícones para as categorias
      this.atribuirIconesParaCategorias();

      // Agrupar produtos por categoria
      this.agrupePorCategorias();

      // Definir produtos em destaque
      this.definirProdutosDestaque();
      console.log('Produtos em destaque:', this.produtosDestaque.length);

      // Selecionar categoria de destaques inicialmente
      this.selecionarCategoriaInicial();

      // Atualizar categorias para exibição
      this.categoriasParaExibicao = this.obtenhaCategoriasParaExibicao();

      this.carregando = false;
    });
  }

  obtenhaTipoCardapio(): string {
    return this.idMesa ? 'MESA' : 'DELIVERY';
  }

  agrupePorCategorias(): void {
    this.produtosPorCategoria = {};

    // Inicializar arrays vazios para cada categoria
    this.categorias.forEach(categoria => {
      this.produtosPorCategoria[categoria.id] = [];
    });

    // Agrupar produtos por categoria
    this.produtos.forEach(produto => {
      if (produto.categoria_id) {
        if (!this.produtosPorCategoria[produto.categoria_id]) {
          this.produtosPorCategoria[produto.categoria_id] = [];
        }

        this.produtosPorCategoria[produto.categoria_id].push(produto);
      }
    });
  }

  definirProdutosDestaque(): void {
    this.produtosDestaque = this.produtos.filter(produto => produto.destacado);
  }

  atribuirIconesParaCategorias(): void {
    // Usar o mesmo ícone para todas as categorias
    const iconeUnico = 'fa-tag';

    this.categorias.forEach((categoria) => {
      // Atribuir o mesmo ícone para todas as categorias
      categoria.icone = iconeUnico;

      // Verificar se a categoria tem imagem, se não, atribuir uma imagem padrão genérica
      if (!categoria.imagem) {
        categoria.imagemPadrao = 'categoria-default';
      }
    });
  }

  selecionarCategoriaInicial(): void {
    // Tentar encontrar categoria de destaques
    const categoriaDestaque = this.categorias.find(c => this.isCategoriaDestaque(c));

    if (categoriaDestaque) {
      this.navegarParaCategoria(categoriaDestaque);
    } else if (this.categorias.length > 0) {
      this.navegarParaCategoria(this.categorias[0]);
    }
  }

  verificarCarrinho(): void {
    const pedido = this.carrinhoService.obtenhaPedido();
    if (pedido && pedido.itens) {
      this.pedido = pedido; // Atualiza o pedido no componente
      this.carrinhoItens = pedido.itens.length;
    } else {
      this.carrinhoItens = 0;
    }
  }

  navegarParaCategoria(categoria: any): void {
    if (!categoria) return;

    this.categoriaAtiva = categoria;
    console.log('Categoria selecionada:', categoria);

    // Filtrar produtos pela categoria selecionada
    this.filtrarProdutosPorCategoria(categoria);

    // Fechar o menu em dispositivos móveis após selecionar uma categoria
    this.menuAberto = false;
  }

  // Método para verificar se o produto precisa de configuração e adicionar ao carrinho
  adicionarAoCarrinho(produto: any): void {
    console.log('Adicionando produto ao carrinho:', produto);

    // Verificar se o produto tem variações ou adicionais que precisam ser configurados
    const precisaConfigurar = true;

    if (precisaConfigurar) {
      this.abrirProdutoComoMenuLateral(produto);
    } else {
      // Se não precisar configurar, adicionar diretamente ao carrinho
      this.adicionarProdutoSimples(produto);
    }
  }

  // Método para abrir o produto como menu lateral
  abrirProdutoComoMenuLateral(produto: any): void {
    console.log('Abrindo produto como menu lateral:', produto);
    this.produtoSelecionado = produto;
    this.painelLateralAberto = true;

    setTimeout(() => {
      this.carregarComponenteProduto();
    }, 100);
  }

  // Método para carregar o componente SiteProdutoComponent no painel lateral
  carregarComponenteProduto(): void {
    if (!this.painelLateralContainer) {
      console.error('Container do painel lateral não encontrado');
      return;
    }

    try {
      this.painelLateralContainer.clear();
      const componentRef: any = this.painelLateralContainer.createComponent(SiteProdutoTabletComponent);
      this.componenteRef = componentRef;

      const instance = componentRef.instance;
      instance.produto = this.produtoSelecionado;
      instance.idProduto = this.produtoSelecionado.id;
      instance.pedido = this.carrinhoService.obtenhaPedido();

      // Adicionar handlers para os eventos
      instance.onAdicionarProduto.subscribe((itemPedido: any) => {
        this.adicionarItemAoCarrinho(itemPedido);
      });

      instance.onCancelar.subscribe(() => {
        this.fecharPainelLateral();
      });

      componentRef.changeDetectorRef.detectChanges();
    } catch (error) {
      console.error('Erro ao carregar componente:', error);
    }
  }

  // Método para adicionar item ao carrinho
  adicionarItemAoCarrinho(itemPedido: any): void {
    // Adicionar o item ao pedido
    const itemAdicionado = this.pedido.adicione(
      itemPedido.produto,
      itemPedido.qtde,
      itemPedido.observacao,
      itemPedido.adicionais,
      itemPedido.produtoTamanho,
      itemPedido.sabores
    );

    // Notificar o serviço do carrinho
    this.carrinhoService.notifiqueAdicionouCarrinho(itemAdicionado);

    // Atualizar descontos e taxas
    this.carrinhoService.atualizeDescontosETaxas(this.pedido, this.empresa).then(() => {
      // Atualizar contagem de itens no carrinho
      this.verificarCarrinho();

      // Mostrar notificação de sucesso com mais detalhes
      this.mostrarNotificacao(
        `${itemPedido.produto.nome} adicionado ao carrinho! • Total: ${this.formatarMoeda(this.pedido.total)}`,
        'success'
      );

      // Animar o ícone do carrinho
      this.animarCarrinho();

      // Abrir o carrinho lateral
      this.abrirCarrinhoLateral();
    });
  }

  // Método para formatar moeda
  formatarMoeda(valor: number): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(valor);
  }

  // Método para animar o carrinho
  animarCarrinho(): void {
    const carrinho = document.querySelector('.btn-carrinho');
    if (carrinho) {
      carrinho.classList.add('pulse');
      setTimeout(() => {
        carrinho.classList.remove('pulse');
      }, 1000);
    }
  }

  // Método para fechar o painel lateral
  fecharPainelLateral(): void {
    this.painelLateralAberto = false;

    setTimeout(() => {
      if (this.painelLateralContainer) {
        this.painelLateralContainer.clear();
      }
      this.componenteRef = null;
      this.produtoSelecionado = null;
    }, 300); // Aguardar a animação de fechamento
  }

  // Método para adicionar produto simples diretamente ao carrinho
  adicionarProdutoSimples(produto: any): void {
    console.log('Adicionando produto simples ao carrinho:', produto);

    // Adicionar diretamente ao pedido
    const pedido = this.carrinhoService.obtenhaPedido();
    this.carrinhoService.salvePedido(pedido);

    this.verificarCarrinho();

    // Mostrar notificação de sucesso
    this.mostrarNotificacao('Produto adicionado ao carrinho!', 'success');

    // Abrir o carrinho lateral
    this.abrirCarrinhoLateral();
  }

  // Método para mostrar notificação
  mostrarNotificacao(mensagem: string, tipo: 'success' | 'error'): void {
    this.notificacaoMensagem = mensagem;
    this.notificacaoTipo = tipo;
    this.notificacao = true;

    // Limpar notificação após 3 segundos
    if (this.notificacaoTimeout) {
      clearTimeout(this.notificacaoTimeout);
    }

    this.notificacaoTimeout = setTimeout(() => {
      this.notificacao = false;
    }, 3000);
  }

  navegarParaCarrinho(): void {
    this.router.navigate(['/carrinho']);
  }

  navegarParaPerfil(): void {
    this.router.navigate(['/perfil']);
  }

  buscarProdutos(): void {
    if (!this.termoBusca || this.termoBusca.trim() === '') {
      this.filtrarProdutosPorCategoria(this.categoriaAtiva);
      return;
    }

    const termo = this.termoBusca.toLowerCase();
    this.produtosFiltrados = this.produtos.filter(produto =>
      produto.nome.toLowerCase().includes(termo) ||
      (produto.descricao && produto.descricao.toLowerCase().includes(termo)) ||
      (produto.ingredientes && produto.ingredientes.toLowerCase().includes(termo))
    );
  }

  toggleMenu(): void {
    this.menuAberto = !this.menuAberto;
  }

  exibaNotificacao(mensagem: string, tipo: 'success' | 'error'): void {
    this.notificacaoMensagem = mensagem;
    this.notificacaoTipo = tipo;
    this.notificacao = true;

    clearTimeout(this.notificacaoTimeout);
    this.notificacaoTimeout = setTimeout(() => {
      this.notificacao = false;
    }, 3000);
  }

  obtenhaImagemProduto(produto: any): string {
    // Verificar se o produto tem um array de imagens
    if (produto && produto.imagens && produto.imagens.length > 0 && produto.imagens[0].linkImagem) {
      return 'https://fibo.promokit.com.br/images/empresa/' + produto.imagens[0].linkImagem;
    }

    // Fallback para o método antigo
    if (produto && produto.imagem) {
      // Verificar se a imagem já tem uma URL completa
      if (produto.imagem.startsWith('http')) {
        return produto.imagem;
      }
      return 'https://fibo.promokit.com.br/images/produto/' + produto.imagem;
    }

    // Imagem padrão se não encontrar correspondência
    return '/assets/images/produto-default.jpg';
  }

  obtenhaImagemCategoria(categoria: any): string {
    if (!categoria) return '/assets/images/categoria-default.jpg';

    // Verificar se a categoria tem uma imagem específica
    if (categoria.imagem) {
      // Verificar se a imagem já tem uma URL completa
      if (categoria.imagem.startsWith('http')) {
        return categoria.imagem;
      }
      return 'https://fibo.promokit.com.br/images/empresa/' + categoria.imagem;
    }

    // Se tiver uma imagem padrão atribuída
    if (categoria.imagemPadrao) {
      return `/assets/images/categorias/${categoria.imagemPadrao}.jpg`;
    }

    // Imagem padrão se não encontrar correspondência
    return '/assets/images/categoria-default.jpg';
  }

  formatarPreco(valor: number): string {
    return valor ? valor.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '0,00';
  }

  // Métodos auxiliares para o template
  isCategoriaDestaque(categoria: any): boolean {
    return categoria && categoria.nome && categoria.nome.toUpperCase().includes('DESTAQUE');
  }

  // Método para filtrar produtos por categoria
  filtrarProdutosPorCategoria(categoria: any): void {
    if (!categoria) {
      this.produtosFiltrados = [];
      return;
    }

    console.log('Filtrando produtos para categoria:', categoria.nome);

    // Filtrar produtos pela categoria selecionada
    let produtosFiltrados = this.produtos.filter(produto => {
      return produto.categoria_id === categoria.id;
    });

    // Se não encontrou produtos, tentar filtrar pelo nome da categoria
    if (produtosFiltrados.length === 0) {
      produtosFiltrados = this.produtos.filter(produto =>
        produto.categoria &&
        produto.categoria.nome &&
        produto.categoria.nome.toUpperCase().includes(categoria.nome.toUpperCase())
      );
    }

    console.log('Produtos filtrados por categoria (antes de verificar preços):', produtosFiltrados.length);

    // Filtrar produtos com valor zerado apenas se a configuração exigir
    if (!this.exibirProdutosValorZerado) {
      const produtosComPreco = produtosFiltrados.filter(produto => this.temPrecoValido(produto));

      // Se não houver produtos com preço válido, exibir todos os produtos da categoria
      if (produtosComPreco.length === 0 && produtosFiltrados.length > 0) {
        console.log('AVISO: Nenhum produto com preço válido. Exibindo todos os produtos da categoria.');
        this.produtosFiltrados = produtosFiltrados;
      } else {
        this.produtosFiltrados = produtosComPreco;
      }
    } else {
      this.produtosFiltrados = produtosFiltrados;
    }

    console.log('Produtos filtrados finais:', this.produtosFiltrados.length);

    // Verificar se há produtos para diagnosticar problemas
    this.verificarProdutos();
  }

  // Atualizar o método de verificação de preço válido com base no produto-container
  temPrecoValido(produto: any): boolean {
    // Se estiver configurado para exibir produtos com valor zerado, sempre retornar true
    if (this.exibirProdutosValorZerado) {
      return true;
    }

    // Usar os mesmos campos que o produto-container verifica
    return !produto.indisponivel &&
      (produto.preco > 0 || produto.valorMinimo > 0);
  }

  // Adicionar um método de depuração para verificar o motivo de não exibir produtos
  verificarProdutos(): void {
    console.log('Produtos totais:', this.produtos.length);

    if (this.categoriaAtiva) {
      console.log('Categoria ativa:', this.categoriaAtiva);
      const produtosCategoria = this.produtos.filter(p => p.categoria_id === this.categoriaAtiva.id);
      console.log('Produtos da categoria (antes de filtrar preço):', produtosCategoria.length);

      const produtosValidos = produtosCategoria.filter(p => this.temPrecoValido(p));
      console.log('Produtos com preço válido:', produtosValidos.length);

      // Forçar a exibição de todos os produtos da categoria para fins de diagnóstico
      if (produtosValidos.length === 0 && produtosCategoria.length > 0) {
        console.log('AVISO: Exibindo todos os produtos da categoria, ignorando validação de preço');
        this.produtosFiltrados = produtosCategoria;
      }
    }
  }

  navegarParaSecao(secao: string): void {
    this.secaoAtiva = secao;

    if (secao === 'destaques') {
      // Carregar produtos em destaque
      this.produtosFiltrados = this.produtosDestaque;
    } else if (secao === 'cardapio') {
      // Se estiver voltando para o cardápio, selecionar a categoria ativa
      if (this.categoriaAtiva) {
        this.filtrarProdutosPorCategoria(this.categoriaAtiva);
      } else {
        this.selecionarCategoriaInicial();
      }
    }

    // Fechar o menu em dispositivos móveis
    this.menuAberto = false;
  }

  // Método para obter categorias para exibição
  obtenhaCategoriasParaExibicao(): any[] {
    if (!this.categorias || this.categorias.length === 0) {
      return [];
    }

    // Ordenar categorias por posição (se disponível) ou por nome
    const categoriasOrdenadas = [...this.categorias].sort((a, b) => {
      // Se tiver um campo de posição, usar ele
      if (a.posicao !== undefined && b.posicao !== undefined) {
        return a.posicao - b.posicao;
      }
      // Caso contrário, ordenar por nome
      return a.nome.localeCompare(b.nome);
    });

    // Garantir que a categoria de destaques (se existir) seja a primeira
    const categoriaDestaque = this.categorias.find(c => this.isCategoriaDestaque(c));
    if (categoriaDestaque) {
      return [
        categoriaDestaque,
        ...categoriasOrdenadas.filter(c => c.id !== categoriaDestaque.id)
      ];
    }

    return categoriasOrdenadas;
  }

  // Método para verificar se uma categoria está selecionada
  verificaCategoriaSelecionada(categoria: any): boolean {
    return this.categoriaAtiva && this.categoriaAtiva.id === categoria.id;
  }

  // Método para finalizar o pedido
  navegarParaFinalizarPedido(): void {
    // Verificar se tem as informações necessárias para finalizar o pedido
    if (this.pedido && this.pedido.itens.length > 0) {
      this.router.navigate(['/carrinho']);
    } else {
      this.mostrarNotificacao('Adicione itens ao carrinho primeiro', 'error');
    }
  }

  // Método para remover item do carrinho
  removaItem(item: any): void {
    if (!this.pedido) return;

    this.pedido.removaItem(item);
    this.calculandoTaxa = true;

    this.carrinhoService.atualizeDescontosETaxas(this.pedido, this.empresa).then((erro: any) => {
      this.calculandoTaxa = false;
      if (!erro) {
        this.carrinhoService.salvePedido(this.pedido);
        this.verificarCarrinho();
        this.mostrarNotificacao('Item removido do carrinho', 'success');
      } else {
        this.mostrarNotificacao(erro, 'error');
      }
    });
  }

  // Método para toggle do cupom
  toggleAplicarCupom(): void {
    this.aplicarCupom = !this.aplicarCupom;
  }

  // Método para aplicar cupom
  apliqueCupom(): void {
    if (!this.pedido.codigoCupom || this.aplicandoCupom) return;

    delete this.erroCupom;
    this.aplicandoCupom = true;

    this.clienteService.calculeDescontoCupom(this.pedido.codigoCupom, this.pedido.obtenhaDadosEnvio(this.empresa)).then((cupom: any) => {
      this.aplicandoCupom = false;
      if (cupom.erro) {
        this.erroCupom = cupom.erro;
        this.mostrarNotificacao('Erro ao aplicar cupom: ' + cupom.erro, 'error');
      } else {
        this.pedido.apliqueCupom(cupom);
        this.carrinhoService.salvePedido(this.pedido);
        this.mostrarNotificacao('Cupom aplicado com sucesso!', 'success');
      }
    }).catch((erro) => {
      this.aplicandoCupom = false;
      this.erroCupom = 'Ops! Não foi possível verificar o cupom';
      this.mostrarNotificacao(this.erroCupom, 'error');
    });
  }

  // Método para remover cupom
  removaCupom(): void {
    if (this.pedido.cupom) {
      this.pedido.removaCupom();
      this.carrinhoService.salvePedido(this.pedido);
      this.mostrarNotificacao('Cupom removido', 'success');
    }
  }

  // Método para editar um item do carrinho
  editarItem(indice: number, item: any): void {
    console.log('Editando item:', item);
    // Usar o diálogo para abrir o produto
    this.window = SiteProdutoComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
      this.dialogService, this.deviceDetectorService.isMobile(), item.produto, indice + '');
  }

  deveExibirMenu(): boolean {
    return false;
  }

  deveExibirBarraDeBusca(): boolean {
    return false;
  }

  deveTerBordas(): boolean {
    return false;
  }

  deveExibirBannerTema(): boolean {
    return false;
  }

  deveExibirTopo(): boolean {
    return false;
  }

  deveExibirRodape(): boolean {
    return false;
  }

  // Método para obter o preço de exibição
  obtenhaPrecoExibicao(produto: any): number {
    // Priorizar valorMinimo se existir, caso contrário usar preco
    return produto.valorMinimo ? produto.valorMinimo : produto.preco;
  }

  // Método para verificar se deve exibir unidade
  exibirUnidade(produto: any): boolean {
    return produto.unidade === 'KG';
  }

  // Método para verificar preço antigo
  temPrecoAntigo(produto: any): boolean {
    return produto.precoAntigo && parseFloat(produto.precoAntigo) > 0;
  }

  // Método para verificar se o produto tem variações de preço
  temVariacaoDePreco(produto: any): boolean {
    if (!produto.precos || produto.precos.length <= 1) {
      return false;
    }

    // Filtrar preços válidos (maiores que zero)
    const precosValidos = produto.precos
      .filter(p => p && p.valor && p.valor > 0)
      .map(p => p.valor);

    // Verificar se há mais de um preço único
    const precosUnicos = [...new Set(precosValidos)];
    return precosUnicos.length > 1;
  }

  // Método para abrir os meus pedidos
  abrirMeusPedidos(): void {
    this.verificarCarrinho(); // Atualiza this.pedido com os dados do carrinho
    this.secaoAtiva = 'conta';
    this.selecionarAba('carrinho');

    // Se o carrinho estiver vazio, tenta carregar o histórico
    if (this.pedido && this.pedido.itens.length === 0) {
      this.carregarHistoricoPedidos();
    }
  }

  // Método para selecionar aba na seção de pedidos
  selecionarAba(aba: string): void {
    this.abaAtiva = aba;

    // Se for a aba de histórico e ainda não tiver carregado os pedidos
    if (aba === 'historico' && !this.historicoCarregado) {
      this.carregarHistoricoPedidos();
    }
  }

  // Método para carregar histórico de pedidos
  carregarHistoricoPedidos(): void {
    if (this.carregandoHistorico) {
      return;
    }

    this.carregandoHistorico = true;

    // Simulação de pedidos para demonstração
    setTimeout(() => {
      this.pedidosHistorico = [
        {
          codigo: '1001',
          data: new Date(new Date().getTime() - 4 * 60 * 60 * 1000), // 4 horas atrás
          status: 'Entregue',
          itens: [
            { qtde: 1, descricao: 'Pizza Calabresa Grande', total: 59.90 },
            { qtde: 2, descricao: 'Refrigerante 2L', total: 25.80 }
          ],
          total: 85.70
        },
        {
          codigo: '1002',
          data: new Date(new Date().getTime() - 60 * 60 * 1000), // 1 hora atrás
          status: 'Em preparação',
          itens: [
            { qtde: 1, descricao: 'Hambúrguer Artesanal', total: 32.90 },
            { qtde: 1, descricao: 'Batata Frita Grande', total: 18.90 },
            { qtde: 1, descricao: 'Milk Shake Chocolate', total: 15.90 }
          ],
          total: 67.70
        },
        {
          codigo: '1003',
          data: new Date(new Date().getTime() - 15 * 60 * 1000), // 15 minutos atrás
          status: 'Novo',
          itens: [
            { qtde: 2, descricao: 'Porção de Pastéis', total: 35.80 },
            { qtde: 1, descricao: 'Caipirinha', total: 14.90 }
          ],
          total: 50.70
        }
      ];

      this.historicoCarregado = true;
      this.carregandoHistorico = false;
    }, 1500);
  }

  // Método para repetir um pedido do histórico
  repetirPedido(pedidoHistorico: any): void {
    // Aqui você implementaria a lógica para copiar itens do pedido histórico para o carrinho atual
    this.mostrarNotificacao('Repetindo pedido #' + pedidoHistorico.codigo, 'success');
    this.selecionarAba('carrinho');
  }

  // Método para abrir o painel de adicionar produto
  abrirPainelAdicionarProduto(produto) {
    this.produtoSelecionado = produto;
    this.itemPedidoAtual = new ItemPedido(produto, produto.qtdeMinima || 1, '');
    this.itemPedidoAtual.adicionais = [];
    this.itemPedidoAtual.produtoTamanho = null;
    this.itemPedidoAtual.sabores = [];
    this.painelAdicionarProdutoAberto = true;
    this.erro = null;
  }

  // Método para fechar o painel de adicionar produto
  fecharPainelAdicionarProduto() {
    this.painelAdicionarProdutoAberto = false;
    this.produtoSelecionado = {};
    this.itemPedidoAtual = null;
    this.erro = null;
  }

  // Método para aumentar a quantidade
  aumentarQtde() {
    if (!this.itemPedidoAtual) return;

    this.itemPedidoAtual.qtde = Number((this.itemPedidoAtual.qtde + (this.itemPedidoAtual.produto.incremento || 1))
      .toFixed(3));

    if (this.produtoSelecionado.pesoMaximo != null && this.itemPedidoAtual.qtde > this.produtoSelecionado.pesoMaximo) {
      this.itemPedidoAtual.qtde = this.produtoSelecionado.pesoMaximo;
    }

    this.itemPedidoAtual.atualizeTotal();
  }

  // Método para diminuir a quantidade
  diminuirQtde() {
    if (!this.itemPedidoAtual) return;

    this.itemPedidoAtual.qtde = Number((this.itemPedidoAtual.qtde - (this.itemPedidoAtual.produto.incremento || 1))
      .toFixed(3));

    if (this.produtoSelecionado.pesoMinimo != null && this.itemPedidoAtual.qtde < this.produtoSelecionado.pesoMinimo) {
      this.itemPedidoAtual.qtde = (this.produtoSelecionado.pesoMinimo || this.itemPedidoAtual.produto.valorInicial);
    }
    else if (this.itemPedidoAtual.qtde < this.produtoSelecionado.qtdeMinima) {
      this.itemPedidoAtual.qtde = (this.produtoSelecionado.qtdeMinima || this.itemPedidoAtual.produto.valorInicial);
    }

    this.itemPedidoAtual.atualizeTotal();
  }

  // Método para posicionar no complemento com erro
  posicioneNoComplemento(campoAdicional) {
    const $controleAdicional = document.getElementById('adicional_' + campoAdicional.id);

    if ($controleAdicional) {
      const topo = $controleAdicional.offsetTop - 10;
      document.querySelector('.painel-lateral-content').scrollTo(0, topo);
    } else {
      setTimeout(() => {
        let $erros: any = document.getElementsByClassName('alert-danger');

        if (!$erros || !$erros.length)
          $erros = document.getElementsByClassName('badge-danger');

        if ($erros && $erros.length) {
          const topo = $erros[0].offsetTop - 40;
          document.querySelector('.painel-lateral-content').scrollTo(0, topo);
        }
      }, 0);
    }
  }

  // Método para adicionar o produto ao carrinho
  adicionarProduto() {
    delete this.erro;

    if (this.empresa.fechadoTemporariamente || (!this.estaRecebendoPedidos && !this.permiteAgendamento)) {
      return $("#alertaFechado").modal();
    }

    if (this.modoVisualizacaoQRcode && this.pedido.mesa) {
      this.mensagemAbrirPedidos = 'Cardápio De Mesa apenas para visualização. Chame o garçom para fazer seu pedido!';
      return $("#alertaFechado").modal();
    }

    if (this.modoVisualizacao) {
      this.mensagemAbrirPedidos = 'Faça seu pedido pelo nosso Whatsapp!';
      return $("#alertaFechado").modal();
    }

    if (this.adicionaisCustomizados) {
      let validos = this.adicionaisCustomizados.valideCampos();
      if (!validos) {
        return;
      }
    }

    this.itemPedidoAtual.produto.empresa = {
      id: this.empresa.id,
      nome: this.empresa.nome,
      logo: this.empresa.logo,
      dominio: this.empresa.dominio
    };

    if (this.produtoSelecionado.camposAdicionais) {
      let totalAdicionais = this.produtoSelecionado.camposAdicionais.length;

      for (let i = 0; i < totalAdicionais; i++) {
        const campoAdicional = this.produtoSelecionado.camposAdicionais[i];

        if (!this.itemPedidoAtual.valideCampoAdicional(campoAdicional)) {
          this.ctrlAdicionais.toArray()[i].exibaErro('Complemento é obrigatório');
          this.posicioneNoComplemento(campoAdicional);
          this.adicionandoProduto = false;
          return;
        }
      }

      let posicaoSabor = this.produtoSelecionado.camposAdicionais.length - 1;

      for (let i = 0; i < this.itemPedidoAtual.sabores.length; i++) {
        const sabor = this.itemPedidoAtual.sabores[i];
        if (sabor.produto !== this.produtoSelecionado.id) {
          for (let j = 0; j < sabor.camposAdicionais.length; j++) {
            posicaoSabor++;
            const campoAdicionalSabor = sabor.camposAdicionais[j];

            if (!this.itemPedidoAtual.valideCampoAdicional(campoAdicionalSabor)) {
              this.ctrlAdicionais.toArray()[posicaoSabor].exibaErro('Complemento é obrigatório');
              this.posicioneNoComplemento(campoAdicionalSabor);
              this.adicionandoProduto = false;
              return;
            }
          }
        }
      }
    }

    if (this.pedido.codigo) {
      this.erro = String(`O pedido #${this.pedido.codigo} está aguardando pagamento`);
    }

    let limiteProdutos = this.empresa.cardapio ? this.empresa.cardapio.limiteProdutos : null;

    if (limiteProdutos) {
      let qtdePedido = this.pedido.obtenhaQtdeItens(this.itemPedidoAtual) + this.itemPedidoAtual.qtde;

      if (qtdePedido > limiteProdutos) {
        this.erro = `Máximo de itens permitido no carrinho são ${limiteProdutos}`;
      }
    }

    if (!this.erro) {
      this.adicionandoProduto = true;

      let itemPedidoAdicionado = this.pedido.adicione(
        this.itemPedidoAtual.produto,
        this.itemPedidoAtual.qtde,
        this.itemPedidoAtual.observacao,
        this.itemPedidoAtual.adicionais,
        this.itemPedidoAtual.produtoTamanho,
        this.itemPedidoAtual.sabores
      );

      this.carrinhoService.atualizeDescontosETaxas(this.pedido, this.empresa).then(() => {
        this.adicionandoProduto = false;
        this.fecharPainelAdicionarProduto();

        // Mostrar notificação de sucesso
        this.mostrarNotificacao('Produto adicionado ao carrinho!', 'success');
      });
    } else {
      this.adicionandoProduto = false;
    }
  }

  // Método para abrir o carrinho
  abrirCarrinho(): void {
    // Abrir o carrinho lateral em vez de navegar para a seção de conta
    this.abrirCarrinhoLateral();
  }

  // Método para abrir o carrinho lateral
  abrirCarrinhoLateral(): void {
    // Fechar o painel lateral de produto se estiver aberto
    if (this.painelLateralAberto) {
      this.fecharPainelLateral();
    }

    // Abrir o carrinho lateral
    this.carrinhoLateralAberto = true;
  }

  // Método para fechar o carrinho lateral
  fecharCarrinhoLateral(): void {
    this.carrinhoLateralAberto = false;
  }

  // Método para enviar o pedido
  enviarPedido(): void {
    // Verificar se tem itens no pedido
    if (!this.pedido || this.pedido.itens.length === 0) {
      this.mostrarNotificacao('Adicione itens ao carrinho primeiro', 'error');
      return;
    }

    // Mostrar indicador de carregamento
    this.eviandoPedido = true;

    // Obter dados do pedido para envio
    const dadosEnvio = this.pedido.obtenhaDadosEnvio();

    dadosEnvio.contato = {
      telefone: '00000000000',
      codigoPais: '+55',
      nome: 'Consumidor não identificado',
      naoIdentificado: true
    };

    // Enviar o pedido para o servidor
    this.pedidosService.salvePedido(dadosEnvio)
      .then((resposta: any) => {
        if (resposta.erro) {
          // Se houver erro na resposta do servidor
          this.mostrarNotificacao(resposta.erro, 'error');
          this.eviandoPedido = false;
          return;
        }

        // Fechar o carrinho lateral
        this.fecharCarrinhoLateral();

        // Mostrar notificação de sucesso
        this.mostrarNotificacao('Pedido enviado com sucesso!', 'success');

        // Limpar o carrinho
        this.carrinhoService.limpePedido(this.pedido);

        // Atualizar o contador de itens
        this.carrinhoItens = 0;

        // Desativar indicador de carregamento
        this.eviandoPedido = false;

        // Navegar para a página de acompanhamento do pedido ou outra página relevante
        if (resposta && resposta.guid) {
          this.router.navigate(['/pedido', resposta.guid]);
        } else {
          this.router.navigate(['/meus-pedidos']);
        }
      })
      .catch((erro: any) => {
        console.error('Erro ao enviar pedido:', erro);
        // Capturar a mensagem de erro do servidor
        const mensagemErro = erro || 'Erro ao enviar pedido. Por favor, tente novamente.';
        this.mostrarNotificacao(mensagemErro, 'error');
        this.eviandoPedido = false;
      });
  }

  fecharDialogConfirmacao(): void {
    this.pedidoConfirmado = false;
  }

  limparErroPedido(): void {
    this.erroPedido = false;
    this.mensagemErroPedido = '';
  }

  // Métodos para leitura de cartão do cliente
  abrirLeituraCartao(): void {
    this.leituraCartaoAberta = true;
    this.codigoCartao = '';

    // Iniciar a câmera após a abertura do modal
    setTimeout(() => {
      this.iniciarCamera();
    }, 500);
  }

  fecharLeituraCartao(): void {
    this.leituraCartaoAberta = false;
    this.pararCamera();
  }

  pularLeituraCartao(): void {
    this.fecharLeituraCartao();

    if (this.secaoAtiva === 'conta') {
      this.navegarParaFinalizarPedido();
    } else {
      this.enviarPedido();
    }
  }

  confirmarCodigoCartao(): void {
    if (this.codigoCartao && this.codigoCartao.trim().length > 0) {
      // Adicionar o código do cartão ao pedido
      if (this.pedido) {
        this.pedido.codigoCartaoCliente = this.codigoCartao.trim();
      }

      this.fecharLeituraCartao();

      if (this.secaoAtiva === 'conta') {
        this.navegarParaFinalizarPedido();
      } else {
        this.enviarPedido();
      }
    } else {
      this.exibaNotificacao('Por favor, informe o código do cartão do cliente', 'error');
    }
  }

  async iniciarCamera(): Promise<void> {
    try {
      // Verificar se o BarcodeDetector é suportado
      if ('BarcodeDetector' in window) {
        // @ts-ignore - A API BarcodeDetector pode não estar definida no TypeScript
        this.barcodeDetector = new BarcodeDetector({
          formats: ['qr_code', 'code_128', 'code_39', 'ean_13', 'ean_8']
        });
      } else {
        console.warn('BarcodeDetector não suportado neste navegador');
      }

      // Obter referência ao elemento de vídeo
      this.videoElement = document.querySelector('#videoElement') as HTMLVideoElement;

      if (!this.videoElement) {
        console.error('Elemento de vídeo não encontrado');
        return;
      }

      // Listar dispositivos de câmera disponíveis
      const dispositivos = await navigator.mediaDevices.enumerateDevices();
      this.dispositivosDisponiveis = dispositivos.filter(
        device => device.kind === 'videoinput'
      );

      // Usar a câmera traseira por padrão, se disponível
      this.dispositivoAtual = this.obterCameraTraseira() || '';

      // Iniciar stream de vídeo
      const constraints = {
        video: {
          deviceId: this.dispositivoAtual ? { exact: this.dispositivoAtual } : undefined,
          facingMode: this.dispositivoAtual ? undefined : 'environment',
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      };

      this.streamAtivo = await navigator.mediaDevices.getUserMedia(constraints);
      this.videoElement.srcObject = this.streamAtivo;

      // Iniciar detecção de código quando o vídeo estiver pronto
      this.videoElement.onloadedmetadata = () => {
        this.videoElement.play();
        this.iniciarDeteccao();
      };
    } catch (error) {
      console.error('Erro ao iniciar câmera:', error);
    }
  }

  pararCamera(): void {
    this.leituraEmProgresso = false;

    if (this.streamAtivo) {
      this.streamAtivo.getTracks().forEach(track => track.stop());
      this.streamAtivo = null;
    }

    if (this.videoElement) {
      this.videoElement.srcObject = null;
    }
  }

  obterCameraTraseira(): string {
    // Tentar encontrar a câmera traseira
    const cameraTraseira = this.dispositivosDisponiveis.find(
      device => device.label.toLowerCase().includes('back') ||
                device.label.toLowerCase().includes('traseira') ||
                device.label.toLowerCase().includes('rear')
    );

    return cameraTraseira ? cameraTraseira.deviceId : '';
  }

  async iniciarDeteccao(): Promise<void> {
    if (!this.barcodeDetector || !this.videoElement) {
      return;
    }

    this.leituraEmProgresso = true;

    const detectar = async () => {
      if (!this.leituraEmProgresso || !this.videoElement || !this.barcodeDetector) {
        return;
      }

      try {
        const codigos = await this.barcodeDetector.detect(this.videoElement);

        if (codigos.length > 0) {
          // Usar o primeiro código encontrado
          this.codigoCartao = codigos[0].rawValue;

          // Executar em um timeout para permitir que a UI seja atualizada
          setTimeout(() => {
            this.confirmarCodigoCartao();
          }, 500);

          return;
        }
      } catch (error) {
        console.error('Erro na detecção de código:', error);
      }

      // Continuar detectando se nenhum código foi encontrado e a leitura ainda está em progresso
      if (this.leituraEmProgresso) {
        requestAnimationFrame(detectar);
      }
    };

    detectar();
  }
}
