import {Component, OnDestroy, OnInit, ViewChild} from "@angular/core";

import {ITela} from "../../../objeto/ITela";
import {TabletService} from "../tablet.service";
import {Router} from "@angular/router";

@Component({
  selector: 'app-tablet-pedidos-configurar',
  templateUrl: './tablet-pedidos-configurar.html',
  styleUrls: ['./tablet-pedidos-configurar.scss']
})
export class TabletPedidosConfigurarComponent implements OnInit, ITela, OnDestroy {
  // Estados da aplicação
  etapaAtual: 'login' | 'mesa' | 'confirmacao' | 'sucesso' = 'login';
  carregando = false;

  mensagemErro = '';
  mensagemSucesso = '';
  usuario: any;
  token: string;
  // Dados do formulário
  numeroTablet = '';
  garcomAutenticado: any = null;
  mesaAtual: any = null;
  mesaSelecionada: any = null;
  mostrandoTrocaMesa = false;

  // Lista de mesas
  mesas: any[] = [];
  tablet: any;
  constructor(private tabletService: TabletService, private router: Router) {
    if(window.history.state.tablet ){
      this.tablet = window.history.state.tablet;
      this.numeroTablet = this.tablet.numero;
      this.mesaAtual = this.tablet.mesa;
    } else {
      // Gerar número do tablet baseado no dispositivo ou usar um fixo para teste
      this.numeroTablet = tabletService.getNumeroTablet();
    }
  }


  ngOnInit(): void {

  }

  ngOnDestroy(): void {
  }


  autenticouUsuario(resposta: any){
    this.usuario =  resposta.usuario;
    this.token = resposta.token;

    // Definir o token no service para as próximas requisições
    this.tabletService.setToken(this.token);

    this.etapaAtual = 'mesa';
    this.mensagemSucesso = `Bem-vindo, ${this.usuario.nome}!`;


    this.verificarStatusTablet();

  }

  private verificarStatusTablet(): void {
    if(this.tablet){
      this.carregarMesas();
    } else {
      this.carregando = true;

      this.tabletService.obterInfoTablet(this.numeroTablet).then((tablet: any) => {
        this.tablet  = tablet;
        if(tablet.mesa)
          this.mesaAtual = tablet.mesa;
        this.carregarMesas();

      }).catch((err) => {
        this.mensagemErro = err;
        this.carregando = false;
      });
    }

  }

  private carregarMesas(): void {

    this.tabletService.listarMesas().then((mesas: any[]) => {
      this.mesas = mesas;
      this.carregando  = false;

      // Log temporário para debug
      console.log('Mesa atual:', this.mesaAtual);
      console.log('Mesas carregadas:', this.mesas);

      // Verificar se as IDs estão sendo comparadas corretamente
      if (this.mesaAtual) {
        this.mesas.forEach(mesa => {
          const ehMesaAtual = mesa.id === this.mesaAtual.id;
          console.log(`Mesa ${mesa.nome} (ID: ${mesa.id}) é atual? ${ehMesaAtual}`);
        });
      }

    }).catch((erro: any) => {
      this.carregando = false;
      this.mensagemErro = erro || 'Erro ao carregar mesas';
    });
  }



  selecionarMesa(mesa: any): void {
    // Se é a mesa atual, não fazer nada
    if (this.mesaAtual && mesa.id === this.mesaAtual.id) {
      return;
    }

    // Se mesa não está disponível e não é a atual, mostrar erro
    if (!mesa.disponivel) {
      this.mensagemErro = `Mesa "${mesa.nome}" já está associada ao tablet ${mesa.tablet?.numero}`;
      return;
    }

    this.mesaSelecionada = mesa;
    this.etapaAtual = 'confirmacao';
    this.mensagemErro = '';
  }

  iniciarTrocaMesa(): void {
    this.mostrandoTrocaMesa = true;
    this.mensagemErro = '';
    this.mensagemSucesso = '';
  }

  cancelarTrocaMesa(): void {
    this.mostrandoTrocaMesa = false;
    this.mesaSelecionada = null;
    this.mensagemErro = '';
  }

  voltarParaSelecaoMesa(): void {
    if (this.mesaAtual) {
      // Se já tem mesa configurada, volta para o estado inicial
      this.etapaAtual = 'mesa';
      this.mostrandoTrocaMesa = false;
    } else {
      // Se não tem mesa, volta para seleção
      this.etapaAtual = 'mesa';
    }
    this.mesaSelecionada = null;
    this.mensagemErro = '';
  }

  confirmarAssociacao(): void {
    if (!this.mesaSelecionada) return;

    this.carregando = true;
    this.mensagemErro = '';

    const acaoTexto = this.mesaAtual ? 'troca' : 'associação';

    this.tabletService.associarMesa(this.numeroTablet, this.mesaSelecionada.id).then((resposta: any) => {
      this.carregando = false;
      this.etapaAtual = 'sucesso';

      // Atualizar mesa atual
      const mesaAnterior = this.mesaAtual ? this.mesaAtual.nome : 'nenhuma';
      this.mesaAtual = resposta.mesa || this.mesaSelecionada;

      // Mensagem personalizada baseada na ação
      if (this.mostrandoTrocaMesa) {
        this.mensagemSucesso = `Mesa trocada com sucesso!\nDe: ${mesaAnterior}\nPara: ${this.mesaAtual.nome}`;
      } else {
        this.mensagemSucesso = `Tablet associado com sucesso à mesa ${this.mesaAtual.nome}!`;
      }

      // Reset estados
      this.mostrandoTrocaMesa = false;
      this.mesaSelecionada = null;

    }).catch((erro: any) => {
      this.carregando = false;
      this.mensagemErro = erro || `Erro ao realizar ${acaoTexto} de mesa. Tente novamente.`;
    });
  }

  voltarParaCardapio(): void {
    // Redirecionar para o cardápio ou fechar a janela de configuração
    this.router.navigateByUrl(`/pedido-tablet`, {}).then( () => { });
  }

  reiniciarConfiguracao(): void {
    this.etapaAtual = 'login';
    this.garcomAutenticado = null;
    this.mesaSelecionada = null;
    this.mensagemErro = '';
    this.mensagemSucesso = '';
    this.mostrandoTrocaMesa = false;

    // Limpar token do service
    this.tabletService.setToken('');
    this.token = '';
  }

  // Métodos da interface ITela
  deveExibirMenu(): boolean {
    return false;
  }

  deveExibirBarraDeBusca(): boolean {
    return false;
  }

  deveTerBordas(): boolean {
    return false;
  }

  deveExibirBannerTema(): boolean {
    return false;
  }

  deveExibirTopo(): boolean {
    return false;
  }

  deveExibirRodape(): boolean {
    return false;
  }
}
