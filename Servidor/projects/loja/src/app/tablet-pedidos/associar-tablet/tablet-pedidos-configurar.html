<div class="tablet-configuracao-container">
  <!-- Cabeçalho -->
  <div class="configuracao-header">
    <div class="lock-icon">
      <i class="fas fa-user-lock"></i>
    </div>
    <h2>Área Administrativa</h2>
  </div>

  <!-- Etapa 1: Login de garçom -->
  <div class="configuracao-content" *ngIf="etapaAtual === 'login'">
    <app-tablet-auth (onAutenticou)="autenticouUsuario($event)"></app-tablet-auth>

  </div>

  <!-- Etapa 2: Selecionar mesa -->
  <div class="configuracao-content mesa-selection" *ngIf="etapaAtual === 'mesa'">
    <div class="mesa-config-section">
      <div class="settings-icon">
        <i class="fas fa-cog"></i>
      </div>
      <h3>Configuração do Tablet
        <span class="garcom-info"  > <b> {{numeroTablet}}</b>   </span>
      </h3>

      <!-- Mesa atual do tablet -->
      <div class="mesa-atual-info" *ngIf="mesaAtual">
        <div class="alert alert-info">
          <div class="mesa-atual-header">
            <i class="fas fa-table"></i>
            <h4>Mesa Atual Configurada</h4>
          </div>
          <div class="mesa-atual-detalhes">
            <p><strong>{{mesaAtual.nome}}</strong></p>
            <p class="mesa-codigo" *ngIf="mesaAtual.codigoPdv">Código: {{mesaAtual.codigoPdv}}</p>
            <p class="texto-info">Este tablet está configurado para a mesa acima.</p>
          </div>
          <div class="mesa-atual-acoes">
            <button class="btn btn-warning btn-trocar-mesa" (click)="iniciarTrocaMesa()">
              <i class="fas fa-exchange-alt"></i>
              Trocar Mesa
            </button>
          </div>
        </div>
      </div>

      <!-- Mensagem quando não há mesa configurada -->
      <div class="sem-mesa-info" *ngIf="!mesaAtual && !carregando">
        <div class="alert alert-warning">
          <div class="sem-mesa-header">
            <i class="fas fa-exclamation-triangle"></i>
            <h4>Tablet sem Mesa Configurada</h4>
          </div>
          <p>Este tablet não está associado a nenhuma mesa. Selecione uma mesa abaixo para configurar.</p>
        </div>
      </div>

      <!-- Loading das mesas -->
      <div class="loading-mesas" *ngIf="carregando">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Carregando informações...</p>
      </div>

      <!-- Lista de mesas (só aparece se não há mesa atual ou se está trocando) -->
      <div class="mesas-container" *ngIf="!carregando && (!mesaAtual || mostrandoTrocaMesa)">
        <h4 *ngIf="mostrandoTrocaMesa">Selecione a Nova Mesa</h4>
        <h4 *ngIf="!mesaAtual">Selecione uma Mesa</h4>

        <div class="mesas-grid" *ngIf="mesas.length > 0">
          <div class="mesa-card"
               *ngFor="let mesa of mesas"
               [class.disponivel]="mesa.disponivel"
               [class.ocupada]="!mesa.disponivel"
               [class.mesa-atual]="mesaAtual && mesa.id === mesaAtual.id"
               [class.debug-mesa-atual]="mesaAtual && mesa.id === mesaAtual.id"
               [attr.data-mesa-id]="mesa.id"
               [attr.data-mesa-atual-id]="mesaAtual?.id"
               [attr.data-eh-mesa-atual]="mesaAtual && mesa.id === mesaAtual.id"
               (click)="selecionarMesa(mesa)"
               [ngStyle]="mesaAtual && mesa.id === mesaAtual.id ? {
                 'border-color': '#2196f3 !important',
                 'background': 'linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%) !important'
               } : {}">

            <div class="mesa-icon">
              <i class="fas fa-table"
                 [ngStyle]="mesaAtual && mesa.id === mesaAtual.id ? {'color': '#2196f3'} : {}"></i>
            </div>

            <div class="mesa-info">
              <h4>{{mesa.nome}}</h4>
              <p class="mesa-codigo" *ngIf="mesa.codigoPdv">Código: {{mesa.codigoPdv}}</p>
            </div>

            <div class="mesa-status">
              <div class="status-indicator" *ngIf="mesa.disponivel">
                <i class="fas fa-check-circle text-success"></i>
                <span class="status-text">Disponível</span>
              </div>
              <div class="status-indicator" *ngIf="!mesa.disponivel && (!mesaAtual || mesa.id !== mesaAtual.id)">
                <i class="fas fa-tablet-alt text-danger"></i>
                <span class="status-text">
                  Tablet {{mesa.tablet?.numero}}
                </span>
              </div>
              <div class="status-indicator" *ngIf="mesaAtual && mesa.id === mesaAtual.id">
                <i class="fas fa-check-circle text-primary"></i>
                <span class="status-text">Mesa Atual</span>
              </div>
            </div>

            <div class="selecionar-btn" *ngIf="mesa.disponivel || (mesaAtual && mesa.id === mesaAtual.id)">
              <i class="fas fa-arrow-right" *ngIf="mesa.disponivel"></i>
              <i class="fas fa-check" *ngIf="mesaAtual && mesa.id === mesaAtual.id"></i>
            </div>
          </div>
        </div>

        <!-- Botão cancelar troca -->
        <div class="acoes-troca mb-2" *ngIf="mostrandoTrocaMesa">
          <button class="btn btn-secondary" (click)="cancelarTrocaMesa()">
            <i class="fas fa-times"></i>
            Cancelar Troca
          </button>
        </div>
      </div>

      <!-- Mensagem quando não há mesas -->
      <div class="no-mesas" *ngIf="!carregando && mesas.length === 0">
        <i class="fas fa-exclamation-triangle"></i>
        <p>Nenhuma mesa encontrada</p>
      </div>

      <!-- Legenda -->
      <div class="legenda" *ngIf="!carregando && mesas.length > 0 && (!mesaAtual || mostrandoTrocaMesa)">
        <div class="legenda-item">
          <div class="cor-indicador verde"></div>
          <span>Disponível</span>
        </div>
        <div class="legenda-item">
          <div class="cor-indicador vermelho"></div>
          <span>Já associada</span>
        </div>
        <div class="legenda-item" *ngIf="mesaAtual">
          <div class="cor-indicador azul"></div>
          <span>Mesa atual</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Etapa 3: Confirmar troca/associação -->
  <div class="configuracao-content confirmacao-section" *ngIf="etapaAtual === 'confirmacao'">
    <div class="confirmacao-config-section">
      <div class="confirm-icon">
        <i class="fas fa-question-circle"></i>
      </div>
      <h3>Confirmar {{mesaAtual ? 'Troca' : 'Associação'}} de Mesa</h3>

      <div class="confirmacao-detalhes">
        <div class="tablet-info">
          <p><strong>Tablet:</strong> {{numeroTablet}}</p>
          <p><strong>Usuário:</strong> {{usuario.nome}}</p>
        </div>

        <div class="troca-visual" *ngIf="mesaAtual">
          <div class="mesa-origem">
            <h4>Mesa Atual</h4>
            <div class="mesa-card-small atual">
              <i class="fas fa-table"></i>
              <span>{{mesaAtual.nome}}</span>
              <small *ngIf="mesaAtual.codigoPdv">{{mesaAtual.codigoPdv}}</small>
            </div>
          </div>

          <div class="seta-troca">
            <i class="fas fa-arrow-right"></i>
            <span>TROCAR PARA</span>
          </div>

          <div class="mesa-destino">
            <h4>Nova Mesa</h4>
            <div class="mesa-card-small nova">
              <i class="fas fa-table"></i>
              <span>{{mesaSelecionada.nome}}</span>
              <small *ngIf="mesaSelecionada.codigoPdv">{{mesaSelecionada.codigoPdv}}</small>
            </div>
          </div>
        </div>

        <div class="associacao-visual" *ngIf="!mesaAtual">
          <div class="mesa-destino-nova">
            <h4>Associar Tablet à Mesa</h4>
            <div class="mesa-card-small nova">
              <i class="fas fa-table"></i>
              <span>{{mesaSelecionada.nome}}</span>
              <small *ngIf="mesaSelecionada.codigoPdv">{{mesaSelecionada.codigoPdv}}</small>
            </div>
          </div>
        </div>
      </div>

      <div class="action-buttons confirmacao-buttons">
        <button type="button"
                class="btn btn-secondary btn-voltar"
                (click)="voltarParaSelecaoMesa()">
          <i class="fas fa-arrow-left"></i>
          Voltar
        </button>

        <button type="button"
                class="btn btn-primary btn-confirmar"
                [disabled]="carregando"
                (click)="confirmarAssociacao()">
          <i class="fas fa-check" *ngIf="!carregando"></i>
          <i class="fas fa-spinner fa-spin" *ngIf="carregando"></i>
          Confirmar {{mesaAtual ? 'Troca' : 'Associação'}}
        </button>
      </div>
    </div>
  </div>

  <!-- Etapa 4: Sucesso -->
  <div class="configuracao-content" *ngIf="etapaAtual === 'sucesso'">
    <div class="success-section">
      <div class="success-icon">
        <i class="fas fa-check-circle"></i>
      </div>
      <h3>Mesa {{mesaAtual?.nome}} configurada com sucesso!</h3>
      <p>O tablet está associado à mesa e pronto para uso.</p>

      <div class="action-buttons">
        <button type="button"
                class="btn btn-primary"
                (click)="voltarParaCardapio()">
          Voltar para o Cardápio
        </button>
      </div>
    </div>
  </div>

  <!-- Mensagens de erro e sucesso -->
  <div class="alert alert-danger" *ngIf="mensagemErro">
    <i class="fas fa-exclamation-triangle"></i>
    {{mensagemErro}}
  </div>

  <div class="alert alert-success" *ngIf="mensagemSucesso && etapaAtual !== 'sucesso'">
    <i class="fas fa-check-circle"></i>
    {{mensagemSucesso}}
  </div>

  <!-- Informações do tablet -->
  <div class="tablet-info">
    <small class="text-muted">
      <i class="fas fa-tablet-alt"></i>
      Tablet: {{numeroTablet}}
    </small>
  </div>
</div>
