import {Component, EventEmitter, OnInit, Output, ViewChild} from "@angular/core";
import {NgForm} from "@angular/forms";
import {TabletService} from "../tablet.service";

@Component({
  selector: 'app-tablet-auth',
  templateUrl: './tablet-auth.html',
  styleUrls: ['./tablet-auth.scss']
})
export class TabletAuthComponent implements OnInit{
  @ViewChild('frmLogin') frmLogin: NgForm;
  @Output() onAutenticou: any  = new EventEmitter();
  // Configurações
  tentativasLogin = 0;
  maxTentativas = 10;
  bloqueadoAte: Date | null = null;
  login = '';
  senha = '';
  carregando: boolean;
  mensagemErro: string;
  constructor(private tabletService: TabletService) {}

  ngOnInit(): void {
    this.login = '';
    this.senha = '';
  }

  autenticarGarcom(): void {
    if (!this.frmLogin.valid ||   this.carregando) return;

    this.carregando = true;
    this.mensagemErro = '';

    this.tabletService.autenticarGarcom(this.login, this.senha).then((resposta: any) => {
      this.carregando = false;

      if (resposta.token) {
        this.tentativasLogin = 0;
        this.onAutenticou.emit(resposta);
      } else {
        this.tentativasLogin++;
        const tentativasRestantes = this.maxTentativas - this.tentativasLogin;
        this.mensagemErro = `Login ou senha incorretos`;
      }
    }).catch((erro: any) => {
      this.carregando = false;
      this.mensagemErro = erro || 'Erro ao autenticar. Tente novamente.';
    });
  }

  private estaBloqueado(): boolean {
    return this.bloqueadoAte !== null && new Date() < this.bloqueadoAte;
  }

  private bloquearTemporariamente(): void {
    this.bloqueadoAte = new Date(Date.now() + 5 * 60 * 1000); // 5 minutos
    this.mensagemErro = 'Muitas tentativas incorretas. Acesso bloqueado por 5 minutos.';
  }

}
