<div class="lojas">
  <div class="container-fluid">
    <div class="input-group mb-3">
      <div class="input-group-prepend">
                  <span class="input-group-text"  >
                    <i class=" fa fa-search fa-lg text-muted"></i>
                  </span>
      </div>

      <input type="text" class="form-control" placeholder="Busque uma loja"
             aria-label="buscar por cidades"   [(ngModel)]="filtro.texto"  appAutoFocus [autoFocus]="true"
             (input)="onFiltreLojas($event)">

    </div>

    <div class="row">
      <div class="col" *ngIf="!buscando && !lojas.length">
        <div class="card-box" style="padding: 0.5rem;">
          <div class="alert alert-info">
            Nenhuma loja encontrada <b>"{{filtro.texto}}"</b>
            <button class="btn btn-secondary btn-sm ml-2" (click)="limpeBusca();"> ver todas lojas</button>
          </div>
        </div>
      </div>
      <div class="col-12" *ngFor="let loja of lojas" [class.col-sm-6]="!popoup">
        <div (click)="abraLink(loja)" class="card-box cpointer "
           style="padding: 0.5rem;margin-bottom: 18px;">
          <div class="row">
            <div class="col-3">
              <img [src]="loja.logo" style="max-width: 100px;border-radius: 5px;" class="logo ml-2 mr-1">
              <div class="position-absolute" style="bottom: 30px;right: 10px;" *ngIf="grupoDeLojas.paginaDirecionarPara === 'whatsapp'">
                <img class="card-img" src="/assets/icons/whatsapp_logo_oficial.png" alt="whatsapp" style="width: 32px;">
              </div>
            </div>
            <div class="col-9">
              <h4>{{loja.nome}}</h4>
              <p class="text-muted endereco">{{loja.endereco}}</p>


              <div class="whatsapp" *ngIf="grupoDeLojas.paginaDirecionarPara === 'whatsapp'" style="margin-left: 1px;">
                  <span>
                  <i class="fab fa-whatsapp font-14"></i>
                    {{loja.numeroWhatsapp.whatsapp | mask: '(99) 9-9999-9999'}}
                  </span>
                </div>

              <ng-container *ngIf="loja.estaAberta">
                <span  class="text-success font-13" >
                  <span class="bolinha aberto"> </span> ABERTO AGORA
                </span>

                <span class="text-black-50 font-13" title="Tempo de entrega estimado">
                       <i class="fe-clock fa  "></i> {{loja.descricaoTempoEntrega ? loja.descricaoTempoEntrega: 'Verificar com a loja'}}
                  </span>
              </ng-container>
              <ng-container *ngIf="!loja.estaAberta">
                <span  class="text-danger font-14">
                  <span class="bolinha fechada"> </span> FECHADA
                </span>
              </ng-container>
            </div>
          </div>

        </div>
      </div>

      <div class="col" *ngIf="buscando">
        <div class="k-i-loading ml-1 mr-1" style="font-size: 40px;height: 200px;"></div>
      </div>
    </div>

  </div>
</div>
