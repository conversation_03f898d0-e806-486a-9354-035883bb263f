header{
  display: table;
}
.form-control{
  border: none;
}

.input-group-prepend{
  border:none;
  .input-group-text{
    background: #fff;
    border: none;
  }
}
input{
  height: 60px;
  line-height: 30px;
  font-size: 24px;
}
.container-fluid{
  max-width: 1100px;
}


.navbar-custom {
  position: absolute;
  background-color: #fff;
  height: 80px;
  .fe-menu{
    font-size: 40px;
  }
  .logo-topo{
    display: inline-block;

  }
  .button-menu-mobile {
    color: #000;
  }
}
.content{
  margin-top: 80px;
}

.capa{
  background-color: #333;
  height: 600px;
  background: url('/images/franquia/cib/bg1.jpg') center;
  position: relative;

  .banner-promo{
    top: 25px;
    position: relative;
    height: 250px;
    img{
      height: 100%;
      margin: 0 auto;
      display: block;
    }
  }

  .box{
    max-width: 575px;
    margin: 0 auto;
    padding-top: 50px;
  }
  .busca-loja{
    background: #000000a3;
    color: #fff;

    .titulo{
      color: #fff;
      font-size: 25px;
      line-height: 30px;
      text-align: center;
    }


    button{
      font-size: 20px;
      padding: 10px 30px;
    }


  }

  .retirar{
    background-color: #75716feb;
    color: #fff;
    h5{
      color: #fff;
      font-size: 1.5em;
      padding: 0.2em;
    }
    .icone-store{
      height: 25px;
      width: 30px;
      position: absolute;
      left: 20px;
      top: 12px;
      background-color: #fff;
      -webkit-mask: url('/images/franquia/cib/store_1.svg') no-repeat center;
      mask: url('/images/franquia/cib/store_1.svg') no-repeat center;

    }
  }
}

.lojas {
  background-color: #e8e8e8;
  min-height: 600px;
  padding-top: 30px;
  padding-bottom: 40px;

  .card-box{
    display: table;
    width: 100%;
  }
  .bolinha{
    margin: 5px 5px 0px 2px;
    width: 8px;
    height: 8px;
    border-radius: 100px;
    float: left;
    &.aberto{
      background: #6db31b;
    }

    &.fechada{
      background: #f1556c;
    }
  }

  .endereco{
    margin-top: 0;
    margin-bottom: 0;
    height: 40px;
    overflow: hidden;
  }
}
.btn-secondary {
  background-color: #313131 !important;
}

@media (max-width: 900px) {
  h3, .h3 {
    font-size: 1.2rem;
  }
  .container-fluid {
    max-width: inherit;
    width: 100%;
  }
  input{
    height: 45px;
    line-height: 25px;
    font-size: 18px;
  }
  .capa {
    height: 465px;
    width: 100%;
    background-position: -87px 28px;
    background-size: 139%;
    background-repeat: no-repeat;
    background-color: #29292b;

    .box{
      max-width: 88%;
    }
    .banner-promo{
      height: auto;
      width: 100%;
      img{
        height: auto;
        width: 100%;
      }
    }
    .busca-loja {
      .titulo {
        font-size: 18px;
      }
      button{
        font-size: 18px;
        padding: 0px 20px;

      }
    }
  }

  .whatsapp {
    margin-top: 10px;
  }

  .lojas{
    padding-left: 10px;
    padding-right: 10px;
    .card-box{
      padding: 10px 5px;
      img.logo {
        height: 70px;
        margin-top: 20px;
      }
      .row>div{
        padding-right: 0px;
        padding-left: 0px
      }
    }
  }
}

::ng-deep .mobile {
  .lojas {
    margin-left: -12px;
    margin-right: -12px;
  }
}


.whatsapp {
  display: block;
  margin-bottom: 5px;
}

.whatsapp span{
  font-size:12px;
  font-weight: 600;

  color: #199D0F
}
