import {Component, OnInit, ViewChild} from '@angular/core';
import {HeaderLojaComponent} from "../topo-menu/header-loja.component";
import {DialogService} from "@progress/kendo-angular-dialog";
import {PopupUtils} from "../../objeto/PopupUtils";
import {DialogRef} from "@progress/kendo-angular-dialog";
@Component({
  selector: 'app-painel-horarios',
  templateUrl: './painel-horarios.component.html',
  styleUrls: ['./painel-horarios.component.scss']
})
export class PainelHorariosComponent implements OnInit {
  @ViewChild('header', {static: false })  header: HeaderLojaComponent;
  horarioDoServico: any =   {}
  abriuComoModal = false;
  urlDestino: string;
  window: any
  empresa: any = {}
  diasDaSemana = ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', '<PERSON>ta', '<PERSON><PERSON>bado'];
  constructor() { }

  static abraComoPopup(router,  location, activatedRoute, dialogService: DialogService, empresa: any, horarioServico: any ){
    let dimensao = PopupUtils.calculeAlturaLargura(false)

    const windowRef: DialogRef = dialogService.open({
      title: null,
      content: PainelHorariosComponent,
      minWidth: 200,
      width: dimensao.largura,
      height: 500
    });

    let tela: PainelHorariosComponent = windowRef.content.instance;

    tela.empresa = empresa;
    tela.horarioDoServico = horarioServico;
    tela.setModal(windowRef);

    PopupUtils.abraJanela(router,  location, activatedRoute, windowRef, {}, null);
  }

  ngOnInit(): void {
  }

  obtenhaHorariosDia(dia){

    let horarios = this.horarioDoServico.horarios.filter((item: any) => item.diaDaSemana === dia)

    return horarios;
  }

  ehHoje(dia: number){
    let hoje  = false;

    this.obtenhaHorariosDia(dia).forEach((item: any) => {
      if(item.hoje) hoje = true;
    })

    return hoje;
  }


  obtenhadDiaSemana(dia: number){
    return this.diasDaSemana[dia]
  }

  obtenhaHorario(horario: string){
    return horario.substring(0, 5)
  }


  setModal(window, telefone: string = null){
    this.window = window;
    this.abriuComoModal = true;

  }

  fecheTela() {
    this.window.close();
  }

}
