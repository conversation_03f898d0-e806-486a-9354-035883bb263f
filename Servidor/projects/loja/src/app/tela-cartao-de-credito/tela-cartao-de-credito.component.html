<app-header-tela [titulo]="'Cartão de Crédito'" [icon]="'fas fa-credit-card card-icon'"
                 [exibirFechar]="true" (fechou)="fecheTela()" ></app-header-tela>

<div class="mt-3" *ngIf="pedido">
    <div class="row">
      <div class="col">
        <div class="alert alert-danger mb-2" role="alert" *ngIf="msgErro" #erroDiv>
          <i class="fas fa-exclamation-triangle"></i> {{msgErro}}
        </div>

        <div class="alert alert-warning mb-2" role="alert" *ngIf="mensagemAlerta">
          <i class="fas fa-exclamation-triangle mr-1"></i> {{mensagemAlerta}}
        </div>

        <ng-container *ngIf="gatewayMercadoPago()">
          <app-cad-cartao-mercadopago #dadosCartaoMercadoPago [pedido]="pedido" (onGerouToken)="gerouTokenCartao($event)">

          </app-cad-cartao-mercadopago>
        </ng-container>

        <ng-container *ngIf="gatewayPagSeguro()">
          <cad-cartaocredito-pag-seguro #dadosCartao [pedido]="pedido"></cad-cartaocredito-pag-seguro>
        </ng-container>

        <ng-container *ngIf="gatewayPagSeguroConnect()">
          <app-cad-cartao-pagseguro-connect #dadosCartaoPagaseguroConnect [pedido]="pedido"></app-cad-cartao-pagseguro-connect>
        </ng-container>

        <ng-container *ngIf="gatewayPagCielo()">
          <app-cad-cartao-cielo #ctlDadosCartaoCielo [pedido]="pedido"></app-cad-cartao-cielo>
        </ng-container>

        <ng-container *ngIf="gatewayPagarme()">
          <app-cad-cartao-pagarme #ctlDadosCartaoPagarme [pedido]="pedido" [gateway]="this.gateway"></app-cad-cartao-pagarme>
        </ng-container>

        <ng-container *ngIf="gatewayERede()">
          <app-cad-cartao-erede [pedido]="pedido"  #ctlDadosCartaoRede></app-cad-cartao-erede>
        </ng-container>

        <ng-container *ngIf="gatewayTunaPay()">
          <app-cad-cartao-tunapay #ctlDadosCartaoTuna [pedido]="pedido"></app-cad-cartao-tunapay>
        </ng-container>

      </div>
    </div>

      <div class="card mt-0" *ngIf="informarEnderecoCobranca()">
        <div class="card-body pt-0">
          <h4 class="mb-3 mt-1 label">
            <i class="fas fa-map-marker-alt mr-2"></i>
            Endereço de Cobrança
          </h4>

          <div class="mb-4" *ngIf="!obrigarEndereco">
            <div class="custom-control custom-switch">
              <input type="checkbox" class="custom-control-input"
                     id="usarEnderecoEntrega"
                     [(ngModel)]="usarEnderecoDeEntrega"
                     (ngModelChange)="alterouUsarEnderecoDeEntrega()">
              <label class="custom-control-label label" for="usarEnderecoEntrega">
                <strong>Usar o mesmo endereço de entrega</strong>
              </label>
            </div>
          </div>

          <div *ngIf="!usarEnderecoDeEntrega">
            <form id="frmEscolherEndereco" [ngClass]="{'needs-validation': !frmEscolherEndereco.submitted, 'was-validated': frmEscolherEndereco.submitted}"
                  novalidate #frmEscolherEndereco="ngForm" *ngIf="enderecos.length && !informarNovo">
              <div class="row" >
                <div class="col-12">
                  <div class="form-group">
                    <label class="d-flex justify-content-between align-items-center">
                      <div class="label">
                        <i class="fas fa-address-book mr-1"></i>
                        Selecione um endereço
                      </div>
                      <button class="btn btn-sm btn-outline-blue"
                              type="button"
                              (click)="novoEndereco()"
                              [hidden]="informarNovo">
                        <i class="fas fa-plus mr-1"></i>
                        Novo Endereço
                      </button>
                    </label>
                    <kendo-combobox   [required]="!informarNovo"
                                      name="enderecoSelecionado"
                                      [data]="enderecos"  #enderecoSelecionado="ngModel"
                                      placeholder="Escolha um endereço cadastrado"
                                      [(ngModel)]="enderecoCobranca"
                                      [valueField]="'id'"
                                      [textField]="'descricaoCompleta'"
                                      class="w-100 form-control">
                    </kendo-combobox>

                    <div class="invalid-feedback" *ngIf="!informarNovo && !enderecoCobranca">
                      Selecione um endereço de cobrança
                    </div>
                  </div>
                </div>
              </div>

            </form>

            <div class="mt-3" *ngIf="informarNovo">
              <div class="card">
                <div class="card-header bg-light">
                  <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                      <i class="fas fa-plus-circle mr-2"></i>
                      Novo Endereço
                    </h5>
                    <button *ngIf="enderecos.length"  class="btn btn-sm btn-outline-secondary"
                            (click)="informarNovo = false">
                      <i class="fas fa-times mr-1"></i>
                      Cancelar
                    </button>
                  </div>
                </div>
                <div class="card-body">
                  <app-form-endereco
                    [buscouUmEndereco]="true"
                    [exibirBotao]="false"
                    [exibirDepoisCep]="true"
                    [pontoReferencia]="false"
                    #formEndereco
                    [ignorarConfigEmpresa]="true"
                    [cepObrigatorio]="true">
                  </app-form-endereco>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    <footer class="footer">
      <div>
        <div class="row">
          <div class="col">
            <button class="btn btn-blue btn-block" type="submit" id="confimarCartao"  [hidden]="gatewayMercadoPago()"
                    [disabled]="processandoDados" (click)="salvarCartao()">
              <i class="k-icon k-i-loading" *ngIf="processandoDados"></i>
              Confirmar Cartão</button>
          </div>
        </div>
      </div>
    </footer>
</div>
