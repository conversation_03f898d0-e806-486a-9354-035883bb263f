import {AfterViewInit, Component, HostListener, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {ITela} from "../../objeto/ITela";
import {ConstantsService} from "../../services/ConstantsService";
import {ProdutoService} from "../../services/produto.service";
import {ActivatedRoute, Router} from "@angular/router";
import {DominiosService} from "../../services/dominios.service";
import {MyDetectorDevice} from "../shared/MyDetectorDevice";
import {SiteProdutoComponent} from "../../site-produto/site-produto.component";
import {Location} from "@angular/common";
import {DialogService} from "@progress/kendo-angular-dialog";
import {SiteMontarpizzaComponent} from "../../site-montarpizza/site-montarpizza.component";
import {CdkVirtualScrollViewport} from "@angular/cdk/scrolling";
import { fromEvent, Subject, of } from 'rxjs';
import { debounceTime, distinctUntilChanged, takeUntil, filter, switchMap } from 'rxjs/operators';
import {CarrinhoService} from "../../services/carrinho.service";
import {ProdutosTelaUtils} from "../../produtos/ProdutosTelaUtils";

declare var _;

@Component({
  selector: 'app-tela-busca-produtos',
  templateUrl: './tela-busca-produtos.component.html',
  styleUrls: ['./tela-busca-produtos.component.scss', '../../produtos/produtos-categorias.scss']
})
export class TelaBuscaProdutosComponent implements OnInit, ITela, AfterViewInit , OnDestroy  {
  @ViewChild('menutopo') menutopo: any;
  @ViewChild('containerProdutos', {static: true}) containerProdutos: any;
  @ViewChild(CdkVirtualScrollViewport)
  viewport: CdkVirtualScrollViewport;
  carregando = true;
  produtos = [];
  backup: any = {
     produtos: [],
     categorias: []
  }
  listaItensPorCategoria: any = {};
  categorias: any  = [];
  produtosPorCategoria: any = {}
  nomePagina: string;
  veioParamsNoState = false;
  isMobile = false;
  window: any;
  ehMesa: boolean;
  nomeCategoria: string;
  idCategoria: number;
  idProduto: number;
  layoutHorizontal: boolean;
  carregandoPagina: boolean;
  qtdPorLinha: number;
  valorBuscado: any;
  destaques =  [];
  nomeCategoriaDestaque = 'DESTAQUES';
  categoriaDestaque = {
    id: 2,
    nome: this.nomeCategoriaDestaque,
    imagem: null
  };

  categoriaMonteSuaPizza = {
    id: 1,   nome: ''
  };
  timerbusca: any;
  termoBusca
  msgBusca
  ehMercado: boolean
  empresa: any = {};
  exibirPrecos = true;
  evtCatalogo: any;
  timer: any;
  isDestroyed = false;
  private searchTerms = new Subject<string>();
  private destroy$ = new Subject<void>();
  constructor(private router: Router, private constantsService: ConstantsService,
              private produtoService: ProdutoService, private dominiosService: DominiosService,
              private detectorDevice: MyDetectorDevice, private dialogService: DialogService,
              private activatedRoute: ActivatedRoute, private location: Location,
              private carrinhoService: CarrinhoService) {
    this.nomePagina = dominiosService.obtenhaRaizCardapio();

    this.isMobile = this.detectorDevice.isMobile();

    if( window.history.state.produtos )
      this.veioParamsNoState = true;

    this.constantsService.empresa$.subscribe( (empresa) => {
      if(empresa)
        this.layoutHorizontal = empresa.tipoDeLoja === 'MERCADO' || empresa.tipoDeLoja === 'ECOMMERCE';
    });

    window.scroll(0, 0);
  }

  setQtdePorLinha(){

    let largura = this.containerProdutos.nativeElement.clientWidth;

    this.qtdPorLinha = Math.floor( (largura) / 165)

    console.log('qtdePorLinha: ' + this.qtdPorLinha)

  }


  ngOnInit() {
    if( window.history.state.produtos )
      this.veioParamsNoState = true;

    let params: any = this.activatedRoute.snapshot.queryParams;

   // this.nomeCategoria =  params.c;
    this.idCategoria = params.cid ? Number(params.cid) : null;
    this.idProduto = params.c ? Number(params.c) : null;

    // Configurar o debounce para a busca usando RxJS
    this.searchTerms.pipe(
      takeUntil(this.destroy$),
      debounceTime(500), // Aguarda 300ms após a última digitação
      distinctUntilChanged(), // Ignora se o termo de busca não mudou (já tratamos isso no testeBusca)
      filter(term => {
        if (!term) {
          this.carregueProdutosDaTela();
          return false;
        }

        if (this.layoutHorizontal && term.length < 3) {
          this.msgBusca = 'informe pelo menos 3 letras';
          this.carregando = false; // Desativar o loading se não tiver caracteres suficientes
          return false;
        }

        return true;
      }),
      switchMap(term => {
        // Executar a busca e retornar um Observable
        this.busqueComFiltro(term);
        return of(null); // Retorna um Observable vazio
      })
    ).subscribe();

    this.constantsService.empresa$.subscribe( (empresa) => {
      if(!empresa || this.empresa.id)  return;

      this.empresa = empresa;
      this.ehMercado = empresa.tipoDeLoja === 'MERCADO';
      this.layoutHorizontal =  this.ehMercado || empresa.tipoDeLoja === 'ECOMMERCE';

      if( empresa.nomeCategoriaDestaques && ! this.idCategoria) {
        let nomeAnterior = this.categoriaDestaque.nome
        this.categoriaDestaque.nome  = empresa.nomeCategoriaDestaques;
        this.categoriaDestaque.imagem  = empresa.imagemCategoriaDestaque;
        this.produtosPorCategoria[this.categoriaDestaque.nome] = this.produtosPorCategoria[nomeAnterior]
      }

      this.setQtdePorLinha();

      this.ehMesa = window.history.state.ehMesa;

      if(! this.ehMesa){
        const pedido: any = this.carrinhoService.obtenhaPedido();

        if((pedido && pedido.mesa ))
          this.ehMesa = true
      }

      let state: any  = window.history.state;

      if(!this.ehMercado && state.categorias)
      {
        this.categorias = window.history.state.categorias;
        if( state.produtosPorCategoria){
          this.produtos = state.produtos;
          this.produtosPorCategoria = state.produtosPorCategoria;
          this.agrupeCategorias(true);
          this.carregando = false;
        } else {
            this.carregueProdutosDaTela();
        }
      } else {
        this.carregueProdutosDaTela();
      }


      const obs = fromEvent(window, 'resize').subscribe(
        () => {
          if(this.timer) clearTimeout(this.timer)
          console.log('*** chamou rezize ***');
          this.timer = setTimeout(() =>
          {
            if (!this.isDestroyed) {
              console.log('*** REDIMENSIONANDO ***');
              this.setQtdePorLinha();
              if(!this.carregando)
                this.reagrupeCategorias();
            } else {
              console.log('*** tela ja morreu***');
            }

          }, 250)
        })
    });
  }

  carregueCardapioPorEventos(){
   // Garantir que o loading esteja ativado
   this.carregando = true;
   this.evtCatalogo = this.constantsService.obtenhaCarpadioCompleto( this.obtenhaTipoCardapio(), this.empresa)
     .subscribe((cardapio: any) => {
      if(!cardapio) {
        this.carregando = false; // Desativar loading se não houver cardápio
        return;
      }

      if(cardapio.categorias){
        this.categorias = cardapio.categorias;

        if(this.idCategoria && this.categorias.length > 1)
          this.categorias =  this.categorias.filter((item: any) => item.id === this.idCategoria)
      }


      if(cardapio.produtos ){
        this.produtos = cardapio.produtos;
        this.agrupeCategorias();
        this.carregando = false;
      } else {
        this.carregando = false; // Desativar loading se não houver produtos
      }

    }, error => {
      // Desativar o loading em caso de erro
      this.carregando = false;
      console.error('Erro ao carregar cardápio:', error);
    })
  }

  obtenhaTipoCardapio(){
    return this.ehMesa ? 'MESA' : 'DELIVERY';
  }

  carregueProdutosDaTela(){
    // Garantir que o loading esteja ativado
    this.carregando = true;

    let carregarPorEvento = this.empresa.cardapio && this.empresa.cardapio.exibirSelecaoCategorias;

    //todo: testar nova forma carregar catagalogo completo por eventos
    if(carregarPorEvento) return this.carregueCardapioPorEventos();

    let quantidadePorPagina = this.layoutHorizontal ? 50 : null
    let indice = this.layoutHorizontal ? 0 : null

    this.produtoService.listeAhVenda(this.empresa, this.obtenhaTipoCardapio(), this.layoutHorizontal,
      (this.idCategoria ||  this.nomeCategoria),
      quantidadePorPagina, indice, this.termoBusca).subscribe((resposta: any) => {
      this.produtos = resposta.produtos
      this.categorias = resposta.categorias;
      this.agrupeCategorias();
      this.carregando = false;
    }, error => {
      // Desativar o loading em caso de erro
      this.carregando = false;
      console.error('Erro ao carregar produtos:', error);
    });
  }

  getPosicao(nomeCategoria){
    let _produto = this.produtos.find( produto => produto.categoria && produto.categoria.nome === nomeCategoria)

    let posicao = _produto ? _produto.categoria.posicao : this.produtos.length;

    return posicao;
  }

  deveExibirTopo() {
    return false;
  }
  deveExibirMenu(){
    return true;
  }

  abraDetalhesProduto(produto: any) {
    if(!produto.montar){
      this.window = SiteProdutoComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
        this.dialogService, this.isMobile, produto);
    } else {
      this.window =  SiteMontarpizzaComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
        this.dialogService, this.isMobile, produto);
    }

  }

  @HostListener('window:popstate', ['$event'])
  onPopState(event) {
    if( this.window ) {
      this.window.close({
        back: true
      });
    }
  }

  exibirUnidade(produto: any) {
      return produto.tipoDeVenda && produto.tipoDeVenda === 'Peso';
  }


  ngOnDestroy(): void {
    this.isDestroyed = true;

    // Completar os Subjects para evitar memory leaks
    this.destroy$.next();
    this.destroy$.complete();
    this.searchTerms.complete();

    if(this.evtCatalogo)
      this.evtCatalogo.unsubscribe();

    if(this.timer) clearTimeout(this.timer);
    if(this.timerbusca) clearTimeout(this.timerbusca);
  }
  private exibaApenasProdutosDaCategoria(categoria: any, indiceInicial = 0) {
    if( !categoria ) {
      return;
    }

    if(this.produtosPorCategoria[categoria.nome] && this.produtosPorCategoria[categoria.nome].length > indiceInicial)
      this.produtos = this.produtosPorCategoria[categoria.nome]


    const mapProdutosPorCategoria = {};

    mapProdutosPorCategoria[categoria.nome] = this.produtos;

    this.produtosPorCategoria = mapProdutosPorCategoria;

    this.categorias = [categoria];
  }

  ngAfterViewInit(): void {
    this.activatedRoute.queryParams.subscribe( (params) => {
      this.menutopo.query = params.q;
    });

    if(this.layoutHorizontal &&  this.viewport)
      this.viewport.elementScrolled().subscribe((event) => {
        this.busqueMaisProdutos(event)
      })
  }

  apertouVoltar($event) {
    this.dominiosService.vaParaHome();
  }

  private agrupeCategorias(agrupado = false){
    if(!this.backup.produtos.length)
      this.backup = {
        produtos: this.produtos,
        categorias: this.categorias
      }

    if(!agrupado){
      this.produtosPorCategoria =
        _.groupBy(this.produtos, produto => produto.categoria ? produto.categoria.nome.trim() : 'Outros');
    }

    if(!this.idCategoria || this.idCategoria === this.categoriaDestaque.id)
      ProdutosTelaUtils.definaCategoriasFixas(this);

    this.reagrupeCategorias();


    if(this.idProduto){
      let produto = this.produtos.find((item: any) => item.id === this.idProduto);

      if(produto) this.abraDetalhesProduto(produto);
    }

  }

  reagrupeCategorias(){
    console.log('reagrupar categorias...')
    this.listaItensPorCategoria = {}
    try{
      if(this.nomeCategoria) {
        console.log('** definiu o nome da categoria **')
        this.listaItensPorCategoria[this.nomeCategoria] =
          this.agrupeDeXemX(this.produtosPorCategoria[this.nomeCategoria], this.qtdPorLinha)
      }
      else
        for(let categoria of this.categorias)
          this.listaItensPorCategoria[categoria.nome] =
            this.agrupeDeXemX(this.produtosPorCategoria[categoria.nome], this.qtdPorLinha)
    }catch (e){
      console.error(e)
    }
  }

  busqueNaTela(filtro){
    if(filtro) {
      const termoBusca = filtro.toUpperCase();
      this.produtos = this.backup.produtos.filter((produto: any) => {
        // Busca no nome do produto
        const nomeContem = produto.nome && produto.nome.toUpperCase().indexOf(termoBusca) >= 0;
        // Busca na descrição do produto
        const descricaoContem = produto.descricao && produto.descricao.toUpperCase().indexOf(termoBusca) >= 0;
        // Retorna true se o termo for encontrado no nome OU na descrição
        return nomeContem || descricaoContem;
      });
    } else {
      this.produtos = this.backup.produtos;
    }

    this.agrupeCategorias();
    // Garantir que o loading seja desativado após a busca
    this.carregando = false;
  }


  busqueComFiltro(filtro: string) {
    this.carregando = true

    if(!this.layoutHorizontal) return this.busqueNaTela(filtro);

    this.produtoService.listeAhVenda(this.empresa, this.ehMesa ? 'MESA' : 'DELIVERY', this.layoutHorizontal,
      (this.idCategoria ||  this.nomeCategoria),
      50, 0, filtro ).subscribe((resposta: any) => {
      this.produtos = resposta.produtos;
      this.categorias = resposta.categorias;
      this.agrupeCategorias();
      // Desativar o loading após receber a resposta
      this.carregando = false;

      console.log('** LISTA ITENS POR CATEGORIA **', this.listaItensPorCategoria)
    }, error => {
      // Desativar o loading em caso de erro
      this.carregando = false;
      console.error('Erro ao buscar produtos:', error);
    })
  }

  busqueMaisProdutos($event) {
    if(!this.qtdPorLinha) return;
    if(!this.layoutHorizontal) return;
    if(this.valorBuscado) return;
    const end = this.viewport.getRenderedRange().end;
    const total = this.viewport.getDataLength();
    console.log(`${end}, '>=', ${total}`);
    console.log('Quantidade de produtos:', this.produtos.length)
    if (end === total) {

      console.log('agora deveria procurar próxima página: ' + this.qtdPorLinha)

      if(this.carregandoPagina) {
        console.log('não vai buscar porque já está buscando')
        return;
      }
      this.carregandoPagina = true;
      console.log('Categoria sendo buscada: ' + this.nomeCategoria)
      this.produtoService.listeAhVenda(this.empresa, this.ehMesa ? 'MESA' : 'DELIVERY', this.layoutHorizontal,
        (this.idCategoria ||  this.nomeCategoria),
        50, this.produtos.length ).subscribe((resposta: any) => {
          let produtos = []
          if(resposta)
            produtos = resposta.produtos
        if(produtos.length === 0) {
          console.log("não tem mais produtos a adicionar")
          this.carregandoPagina = false;
          return;
        }


        if(this.produtos[this.produtos.length - 1].id === produtos[produtos.length - 1].id) {
          this.carregandoPagina = false;
          return;
        }


        this.produtos = this.produtos.concat(produtos);

        this.produtosPorCategoria =  _.groupBy(this.produtos, produto => produto.categoria ? produto.categoria.nome : 'Outros')

        this.listaItensPorCategoria[this.nomeCategoria] = this.agrupeDeXemX(this.produtosPorCategoria[this.nomeCategoria], this.qtdPorLinha)


        for(let produto of produtos) {

          if(produto.camposAdicionais && produto.camposAdicionais.length > 0) {
            let valorMinimo = produto.preco

            for(let campoAdicional of produto.camposAdicionais)
              if(campoAdicional.obrigatorio) {
                let maisBarato = campoAdicional.opcoesDisponiveis[0]
                for(let opcao of campoAdicional.opcoesDisponiveis)
                  if(opcao.valor < maisBarato.valor) maisBarato = opcao

                valorMinimo += maisBarato.valor

              }

            if(valorMinimo > produto.preco) produto.valorMinimo = valorMinimo
            //teste
          }
        }

        const mapCategoriasPorId = {};
        const mapCategoriasPorNome = {};

        this.produtos.map(produto => {
          if( produto.categoria ) {
            mapCategoriasPorId[produto.categoria.id] = produto.categoria;
            mapCategoriasPorNome[produto.categoria.nome] = produto.categoria;
          }
        });

      //  this.categorias = Object.values(mapCategoriasPorId).sort( (a: any, b: any) => a.posicao - b.posicao);


        this.carregandoPagina = false;
      })
    }
  }

  private agrupeDeXemX(produtos: any[], x = 6, indiceInicial = 0, listaAtual = []) {
    let listaAgrupada: any[] = [...listaAtual];
    let inicio = indiceInicial;

    if(!produtos) return;

    if(listaAgrupada.length > 0 && listaAgrupada[listaAgrupada.length - 1].length < x)
      inicio = this.insiraProdutosNoItem(inicio, produtos, x, listaAgrupada[listaAgrupada.length - 1])


    for(let i = inicio; i < produtos.length; i = i + x) {
      let item: any = []
      let y = this.insiraProdutosNoItem(i, produtos, x, item);
      listaAgrupada.push(item)
    }

    return listaAgrupada
  }

  private insiraProdutosNoItem(i: number, produtos: any[], x: number, item: any) {
    let y;
    let quantidadeAdicionar = x - item.length
    let quantidadeInserido
    for (y = i, quantidadeInserido = i; (y < produtos.length) && (quantidadeInserido < (i + quantidadeAdicionar)); y++)
      if(produtos[y].preco > 0 || produtos[y].valorMinimo > 0) {
        item.push(produtos[y])
        quantidadeInserido++;
      }


    return y
  }

  testeBusca(valor: any) {
    // Permitir sempre a execução, mesmo quando carregando
    delete this.msgBusca;

    // Verificar se o valor mudou
    const valorMudou = this.termoBusca !== valor;

    // Se o valor não mudou, não fazemos nada
    if (!valorMudou) {
      // Se estiver carregando, desativar o loading
      if (this.carregando) {
        this.carregando = false;
      }
      return; // Sair do método sem fazer nada
    }

    // Atualizar o termo de busca apenas se o valor mudou
    this.termoBusca = valor;

    // Ativar o loading quando o usuário digita (se tiver valor e o valor mudou)
    if (valor) {
      this.carregando = true;
      this.valorBuscado = valor;
    }

    // Emitir o valor para o Subject que vai lidar com o debounce
    this.searchTerms.next(valor);
  }


  nenhumProduto(){
    if(this.carregando) return false;

    if(!this.produtos.length) return true;

    return !Object.keys(this.listaItensPorCategoria).length;
  }


  deveTerBordas() {
    return true;
  }

  deveExibirBannerTema() {
    return false;
  }
}
