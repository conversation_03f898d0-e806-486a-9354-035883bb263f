import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'listFilter'
})
export class FiltroProdutosPipe implements PipeTransform {

  transform(list: any[], filterText: string): any {
    if( !filterText ) {
      return list;
    }

    if( list ) {
      return list.filter(item => {
        return item.nome.search(new RegExp(filterText, 'i')) > -1 ||
          item.descricao.search(new RegExp(filterText, 'i')) > -1;
      });
    }

    return [];
  }
}
