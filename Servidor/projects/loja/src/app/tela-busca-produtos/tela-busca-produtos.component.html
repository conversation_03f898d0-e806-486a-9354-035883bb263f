<app-header-loja #menutopo [busca]="true" [focarBusca]="false"
                 (evtVoltar)="apertouVoltar($event)" (mudouValorBusca)="testeBusca($event)"></app-header-loja>

<div #containerProdutos class="container mt-3 mercado {{empresa?.tema}}" style="position: relative; padding:0">

  <!-- Loading overlay dentro do container para ficar apenas na lista de produtos -->
  <div class="loading-overlay" *ngIf="carregando">
    <div class="loading-container">
      <div class="logo-container">
        <img *ngIf="empresa?.logo" class="empresa-logo" [src]="'https://promokit.com.br/images/empresa/' + empresa?.logo" alt="Logo da empresa"/>
        <div class="logo-pulse"></div>
      </div>
      <div class="loading-text">Buscando produtos...</div>
      <div class="loading-dots">
        <div class="dot"></div>
        <div class="dot"></div>
        <div class="dot"></div>
      </div>
    </div>
  </div>

  <div class="alert alert-info" *ngIf="nenhumProduto()">
    Nenhum produto encontrado <span *ngIf="termoBusca"> com "<b>{{termoBusca}}</b>"</span>
  </div>

  <div class="alert alert-info" *ngIf="!carregando && msgBusca">
      {{msgBusca}}
  </div>

  <ng-container *ngIf="!nomeCategoria && listaItensPorCategoria">
    <div  *ngFor="let categoria of categorias">
        <div class="mb-3 secaoCategoria" #secaoCategoria style="position: relative;"  [ngClass]="{'categoria_ia': categoria.ia}"
             [class.ecommerce]="categoria.nome === categoriaDestaque.nome || categoria.vitrine || categoria.destaque"
             *ngIf="(produtosPorCategoria[categoria.nome])?.length > 0">

          <h4 [id]="categoria.nome" class="categoria  font-weight-bold"  >
            <ng-container *ngIf="categoria.ia">
              <i class="fa fa-magic"></i>
            </ng-container>
            {{categoria.nome}}
          </h4>
          <ng-container *ngIf="categoria.ia">
            <h6>Por que você pediu esse produto na mensagem</h6>
          </ng-container>
          <hr>

          <div class="produtos_categoria" #divProdutosCategoria>
            <div class="scroll_categoria">
              <ng-container  *ngFor="let grupo of listaItensPorCategoria[categoria.nome] " >
                <ng-container *ngFor="let produto of grupo"   >
                  <app-produto-container [produto]="produto" [exibirPrecos]="exibirPrecos" [layoutHorizontal]="layoutHorizontal"
                                         (onAbrirDetalhes)="abraDetalhesProduto($event)"></app-produto-container>
                </ng-container>
              </ng-container>
            </div>
          </div>

        </div>
    </div>
  </ng-container>
  <ng-container *ngIf="nomeCategoria && listaItensPorCategoria">
    <div class="container-viewport" *ngFor="let categoria of categorias">
      <h4 *ngIf="(listaItensPorCategoria[categoria.nome])?.length > 0">{{categoria.nome}}</h4>
      <cdk-virtual-scroll-viewport  itemSize="220" (scrolledIndexChange)="busqueMaisProdutos($event)">
        <div class="loading-more" *ngIf="carregandoPagina">
          <div class="loading-dots">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>
        </div>
        <div class="row" *cdkVirtualFor="let grupo of listaItensPorCategoria[categoria.nome]"  >
          <ng-container *ngFor="let produto of grupo" >
            <div (click)="abraDetalhesProduto(produto)" class="col produto pt-2 pb-2"
                  style=" width: 150px; display:inline-block;">
              <div class=" justify-content-center align-items-center" style="width: 128px; height: 92px; margin-bottom: 4px "  >
                <img *ngIf="produto.imagens && produto.imagens.length > 0" class="img img-fluid" [src]="'/images/empresa/' + produto.imagens[0].linkImagem" alt="Imagem" >
                <img *ngIf="produto.imagemCodigoDeBarras" class="img img-fluid" [src]="'/images/produtos/' + (produto.imagemCodigoDeBarras.linkImagem ? produto.imagemCodigoDeBarras.linkImagem : 'carrinho-produtos.svg' )" alt="Imagem" >

                <!--<ng-container *ngIf="produto.imagens && produto.imagens.length > 0">

                </ng-container>-->

              </div>
              <div class="media">
                <div class="media-body pt-0 cinco-linhas">

                  <h5 class="mt-0 mb-1 font-15"><span class=" text-muted font-12"  *ngIf="produto.valorMinimo"><i>A partir de</i></span>

                    {{(produto.valorMinimo ? produto.valorMinimo : produto.preco) | currency: 'BRL'}}</h5>
                  <h5 class="preco font-14" *ngIf="!produto.indisponivel">

                    <i *ngIf="produto.qtdMaxima"><label class="text-muted" style="font-size: 10px">*Até {{produto.qtdMaxima}} por pedido</label></i>
                    <span *ngIf="exibirUnidade(produto)" class="text-muted font-11">
               / kg
            </span>
                  </h5>


                  <h5 *ngIf="produto.indisponivel" class="text-danger">Indisponível!</h5>
                  <span>{{produto.nome}}</span><br>
                  <span class="text-muted">{{produto.descricao}}</span>



                </div>

              </div>
            </div>
          </ng-container>
          <br>

        </div>

      </cdk-virtual-scroll-viewport>
    </div>
  </ng-container>


</div>
<div class="mb-3">
</div>
