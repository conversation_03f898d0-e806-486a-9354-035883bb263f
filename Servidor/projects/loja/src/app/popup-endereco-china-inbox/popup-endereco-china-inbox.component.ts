import {AfterViewInit, ApplicationRef, Component, ElementRef, NgZone, OnInit, ViewChild} from '@angular/core';
import {EnderecoService} from "../../services/endereco.service";
import {ComboBoxComponent} from "@progress/kendo-angular-dropdowns";
import {NgForm} from "@angular/forms";
import {ChinainboxService} from "../../services/chinainbox.service";
import {CarrinhoService} from "../../services/carrinho.service";
import {PedidoLoja} from "../../objeto/PedidoLoja";
import {FormaDeEntrega} from "../../objeto/FormaDeEntrega";
import {Endereco} from "../../objeto/Endereco";
import {MyDetectorDevice} from "../shared/MyDetectorDevice";
import {ConstantsService} from "../../services/ConstantsService";
import {ClienteService} from "../../services/cliente.service";

declare var $;
declare var google;

@Component({
  selector: 'app-popup-endereco-china-inbox',
  templateUrl: './popup-endereco-china-inbox.component.html',
  styleUrls: ['./popup-endereco-china-inbox.component.scss']
})
export class PopupEnderecoChinaInboxComponent implements OnInit, AfterViewInit {
  @ViewChild('frm', { static: true})  frm: NgForm;
  @ViewChild('txtNumero', {static: false})   txtNumero: ElementRef;
  @ViewChild('cboZonaDeEntrega', {static: false})   cboZonaDeEntrega: ComboBoxComponent;
  @ViewChild('txtLogradouro', {static: false}) private txtLogradouro: any;
  @ViewChild('txtCEP', {static: false}) private txtCEP: any;
  @ViewChild('complementoInput', {static: true})   complementoInput: ElementRef;

  window: any;
  endereco: any = Endereco.novo();
  buscandoCEP: boolean;
  cepValidado: boolean;
  respostaCep: any;
  estados: any = [];
  cidades: any = [];
  escolheuUmEnderecoServer: any;
  msgErro: any;
  exibirBotao: any = true;
  calculandoTaxa: any;
  escolheuPreencherCep;
  pedido: PedidoLoja;
  lojaCerta = null;
  opcaoEntrega = '';
  processando = false;
  lojaEncontrada = null;
  escolherUsarLocalizacao = false;
  ehDesktop = false;
  link: any = '';
  mask: any;
  maskedInputController: any;
  empresa: any = {};
  grupoDeLojas = null;
  escolhaLojaRetirar = false;

  constructor(private ngZone: NgZone, private applicationRef: ApplicationRef,
              private enderecoService: EnderecoService, private chinaInBoxService: ChinainboxService,
              private carrinhoService: CarrinhoService, private detectorDevice: MyDetectorDevice,
              private constantsService: ConstantsService, private clienteService: ClienteService) {
    this.mask = [/\d/, /\d/, '.', /\d/, /\d/, /\d/, '-', /\d/, /\d/, /\d/];

    this.clienteService.obtenhaGrupoDaEmpresa().then( (grupoDeLojas) => {
      this.grupoDeLojas = grupoDeLojas;
    });

    this.constantsService.empresa$.subscribe( (empresa) => {
      if( !empresa ) { return; }

      this.empresa = empresa;
    });
  }

  ngOnInit(): void {
    this.pedido = this.carrinhoService.obtenhaPedido();

    this.ehDesktop = this.detectorDevice.isDesktop();

    this.enderecoService.obtenhaEstados().then( (estados) => {
      this.estados = estados;
      if(this.endereco.estado){
        this.endereco.estado = this.estados.find( estado => estado.sigla === this.endereco.estado.sigla)
        if(this.endereco.cidade)
          this.cidades = [this.endereco.cidade]
      }
    });
  }

  alterou($evento: Event) {
    if( this.endereco.cep.length < 8 ) {
      return;
    }

    this.buscandoCEP = true;

    this.enderecoService.busquePorCEP(this.unmask(this.endereco.cep)).then( (resposta) => {
      this.buscandoCEP = false;

      this.respostaCep = resposta;

      this.cepValidado = resposta.localidade != null;
      this.endereco.logradouro = resposta.logradouro;
      this.endereco.bairro = resposta.bairro;

      setTimeout( () => {
        $("#numero").focus();

        const localidade = resposta.localidade;

        const nomeCidade = localidade.split('/')[0];
        const sigla = localidade.split('/')[1];
        this.carregueEstadoECidade(nomeCidade, sigla);
      }, 200);
    }).catch( () => {
      this.cepValidado = false;
      this.buscandoCEP = false;
    });
  }

  unmask(val) {
    return val ? val.replace(/\D+/g, '') : val;
  }

  private carregueEstadoECidade(nomeCidade: string, nome: string) {
    for( const estado of this.estados ) {
      if( nome === estado.nome || nome === estado.sigla) {
        this.enderecoService.obtenhaCidades(estado).then( (cidades) => {
          this.cidades = cidades;
          this.applicationRef.tick();

          for( const cidade of this.cidades ) {
            if( nomeCidade.toUpperCase() === cidade.nome.toUpperCase() ) {
              this.endereco.estado = estado;
              this.endereco.cidade = cidade;

              setTimeout( () => {
                this.applicationRef.tick();
              }, 0);

              break;
            }
          }
        });

        break;
      }
    }
  }

  mudouEstado(estado: any) {
    if( estado == null ) {
      return;
    }

    this.enderecoService.obtenhaCidades(estado).then( (cidades) => {
      if(cidades && cidades.length)
        this.cidades = cidades;
    })
  }

  onSubmit($event: any) {
    if( !this.frm.valid ) {
      return;
    }

    this.processando = true;
    this.lojaEncontrada = null;

    this.chinaInBoxService.localizeLoja(this.empresa, this.endereco).then( (resposta) => {
      if( resposta.encontrou === false ) {
        this.processando = false;
        this.lojaCerta = false;
        return;
      }

      /*
      if( !resposta.lojaCerta ) {
        this.lojaEncontrada = resposta.loja;
        this.lojaCerta = false;
        this.pedido.entrega.permiteEntrega = false;
        this.carrinhoService.salvePedido(this.pedido);

        return;
      }
       */

      this.endereco.localizacao = resposta.localizacao;
      if( location.origin !== resposta.link ) {
        window.location.href = resposta.link + '?end=' + encodeURIComponent(JSON.stringify(this.endereco));
        return;
      }

      this.pedido.entrega.formaDeEntrega = FormaDeEntrega.RECEBER_EM_CASA;
      this.lojaCerta = resposta.lojaCerta;
      this.link = resposta.link;
      this.enderecoService.calculeTaxaDeEntrega(this.pedido.entrega.formaDeEntrega, this.endereco,
        this.pedido.obtenhaSubTotal()).then( (dados: any) => {
        this.processando = false;
        this.pedido.entrega.setTaxaEntrega(this.endereco, dados)
        this.pedido.novosEnderecos.push(this.endereco);

        this.carrinhoService.salvePedido(this.pedido);

        this.pedido.calculeTotal();
        //checar se tá na loja certa
        this.window.close({
          back: true
        });
      }).catch( (erro) => {
        this.processando = false;
      });
    }).catch( (erro) => {
      this.msgErro = erro;

      this.processando = false;
    });
  }

  usarLocalizacao() {
    this.escolherUsarLocalizacao = true;

    navigator.geolocation.getCurrentPosition((posicao) => {
      const latlng = {
        lat: posicao.coords.latitude,
        lng: posicao.coords.longitude,
      };
      const geocoder = new google.maps.Geocoder();
      geocoder.geocode({ location: latlng }, (results, status) => {
        if (status === "OK") {
          if (results[0]) {
            this.cepValidado = true;
            this.respostaCep = {
              logradouro: '',
              bairro: ''
            };

            this.convertaEmEndereco(results[0]);

            this.applicationRef.tick();
          } else {
            window.alert("No results found");
          }
        } else {
          window.alert("Geocoder failed due to: " + status);
        }
      });
    }, (error: any) => {
      alert('User not allowed')
    }, {timeout: 10000});
  }

  convertaEmEndereco(place) {
    const componentForm = {
      street_number: 'short_name',
      route: 'long_name',
      locality: 'long_name',
      administrative_area_level_1: 'short_name',
      administrative_area_level_2: 'long_name',
      sublocality_level_1: 'long_name',
      postal_code: 'short_name'
    };

    const mapFormGoogle = {
      street_number: 'numero',
      route: 'logradouro',
      sublocality_level_1: 'bairro',
      administrative_area_level_1: 'estado',
      administrative_area_level_2: 'cidade',
      postal_code: 'cep'
    };

    const estadoCidade: any = {};

    for (let i = 0; i < place.address_components.length; i++) {
      let address_component = place.address_components[i];
      for( let j = 0; j < address_component.types.length; j++ ) {
        const addressType = address_component.types[j];

        if (componentForm[addressType]) {
          const campo = componentForm[addressType];
          const val = place.address_components[i][campo];

          const nomeCampo = mapFormGoogle[addressType];

          if( nomeCampo !== 'estado' && nomeCampo !== 'cidade' ) {
            this.endereco[nomeCampo] = val;
          } else {
            estadoCidade[nomeCampo] = val;
          }
        }
      }
    }

    setTimeout( () => {
      this.ngZone.run( () => {
        this.carregueEstadoECidade(estadoCidade.cidade, estadoCidade.estado);
        $("#numero").focus();
      });
    }, 0);
  }

  focoCampoCEP() {
    this.escolheuPreencherCep = true;
  }

  fecheTela() {
    this.window.close({
      back: false
    });
  }

  voltarEscolhaPreencherCEP() {
    this.escolheuPreencherCep = false;
    this.cepValidado = false;
    this.endereco = Endereco.novo();
    this.frm.reset();
  }

  voltarEscolhaUsarLocalizacao() {
    this.escolheuPreencherCep = false;
    this.escolherUsarLocalizacao = false;
    this.cepValidado = false;
    this.endereco = Endereco.novo();
    this.frm.reset();
  }

  receberEmCasa() {
    this.opcaoEntrega = 'retirada';
  }

  mudarOpcaoEntrega() {
    this.opcaoEntrega = '';
    this.cepValidado = false;
    this.lojaCerta = null;
    this.endereco = Endereco.novo();
    this.frm.reset();
  }

  escolherOutroCEP() {
    this.lojaCerta = null;
    this.escolheuPreencherCep = false;
    this.opcaoEntrega = 'retirada';
    this.cepValidado = false;
    this.endereco = Endereco.novo();
    this.frm.reset();
  }

  retirarNaLoja() {
    this.escolhaLojaRetirar = true;
    /*
    this.pedido.entrega.formaDeEntrega = FormaDeEntrega.RETIRAR;
    this.pedido.entrega.taxaDeEntrega = 0.0;
    this.carrinhoService.salvePedido(this.pedido);
     */
  }

  mudarLoja() {
    window.location.href = this.lojaEncontrada.link;
  }

  ngAfterViewInit(): void {
  }

  apliqueMascara() {
  }

  voltarOpcaoEntregar() {
    this.escolhaLojaRetirar = false;
  }
}
