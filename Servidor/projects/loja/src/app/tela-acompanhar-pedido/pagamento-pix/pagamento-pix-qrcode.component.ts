import {Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output} from "@angular/core";
import {Clipboard} from '@angular/cdk/clipboard';
import {PedidosService} from "../../../services/pedidos.service";

@Component({
  selector: 'app-pagamento-pix-qrcode',
  templateUrl: './pagamento-pix-qrcode.component.html',
  styleUrls: ['./pagamento-pix-qrcode.component.scss']
})
export class PagamentoPixQrcodeComponent   implements OnInit,  OnDestroy {
  @Input() pagamento: any = {}
  @Input() pedido: any = {}
  @Input() desktop: any = {}
  @Output() onGerarPix: any = new EventEmitter();
  tempoRestante = {
    minutos: 0,
    segundos: 0
  };
  private intervalId: any;
  msgCopiar = '';
  tentandoNovoPagamento: boolean;
  constructor(private pedidosService: PedidosService, private clipboard: Clipboard) {
  }

  ngOnD<PERSON>roy(): void {
    if (this.intervalId)
      clearInterval(this.intervalId);
  }

  ngOnInit(): void {
    this.setContadorPix();
  }

  copiarCodigoPix() {
    this.clipboard.copy(this.pagamento.codigoQrCode);
    this.msgCopiar = 'Código PIX copiado com sucesso. Agora só acessar seu Internet Banking.';
  }





  private setContadorPix() {
    if(this.intervalId ) {
      if(this.pedido && !this.pedido.tempoRestantePagar)
        clearInterval(this.intervalId);
      return;
    }

    if (this.pedido && this.pedido.tempoRestantePagar > 0) {
      this.atualizeTempo();
      this.intervalId = setInterval(() => {
        if (this.pedido.tempoRestantePagar > 0) {
          this.pedido.tempoRestantePagar--;
          this.atualizeTempo();
        } else {
          clearInterval(this.intervalId);
        }
      }, 1000);
    }
  }

  private atualizeTempo() {
    this.tempoRestante.minutos = Math.floor(this.pedido.tempoRestantePagar / 60);
    this.tempoRestante.segundos = this.pedido.tempoRestantePagar % 60;
  }

  atualizouPagamento(pedido: any, pagamento: any) {
    this.tentandoNovoPagamento = false;
    this.pedido = pedido;
    this.pagamento =  pagamento;
    this.setContadorPix();
  }

  gerarNovoPix(){
    this.tentandoNovoPagamento = true;
    this.onGerarPix.emit({})

  }
}
