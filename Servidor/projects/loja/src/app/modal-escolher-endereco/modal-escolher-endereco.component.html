<div class="modal-content">
  <div>
    <div *ngIf="!enderecoEscolhido">
      <div class="form-group">
        <label for="descricao">Selecione o endereço de entrega </label>
        <kendo-combobox id="descricao" name="descricao" [data]="enderecos" placeholder="selecionar"
                        class="form-control" appAutoFocus [autoFocus]="true" [allowCustom]="false"
                        [(ngModel)]="endereco" [valueField]="'id'" [textField]="'descricaoCompleta'">
        </kendo-combobox>
      </div>

      <div class="alert alert-danger alert-dismissible fade show mt-2 mb-2" role="alert" *ngIf="msgErro">
        {{msgErro}}
        <button type="button" class="close" data-dismiss="alert" aria-label="Fechar">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>

      <div class="mt-2 text-center">
        <div class="row">
           <div class="col">
             <button type="button" class="btn btn-lg btn-blue btn-block" [disabled]="!endereco || calculandoTaxa"
                     (click)="calculeTaxaEntrega()">
               <i class="k-icon k-i-loading  " *ngIf="calculandoTaxa"></i>
               Entregar nesse
             </button>
           </div>
          <div class="col"  *ngIf="endereco?.id" >
            <button   type="button" class="btn btn-lg btn-warning btn-block"  [disabled]="calculandoTaxa"
                      (click)="editarEndereco()"  >
              Alterar endereço</button>


          </div>
        </div>

        <div class="mt-2 row">
          <div class="col">
            <button type="button" class="btn btn-lg btn-primary btn-block" (click)="cadastrarNovo()">Cadastrar Novo
            </button>
          </div>
          <div class="col" *ngIf="!entregaPorZona && formaReceberEmCasa.permiteUsarGps">
            <button class="btnUsarLocalizacao btn btn-lg btn-block btn-outline-primary" type="button"
                    (click)="usarLocalizacao()"  [disabled]="calculandoLocalizacao">
              <i class="k-icon k-i-loading  " *ngIf="calculandoLocalizacao"></i>

              <i class="fas fa-location-arrow"></i>
              Minha Localização
            </button>
          </div>
        </div>
      </div>
    </div>

    <div [hidden]="!enderecoEscolhido">
      <button class="btn btn-sm btn-outline-blue mb-2" (click)="voltarDoCadastro()"><i class="fas fa-chevron-left"></i></button>
      <app-form-endereco (submit)="salveEndereco($event)" [pedido]="pedido" #telaEndereco
                         [cepObrigatorio]="pedido.entrega.cepObrigatorio"
                         [complementoObrigatorio]="pedido.entrega.complementoObrigatorio"></app-form-endereco>
    </div>
  </div>
</div>

