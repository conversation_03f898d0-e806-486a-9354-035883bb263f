import {TunapayService} from "../../services/tunapay.service";
import {ApplicationRef, Renderer2} from "@angular/core";
import {DadosCartao} from "../../objeto/DadosCartao";
declare var Tuna: any;

export class TunapayScript {
  protected erroInicializacao: string;
  protected carregandoScript = true;
  protected definiuSessao = false;
  protected tunaInstance: any;
  protected cartao: any = new DadosCartao();
  constructor(protected tunapayService: TunapayService,  protected app: ApplicationRef,
              protected renderer: Renderer2) {
  }



  inicializeTuna(guid: string): Promise<any> {
    delete this.erroInicializacao;
    return new Promise<any>(async (resolve) => {
      if(typeof Tuna !== 'function')
        await this.insiraScript();

      await this.crieSessaoPagamento(guid);
      resolve(this.erroInicializacao)

    })

  }


  private insiraScript(){
    this.loadApplePayScript();

    return new Promise<any>(resolve => {
      const script = document.createElement('script');
      script.src = 'https://storage.googleapis.com/tuna-statics/tuna-v2.js';
      // script.src = 'https://js.tuna.uy/tuna-essentials.js';
      script.async = true;
      script.onload = async () => {
        this.carregandoScript = false;
        resolve(true)
      };
      document.body.appendChild(script);
    });
  }

  private loadApplePayScript(): void {
    if(this.renderer){
      const script = this.renderer.createElement('script');
      script.src = 'https://applepay.cdn-apple.com/jsapi/v1.1.0/apple-pay-sdk.js';
      script.async = true;
      script.crossOrigin = 'anonymous';

      this.renderer.appendChild(document.body, script);
    }

  }


  private async crieSessaoPagamento(guid: string) {
    let resp: any = await this.tunapayService.crieSessao(guid).catch((erro) => {
      console.log(erro)
      this.erroInicializacao = 'Não foi possível criar sessão de pagamento.'
    })

    if(resp){
      this.definiuSessao = true;
      this.tunaInstance = new Tuna(resp.sessionId, resp.env, { key: "cybersource", value: "cybersource_key" }) ;
      this.cartao.tokenSession = resp.sessionId;
      this.app.tick();
    }

  }

  async crieTokenGoooglePay(tokenGoogke){
    const tokenizator = this.tunaInstance.tokenizator();
    const response = await tokenizator.generate(tokenGoogke);

    return response.token;
  }
}
