import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SiteAdicionarProdutoComponent } from './site-adicionar-produto.component';

describe('SiteAdicionarProdutoComponent', () => {
  let component: SiteAdicionarProdutoComponent;
  let fixture: ComponentFixture<SiteAdicionarProdutoComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ SiteAdicionarProdutoComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SiteAdicionarProdutoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
