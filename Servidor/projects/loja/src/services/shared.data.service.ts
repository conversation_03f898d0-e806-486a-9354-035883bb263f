import { Injectable } from '@angular/core';
import { Subject, BehaviorSubject, Observable, ReplaySubject } from 'rxjs';
@Injectable({
  providedIn: 'root'
})
export class SharedDataService {
  public exibirMenuMobile  = false;
  constructor(){}
  public subject = new Subject<any>();
  private exibirMenu$ = new  BehaviorSubject(this.exibirMenuMobile);
  exibirMenu = this.exibirMenu$.asObservable();


  notifiqueExibirMenu() {
    this.exibirMenuMobile = !this.exibirMenuMobile;
    this.exibirMenu$.next(this.exibirMenuMobile)
  }
}
