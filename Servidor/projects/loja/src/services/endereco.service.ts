import { Injectable } from '@angular/core';
import {ServerService} from "./ServerService";
import {HttpClient} from "@angular/common/http";
import {Endereco} from "../objeto/Endereco";
import {Observable} from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class EnderecoService  extends ServerService {
  constructor(http: HttpClient) {
    super(http);
  }

  obtenhaEstados(): Promise<any> {
    return this.obtenha('/api/estados', { });
  }

  obtenhaCidades(estado): Promise<any> {
    return this.obtenha('/api/cidades/' + estado.id, { });
  }

  busquePorCEP(cep) {
    return this.obtenha('/api/endereco/' + cep, { });
  }

  obtenhaEnderecos(contato: any) {
    return this.obtenha('/contatos/' + contato.id + '/enderecos', { });
  }

  encontreLoja(grupoDeLojas, endereco: any) {
    return this.facaPost('/api/encontreLoja/' + grupoDe<PERSON>ojas, {
      endereco: endereco
    });
  }

  salveEnderecoLocal(pedido: any, endereco: any) {
    if( !pedido.novosEnderecos ) {
      pedido.novosEnderecos = [];
    }

    pedido.novosEnderecos.push(endereco);
  }

  calculeLocalizacaoEndereco(formaDeEntrega: any, endereco: Endereco, valorCompra: number) {
    return this.salve('/api/localizacaoEndereco', {
      forma: formaDeEntrega,
      zona: endereco.zonaDeEntrega,
      endereco: endereco,
      valor: valorCompra
    });
  }

  calculeTaxaDeEntrega(formaDeEntrega: any, endereco: Endereco, valorCompra: number) {
    return this.salve('/api/taxaDeEntrega', {
      forma: formaDeEntrega,
      zona: endereco.zonaDeEntrega,
      endereco: endereco,
      valor: valorCompra
    });
  }

  autocomplete(q: string): Observable<any> {
    return this.http.get('/api/autocomplete?q=' + encodeURIComponent(q), {params: {}});
  }

  encontreLojaGPS(grupoDeLojas: any, latLng) {
    return this.facaPost('/api/encontreLojaGps/' + grupoDeLojas, {
      p: latLng
    });
  }
}
