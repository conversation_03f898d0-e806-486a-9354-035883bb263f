import { Injectable } from '@angular/core';
import {ServerService} from "./ServerService";
import {HttpClient} from "@angular/common/http";

@Injectable({
  providedIn: 'root'
})
export class MercadopagoService  extends ServerService {


  constructor(http: HttpClient) {
    super(http);
  }

  obtenhaPublicToken() {
    return new Promise( (resolve, reject) => {
      this.obtenha('/mercadopago/pbtoken', {}).then( (token: any) => {
        resolve(token);
      }).catch( (erro: Error) => {
        reject(erro);
      });
    });
  }

}
