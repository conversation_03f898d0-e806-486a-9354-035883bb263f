import { Pipe, PipeTransform } from '@angular/core';
import * as libphonenumber from 'google-libphonenumber';

@Pipe({
  name: 'telefone'
})
export class TelefonePipe implements PipeTransform {

  transform(value: any, ...args: any[]): any {
    if(!value) return value;
    let codigoPais = '+55'
    if(args && args.length > 0)
      codigoPais = args[0]

    const phoneNumberUtil = libphonenumber.PhoneNumberUtil.getInstance();
    const phoneNumber = phoneNumberUtil.parse(codigoPais + value, 'ZZ');
    let format = codigoPais === '+55' ? libphonenumber.PhoneNumberFormat.NATIONAL : libphonenumber.PhoneNumberFormat.INTERNATIONAL
    return phoneNumberUtil.format(phoneNumber, format);


  }


}


@Pipe({
  name: 'cpf'
})
export class CPFPipe implements PipeTransform {

  transform(value: any, ...args: any[]): any {
    if(!value) return value;

    return  value.substr(0, 3)  + '.' +
      value.substr(3, 3)  + '.' +
      value.substr(6, 3) + '-'  + value.substr(9);
  }


}
