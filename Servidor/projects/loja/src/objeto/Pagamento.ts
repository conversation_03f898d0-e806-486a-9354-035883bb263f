import {TipoDePagamentoEnum} from "./TipoDePagamentoEnum";
import {FormaDePagamento} from "./FormaDePagamento";
import {PedidoLoja} from "./PedidoLoja";
import {DadosCartao} from "./DadosCartao";
import {DadosPix} from "./DadosPix";

export class Pagamento {
  formaDePagamento: FormaDePagamento;
  trocoPara: number;
  temTroco: string;
  dadosCartao: DadosCartao;
  dadosPix: DadosPix;
  valor: number;
  tipoDePagamento: string;
  constructor() {
    this.trocoPara = 0.00;
    this.formaDePagamento = null;
  }

  limpePagamento(){
    //quando add item pedido tem refazer fluxo pagamento
    this.formaDePagamento = null;
    this.trocoPara = 0.00;
    this.valor = null;
    this.dadosCartao = null;
    this.tipoDePagamento = null
    this.dadosPix = null;
  }
}
