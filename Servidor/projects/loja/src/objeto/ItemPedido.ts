import {CalculadoraPrecoSabor} from "./SaborPizzaLoja";
import {ObjetoComAdicionais} from "./ObjetoComAdicionais";

const PESO = 'Peso'

export class ItemPedido extends ObjetoComAdicionais {
  total: number;
  valorResgatado: number;
  adicionais: any;
  produtoTamanho: any;
  sabores = [];
  guid: string;
  qtdeSabores: number;
  brinde: any;
  opcoes: any[] = [];

  constructor(public produto: any, public qtde: number,
              public observacao: string) {
    super();
    this.adicionais = {}
    if(produto && produto.brinde)
      this.brinde = true;
    try{
      this.guid =  (crypto as any).randomUUID();
    } catch (e) {
      this.guid = new Date().getTime().toString()
    }
    this.atualizeTotal();
  }

  atualize(qtde, observacao: string, adicionais: any, tamanho: any , sabores: any){
    this.qtde = qtde;
    this.produtoTamanho = tamanho;
    this.sabores = sabores || [];

    this.observacao = observacao

    this.adicionais = adicionais
    this.atualizeTotal()
  }


  atualizeTotal() {
    if(this.produto && this.brinde){
      this.total = 0;
      this.valorResgatado = this.produto.valorResgate * this.qtde;
    } else {
      this.total = this.obtenhaValor();
    }
  }

  obtenhaValor(): number {

    if(this.produto.tipoDeVenda  && this.produto.tipoDeVenda === PESO)
      return this.obtenhaValorPorPeso();

    let valorUnitario = this.obtenhaPreco() + this.obtenhaValorAdicionais();

    //quando for por unidade adicionais e calculado junto
    return valorUnitario * this.qtde;
  }

  obtenhaPontosFidelidade(atividade: any){
    if(atividade.cashback != null){//ativiade que determina sera chashback ou pontos
      let preco = this.total, cashback = atividade.cashback;

      if(this.produtoTamanho && this.produtoTamanho.template){
        if(this.produtoTamanho.template.cashback != null)
          cashback = (this.produtoTamanho.template.cashback / 100)
      } else  if(this.produto.cashback != null){
        cashback = (this.produto.cashback / 100)
      }

      let pontos = cashback  * preco;

      return Number(pontos.toFixed(2))
    }

    let pontosPorProduto =    ( atividade.pontosGanhos || 0);

    if(this.produtoTamanho && this.produtoTamanho.template ){
      if(this.produtoTamanho.template.pontosGanhos != null)
        pontosPorProduto = this.produtoTamanho.template.pontosGanhos
    } else if(this.produto.pontosGanhos != null)
      pontosPorProduto = this.produto.pontosGanhos

    return pontosPorProduto * this.qtde;
  }

  private obtenhaPreco(){
    if(this.sabores && this.sabores.length)
       return  CalculadoraPrecoSabor.calculeValorSabores(this.produto.template, this.sabores);

    if(this.produtoTamanho)
      return this.produtoTamanho.preco;

    return this.produto.preco;
  }

  obtenhaValorPorPeso(): number{
    let qtdePorPeso = this.qtde;

    if(this.produto.unidadeMedida && this.produto.unidadeMedida.sigla === 'g'  )
       qtdePorPeso = this.qtde / 1000;

    let valorDoPeso = Number((this.produto.preco * qtdePorPeso ).toFixed(2));

    //quando for por peso adicionais e calculado a parte
    return valorDoPeso + this.obtenhaValorAdicionais();
  }

  obtenhaDescricao(){
    let descricao = this.produto.nome;

    if(this.produtoTamanho){
      descricao = String(`${this.produto.nome} ${this.produtoTamanho.descricao}`);

      if(this.sabores && this.sabores.length){
        let listaSabores = this.sabores.map( (sabor: any) => sabor.nome);

        if(listaSabores.length   === 1)
          descricao = String(`${this.produto.template.identificador} ${this.produtoTamanho.descricao}  - ${listaSabores[0]}`);
        else{
          for(let i = 0 ; i < listaSabores.length; i++){
            listaSabores[i] = String(`${(i + 1)}°: ${listaSabores[i]}`)
          }
          descricao = String(`${this.produto.template.identificador} ${this.produtoTamanho.descricao} ${this.sabores.length} Sabores ( ${listaSabores.join('; ') } )`);
        }


      }
    }

    return descricao;
  }

  obtenhaUnidade(){
    if(this.produto.tipoDeVenda && this.produto.tipoDeVenda === PESO)
      return this.produto.unidadeMedida.sigla.toLowerCase() + " de ";

    return 'x'
  }

  temOpcaoSelecionada(campo: any, opcao: any): boolean {
    if (!this.opcoes) {
      return false;
    }
    
    return this.opcoes.some(op => 
      op.campoAdicional && op.campoAdicional.id === campo.id && 
      op.opcao && op.opcao.id === opcao.id
    );
  }
}


