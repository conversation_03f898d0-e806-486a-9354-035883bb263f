import {ItemPedido} from "./ItemPedido";
import {ObjetoComAdicionais} from "./ObjetoComAdicionais";

export class AdicionalUtils {
  static prepareItemdoPedido(itemPedido: ItemPedido){
    AdicionalUtils.prepareAdicionais(itemPedido, itemPedido.produto.camposAdicionais)
  }

  static prepareAdicionaisSabores( itemPedido: any, produto: any ){
    if(!itemPedido.sabores) return;

    let camposAdicionais = produto.camposAdicionais;
    let contadorMultipla = camposAdicionais.filter((adicional: any) => adicional.tipo === 'multipla-escolha').length ;
    let contadorAdicionalSimples = camposAdicionais.filter((adicional: any) => adicional.tipo === 'escolha-simples').length;

    let limparSabores  = true, proximaChave = contadorAdicionalSimples;

    while (limparSabores){
      let chave = String(`campo${proximaChave}`)
      if(itemPedido.adicionais[chave]){
         delete itemPedido.adicionais[chave]
         delete itemPedido.adicionais[String(`escolhido_campo${proximaChave}`)]
         proximaChave++
      } else {
        limparSabores = false;
      }
    }

    limparSabores = true;
    proximaChave = contadorMultipla;

    while (limparSabores){
      let chave = String(`lista${proximaChave}`)
      if(itemPedido.adicionais[chave]){
        delete itemPedido.adicionais[chave]
        proximaChave++
      } else {
        limparSabores = false;
      }
    }


    itemPedido.sabores.forEach((sabor) => {
      if (sabor.produto !== produto.id) {
        for(let campoAdicionalSabor of sabor.camposAdicionais) {
          if(campoAdicionalSabor.tipo === 'multipla-escolha') {
            campoAdicionalSabor.posicao = contadorMultipla++
            AdicionalUtils.setListaMultiplaEscolha(itemPedido, campoAdicionalSabor)

          } else if(campoAdicionalSabor.tipo === 'escolha-simples') {
            campoAdicionalSabor.posicao = contadorAdicionalSimples++
            AdicionalUtils.setOpcaoSimples(itemPedido, campoAdicionalSabor)
          }
        }
      }
    })
  }

  static prepareAdicionais(itemPedido: ObjetoComAdicionais, camposAdicionais: any) {
    let campoAdicional: any
    let multiplaEscolha = 0
    let escolhaUnica = 0
    for(campoAdicional of camposAdicionais) {
      if(campoAdicional.tipo === 'multipla-escolha') {
        campoAdicional.posicao = multiplaEscolha++
        AdicionalUtils.setListaMultiplaEscolha(itemPedido, campoAdicional)

      } else if(campoAdicional.tipo === 'escolha-simples') {
        campoAdicional.posicao = escolhaUnica++
        AdicionalUtils.setOpcaoSimples(itemPedido, campoAdicional)
      }
    }

    itemPedido.atualizeTotal();
  }

  static setListaMultiplaEscolha(itemPedido: any, campoAdicional: any){
    if( !itemPedido.adicionais['lista' + campoAdicional.posicao]){
      itemPedido.adicionais['lista' + campoAdicional.posicao] = {}
      itemPedido.adicionais['lista' + campoAdicional.posicao].totalSelecionado = 0
      itemPedido.adicionais['lista' + campoAdicional.posicao].tipoDeCobranca = campoAdicional.tipoDeCobranca
      itemPedido.adicionais['lista' + campoAdicional.posicao].ordem = campoAdicional.ordem
    }

    for(let opcao of campoAdicional.opcoesDisponiveis) {
      if(!itemPedido.adicionais['lista' + campoAdicional.posicao]['opcao_' + opcao.id]){
        itemPedido.adicionais['lista' + campoAdicional.posicao]['opcao_' + opcao.id] = {
          selecionada: false,
          opcao: opcao
        }

        if(campoAdicional.podeRepetirItem)
          itemPedido.adicionais['lista' + campoAdicional.posicao]['opcao_' + opcao.id].qtde = 0

      }
    }
  }

  static setOpcaoSimples(itemPedido: any, campoAdicional: any){
    if(itemPedido.adicionais['campo' + campoAdicional.posicao]){
      itemPedido.adicionais['escolhido_campo' + campoAdicional.posicao] =
        itemPedido.adicionais['campo' + campoAdicional.posicao].id
    }
  }

}
