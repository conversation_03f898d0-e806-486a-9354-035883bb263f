.form-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;

  input {
    width: 100%;
  }

  .erro {
    color: red;
    font-weight: bold;
  }
}

.loading {
  text-align: center;
  padding: 2rem;

  p {
    margin-top: 1rem;
    font-size: 1.2rem;
  }
}

/* Layout de duas colunas */
.cartao-cliente-duas-colunas {
  display: flex;
  flex: 1;
  overflow: hidden;
  background-color: #1a1a1a;

  /* Coluna esquerda - Scanner */
  .coluna-scanner {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .scanner-container {
      position: relative;
      background-color: #000;
      overflow: hidden;

      margin: 1rem;
      border: 1px solid #ccc;
      border-radius: 8px;
      max-height: 300px;

      .scanner-frame {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 70%;
        height: 60%;
        border: 2px solid rgba(255, 69, 0, 0.5);
        border-radius: 8px;
        z-index: 2;

        .scanner-line {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 2px;
          background-color: #ff4500;
          box-shadow: 0 0 15px #ff4500;
          animation: scanLine 2s linear infinite;
        }

        .scanner-corners {
          position: absolute;
          width: 20px;
          height: 20px;
          border-color: #ff4500;
          border-style: solid;
          border-width: 0;

          &.top-left {
            top: -2px;
            left: -2px;
            border-top-width: 3px;
            border-left-width: 3px;
            border-top-left-radius: 4px;
          }

          &.top-right {
            top: -2px;
            right: -2px;
            border-top-width: 3px;
            border-right-width: 3px;
            border-top-right-radius: 4px;
          }

          &.bottom-left {
            bottom: -2px;
            left: -2px;
            border-bottom-width: 3px;
            border-left-width: 3px;
            border-bottom-left-radius: 4px;
          }

          &.bottom-right {
            bottom: -2px;
            right: -2px;
            border-bottom-width: 3px;
            border-right-width: 3px;
            border-bottom-right-radius: 4px;
          }
        }
      }

      .scanner-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  /* Coluna direita - Instruções */
  .coluna-instrucoes {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 30px;
    overflow-y: auto;
    background-color: #222;

    .instrucoes-container {
      width: 100%;
      margin-bottom: 40px;
      background-color: #333;
      padding: 25px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      border-left: 4px solid #ff4500;

      .instrucao-texto {
        margin-bottom: 20px;

        h3 {
          font-size: 24px;
          font-weight: 600;
          color: #ff4500;
          margin-bottom: 15px;
          display: flex;
          align-items: center;
          gap: 10px;

          i {
            font-size: 22px;
          }
        }

        p {
          font-size: 16px;
          color: #ddd;
          margin-bottom: 10px;
          line-height: 1.5;

          i {
            color: #ff4500;
            margin-right: 5px;
          }

          strong {
            color: #fff;
          }
        }
      }

      .instrucao-imagem {
        display: flex;
        justify-content: center;
        margin-top: 20px;

        img {
          max-width: 400px;
          width: 100%;
          height: auto;
          filter: drop-shadow(0 4px 8px rgba(255, 69, 0, 0.3));
          transition: transform 0.3s ease;

          &:hover {
            transform: scale(1.05);
          }
        }
      }
    }

    .codigo-manual {
      width: 100%;

      h4 {
        font-size: 18px;
        font-weight: 500;
        color: #ddd;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          color: #ff4500;
          font-size: 16px;
        }
      }

      .input-group {
        display: flex;

        input {
          flex: 1;
          padding: 15px;
          border: 1px solid #444;
          background-color: #2a2a2a;
          color: #fff;
          border-radius: 4px 0 0 4px;
          font-size: 16px;

          &:focus {
            outline: none;
            border-color: #ff4500;
            box-shadow: 0 0 0 2px rgba(255, 69, 0, 0.2);
          }

          &::placeholder {
            color: #777;
          }
        }

        .btn-confirmar-codigo {
          background-color: #ff4500;
          color: white;
          border: none;
          padding: 0 25px;
          border-radius: 0 4px 4px 0;
          cursor: pointer;
          font-weight: 600;
          font-size: 16px;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          gap: 8px;

          i {
            font-size: 14px;
          }

          &:hover {
            background-color: darken(#ff4500, 10%);
            transform: translateX(2px);
          }

          &:active {
            transform: translateY(1px);
          }
        }
      }

      .dica-codigo {
        margin-top: 15px;
        padding: 12px;
        background-color: rgba(255, 69, 0, 0.1);
        border-radius: 4px;
        border-left: 3px solid #ff4500;

        p {
          color: #bbb;
          font-size: 14px;
          margin: 0;
          font-style: italic;
        }
      }
    }
  }
}

/* Responsividade para telas menores */
@media (max-width: 768px) {
  .cartao-cliente-duas-colunas {
    flex-direction: column;

    .coluna-scanner {
      height: 40vh;
    }

    .coluna-instrucoes {
      padding: 20px;

      .instrucoes-container {
        padding: 15px;
        margin-bottom: 20px;

        .instrucao-texto {
          h3 {
            font-size: 20px;
          }
        }
      }
    }
  }
}
