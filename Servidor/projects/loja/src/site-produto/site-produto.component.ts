import {Component, ElementRef, Input, Output, EventEmitter} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {ProdutoService} from "../services/produto.service";
import {Location} from "@angular/common";
import {ClienteService} from "../services/cliente.service";
import {CarrinhoService} from "../services/carrinho.service";
import {DominiosService} from "../services/dominios.service";
import {ConstantsService} from "../services/ConstantsService";
import {MyDetectorDevice} from "../app/shared/MyDetectorDevice";
import {SiteProdutoBase} from "./site-produto-base";

@Component({
  selector: 'app-site-produto',
  templateUrl: './site-produto.component.html',
  styleUrls: ['./site-produto.component.scss']
})
export class SiteProdutoComponent extends SiteProdutoBase {
  @Input() temaDark: boolean = false;
  @Output() onAdicionarProduto = new EventEmitter<any>();
  @Output() onCancelar = new EventEmitter<any>();

  constructor(
    router: Router, 
    produtoService: ProdutoService, 
    deviceService: MyDetectorDevice,
    el: ElementRef,
    activatedRoute: ActivatedRoute, 
    _location: Location,
    clienteService: ClienteService, 
    carrinhoService: CarrinhoService,
    dominiosService: DominiosService, 
    constantsService: ConstantsService
  ) {
    super(router, produtoService, deviceService, el, activatedRoute, _location, 
          clienteService, carrinhoService, dominiosService, constantsService);
  }

  // Método para cancelar
  cancelar(): void {
    if (this.onCancelar) {
      this.onCancelar.emit();
    } else {
      this.fecheTela();
    }
  }
  
  // Método para adicionar ao carrinho
  adicionarAoCarrinho(): void {
    if (this.onAdicionarProduto) {
      this.onAdicionarProduto.emit(this.itemPedido);
    } else {
      // Implementar método para adicionar produto
    }
  }
}
