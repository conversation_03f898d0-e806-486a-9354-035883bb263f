<div class="tema-dark-container" *ngIf="produto">
  <div class="produto-header">
    <h2>{{produto?.nome}}</h2>
    <button class="btn-fechar" (click)="cancelar()">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <div class="produto-content">
    <!-- Galeria de imagens do produto -->
    <div class="produto-galeria" *ngIf="imagens && imagens.length > 0">
      <div class="row">
        <!-- Miniaturas (quando houver mais de uma imagem) -->
        <div class="col-auto miniaturas-container" *ngIf="imagens.length > 1">
          <div class="miniaturas">
            <ng-container *ngFor="let imagem of imagens; let i = index">
              <img class="miniatura"
                   (mouseover)="selecionou(i)"
                   (touchend)="selecionou(i)"
                   [class.selecionada]="i == selecionada"
                   [src]="'https://fibo.promokit.com.br/images/empresa/' + imagem.linkImagem"/>
            </ng-container>
          </div>
        </div>

        <!-- Imagem principal -->
        <div class="col imagem-principal-container">
          <div class="imagem-principal">
            <img class="img-principal"
                 (click)="exibaFullScreen()"
                 [src]="'https://fibo.promokit.com.br/images/empresa/' + imagens[selecionada]?.linkImagem"/>
          </div>

          <!-- Slider oculto para visualização em tela cheia -->
          <div style="position: absolute; left: -1500px">
            <ng-image-slider #nav
                            [images]="imagensSlider"
                            (imageClick)="abriuImagem($event)"
                            [autoSlide]="1"
                            slideImage="1">
            </ng-image-slider>
          </div>
        </div>
      </div>
    </div>

    <!-- Informações do produto -->
    <div class="produto-info">
      <p [innerHTML]="produto.descricao"></p>

      <div class="produto-preco" *ngIf="!produto.brinde">
        <span class="preco-valor">{{(produto.valorMinimo ? produto.valorMinimo : produto.preco) | currency: 'BRL'}}</span>
        <span class="preco-riscado" *ngIf="produto.precoAntigo">{{produto.precoAntigo | currency: 'BRL'}}</span>
      </div>
    </div>

    <!-- Detalhes do produto -->
    <div class="detalhes-box" *ngIf="produto.detalhes">
      <div class="detalhes-titulo">
        <i class="fas fa-info-circle"></i>
        <h4>Detalhes:</h4>
      </div>
      <p>- copo de 300ml montado com 2 adicionais: farofa de amendoim + leite condensado.</p>
    </div>

    <!-- Informações importantes -->
    <div class="importante-box" *ngIf="produto.importante">
      <h4>Importante:</h4>
      <p>- não será permitido envio de adicionais separados, apenas o copo montado;</p>
      <p>- não é permitido a troca de adicionais, apenas a remoção.</p>
      <p>- adicionais fora da promoção, serão cobrados a parte.</p>
    </div>

    <!-- Campos Adicionais -->
    <div *ngFor="let campoAdicional of produto.camposAdicionais" id="adicional{{campoAdicional.posicao}}">
      <app-site-campo-adicional #adicionalComponent
                               [id]="'adicional_' + campoAdicional.id"
                               [campoAdicional]="campoAdicional"
                               [produto]="produto"
                               [itemPedido]="itemPedido"
                               [posicao]="campoAdicional.posicao"
                               (onDesmarcouOpcao)="desmarcouNovaOpcao($event)"
                               (onMarcouOpcao)="escolheuNovaOpcao($event)">
      </app-site-campo-adicional>
    </div>

    <!-- Sabores adicionais -->
    <ng-container *ngFor="let sabor of itemPedido.sabores">
      <ng-container *ngIf="sabor.produto !== produto.id">
        <div *ngFor="let adicionalSabor of sabor.camposAdicionais">
          <app-site-campo-adicional #adicionalComponent
                                   class="campo-adicional"
                                   [campoAdicional]="adicionalSabor"
                                   [produto]="sabor"
                                   [itemPedido]="itemPedido"
                                   [posicao]="adicionalSabor.posicao"
                                   [exibirSabor]="true"
                                   [tamanhoSabor]="sabor.produtoTamanho"
                                   (onDesmarcouOpcao)="desmarcouNovaOpcao($event)"
                                   (onMarcouOpcao)="escolheuNovaOpcao($event)">
          </app-site-campo-adicional>
        </div>
      </ng-container>
    </ng-container>

    <!-- Adicionais Customizados -->
    <app-adicionais-customizados #adicionaisCustomizados
      *ngIf="produto.adicionaisCustomizados"
      [itemPedido]="itemPedido"
      (onEscolheuOpcao)="escolheuNovaOpcao($event)"
      (alterouTamanho)="onAlterouTamanho($event)">
    </app-adicionais-customizados>

    <!-- Controle de quantidade -->
    <div class="controle-quantidade">
      <button class="btn-menos" (click)="diminuirQuantidade()" [disabled]="itemPedido.qtde <= 1">-</button>
      <span class="qtde">{{itemPedido.qtde}}</span>
      <button class="btn-mais" (click)="aumentarQuantidade()" [disabled]="itemPedido.qtde >= produto.qtdMaxima">+</button>
    </div>

    <!-- Observação -->
    <div class="produto-observacao">
      <h4>Alguma Observação?</h4>
      <textarea
        placeholder="Inclua uma observação sobre o pedido."
        [(ngModel)]="itemPedido.observacao"
        rows="3">
      </textarea>
    </div>

    <!-- Mensagem de erro -->
    <div class="erro-mensagem" *ngIf="erro">
      <p class="text-danger"><b>{{erro}}</b></p>
    </div>

    <!-- Espaço para garantir que o conteúdo não fique escondido atrás do botão fixo -->
    <div class="espaco-footer"></div>
  </div>

  <!-- Botão de adicionar fixo no rodapé -->
  <div class="produto-acoes-fixo">
    <p *ngIf="erro" class="text-danger"><b>{{erro}}</b></p>
    <button class="btn-adicionar" (click)="adicionarAoCarrinho()" [disabled]="produto.indisponivel">
      <i class="fas fa-shopping-cart"></i>
      <span *ngIf="!adicionandoProduto">
        Adicionar
        <span *ngIf="!itemPedido.brinde">({{(itemPedido.total) | currency: 'BRL'}})</span>
        <span *ngIf="itemPedido.brinde">({{itemPedido.valorResgatado}} {{itemPedido.produto.acumulo}})</span>
      </span>
      <span *ngIf="adicionandoProduto">
        <i class="fas fa-spinner fa-spin"></i> Adicionando...
      </span>
    </button>
  </div>
</div>

<kendo-dialog *ngIf="dialogAberto"
             [title]="'Aviso'"
             (close)="fecharDialog()"
             [minWidth]="250"
             [width]="450">
  <div class="text-center">
    <i class="dripicons-information h1 text-info"></i>
    <h4 class="mt-2">{{mensagemAbrirPedidos}}</h4>
    <p class="mt-3">Continue olhando nosso cardápio à vontade.</p>
  </div>

  <kendo-dialog-actions>
    <button kendoButton (click)="fecharDialog()">Continuar</button>
    <button kendoButton *ngIf="modoVisualizacao" [primary]="true" class="ml-2">
      <app-exibir-whatsapp [empresa]="empresa" [light]="true"></app-exibir-whatsapp>
    </button>
  </kendo-dialog-actions>
</kendo-dialog>
