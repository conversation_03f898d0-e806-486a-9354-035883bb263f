// Variáveis para o tema escuro
$cor-fundo-dark: #1a1a1a;
$cor-texto-dark: #ffffff;
$cor-primaria-dark: #ff4500;
$cor-secundaria-dark: #2a2a2a;
$cor-borda-dark: #444;
$cor-input-dark: rgba(255, 255, 255, 0.05);
$cor-input-borda-dark: rgba(255, 255, 255, 0.1);
$cor-botao-dark: #e67e22;
$cor-botao-texto-dark: #ffffff;
$cor-botao-secundario-dark: transparent;
$cor-botao-secundario-texto-dark: #ffffff;
$cor-destaque: #e67e22;
$cor-preco-riscado: rgba(255, 255, 255, 0.5);
$cor-tag-obrigatorio: #e67e22;

// Variáveis para efeitos de glassmorphism
$glass-bg: rgba(40, 40, 40, 0.7);
$glass-border: rgba(255, 255, 255, 0.1);
$glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

// Aplicar tema escuro
.tema-dark-container {
  background-color: $cor-fundo-dark !important;
  color: $cor-texto-dark;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: 'Roboto', sans-serif;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;

  .btn {
    background: linear-gradient(135deg, $cor-secundaria-dark 0%, darken($cor-secundaria-dark, 5%) 100%) !important;
    width: 42px !important;
    height: 42px !important;
    border-radius: 50px !important;
    color: $cor-texto-dark !important;
    border: none !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 18px !important;

    &:hover {
      background: linear-gradient(135deg, rgba($cor-destaque, 0.2) 0%, rgba($cor-destaque, 0.1) 100%) !important;
      color: $cor-destaque !important;
      box-shadow: 0 4px 12px rgba($cor-destaque, 0.3) !important;
      transform: translateY(-2px) !important;
    }

    &:active, &:focus {
      background: linear-gradient(135deg, rgba($cor-destaque, 0.3) 0%, rgba($cor-destaque, 0.2) 100%) !important;
      color: $cor-destaque !important;
      box-shadow: 0 2px 8px rgba($cor-destaque, 0.2) !important;
      transform: translateY(0) !important;
    }
  }

  .botoes_mais_menos {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: rgba(60, 60, 60, 0.2) !important;
    border-radius: 30px !important;
    padding: 5px !important;
    box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1) !important;

    input {
      font-size: 24px !important;
      line-height: 32px !important;
      font-weight: bold !important;
      width: 42px !important;
      height: 42px !important;
      vertical-align: middle !important;
      background-color: transparent !important;
      color: $cor-texto-dark !important;
      border: none !important;
      text-align: center !important;
      margin: 0 5px !important;
    }
  }

  .bg-light {
    background: linear-gradient(135deg, $cor-secundaria-dark 0%, darken($cor-secundaria-dark, 3%) 100%) !important;
    border-radius: 10px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
    margin-bottom: 15px !important;
    padding: 15px !important;

    h4 {
      color: $cor-texto-dark !important;
      font-weight: 600 !important;
      margin-bottom: 8px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: space-between !important;

      .fa-search {
        color: $cor-destaque !important;
        cursor: pointer !important;
        font-size: 16px !important;
        margin-left: 10px !important;
        transition: all 0.3s ease !important;

        &:hover {
          transform: scale(1.1) !important;
        }
      }
    }

    .text-muted {
      color: rgba(255, 255, 255, 0.6) !important;
      font-size: 14px !important;
    }
  }

  .badge {
    background-color: $cor-tag-obrigatorio !important;
    color: white !important;
    font-size: 12px !important;
    padding: 4px 10px !important;
    border-radius: 20px !important;
    margin-left: 8px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;

    &.badge-danger {
      background: linear-gradient(135deg, rgba(255, 82, 82, 0.9) 0%, rgba(200, 40, 40, 0.9) 100%) !important;
      color: white !important;
      box-shadow: 0 2px 8px rgba(255, 82, 82, 0.4) !important;
    }

    &.badge-success {
      background: linear-gradient(135deg, rgba(76, 175, 80, 0.9) 0%, rgba(56, 142, 60, 0.9) 100%) !important;
      color: white !important;
      box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4) !important;
    }

    &.badge-info {
      background: linear-gradient(135deg, rgba(33, 150, 243, 0.9) 0%, rgba(25, 118, 210, 0.9) 100%) !important;
      color: white !important;
      box-shadow: 0 2px 8px rgba(33, 150, 243, 0.4) !important;
    }
  }

  input[type="radio"] {
    appearance: none !important;
    -webkit-appearance: none !important;
    width: 24px !important;
    height: 24px !important;
    border-radius: 50% !important;
    border: 2px solid $cor-borda-dark !important;
    background-color: $cor-secundaria-dark !important;
    position: relative !important;
    cursor: pointer !important;
    margin-right: 6px !important;

    &:checked {
      border-color: $cor-destaque !important;

      &:after {
        content: "" !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        width: 12px !important;
        height: 12px !important;
        border-radius: 50% !important;
        background-color: $cor-destaque !important;
      }
    }
  }

  input[type="checkbox"] {
    appearance: none !important;
    -webkit-appearance: none !important;
    width: 24px !important;
    height: 24px !important;
    border-radius: 4px !important;
    border: 2px solid $cor-borda-dark !important;
    background-color: $cor-secundaria-dark !important;
    position: relative !important;
    cursor: pointer !important;
    margin-right: 6px !important;

    &:checked {
      border-color: $cor-destaque !important;
      background-color: $cor-destaque !important;

      &:after {
        content: "" !important;
        position: absolute !important;
        top: 3px !important;
        left: 8px !important;
        width: 6px !important;
        height: 12px !important;
        border: solid white !important;
        border-width: 0 2px 2px 0 !important;
        transform: rotate(45deg) !important;
      }
    }
  }

  .nome_opcao {
    color: $cor-texto-dark !important;
    font-size: 16px !important;
  }

  .badge {
    font-size: 14px !important;
    margin-top: 0px !important;
  }

  // Estilo para mensagens de erro
  .alert-danger {
    background: rgba(50, 30, 30, 0.8) !important;
    color: #ff6b6b !important;
    border: none !important;
    border-left: 3px solid #ff5252 !important;
    border-radius: 12px !important;
    padding: 12px 18px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    box-shadow: 0 8px 25px rgba(255, 82, 82, 0.15) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    display: flex !important;
    align-items: center !important;
    margin-bottom: 15px !important;
    letter-spacing: 0.3px !important;
    position: relative !important;
    overflow: hidden !important;

    &:before {
      content: '' !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      width: 100% !important;
      height: 100% !important;
      background: linear-gradient(45deg, transparent, rgba(255, 82, 82, 0.05), transparent) !important;
      background-size: 200% 200% !important;
      animation: shimmer 3s infinite !important;
      pointer-events: none !important;
    }

    i {
      margin-right: 12px !important;
      font-size: 18px !important;
      color: #ff5252 !important;
    }
  }

  @keyframes shimmer {
    0% {
      background-position: 0% 0% !important;
    }
    50% {
      background-position: 100% 100% !important;
    }
    100% {
      background-position: 0% 0% !important;
    }
  }

  .produto-header {
    background: $glass-bg;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid $glass-border;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);

    h2 {
      margin: 0;
      color: $cor-texto-dark;
      font-size: 1.3rem;
      font-weight: 600;
      letter-spacing: 0.5px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .btn-fechar {
      background: rgba(255, 255, 255, 0.1);
      border: none;
      color: $cor-texto-dark;
      font-size: 1.2rem;
      cursor: pointer;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:hover {
        color: $cor-destaque;
        background: rgba(255, 255, 255, 0.15);
        transform: rotate(90deg);
      }
    }
  }

  .produto-content {
    position: absolute;
    top: 60px; /* Altura aproximada do header */
    left: 0;
    right: 0;
    bottom: 60px; /* Espaço para o footer */
    overflow-y: auto;
    padding: 20px;
    padding-bottom: 80px;

    .produto-info {
      margin-bottom: 25px;

      p {
        color: rgba(255, 255, 255, 0.7);
        line-height: 1.5;
        margin-bottom: 15px;
        font-size: 0.95rem;
      }

      .produto-preco {
        display: flex;
        align-items: baseline;
        flex-wrap: wrap;
        margin-top: 15px;

        .preco-valor {
          color: $cor-destaque;
          font-size: 1.5rem;
          font-weight: bold;
          margin-right: 10px;
        }

        .preco-riscado {
          color: $cor-preco-riscado;
          text-decoration: line-through;
          font-size: 1rem;
        }
      }
    }

    .detalhes-box {
      background-color: $cor-secundaria-dark;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 15px;

      .detalhes-titulo {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        i {
          color: $cor-destaque;
          margin-right: 10px;
          font-size: 1.1rem;
        }

        h4 {
          color: $cor-texto-dark;
          margin: 0;
          font-size: 1.1rem;
          font-weight: 500;
        }
      }

      p {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
        margin: 5px 0;
        padding-left: 25px;
      }
    }

    .importante-box {
      background-color: $cor-secundaria-dark;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 15px;

      h4 {
        color: $cor-texto-dark;
        margin-bottom: 10px;
        font-size: 1.1rem;
      }

      p {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
        margin: 5px 0;
      }
    }

    .campos-adicionais {
      margin: 20px 0;

      ::ng-deep {


          &:focus {
            outline: none !important;
            box-shadow: 0 0 0 2px rgba($cor-destaque, 0.3) !important;
          }

        input[type="checkbox"] {
          appearance: none !important;
          -webkit-appearance: none !important;
          width: 20px !important;
          height: 20px !important;
          border-radius: 4px !important;
          border: 2px solid $cor-borda-dark !important;
          background-color: $cor-secundaria-dark !important;
          position: relative !important;
          cursor: pointer !important;

          &:checked {
            border-color: $cor-destaque !important;
            background-color: $cor-destaque !important;

            &:after {
              content: "" !important;
              position: absolute !important;
              top: 2px !important;
              left: 6px !important;
              width: 5px !important;
              height: 10px !important;
              border: solid white !important;
              border-width: 0 2px 2px 0 !important;
              transform: rotate(45deg) !important;
            }
          }

          &:focus {
            outline: none !important;
            box-shadow: 0 0 0 2px rgba($cor-destaque, 0.3) !important;
          }
        }

        app-site-campo-adicional {
          display: block;
          margin-bottom: 15px;

          .campo-adicional {
            background-color: $cor-secundaria-dark !important;
            border-radius: 8px !important;
            padding: 15px !important;
            border: 1px solid $cor-borda-dark !important;

            .campo-adicional-titulo {
              color: $cor-texto-dark !important;
              font-size: 18px !important;
              font-weight: 600 !important;
              margin-bottom: 10px !important;

              .badge {
                background-color: $cor-tag-obrigatorio !important;
                color: white !important;
                font-size: 12px !important;
                padding: 4px 10px !important;
                border-radius: 20px !important;
                margin-left: 8px !important;
              }
            }

            .opcoes-lista {
              .opcao-item {
                margin-bottom: 12px !important;
                padding: 10px 0 !important;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;

                &:last-child {
                  border-bottom: none !important;
                  margin-bottom: 0 !important;
                }

                label {
                  display: flex !important;
                  align-items: center !important;
                  width: 100% !important;
                  margin-bottom: 0 !important;
                  cursor: pointer !important;

                  &:focus {
                    outline: none !important;
                    box-shadow: 0 0 0 2px rgba($cor-destaque, 0.3) !important;
                  }

                  .nome_opcao {
                    flex: 1 !important;
                    font-size: 16px !important;
                    color: $cor-texto-dark !important;
                  }

                  .opcao-preco {
                    color: $cor-destaque !important;
                    font-weight: 600 !important;
                    font-size: 16px !important;
                    margin-left: 10px !important;
                  }
                }

                .controles-quantidade {
                  display: flex !important;
                  align-items: center !important;
                  margin-top: 8px !important;
                  margin-left: 36px !important;

                  button {
                    width: 36px !important;
                    height: 36px !important;
                    border-radius: 50% !important;
                    background-color: rgba(68, 68, 68, 0.8) !important;
                    border: 1px solid $cor-borda-dark !important;
                    color: $cor-texto-dark !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    font-size: 18px !important;
                    cursor: pointer !important;

                    &:hover {
                      background-color: rgba(230, 126, 34, 0.2) !important;
                      border-color: $cor-destaque !important;
                      color: $cor-destaque !important;
                    }
                  }

                  .qtde {
                    width: 40px !important;
                    text-align: center !important;
                    color: $cor-texto-dark !important;
                    font-size: 18px !important;
                    font-weight: 600 !important;
                    margin: 0 8px !important;
                  }
                }
              }
            }
          }
        }

        app-adicionais-customizados {
          display: block !important;
          margin-bottom: 20px !important;

          .adicionais-customizados {
            background-color: $cor-secundaria-dark !important;
            border-radius: 8px !important;
            padding: 15px !important;
            border: 1px solid $cor-borda-dark !important;

            .titulo {
              color: $cor-texto-dark !important;
              font-size: 18px !important;
              font-weight: 600 !important;
              margin-bottom: 15px !important;
            }

            .lista-adicionais {
              .adicional-item {
                display: flex !important;
                align-items: center !important;
                padding: 10px 0 !important;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;

                &:last-child {
                  border-bottom: none !important;
                }

                .nome {
                  flex: 1 !important;
                  color: $cor-texto-dark !important;
                  font-size: 16px !important;
                }

                .preco {
                  color: $cor-destaque !important;
                  font-weight: 600 !important;
                  margin: 0 15px !important;
                  font-size: 16px !important;
                }

                .controles {
                  display: flex !important;
                  align-items: center !important;

                  button {
                    width: 36px !important;
                    height: 36px !important;
                    border-radius: 50% !important;
                    background-color: rgba(68, 68, 68, 0.8) !important;
                    border: 1px solid $cor-borda-dark !important;
                    color: $cor-texto-dark !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    font-size: 18px !important;
                    cursor: pointer !important;
                    transition: all 0.2s ease !important;

                    &:hover {
                      background-color: rgba(230, 126, 34, 0.2) !important;
                      border-color: $cor-destaque !important;
                      color: $cor-destaque !important;
                    }

                    &:active {
                      transform: scale(0.95) !important;
                    }
                  }

                  .qtde {
                    width: 40px !important;
                    text-align: center !important;
                    color: $cor-texto-dark !important;
                    font-size: 18px !important;
                    font-weight: 600 !important;
                    margin: 0 8px !important;
                  }
                }
              }
            }
          }
        }
      }
    }

    .controle-quantidade {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 25px 0;
      background: $glass-bg;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border-radius: 50px;
      padding: 8px 15px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      width: fit-content;
      margin-left: auto;
      margin-right: auto;

      .btn-menos,
      .btn-mais {
        background: linear-gradient(135deg, rgba(60, 60, 60, 0.8) 0%, rgba(40, 40, 40, 0.8) 100%);
        color: $cor-texto-dark;
        border: none;
        width: 45px;
        height: 45px;
        border-radius: 50%;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;

        &:hover {
          background: linear-gradient(135deg, rgba($cor-destaque, 0.2) 0%, rgba($cor-destaque, 0.1) 100%);
          color: $cor-destaque;
          transform: scale(1.05);
          box-shadow: 0 6px 15px rgba($cor-destaque, 0.2);
        }

        &:active {
          transform: scale(0.95);
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
      }

      .qtde {
        margin: 0 20px;
        color: $cor-texto-dark;
        font-size: 1.5rem;
        font-weight: 600;
        min-width: 40px;
        text-align: center;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
    }

    .produto-observacao {
      margin-bottom: 25px;

      h4 {
        color: $cor-texto-dark;
        font-size: 1.1rem;
        margin-bottom: 10px;
      }

      textarea {
        width: 100%;
        background-color: $cor-secundaria-dark;
        border: 1px solid $cor-borda-dark;
        border-radius: 8px;
        color: $cor-texto-dark;
        padding: 12px;
        resize: none;

        &::placeholder {
          color: rgba(255, 255, 255, 0.4);
        }

        &:focus {
          outline: none;
          border-color: $cor-destaque;
        }
      }
    }

    .espaco-footer {
      height: 20px;
    }
  }

  .produto-acoes-fixo {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: $glass-bg;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 15px 20px;
    border-top: 1px solid $glass-border;
    z-index: 100;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);

    .text-danger {
      background: rgba(50, 30, 30, 0.8);
      color: #ff6b6b;
      padding: 14px 18px;
      border-radius: 12px;
      margin-bottom: 15px;
      border-left: 3px solid #ff5252;
      font-size: 14px;
      text-align: center;
      font-weight: 500;
      box-shadow: 0 8px 25px rgba(255, 82, 82, 0.15);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      display: flex;
      align-items: center;
      justify-content: center;
      letter-spacing: 0.3px;
      position: relative;
      overflow: hidden;

      &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, transparent, rgba(255, 82, 82, 0.05), transparent);
        background-size: 200% 200%;
        animation: shimmer 3s infinite;
        pointer-events: none;
      }

      b {
        display: flex;
        align-items: center;

        &:before {
          content: '\f071';
          font-family: 'Font Awesome 5 Free';
          font-weight: 900;
          margin-right: 12px;
          font-size: 18px;
          color: #ff5252;
        }
      }
    }

    .btn-adicionar {
      width: 100%;
      background: linear-gradient(135deg, $cor-botao-dark 0%, darken($cor-botao-dark, 10%) 100%);
      color: $cor-botao-texto-dark;
      border: none;
      border-radius: 10px;
      padding: 16px;
      font-size: 1rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      i {
        margin-right: 10px;
        font-size: 18px;
      }

      &:hover {
        background: linear-gradient(135deg, lighten($cor-botao-dark, 3%) 0%, $cor-botao-dark 100%);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        transform: translateY(-2px);
      }

      &:active {
        transform: translateY(1px);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      }

      &:disabled {
        background: linear-gradient(135deg, #444 0%, #333 100%);
        color: #888;
        cursor: not-allowed;
        box-shadow: none;

        &:hover {
          background: linear-gradient(135deg, #444 0%, #333 100%);
          transform: none;
          box-shadow: none;
        }
      }
    }
  }

  // Estilos para a galeria de imagens
  .produto-galeria {
    margin-bottom: 20px;

    .miniaturas-container {
      padding-left: 2px;
      padding-right: 2px;

      .miniaturas {
        display: flex;
        flex-direction: column;
        align-items: center;

        .miniatura {
          width: 50px;
          height: 50px;
          object-fit: cover;
          margin-bottom: 8px;
          border-radius: 6px;
          border: 1px solid $cor-borda-dark;
          cursor: pointer;
          transition: all 0.2s ease;

          &.selecionada {
            border: 2px solid $cor-destaque;
            box-shadow: 0 0 8px rgba($cor-destaque, 0.5);
          }

          &:hover {
            border-color: $cor-destaque;
          }
        }
      }
    }

    .imagem-principal-container {
      .imagem-principal {
        text-align: center;

        .img-principal {
          max-height: 300px;
          max-width: 100%;
          object-fit: contain;
          border-radius: 8px;
          cursor: pointer;
        }
      }
    }
  }
}

// Estilos para as janelas modais
::ng-deep {
  kendo-window {
    .k-window {
      background-color: $cor-secundaria-dark !important;
      border-color: $cor-borda-dark !important;

      .k-window-titlebar {
        background-color: $cor-secundaria-dark !important;
        color: $cor-texto-dark !important;
        border-bottom-color: $cor-borda-dark !important;

        .k-window-actions .k-button {
          color: $cor-texto-dark !important;

          &:hover {
            color: $cor-destaque !important;
          }
        }
      }

      .k-window-content {
        background-color: $cor-secundaria-dark !important;
        color: $cor-texto-dark !important;
        padding: 15px !important;

        input.form-control {
          background-color: $cor-input-dark !important;
          border-color: $cor-input-borda-dark !important;
          color: $cor-texto-dark !important;

          &:focus {
            border-color: $cor-destaque !important;
            box-shadow: 0 0 0 0.2rem rgba($cor-destaque, 0.25) !important;
          }
        }

        .btn-success {
          background-color: $cor-destaque !important;
          border-color: $cor-destaque !important;

          &:hover {
            background-color: darken($cor-destaque, 5%) !important;
            border-color: darken($cor-destaque, 5%) !important;
          }
        }

        .btn-light {
          background-color: rgba(255, 255, 255, 0.1) !important;
          border-color: $cor-borda-dark !important;
          color: $cor-texto-dark !important;

          &:hover {
            background-color: rgba(255, 255, 255, 0.2) !important;
          }
        }
      }
    }
  }
}

// Estilos específicos para os controles numéricos
::ng-deep {
  .btn {
    background-color: $cor-secundaria-dark !important;
    border: 1px solid $cor-borda-dark !important;
    color: $cor-texto-dark !important;

    &:hover {
      background-color: rgba(230, 126, 34, 0.2) !important;
      border-color: $cor-destaque !important;
      color: $cor-destaque !important;
    }
  }

  .form-control {
    background-color: transparent !important;
    border: none !important;
    color: $cor-texto-dark !important;

    &:focus {
      box-shadow: none !important;
    }
  }

  // Melhorias para os inputs numéricos
  .input-group {
    background-color: $cor-secundaria-dark !important;
    border-radius: 8px !important;
    overflow: hidden !important;

    .input-group-prepend,
    .input-group-append {
      .btn {
        border-radius: 0 !important;
        width: 40px !important;
        height: 40px !important;
        padding: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
      }
    }
  }

  // Garantir que os checkboxes e radios funcionem corretamente
  input[type="radio"],
  input[type="checkbox"] {
    &:focus {
      box-shadow: 0 0 0 2px rgba($cor-destaque, 0.3) !important;
    }
  }
}

// Nova variação do tema dark
.tema-dark-container.tema-dark-elegante {
  --dark-bg-primary: #1a1b1f;
  --dark-bg-secondary: #23252d;
  --dark-text-primary: #ffffff;
  --dark-text-secondary: #b8b9bd;
  --dark-accent: #7e57c2; // Roxo elegante
  --dark-accent-hover: #9575cd;
  --dark-border: #2f3239;
  --dark-button: #7e57c2;
  --dark-button-hover: #9575cd;
  --dark-input-bg: #2a2c34;
  --dark-card-bg: #23252d;
  --dark-shadow: rgba(0, 0, 0, 0.2);
  --dark-overlay: rgba(0, 0, 0, 0.75);

  background-color: var(--dark-bg-primary);
  color: var(--dark-text-primary);

  .produto-header {
    background: var(--dark-bg-secondary);
    border-bottom: 1px solid var(--dark-border);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;

    h2 {
      color: var(--dark-text-primary);
    }

    .btn-fechar {
      color: var(--dark-text-secondary);
      &:hover {
        color: var(--dark-text-primary);
        background: var(--dark-accent);
      }
    }
  }

  .produto-content {
    background: var(--dark-bg-primary);
    position: absolute;
    top: 60px; /* Altura aproximada do header */
    left: 0;
    right: 0;
    bottom: 60px; /* Espaço para o footer */
  }

  .miniatura {
    border: 2px solid var(--dark-border);

    &.selecionada {
      border-color: var(--dark-accent);
    }

    &:hover {
      border-color: var(--dark-accent-hover);
    }
  }

  .produto-info {
    p {
      color: var(--dark-text-secondary);
    }
  }

  .produto-preco {
    .preco-valor {
      color: var(--dark-accent);
    }

    .preco-riscado {
      color: var(--dark-text-secondary);
    }
  }

  .detalhes-box, .importante-box {
    background: var(--dark-bg-secondary);
    border: 1px solid var(--dark-border);

    h4 {
      color: var(--dark-accent);
    }
  }

  .controle-quantidade {
    button {
      background: var(--dark-button);
      color: var(--dark-text-primary);

      &:hover {
        background: var(--dark-button-hover);
      }
    }

    .qtde {
      background: var(--dark-input-bg);
      color: var(--dark-text-primary);
    }
  }

  textarea {
    background: var(--dark-input-bg);
    border: 1px solid var(--dark-border);
    color: var(--dark-text-primary);

    &::placeholder {
      color: var(--dark-text-secondary);
    }
  }

  .produto-acoes-fixo {
    background: var(--dark-bg-secondary);
    border-top: 1px solid var(--dark-border);

    .btn-adicionar {
      background: var(--dark-accent);
      color: var(--dark-text-primary);

      &:hover {
        background: var(--dark-accent-hover);
      }
    }
  }
}
