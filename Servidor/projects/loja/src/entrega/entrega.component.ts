import {Component, OnInit, ViewChild} from '@angular/core';
import {PedidoLoja} from "../objeto/PedidoLoja";
import {NgForm} from "@angular/forms";
import {ClienteService} from "../services/cliente.service";
import {CarrinhoService} from "../services/carrinho.service";
import {Router} from "@angular/router";
import {Location} from "@angular/common";
import {Entrega} from "../objeto/Entrega";
import {ITela} from "../objeto/ITela";
import {DominiosService} from "../services/dominios.service";
import {ConstantsService} from "../services/ConstantsService";
import {GeoService} from "../../../../src/app/services/geo.service";
import {Endereco} from "../objeto/Endereco";
import {FormaDeEntrega} from "../objeto/FormaDeEntrega";

@Component({
  selector: 'app-entrega',
  templateUrl: './entrega.component.html',
  styleUrls: ['./entrega.component.scss']
})
export class EntregaComponent implements OnInit, ITela {
  @ViewChild('frm')  frm: NgForm;
  pedido: PedidoLoja;
  entrega: Entrega =  new Entrega();
  empresa: any = {};
  msgErro: any = '';
  nomePagina: string;
  formasDeEntrega: any = [];
  agendarEntrega: boolean;
  agendouEntrega: boolean;
  apenasAgendamento: any;
  taxaAntiga: number;
  escolherTipo = false;
  possuiLocalizacao = false;
  entregaPorZona = false;
  enderecoAntigo: any = null;
  formaDelivery: any = null;
  erroLocalizacao = false;
  msgErroLocalizacao = '';

  constructor(private clienteService: ClienteService, private carrinhoService: CarrinhoService,
              private router: Router, private location: Location, private constantsService: ConstantsService,
              private dominiosService: DominiosService, private geoService: GeoService) {
    this.nomePagina = dominiosService.obtenhaRaizCardapio();
    this.entrega = new Entrega();

    if ("geolocation" in navigator) {
      this.possuiLocalizacao = true;
    }
  }

  ngOnInit() {
    this.pedido = this.carrinhoService.obtenhaPedido();
    this.entrega = this.pedido.entrega;
    this.formasDeEntrega = [];
    this.taxaAntiga = this.entrega ? this.entrega.taxaDeEntrega : null
    this.enderecoAntigo = (this.entrega.endereco && (this.entrega.endereco.taxaDeEntrega !== undefined)) ? this.entrega.endereco : null

    if(this.entrega && this.entrega.endereco &&   this.entrega.endereco.taxaDeEntrega === undefined) {
      this.entrega.endereco = this.enderecoAntigo

      if(this.entrega.endereco)
        this.entrega.taxaDeEntrega = this.entrega.endereco.taxaDeEntrega
      else
        this.entrega.taxaDeEntrega = null
//      this.entrega = null
//      this.pedido.entrega = null
    }


    this.constantsService.empresa$.subscribe( (empresa) => {
       if(empresa) {
         this.empresa = empresa;

         this.apenasAgendamento = !this.empresa.estaRecebendoPedidos && this.empresa.permiteAgendamento
         if(this.apenasAgendamento) this.agendarEntrega = true
         this.empresa.apenasAgendamento = this.apenasAgendamento

         this.empresa.formasDeEntrega.forEach(forma => {
           if(forma.ativa) {
             if( forma.formaDeEntrega.nome === FormaDeEntrega.RECEBER_EM_CASA ) {
               this.formaDelivery = forma;
             }
             this.formasDeEntrega.push(Object.assign({}, forma))

             if(forma.permiteComerNoLocal) {
               let formaComerNoLocal = Object.assign({}, forma);

               formaComerNoLocal.comerNoLocal = true;
               formaComerNoLocal.nome = 'Comer no local';

               this.formasDeEntrega.push(formaComerNoLocal)
             }
           }

           if(!this.empresa.apenasAgendamento)
             if( this.pedido.entrega.formaDeEntrega === forma.formaDeEntrega.nome ) {
               this.agendarEntrega = forma.agendamentoObrigatorio;
               this.apenasAgendamento = forma.agendamentoObrigatorio;
             }
         });

         if(this.pedido.dataEntrega) {
           this.agendouEntrega = true
           this.agendarEntrega = true
         }
       }
    });

  }

  async onSubmit() {
    if( !this.frm.valid ) {
      return;
    }

    if(!this.agendarEntrega) {
      this.pedido.dataEntrega = null;
      this.pedido.horarioEntrega = null;
    }

    if(this.pedido.temValorMinimo(this.entrega.formaDeEntrega, this.formasDeEntrega)){
      if(!this.pedido.ultrapassouValorMaximo(this.entrega.formaDeEntrega, this.formasDeEntrega)){
        this.pedido.entrega = this.entrega;
        if(this.taxaAntiga && this.taxaAntiga !== this.pedido.entrega.taxaDeEntrega){ //editou valor
          this.pedido.setNovoPagamento()
        }

        this.pedido.calculeTotal();
        this.carrinhoService.salvePedido(this.pedido);

        let codigopromo = this.pedido.cupom ? this.pedido.cupom.codigo : null;

        if(codigopromo)
          await     this.carrinhoService.atualizeValorDesconto(codigopromo, this.empresa);

        if( this.entrega.ehDelivery() && !this.entrega.foiPreenchida() ) {
          this.router.navigate(['/' + this.nomePagina + '/criar-endereco'], {queryParamsHandling: 'merge'});
        }
        else {
          this.router.navigate(['/' + this.nomePagina + '/carrinho'], {queryParamsHandling: 'merge'});
          //this.location.back();
        }
      } else {
        this.exibaErroValorMaximo()
      }

    } else {
      this.exibaErroValorMinimo();
    }
  }

  exibaErroValorMinimo(){
    let valorMinimo = this.pedido.obtenhaValorMinimoParaEntrega(this.entrega.formaDeEntrega, this.formasDeEntrega);

    valorMinimo = ' R$' + valorMinimo.toFixed('2').replace('.', ',');

    if(this.entrega.ehRetirada()){
      this.msgErro = 'Para fazer retiradas , o valor mínimo do pedido é de ' + valorMinimo
    } else {
      this.msgErro = 'Para receber em casa,  o valor mínimo do pedido é de ' + valorMinimo
    }
  }

  exibaErroValorMaximo(){
    let valorMaximo = this.pedido.obtenhaValorMaximoParaEntrega(this.entrega.formaDeEntrega, this.formasDeEntrega)

    valorMaximo = ' R$' + valorMaximo.toFixed('2').replace('.', ',');

    if(this.entrega.ehRetirada()){
      this.msgErro = 'Para fazer retiradas , o valor máximo do pedido é de ' + valorMaximo
    } else {
      this.msgErro = 'Para receber em casa,  o valor máximo do pedido é de ' + valorMaximo
    }
  }

  obtenhaFormaDoPedido() {
    for (let i = 0; i < this.empresa.formasDeEntrega.length; i++) {
      const formaDeEntrega = this.empresa.formasDeEntrega[i];

      if (formaDeEntrega.nome === FormaDeEntrega.RECEBER_EM_CASA)
        return formaDeEntrega;
    }

    return {};
  }

  alterarEndereco() {
    /*
    if( !this.frm.valid ) {
      return;
    }*/
    if(this.pedido.temValorMinimoDaFormaEscolhida(this.formasDeEntrega)){
      if(!this.pedido.ultrapassouValorMaximoDaFormaEscolhida(this.formasDeEntrega)){
        this.pedido.entrega = this.entrega;
        this.carrinhoService.salvePedido(this.pedido);

        const formaReceberEmCasa = this.obtenhaFormaDoPedido();
        if (formaReceberEmCasa && formaReceberEmCasa.tipoDeCobranca === 'zona' || !formaReceberEmCasa.permiteUsarGps) {
          this.router.navigate(['/' + this.nomePagina + '/criar-endereco'], {queryParamsHandling: 'merge'});
        } else {
          this.escolherTipo = true;
        }
      } else {
        this.exibaErroValorMaximo();
      }
    } else {
      this.exibaErroValorMinimo();
    }
  }

  informarEndereco() {
    this.router.navigate(['/' + this.nomePagina + '/criar-endereco'], {queryParamsHandling: 'merge'});
  }

  deveExibirTopo() {
    return false;
  }

  deveExibirMenu(){
    return false;
  }

  alterouFormaDeEntrega(formaEntrega: any) {
    delete this.msgErro;
    if( this.entrega.ehRetirada() ) {
      this.entrega.taxaDeEntrega = 0;
    } else if( this.entrega.endereco ) {
      this.entrega.taxaDeEntrega = this.entrega.endereco.taxaDeEntrega;
    }

    if(!this.empresa.apenasAgendamento) {
      this.agendarEntrega = formaEntrega.agendamentoObrigatorio;
      this.apenasAgendamento = formaEntrega.agendamentoObrigatorio;
    }
  }

  pegueLocalizacao() {
    navigator.geolocation.getCurrentPosition((posicao) => {
      const latlng = {
        lat: posicao.coords.latitude,
        lng: posicao.coords.longitude,
      };

      this.geoService.obtenhaEndereco(latlng).then((respEndereco) => {
        const endereco = Endereco.novo();
        Object.assign(endereco, respEndereco);

        this.router.navigateByUrl('/' + this.nomePagina +
          `/criar-endereco?endereco=1&gps=1&lat=${latlng.lat}&lng=${latlng.lng}`).then((result) => {
        });
      });
    }, (erro) => {
      this.msgErroLocalizacao = 'Você precisa ativar a geolocalização para poder utilizar essa funcionalidade. Motivo: ' +
        erro.message;
      this.erroLocalizacao = true;
    }, {enableHighAccuracy: true});
  }

  deveTerBordas() {
    return true;
  }

  deveExibirBannerTema() {
    return false;
  }
}
