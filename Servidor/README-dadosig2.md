# Rota dadosig2 - Extração de Dados do Instagram com IA

## Descrição

A rota `POST /api/leads/dadosig2` foi criada para extrair dados de leads a partir de texto bruto copiado de perfis do Instagram, utilizando inteligência artificial (ChatGPT) para processar e estruturar as informações.

## Endpoint

```
POST /api/leads/dadosig2
```

## Parâmetros de Entrada

```json
{
  "texto": "string (obrigatório) - Texto bruto copiado do perfil do Instagram",
  "crmEmpresaId": "number (opcional) - ID da empresa CRM para vincular o lead"
}
```

## Exemplo de Uso

### Texto de Entrada (exemplo do Torii Sushi):
```
toriisushi_62
Seguir
Enviar mensagem
83 publicações
3.186 seguidores
1.258 seguindo
Torii Sushi Goiânia
Restaurante japonês
Atendimento presencial, Delivery & Retirada
(62)99305-7090
Atendemos de TER a DOM: 18h-23h
Localização 👇🏼
Av. <PERSON>, QD. 09 - Lt.33 - <PERSON><PERSON> das Amendoeiras, Goiânia, Brazil 74780-500
wa.me/message/VJHRWYF5NLXPF1
```

### Requisição:
```bash
curl -X POST http://localhost:3000/api/leads/dadosig2 \
  -H "Content-Type: application/json" \
  -d '{
    "texto": "toriisushi_62\nSeguir\nEnviar mensagem\n83 publicações\n3.186 seguidores\n1.258 seguindo\nTorii Sushi Goiânia\nRestaurante japonês\nAtendimento presencial, Delivery & Retirada\n(62)99305-7090\nAtendemos de TER a DOM: 18h-23h\nLocalização 👇🏼\nAv. Paulo Alves da Costa, QD. 09 - Lt.33 - Parque das Amendoeiras, Goiânia, Brazil 74780-500\nwa.me/message/VJHRWYF5NLXPF1"
  }'
```

## Resposta Esperada

A rota retorna um objeto Lead completo com CrmEmpresa vinculada:

```json
{
  "sucesso": true,
  "dados": {
    "id": null,
    "crmEmpresaId": 123,
    "nomeResponsavel": "Torii Sushi Goiânia",
    "empresa": "Torii Sushi Goiânia",
    "telefone": "***********",
    "instagramHandle": "toriisushi_62",
    "linkInsta": null,
    "bioInsta": "Restaurante japonês\nAtendimento presencial, Delivery & Retirada",
    "etapa": "Prospecção",
    "score": 0,
    "origem": "Instagram",
    "segmento": null,
    "dataCriacao": "2025-01-27T10:30:00.000Z",
    "dataUltimaInteracao": null,
    "dataProximoFollowup": null,
    "dataFechamento": null,
    "valorPotencial": null,
    "vendedorId": null,
    "motivoPerda": null,
    "instagramData": {
      "bio": "Restaurante japonês\nAtendimento presencial, Delivery & Retirada",
      "followers": 3186,
      "following": 1258,
      "accountType": "Business",
      "businessCategory": "Restaurante japonês",
      "location": "Av. Paulo Alves da Costa, QD. 09 - Lt.33 - Parque das Amendoeiras, Goiânia, Brazil 74780-500",
      "website": null
    },
    "avatarUrl": null,
    "rapport": null,
    "relatorioIaJson": null,
    "notas": "Horário: TER a DOM: 18h-23h\nServiços: Atendimento presencial, Delivery & Retirada\nWhatsApp: wa.me/message/VJHRWYF5NLXPF1\nPublicações: 83",
    "createdAt": null,
    "updatedAt": null,
    "crmEmpresa": {
      "id": 123,
      "nome": "Torii Sushi Goiânia",
      "cnpj": null,
      "telefone": "***********",
      "email": null,
      "endereco": "Av. Paulo Alves da Costa, QD. 09 - Lt.33 - Parque das Amendoeiras, Goiânia, Brazil 74780-500",
      "ativa": true,
      "createdAt": "2025-01-27T10:30:00.000Z",
      "updatedAt": "2025-01-27T10:30:00.000Z"
    }
  }
}
```

## Estrutura de Dados Extraídos

A IA extrai e estrutura automaticamente os dados em dois objetos principais:

### CrmEmpresa
- **nome**: Nome da empresa extraído do perfil
- **telefone**: Telefone da empresa (apenas números)
- **endereco**: Endereço completo da empresa
- **email**: Email (se disponível)
- **cnpj**: CNPJ (se disponível)
- **ativa**: Status da empresa (sempre true)

### Lead
- **nomeResponsavel**: Nome do responsável ou da empresa
- **empresa**: Nome da empresa
- **telefone**: Telefone de contato
- **instagramHandle**: Username do Instagram (sem @)
- **bioInsta**: Biografia do perfil
- **origem**: Sempre "Instagram"
- **etapa**: Sempre "Prospecção"
- **score**: Sempre 0 (inicial)
- **instagramData**: Dados estruturados do Instagram
  - **bio**: Biografia
  - **followers**: Número de seguidores
  - **following**: Número seguindo
  - **accountType**: Tipo de conta (Business)
  - **businessCategory**: Categoria do negócio
  - **location**: Localização
  - **website**: Website (se houver)
- **linkInsta**: Link externo da bio
- **notas**: Informações extras (horário, serviços, WhatsApp, etc.)

## Funcionalidades

1. **Extração Inteligente**: Utiliza ChatGPT para extrair e estruturar dados do texto bruto
2. **Criação Automática de Empresa**: Cria automaticamente um objeto CrmEmpresa se não existir
3. **Objeto Lead Completo**: Retorna um objeto Lead totalmente estruturado e pronto para uso
4. **Vinculação Automática**: Vincula automaticamente o Lead à CrmEmpresa criada/encontrada
5. **Dados Estruturados**: Organiza todos os dados nos formatos corretos do domínio
6. **Informações Complementares**: Consolida informações extras nas notas do lead

## Arquivos de Teste

- `test-dadosig2.js`: Teste usando Node.js
- `test-dadosig2.sh`: Teste usando curl

Execute os testes para verificar se a rota está funcionando corretamente.

## Dependências

- ChatGPTService configurado com API key válida
- Banco de dados com tabelas de Lead e CrmEmpresa
- Servidor rodando na porta 3000 (ou ajustar nos testes)
