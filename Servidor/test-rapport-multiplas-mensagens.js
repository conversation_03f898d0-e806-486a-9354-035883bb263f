const axios = require('axios');

async function testarRapportMultiplasMensagens() {
  try {
    console.log('Testando geração de múltiplas mensagens de rapport...\n');
    
    const response = await axios.post('http://localhost:3030/api/whatsapp/gerar-rapport', {
      telefone: '551199999999',
      nomeContato: 'João',
      empresa: 'Pizzaria do João',
      tipoAbordagem: 'consultiva',
      produto: 'Meu Cardápio AI'
    });
    
    console.log('Status:', response.status);
    console.log('Resposta completa:', JSON.stringify(response.data, null, 2));
    
    if (response.data.sucesso && response.data.data) {
      const data = response.data.data;
      
      if (data.sugestoes && Array.isArray(data.sugestoes)) {
        console.log(`\n✅ Sucesso! Foram geradas ${data.sugestoes.length} sugestões:\n`);
        
        data.sugestoes.forEach((sug, index) => {
          console.log(`--- Sugestão ${index + 1} (Confiança: ${(sug.confianca * 100).toFixed(0)}%) ---`);
          console.log(sug.texto);
          console.log('');
        });
        
        if (data.observacoes) {
          console.log('Observações:', data.observacoes);
        }
      } else {
        console.log('\n❌ Erro: Resposta não contém array de sugestões');
        console.log('Estrutura recebida:', data);
      }
    }
  } catch (error) {
    console.error('Erro ao testar:', error.response?.data || error.message);
  }
}

// Executar teste
testarRapportMultiplasMensagens();