//teste
var cep = document.getElementById('cep');
var txtTelefone = document.getElementById('telefone');
var logradouro = document.getElementById('logradouro');
var servidor = document.querySelector('#servidor');
var cboEstado = document.getElementById('estado');
var cboCidade = document.getElementById('cidade');
var dominio = document.getElementById('dominio');
var numero = document.getElementById('numero');
var btnSolicitar = document.getElementById('btnSolicitar');
var modal = document.getElementById("myModal");
var btnValidarCodigo = document.querySelector("#btnValidarCodigo");
var divSolicitarCodigo = document.querySelector('#divSolicitarCodigo');
var spanTelefone = document.querySelector('#spanTelefone');

var estados = null;
var cidades = null;

function unmask(val) {
  return val ? val.replace(/\D+/g, '') : val;
}

function preenchaCep() {
  var numeroCep = unmask(cep.value);

  if( numeroCep.length < 8 ) {
    return;
  }

  bairro.disabled = false;
  logradouro.disabled = false;
  cboCidade.disabled = false;
  cboEstado.disabled = false;


  //document.getElementById('search').;
  document.getElementById('search').style.display = "none";
  document.getElementById('carregando').style.display = "block";

  busqueCep(numeroCep, function(dados) {
    var estadoEncontrado = null;
    var cidadeEncontrada = null;

    if( !dados.sucesso ) {
      document.getElementById('carregando').style.display = "none";
      document.getElementById('search').style.display = "block";
      return;
    }

    for( let i = 0; i < estados.data.estados.length; i++ ) {
      var estado = estados.data.estados[i];

      if( estado.sigla === dados.data.estado ) {
        estadoEncontrado = estado;
        break;
      }
    }

    cboEstado.value = estadoEncontrado.id;

    ajax('/api/cidades/' + estadoEncontrado.id + '?__amp_source_origin=1', function(respCidades) {
      cidades = dados;

      adicioneCidades(respCidades.data);
      for( let i = 0; i < respCidades.data.length; i++ ) {
        var cidade = respCidades.data[i];

        if( cidade.nome === dados.data.cidade ) {
          cidadeEncontrada = cidade;
          break;
        }
      }

      cidadeEncontrada.selecionado = true;
      cboCidade.value = cidadeEncontrada.id;
      document.getElementById('numero').focus();
      document.getElementById('search').style.display = "block";
      document.getElementById('carregando').style.display = "none";
    });

    bairro.value = dados.data.bairro;
    logradouro.value = dados.data.logradouro;

    bairro.disabled = true;
    logradouro.disabled = true;
    cboCidade.disabled = true;
    cboEstado.disabled = true;
  });
}

btnSolicitar.addEventListener('click', (event) => {
  var $form = document.getElementById("frm");

  for (var i = 0; i < $form.elements.length; i++) {
    var $elemento = $form.elements[i];

    $elemento.setCustomValidity('');

    if (!$elemento.checkValidity()) {
      var $span = $elemento.parentElement.getElementsByClassName('msg-erro')[0];

      $span.innerText = $elemento.validationMessage;

      $elemento.focus();
      break;
    }
  }

  $form.classList.add('was-validated');

  if (!$form.checkValidity()) {
    return false;
  }

  return true;
});

btnValidarCodigo.addEventListener("click", function(e) {
  btnValidarCodigo.disabled = true;

  var code = document.getElementById('codigo_sms').value;

  window.confirmationResult.confirm(code).then(function (result) {
    // User signed in successfully.
    var user = result.user;
    // ...

    var codigo = window.urlRedirect.replace('/novo/empresa/', '');
    facaPostTelefoneValidado(codigo, () => {
      window.location.href = '/lp/cardapio/obrigado1';
    });
  }).catch(function (error) {
    btnValidarCodigo.disabled = false;
    // User couldn't sign in (bad verification code?)
    // ...
    alert('Código inválido');
  });
});

function valideTelefone(phoneNumber) {
  spanTelefone.innerText = phoneNumber;
  modal.style.display = "block";

  var appVerifier = window.recaptchaVerifier;
  firebase.auth().signInWithPhoneNumber('+55' + phoneNumber, appVerifier)
    .then(function (confirmationResult) {
      // SMS sent. Prompt user to type the code from the message, then sign the
      // user in with confirmationResult.confirm(code).
      divSolicitarCodigo.style.display = 'block';
      divRecaptcha.style.display = 'none';
      window.confirmationResult = confirmationResult;
    }).catch(function (error) {
    alert(error);
  });
}

document.querySelector("#frm").addEventListener("submit", function(e){
  e.preventDefault();

  var $form = document.getElementById("frm");

  for (var i = 0; i < $form.elements.length; i++) {
    var $elemento = $form.elements[i];

    $elemento.setCustomValidity('');

    if( !$elemento.checkValidity() ) {
      var $span = $elemento.parentElement.getElementsByClassName('msg-erro')[0];

      $span.innerText = $elemento.validationMessage;
      break;
    }
  }

  $form.classList.add('was-validated');

  if (!$form.checkValidity()) {
    return false;
  }

  var telefone = txtTelefone.value;

  //this.$loading.style.display = 'block';
  var $alert = document.getElementById('alert-server');
  btnSolicitar.classList.add('loading');
  $alert.style.display = 'none';
  btnSolicitar.disabled = true;

  envieForm( (dados) => {
    //this.$loading.style.display = 'none';
    btnSolicitar.classList.remove('loading');
    btnSolicitar.disabled = false;


    if( dados.sucesso ) {
      window.enviouForm = true;
      window.location.href = '/lp/cardapio/obrigado1';
    } else {
      btnSolicitar.disabled = false;
      var $input = document.getElementById(dados.dados.name);

      if( $input ) {
        $input.setCustomValidity(dados.dados.message);
        $input.focus();
        var $span = $input.parentElement.getElementsByClassName('msg-erro')[0];

        $span.innerText = $input.validationMessage;
      } else {
        $alert.style.display = 'block';
        var $span = $alert.getElementsByTagName('span')[0];
        $span.innerText = dados.dados.message;
      }
    }
  });

  return false;
});

function busqueCep(cep, cb) {
  var url = '/api/endereco/' + cep;

  ajax(url, cb);
}

function busqueEstados() {

}

function ajax(url, cb) {
  var r = new XMLHttpRequest();

  url = 'https://' + servidor.defaultValue + url;

  r.open("GET", url, true);

  r.onreadystatechange = function () {
    if (r.readyState != 4 || r.status != 200) return;

    cb(JSON.parse(r.responseText));
  };

  r.send();
}


function envieForm(cb) {
  var $form = document.getElementById("frm");

  var data = {};

  const formData = new FormData($form);

  console.log( 'Sending data' );

  const XHR = new XMLHttpRequest();

  // Define what happens on successful data submission
  XHR.addEventListener( 'load', function(event) {
    var sucesso = event.currentTarget.status === 200;

    var dados = event.currentTarget.responseText;

    try {
      dados = JSON.parse(event.currentTarget.response);
    } catch(erro) {}

    cb({
      sucesso: sucesso,
      dados: dados
    });
  } );

  // Define what happens in case of error
  XHR.addEventListener( 'error', function(event) {
    if( event.currentTarget.status === 0 ) {
      return cb({
        sucesso: false,
        dados: "Verifique sua conexão com internet e tente novamente mais tarde."
      });
    }

    cb({
      sucesso: false,
      dados: event.currentTarget.response
    })
  } );

  // Set up our request
  XHR.open( 'POST', '/prospects' );

  // Add the required HTTP header for form data POST requests

  // Finally, send our data.
  XHR.send( formData );
}

function facaPostTelefoneValidado(codigo, cb) {
  var data = {};

  var XHR = new XMLHttpRequest();

  // Define what happens on successful data submission
  XHR.addEventListener( 'load', function(event) {
    var sucesso = event.currentTarget.status === 200;

    var dados = event.currentTarget.responseText;

    try {
      dados = JSON.parse(event.currentTarget.response);
    } catch(erro) {}

    cb({
      sucesso: sucesso,
      dados: dados
    });
  } );

  // Define what happens in case of error
  XHR.addEventListener( 'error', function(event) {
    if( event.currentTarget.status === 0 ) {
      return cb({
        sucesso: false,
        dados: "Verifique sua conexão com internet e tente novamente mais tarde."
      });
    }

    cb({
      sucesso: false,
      dados: event.currentTarget.response
    })
  } );

  XHR.open("POST", "/prospects/telefone/validado", true);
  XHR.setRequestHeader('Content-Type', 'application/json');

  XHR.send(JSON.stringify({
    codigo: codigo
  }));
}

