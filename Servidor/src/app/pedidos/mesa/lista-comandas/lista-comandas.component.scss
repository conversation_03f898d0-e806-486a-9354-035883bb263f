// Variáveis
$color-primary: #2563eb;
$color-success: #10b981;
$color-warning: #f59e0b;
$color-danger: #ef4444;
$color-gray: #6b7280;
$color-light: #f3f4f6;
$color-dark: #1f2937;

.modal-comandas {
  display: flex;
  flex-direction: column;
  height: 100%;

  .comandas-list {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;

    .sem-comandas {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.5rem;
      padding: 2rem;
      color: #6c757d;

      i {
        font-size: 2rem;
      }
    }

    .comanda-item {
      background: white;
      border: 1px solid rgba(0,0,0,0.08);
      border-radius: 8px;
      padding: 1rem;
      margin-bottom: 0.75rem;
      cursor: pointer;
      transition: all 0.2s ease;

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        border-color: #007bff;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
      }

      .comanda-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;

        .comanda-codigo {
          font-size: 1rem;
          font-weight: 500;
          color: #2c5282;
        }

        .comanda-valor {
          font-size: 1rem;
          font-weight: 600;
          color: #007bff;
        }
      }

      .comanda-detalhes {
        .comanda-hora {
          font-size: 0.813rem;
          color: #6c757d;
          display: flex;
          align-items: center;
          gap: 0.35rem;

          i {
            font-size: 0.875rem;
          }
        }
      }
    }
  }

  .acoes-comanda {
    display: flex;
    gap: 8px;
    padding: 12px;

    .btn-acao {
      flex: 1;
      padding: 8px 16px;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      transition: all 0.2s ease;
      border: 1px solid transparent;

      i {
        font-size: 14px;
      }

      &.btn-outline {
        background-color: transparent;
        border: 1px solid #e2e8f0;
        color: #475569;

        &:hover {
          background-color: #f8fafc;
          border-color: #cbd5e1;
        }
      }

      &.btn-primary {
        background-color: #2563eb;
        color: white;

        &:hover {
          background-color: #1d4ed8;
        }
      }
    }
  }

  .nova-comanda {
    margin-top: 16px;
    padding: 16px;
    border-top: 1px solid #e2e8f0;

    .btn-nova-comanda {
      width: 100%;
      padding: 12px;
      border: none;
      border-radius: 8px;
      background-color: #4f46e5;
      color: white;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      transition: all 0.2s ease;

      &:hover {
        background-color: #4338ca;
      }

      i {
        font-size: 16px;
      }
    }
  }
}

.modal-lateral {
  position: fixed;
  top: 60px;
  right: 0;
  bottom: 0;
  width: 420px;
  background: white;
  box-shadow: -4px 0 16px rgba(0, 0, 0, 0.1);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s ease-out;

  @media (max-width: 600px) {
    width: 100%;
  }

  .modal-header {
    padding: 1rem;
    background: white;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    justify-content: space-between;

    .header-content {
      .modal-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: $color-dark;
        margin: 0;
        margin-bottom: 0.25rem;
      }

      .subtitle {
        font-size: 0.875rem;
        color: $color-gray;
      }
    }

    .btn-fechar {
      background: none;
      border: none;
      width: 32px;
      height: 32px;
      border-radius: 6px;
      color: $color-gray;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: $color-light;
        color: $color-dark;
      }
    }
  }

  .modal-body {
    flex: 1;
    overflow-y: auto;
    padding: 0.75rem;
    background: #f8fafc;

    .comandas-list {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;

      .sem-comandas {
        .empty-state {
          text-align: center;
          padding: 2rem 1rem;
          color: $color-gray;

          i {
            font-size: 2.5rem;
            margin-bottom: 0.75rem;
            color: #cbd5e1;
          }

          h3 {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.35rem;
            color: $color-dark;
          }

          p {
            font-size: 0.875rem;
            color: $color-gray;
            margin: 0;
          }
        }
      }

      .comanda-item {
        .comanda-card {
          background: white;
          border: 1px solid rgba(0, 0, 0, 0.05);
          border-radius: 16px;
          overflow: hidden;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
          position: relative;

          &:hover {
            border-color: $color-primary;
            background: linear-gradient(to bottom right, rgba(37, 99, 235, 0.02), rgba(37, 99, 235, 0.05));

            .card-header .comanda-valor {
              color: $color-primary;
            }

            .btn-outline-secondary {
              border-color: $color-primary;
              color: $color-primary;
            }
          }

          &:active {
            transform: scale(0.995);
          }

          .card-header {
            padding: 1rem;
            background: none;
            border-bottom: none;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;

            .comanda-identificacao {
              display: flex;
              flex-direction: column;
              gap: 0.35rem;

              .comanda-numero {
                font-size: 1.125rem;
                font-weight: 600;
                color: $color-dark;
                letter-spacing: -0.01em;
              }

              .comanda-status {
                display: inline-flex;
                align-items: center;
                gap: 0.35rem;
                font-size: 0.75rem;
                padding: 0.25rem 0.5rem;
                border-radius: 100px;
                background: rgba(22, 163, 74, 0.1);
                color: #16a34a;
                font-weight: 500;

                i {
                  font-size: 0.5rem;
                }
              }
            }

            .comanda-valor {
              font-size: 1.25rem;
              font-weight: 600;
              color: #16a34a;
              transition: color 0.2s;
            }
          }

          .card-body {
            padding: 0 1rem 1rem;

            .info-row {
              display: flex;
              flex-wrap: wrap;
              gap: 1rem;
              margin-bottom: 0.75rem;

              .info-item {
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 0.875rem;
                color: $color-gray;
                padding: 0.35rem 0.75rem;
                background: #f8fafc;
                border-radius: 8px;

                i {
                  color: #64748b;
                }

                .warning-expired {
                  display: inline-flex;
                  align-items: center;
                  gap: 0.25rem;
                  margin-left: 0.5rem;
                  padding: 0.25rem 0.75rem;
                  border-radius: 6px;
                  background: #fef2f2;
                  color: #dc2626;
                  font-size: 0.75rem;
                  font-weight: 500;
                  border: 1px solid rgba(220, 38, 38, 0.1);

                  i {
                    color: #dc2626;
                    font-size: 0.75rem;
                  }
                }
              }
            }

            .pedidos-indicator {
              margin-top: 0.75rem;
              padding-top: 0.75rem;
              border-top: 1px dashed rgba(0, 0, 0, 0.06);

              .pedidos-count {
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 0.875rem;
                color: $color-primary;
                padding: 0.35rem 0.75rem;
                background: rgba(37, 99, 235, 0.05);
                border-radius: 8px;
                font-weight: 500;

                i {
                  color: $color-primary;
                  font-size: 0.875rem;
                }
              }
            }
          }

          .card-footer {
            padding: 1rem;
            background: none;
            border-top: 1px solid rgba(0, 0, 0, 0.05);

            .gap-2 {
              gap: 0.75rem;
            }

            .btn {
              padding: 0.5rem 1rem;
              font-size: 0.875rem;
              font-weight: 500;
              border-radius: 8px;
              transition: all 0.2s;

              &.btn-outline-secondary {
                border: 1px solid #e2e8f0;
                background: none;
                color: $color-gray;

                &:hover {
                  background: rgba(37, 99, 235, 0.05);
                }
              }

              &.btn-primary {
                background: $color-primary;
                border: none;

                &:hover {
                  background: darken($color-primary, 5%);
                }
              }

              i {
                font-size: 0.875rem;
                margin-right: 0.5rem;
              }
            }
          }
        }
      }
    }

    .nova-comanda {
      margin-top: 0.75rem;
      padding-top: 0.75rem;
      border-top: 1px solid rgba(0, 0, 0, 0.08);

      .btn-nova-comanda {
        width: 100%;
        padding: 0.75rem;
        border: none;
        border-radius: 8px;
        background: $color-primary;
        color: white;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        transition: all 0.2s;
        cursor: pointer;

        &:hover {
          background: darken($color-primary, 5%);
        }

        &:active {
          transform: scale(0.98);
        }

        i {
          font-size: 1rem;
        }
      }
    }
  }
}

.modal-overlay {
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(2px);
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
