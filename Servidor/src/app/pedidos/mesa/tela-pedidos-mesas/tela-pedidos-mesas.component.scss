.mobile-tabs {
  .nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
  }

  .nav-pills-mobile {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    gap: 0.5rem;
    padding: 5px;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    &::-webkit-scrollbar {
      display: none; /* Chrome/Safari/Opera */
    }

    .nav-pill {
      display: flex;
      align-items: center;
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 20px;
      background: white;
      color: #6c757d;
      white-space: nowrap;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: all 0.2s ease;

      i {
        margin-right: 0.5rem;
        font-size: 1rem;
      }

      &.active {
        background: #007bff;
        color: white;
        box-shadow: 0 2px 8px rgba(0,123,255,0.4);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .tab-content {
    background: white;
    min-height: calc(100vh - 200px);
  }
}

.btn-configuracao {
  margin-left: 1rem;
  flex-shrink: 0;

  ::ng-deep .k-button.k-button-md.k-button-solid.k-button-solid-base {
    background: linear-gradient(to bottom, #0d6efd, #0b5ed7);
    color: white;
    border: none;
    border-radius: 20px;
    padding: 0.5rem 1rem;
    height: 38px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
    min-width: 200px;

    &:hover {
      background: linear-gradient(to bottom, #0b5ed7, #0a58ca);
      box-shadow: 0 2px 8px rgba(0,123,255,0.4);
    }

    &:active {
      transform: scale(0.98);
    }

    .k-icon {
      margin-right: 0.5rem;
    }

    .k-button-text {
      font-weight: 500;
    }
  }

  ::ng-deep .k-popup {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    min-width: 200px;
    width: 100%;

    .k-list {
      padding: 4px;
    }

    .k-list-item {
      padding: 8px 16px;
      transition: all 0.2s ease;
      border-radius: 4px;

      &:hover {
        background-color: #f8f9fa;
      }

      .k-icon {
        margin-right: 8px;
        color: #6c757d;
      }
    }
  }
}

// Ajustes responsivos
@media (max-width: 576px) {
  .mobile-tabs {
    .nav-container {
      flex-direction: column;
      gap: 0.5rem;
    }

    .btn-configuracao {
      margin-left: 0;
      width: 100%;

      ::ng-deep .k-button {
        width: 100%;
        justify-content: center;
      }
    }

    .nav-pills-mobile {
      width: 100%;

      .nav-pill {
        padding: 0.5rem 0.75rem;

        i {
          margin-right: 0.35rem;
        }
      }
    }
  }

  .card-body {
    padding: 0;
  }

  .page-title {
    padding: 1rem;
    margin: 0;
    font-size: 1.25rem;
  }
}

.mesas-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(285px, 1fr));
  gap: 20px;
  padding: 20px;
}

.mesa-card {
  .card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    background-color: #ffffff;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
    }
  }

  .card-header {
    padding: 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;

    .mesa-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .mesa-numero {
      margin: 0;
      font-size: 1.3rem;
      font-weight: 600;
      color: #333;
    }

    .mesa-status {
      display: flex;
      gap: 8px;

      .status-badge {
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;

        &.nao-gera-comanda {
          background-color: #ff9800;
          color: white;
        }
      }
    }
  }

  .card-body {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .mesa-disponivel {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: 20px 0;

      .disponivel-icon {
        font-size: 3rem;
        color: #4caf50;
        margin-bottom: 15px;
      }

      .disponivel-texto {
        color: #555;
        font-size: 1rem;
        margin: 0;
      }
    }

    .mesa-ocupada {
      .info-principal {
        margin-bottom: 15px;
      }

      .valor-total {
        display: flex;
        flex-direction: column;
        margin-bottom: 10px;

        .label {
          font-size: 0.8rem;
          color: #757575;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          margin-bottom: 5px;
        }

        .valor {
          font-size: 1.8rem;
          font-weight: 700;
          color: #2ecc71;
        }
      }

      .info-secundaria {
        display: flex;
        justify-content: space-between;

        .tempo, .comandas-count {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 0.9rem;
          color: #757575;

          i {
            color: #9e9e9e;
          }
        }
      }
    }

    .acoes-mesa {
      display: flex;
      gap: 10px;
      margin-top: auto;
      flex-wrap: wrap;

      .botoes-container {
        display: flex;
        gap: 10px;
        width: 100%;

        button {
          flex: 1;
          min-width: 120px;
          transform: translateY(0);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-3px);
          }

          &:active {
            transform: translateY(0);
          }
        }

        @media (max-width: 576px) {
          flex-direction: column;
        }
      }

      button {
        flex: 1;
        min-height: 44px;
        padding: 10px 15px;
        border-radius: 8px;
        border: none;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;

        i {
          margin-right: 8px;
          font-size: 1.1rem;
        }

        &:active {
          transform: scale(0.98);
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
        }
      }

      .btn-selecionar {
        background: linear-gradient(to bottom, #007bff, #0069d9);
        color: white;
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.4);

        &:hover {
          background: linear-gradient(to bottom, #0069d9, #0062cc);
          box-shadow: 0 4px 8px rgba(0, 123, 255, 0.5);
        }

        i {
          color: #f8f9fa;
        }
      }

      .btn-detalhes {
        background: linear-gradient(to bottom, #6c757d, #5a6268);
        color: white;
        box-shadow: 0 2px 4px rgba(108, 117, 125, 0.4);

        &:hover {
          background: linear-gradient(to bottom, #5a6268, #495057);
          box-shadow: 0 4px 8px rgba(108, 117, 125, 0.5);
        }

        i {
          color: #f8f9fa;
        }
      }

      .btn-novo-pedido {
        background: linear-gradient(to bottom, #28a745, #218838);
        color: white;
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.4);

        &:hover {
          background: linear-gradient(to bottom, #218838, #1e7e34);
          box-shadow: 0 4px 8px rgba(40, 167, 69, 0.5);
        }

        i {
          color: #f8f9fa;
        }
      }
    }
  }
}

// Modernização do alerta quando não há mesas
.alert-warning {
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #fff3cd 0%, #ffecb5 100%);
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.15);

  h4 {
    color: #856404;
    display: flex;
    align-items: center;
    gap: 10px;

    i {
      font-size: 1.5rem;
    }
  }

  .btn-primary {
    background: #2ecc71;
    border: none;
    border-radius: 8px;
    padding: 10px 16px;
    font-weight: 600;
    box-shadow: 0 4px 8px rgba(46, 204, 113, 0.25);
    transition: all 0.2s ease;

    &:hover {
      background: #27ae60;
      box-shadow: 0 6px 12px rgba(46, 204, 113, 0.3);
    }

    &:active {
      transform: translateY(2px);
      box-shadow: 0 2px 4px rgba(46, 204, 113, 0.25);
    }

    i {
      margin-right: 8px;
    }
  }
}

// Modernização da navegação
.nav-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  margin: 16px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.nav-pills-mobile {
  .nav-pill {
    border-radius: 30px;
    padding: 10px 16px;
    font-weight: 600;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);

    &.active {
      background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
      box-shadow: 0 4px 8px rgba(0, 123, 255, 0.25);
    }

    i {
      margin-right: 8px;
    }
  }
}

.btn-configuracao {
  ::ng-deep .k-button {
    border-radius: 30px !important;
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.25) !important;

    &:hover {
      background: linear-gradient(135deg, #5a6268 0%, #3d4246 100%) !important;
      box-shadow: 0 6px 12px rgba(108, 117, 125, 0.3) !important;
    }
  }
}

// Ajustes responsivos
@media (max-width: 768px) {
  .mesas-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    padding: 15px;
    gap: 15px;
  }
}

@media (max-width: 576px) {
  .mesas-grid {
    grid-template-columns: 1fr;
  }
}

// Adicione estes estilos para a barra de ações
.acoes-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;

  .search-container {
    flex: 1;
    max-width: 500px;

    .busca {
      width: 100%;

      ::ng-deep .k-textbox {
        border-radius: 30px;
        padding: 8px 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        border: 1px solid #e9ecef;

        &:focus {
          border-color: #80bdff;
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
      }
    }
  }

  .btn-abrir-mesa {
    background: #2ecc71;
    color: white;
    border: none;
    border-radius: 30px;
    padding: 10px 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 8px rgba(46, 204, 113, 0.25);
    transition: all 0.2s ease;

    i {
      font-size: 1.2rem;
    }

    &:hover {
      background: #27ae60;
      box-shadow: 0 6px 12px rgba(46, 204, 113, 0.3);
      transform: translateY(-2px);
    }

    &:active {
      transform: translateY(1px);
      box-shadow: 0 2px 4px rgba(46, 204, 113, 0.25);
    }
  }
}

// Ajustes responsivos para a barra de ações
@media (max-width: 768px) {
  .acoes-container {
    flex-direction: column;
    gap: 12px;

    .search-container {
      width: 100%;
      max-width: 100%;
    }

    .btn-abrir-mesa {
      width: 100%;
      justify-content: center;
    }
  }
}

.comandas-count {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.95rem;
  font-weight: 600;
  color: #333;
  background-color: #f1f8ff;
  padding: 6px 10px;
  border-radius: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  i {
    color: #007bff;
    font-size: 1rem;
  }

  span {
    color: #007bff;
  }
}

// Estilo para o alerta de busca sem resultados
.alert-info {
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.15);

  h4 {
    color: #0c5460;
    display: flex;
    align-items: center;
    gap: 10px;

    i {
      font-size: 1.5rem;
    }
  }

  .btn-outline-primary {
    color: #17a2b8;
    border-color: #17a2b8;
    border-radius: 8px;
    padding: 10px 16px;
    font-weight: 600;
    transition: all 0.2s ease;

    &:hover {
      background-color: #17a2b8;
      color: white;
    }

    &:active {
      transform: translateY(2px);
    }

    i {
      margin-right: 8px;
    }
  }
}

// Estilo aprimorado para mensagem de busca sem resultados
.empty-search-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 60px 20px;
  grid-column: 1 / -1;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
  border-radius: 20px;
}

.empty-search-content {
  max-width: 520px;
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 24px;
  padding: 50px 40px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(0, 0, 0, 0.03),
    inset 0 0 0 1px rgba(255, 255, 255, 0.5);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    z-index: 0;
  }

  .empty-search-icon {
    width: 110px;
    height: 110px;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 30px;
    position: relative;
    box-shadow:
      0 10px 20px rgba(33, 150, 243, 0.15),
      inset 0 -5px 10px rgba(0, 0, 0, 0.05);

    &::after {
      content: '';
      position: absolute;
      top: 10%;
      left: 10%;
      width: 80%;
      height: 80%;
      border-radius: 50%;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, transparent 80%);
      z-index: 1;
    }

    i {
      font-size: 48px;
      background: linear-gradient(135deg, #2196f3 0%, #0d47a1 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      position: relative;
      z-index: 2;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }
  }

  h3 {
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 12px;
    letter-spacing: -0.5px;
    position: relative;
    z-index: 1;
  }

  p {
    font-size: 18px;
    color: #64748b;
    margin-bottom: 35px;
    line-height: 1.5;
    position: relative;
    z-index: 1;

    strong {
      color: #2196f3;
      font-weight: 600;
      background: linear-gradient(135deg, #2196f3 0%, #0d47a1 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      padding: 0 4px;
    }
  }

  .btn-limpar-busca {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: white;
    border: none;
    border-radius: 30px;
    padding: 14px 32px;
    font-size: 16px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow:
      0 10px 20px rgba(33, 150, 243, 0.25),
      0 6px 6px rgba(0, 0, 0, 0.1),
      inset 0 2px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    z-index: 1;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
      z-index: -1;
    }

    i {
      font-size: 16px;
      transition: transform 0.3s ease;
    }

    &:hover {
      transform: translateY(-3px);
      box-shadow:
        0 15px 25px rgba(33, 150, 243, 0.3),
        0 10px 10px rgba(0, 0, 0, 0.08),
        inset 0 2px 0 rgba(255, 255, 255, 0.1);

      i {
        transform: rotate(-15deg);
      }
    }

    &:active {
      transform: translateY(1px);
      box-shadow:
        0 5px 10px rgba(33, 150, 243, 0.2),
        0 3px 3px rgba(0, 0, 0, 0.1),
        inset 0 2px 0 rgba(255, 255, 255, 0.1);
    }
  }
}

// Responsividade aprimorada
@media (max-width: 576px) {
  .empty-search-container {
    padding: 40px 15px;
  }

  .empty-search-content {
    padding: 35px 25px;

    .empty-search-icon {
      width: 90px;
      height: 90px;
      margin-bottom: 25px;

      i {
        font-size: 40px;
      }
    }

    h3 {
      font-size: 24px;
    }

    p {
      font-size: 16px;
      margin-bottom: 30px;
    }

    .btn-limpar-busca {
      padding: 12px 24px;
      font-size: 15px;
    }
  }
}
