export class AdicionaisProdutoUtils{
   static configureAdicionaisDoItem(item) {
    if (item.adicionais) {
      item.valoresAdicionais = [];
      // tslint:disable-next-line:forin
      for (let nomeCampo in item.adicionais) {
        if (nomeCampo.startsWith('lista')) {
          for (let nomeOpcao in item.adicionais[nomeCampo]) {
            if (nomeOpcao.startsWith('opcao') && item.adicionais[nomeCampo][nomeOpcao].selecionada) {
              item.valoresAdicionais.push({
                nome: item.adicionais[nomeCampo][nomeOpcao].opcao.nome,
                qtde: item.adicionais[nomeCampo][nomeOpcao].qtde
              })
            }
          }
        }
        if (nomeCampo.startsWith('campo')) {
          item.valoresAdicionais.push({
            nome: item.adicionais[nomeCampo].nome,
            qtde: 1
          })
        }
      }
    }
  }
}
