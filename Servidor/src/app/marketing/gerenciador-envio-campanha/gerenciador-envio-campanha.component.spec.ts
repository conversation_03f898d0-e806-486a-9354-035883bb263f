import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { GerenciadorEnvioCampanhaComponent } from './gerenciador-envio-campanha.component';

describe('GerenciadorEnvioCampanhaComponent', () => {
  let component: GerenciadorEnvioCampanhaComponent;
  let fixture: ComponentFixture<GerenciadorEnvioCampanhaComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ GerenciadorEnvioCampanhaComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(GerenciadorEnvioCampanhaComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
