import {Component, Input, OnInit} from '@angular/core';
import {ContatosService} from "../../services/contatos.service";
declare  var _;
@Component({
  selector: 'app-extrato-pontos',
  templateUrl: './extrato-pontos.component.html',
  styleUrls: ['./extrato-pontos.component.scss']
})
export class ExtratoPontosComponent implements OnInit {
  @Input() cartao = {}

  acoes: any = [];
  carregando = true;
  inicio = 0;
  total = 10;
  carregouTodos  = false;
  constructor(private contatosService: ContatosService) { }

  ngOnInit() {
    if(!this.acoes.length){
      this.carregueAcoes();
    }
  }

  carregueAcoes(){
    this.contatosService.obtenhaUltimasAcoes(null, this.cartao, this.inicio, this.total, true).then( (resposta) => {
      resposta.forEach( acao => this.acoes.push(acao))
      this.carregouTodos =   resposta.length < this.total;

    })
  }

  carregueMais() {
    this.inicio += this.total;
    this.carregueAcoes();
  }
}
