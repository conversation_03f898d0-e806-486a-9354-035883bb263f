.botao {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  height: 120px;
  background-size: cover;
  flex-direction: column;
  border-radius: 5px;
  position: relative;
  overflow: hidden;
  padding: 25px;

  img.fundo {
    position:absolute;
    z-index: -1;
    width: 100%;
  }

  &:first-child {
    margin-left: 0px;
  }

  &.disabled {
    background: #ccc;
  }
}

.centralizar {
  display: flex;
  width: 100px;
  height: 100px;
  display: block;
}

.branco {
  color: #fff;
}

.botao:hover {
  cursor: pointer;
  box-shadow: 1px 2px 3px #827b7b;
  filter:brightness(105%);

  &.disabled {
    filter: none;
  }
}

@media (min-width: 600px){
  ::ng-deep .custom .modal-dialog{
    max-width: 550px !important;
  }
}

@media screen and (max-width: 600px) {
  .botoes {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .row >div{
    }
  }

  .botao {
    margin-top: 20px;
    width: auto;
  }
}

.resumo {
  .card-box {
    i {
      font-size: 24px;
      color: #3b86ff;
    }

    span {
      font-size: 32px;
      color: #000
    }

    &.com-borda {
      border-bottom: 6px solid #808495;
      padding-bottom: 1.2rem;
    }
  }
}

.icone {
  display: inline-block;
  fill: #3B86FF;
  width: 32px;
  height: 32px;
  vertical-align: middle;
}

.container_icone {
  display: flex;
  align-items:center;
  height: 32px;
}

.table .thead-light th {
  background: #C2C4CC;
  color: #fff;
}

td.icone {
  padding: 0px;
}

.icone_trocar_pontos {
  width: 50px;
  margin-left: -8px;
}

.controle-grafico{
  position: absolute;
  right: 15px;
  top: 15px;
  z-index: 1;
}
