import { Component, OnInit } from '@angular/core';
import { EmpresasService } from '../../superadmin/services/empresas.service';
import { AutorizacaoService } from '../../services/autorizacao.service';
import { HttpClient } from '@angular/common/http';
import {ConstantsService} from "../ConstantsService";

@Component({
  selector: 'app-scripts-marketing',
  templateUrl: './scripts-marketing.component.html',
  styleUrls: ['./scripts-marketing.component.scss']
})
export class ScriptsMarketingComponent implements OnInit {
  empresa: any = {};
  usuario: any = {};
  carregando = true;

  constructor(
    private empresaService: EmpresasService,
    private autorizacaoService: AutorizacaoService,
    private constantsService: ConstantsService,
    private http: HttpClient
  ) { }

  ngOnInit(): void {
    this.carregue();
  }

  carregue() {
    this.carregando = true;

    this.autorizacaoService.carregueUsuario().subscribe(usuario => {
      this.usuario = usuario;

      this.constantsService.empresa$.subscribe(empresa => {
        if( !empresa ) return;

        this.carregando = false;
        this.empresa = empresa;
      });
    });
  }
}
