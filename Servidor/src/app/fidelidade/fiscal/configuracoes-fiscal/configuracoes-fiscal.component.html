<div class="config-fiscal">
  <form #frmConfigNotasFiscais="ngForm" (ngSubmit)="onSubmitConfigNotasFiscais()">

    <!-- Certificado A1 -->
    <div class="section">
      <div class="section-header">
        <i class="fas fa-file-certificate"></i>
        <div>
          <h5>Certificado A1</h5>
          <small>Informações do certificado digital</small>
        </div>
      </div>
      <div class="section-body">
        <div class="upload-area">
          <kendo-upload
            [restrictions]="restricoes"
            (upload)="usuarioEnviouArquivo($event)"
            (remove)="uploadFalhou($event)"

            saveUrl="/nfce/uploadcertificado"
            (progress)="progressoUpload($event)">
            <ng-template kendoUploadDropZone>
              <i class="fas fa-cloud-upload-alt"></i>
              <p>Clique para selecionar ou arraste um arquivo</p>
              <small>Formatos suportados: pfx, p12</small>
            </ng-template>
          </kendo-upload>
        </div>

        <div class="form-group">
          <label>Nova senha do certificado</label>
          <input [type]="exibirSenha ? 'text' : 'password'"
                 class="form-control"
                 placeholder="Informe sua senha"
                 [(ngModel)]="configuracoes.senhaCertificado"
                 name="senhaCertificado">
        </div>
      </div>
    </div>

    <!-- Dados do Contribuinte -->
    <div class="section">
      <div class="section-header">
        <i class="fas fa-user"></i>
        <div>
          <h5>Dados do Contribuinte</h5>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label>Regime tributário</label>
          <kendo-dropdownlist
            class="form-control"
            [(ngModel)]="configuracoes.regimeTributario"
            [data]="regimesTributarios"
            [textField]="'descricao'"
            name="regimeTributario"
            required>
          </kendo-dropdownlist>
        </div>

        <div class="form-group">
          <label>Inscrição estadual</label>
          <input type="text"
                 class="form-control"
                 [(ngModel)]="configuracoes.inscricaoEstadual"
                 name="inscricaoEstadual"
                 required>
        </div>

        <div class="form-group">
          <label>CNAE</label>
          <input type="text"
                 class="form-control"
                 [(ngModel)]="configuracoes.cnae"
                 name="cnae"
                 required>
        </div>
      </div>
    </div>

    <!-- Dados para NFC-e -->
    <div class="section">
      <div class="section-header">
        <i class="fas fa-receipt"></i>
        <div>
          <h5>Dados para NFC-e</h5>
        </div>
      </div>

      <div class="form-row">
        <div class="form-field">
          <label>Identificador Token</label>
          <input type="text"
                 class="form-input"
                 [(ngModel)]="configuracoes.idToken"
                 name="idToken"
                 required>
        </div>

        <div class="form-field">
          <label>Código de segurança (CSC)</label>
          <input type="text"
                 class="form-input"
                 [(ngModel)]="configuracoes.csc"
                 name="csc"
                 required>
        </div>

        <div class="form-field">
          <label>Série</label>
          <input type="number"
                 class="form-input"
                 [(ngModel)]="configuracoes.seriePadrao"
                 name="seriePadrao"
                 required>
        </div>

        <div class="form-field">
          <label>Número inicial</label>
          <input type="number"
                 class="form-input"
                 [(ngModel)]="configuracoes.numeroInicial"
                 name="numeroInicial"
                 required>
        </div>
      </div>

      <div class="form-field">
        <label>Natureza da operação</label>
        <input type="text"
               class="form-input"
               placeholder="Natureza da operação"
               [(ngModel)]="configuracoes.naturezaOperacao"
               name="naturezaOperacao">
      </div>
    </div>

    <!-- Ambiente de Nota -->
    <div class="section">
      <div class="section-header">
        <i class="fas fa-server"></i>
        <div>
          <h5>Ambiente de Nota</h5>
        </div>
      </div>

      <div class="radio-group">
        <label class="radio-option">
          <input type="radio"
                 [(ngModel)]="configuracoes.ambiente"
                 [value]="2"
                 name="ambiente">
          <span class="radio-label">
            <strong>Homologação</strong>
            <small>Ambiente de testes</small>
          </span>
        </label>

        <label class="radio-option">
          <input type="radio"
                 [(ngModel)]="configuracoes.ambiente"
                 [value]="1"
                 name="ambiente">
          <span class="radio-label">
            <strong>Produção</strong>
            <small>Ambiente de produção</small>
          </span>
        </label>
      </div>
    </div>

    <div class="section">
      <div class="section-header">
        <i class="fas fa-cog"></i>
        <div>
          <h5>Configurações de Emissão</h5>
        </div>
      </div>

      <div class="switch-container">
        <label class="switch">
          <input type="checkbox"
                 [(ngModel)]="configuracoes.enviarAutomaticamente"
                 name="enviarAutomaticamente">
          <span class="slider round"></span>
        </label>
        <span class="switch-label">Emitir NFC-e automaticamente ao pagar o pedido</span>
      </div>
      <small class="form-hint">Se desativado, você precisará emitir as notas fiscais manualmente para cada pedido.</small>
    </div>

    <!-- Adicionar mensagens de sucesso e erro aqui, antes do botão de salvar -->
    <div class="alert alert-success" *ngIf="mensagemSucesso" style="margin-top: 20px;">
      <i class="fas fa-check-circle"></i> {{ mensagemSucesso }}
    </div>

    <div class="alert alert-danger" *ngIf="erro" style="margin-top: 20px;">
      <i class="fas fa-exclamation-circle"></i> {{ erro }}
    </div>

    <div class="form-actions">
      <button type="submit" class="btn-primary" [disabled]="salvando">
        <i class="fas fa-save"></i>
        Salvar Configurações
      </button>
    </div>
  </form>
</div>

