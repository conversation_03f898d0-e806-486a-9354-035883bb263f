// Removido todo o SCSS pois agora usamos apenas classes do Tailwind

// Layout e Espaçamento
.p-5 { padding: 1.25rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.mt-1 { margin-top: 0.25rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.ml-2 { margin-left: 0.5rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }

// Grid
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }

@media (min-width: 1024px) {
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
}

// Flexbox
.flex { display: flex; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.justify-center { justify-content: center; }

// Tipografia
.text-xl { font-size: 1.25rem; }
.text-lg { font-size: 1.125rem; }
.text-sm { font-size: 0.875rem; }
.font-medium { font-weight: 500; }
.text-gray-500 { color: #6b7280; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }
.text-indigo-500 { color: #6366f1; }
.text-green-700 { color: #15803d; }
.text-red-700 { color: #b91c1c; }

// Backgrounds
.bg-white { background-color: white; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-green-50 { background-color: #f0fdf4; }
.bg-red-50 { background-color: #fef2f2; }
.bg-indigo-500 { background-color: #6366f1; }
.hover\:bg-indigo-600:hover { background-color: #4f46e5; }

// Bordas
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.border { border-width: 1px; }
.border-gray-200 { border-color: #e5e7eb; }
.border-green-200 { border-color: #bbf7d0; }
.border-red-200 { border-color: #fecaca; }

// Sombras
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }

// Inputs e Forms
.form-control {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  
  &:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }
}

// Botões
.btn-icon {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: #6b7280;
  
  &:hover {
    color: #374151;
  }
}

// Estados
.disabled\:opacity-50:disabled { opacity: 0.5; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }
.focus\:outline-none:focus { outline: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2); }
.focus\:ring-offset-2:focus { box-shadow: 0 0 0 2px white, 0 0 0 4px #6366f1; }
.focus\:ring-indigo-500:focus { box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3); }

// Utilitários
.w-full { width: 100%; }
.block { display: block; }
.flex-1 { flex: 1 1 0%; }

// Cores do sistema
$colors: (
  gray: (
    50: #F9FAFB,
    100: #F3F4F6,
    200: #E5E7EB,
    300: #D1D5DB,
    400: #9CA3AF,
    500: #6B7280,
    600: #4B5563,
    700: #374151,
    800: #1F2937,
    900: #111827
  ),
  blue: (
    500: #3B82F6,
    600: #2563EB
  )
);

.config-fiscal {
  padding: 1.5rem;

  // Cards/Seções
  .section {
    background-color: white;
    border-radius: 0.5rem;
    border: 1px solid #E5E7EB;
    margin-bottom: 1.5rem;
    padding: 1.5rem;

    .section-header {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 1.5rem;

      i {
        color: #6B7280;
        font-size: 1.25rem;
      }

      h5 {
        margin: 0;
        font-size: 0.875rem;
        font-weight: 500;
        color: #111827;
      }

      small {
        font-size: 0.75rem;
        color: #6B7280;
        display: block;
      }
    }
  }

  // Grid para inputs
  .form-row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 1rem;

    @media (max-width: 1024px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  // Campos do formulário - usando o mesmo estilo da tela de tributação
  .form-group, .form-field {
    margin-bottom: 1rem;

    label {
      display: block;
      font-size: 0.875rem;
      color: #374151;
      margin-bottom: 0.5rem;
    }

    // Estilo dos inputs igual ao da tela de tributação
    input,
    .form-input,
    .form-control,
    kendo-dropdownlist {
      display: block;
      width: 100%;
      height: calc(1.5em + 0.75rem + 2px);
      padding: 0.375rem 0.75rem;
      font-size: 0.875rem;
      font-weight: 400;
      line-height: 1.5;
      color: #495057;
      background-color: #fff;
      background-clip: padding-box;
      border: 1px solid #ced4da;
      border-radius: 0.25rem;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

      &:focus {
        color: #495057;
        background-color: #fff;
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }

      &::placeholder {
        color: #6c757d;
        opacity: 1;
      }
    }

    // Estilo específico para o Kendo DropDownList
    kendo-dropdownlist {
      display: block;
      width: 100%;

      .k-dropdown {
        width: 100%;
      }

      .k-dropdown-wrap {
        width: 100%;
        height: calc(1.5em + 0.75rem + 2px);
        padding: 0;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        background-color: #fff;
        display: flex;
        align-items: center;

        .k-input-inner {
          padding: 0.375rem 0.75rem;
          color: #495057;
          font-size: 0.875rem;
          line-height: 1.5;
        }

        .k-input {
          padding: 0.375rem 0.75rem;
          height: 100%;
          color: #495057;
          font-size: 0.875rem;
          line-height: 1.5;
          border: none;
          background: none;
        }

        .k-select {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 2rem;
          height: 100%;
          border: none;
          background: none;
          
          .k-icon {
            color: #495057;
          }
        }

        &:hover {
          border-color: #b3b3b3;
        }
      }

      // Validação
      &.ng-valid[required], &.ng-valid.required {
        .k-dropdown-wrap {
          border-left: 5px solid #42A948;
        }
      }

      &.ng-invalid.ng-touched:not(form) {
        .k-dropdown-wrap {
          border-left: 5px solid #a94442;
        }
      }
    }
  }

  // Área de upload
  .upload-area {
    border: 2px dashed #E5E7EB;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    background-color: #F9FAFB;
    transition: all 0.15s;
    margin-bottom: 1.5rem;
    cursor: pointer;

    &:hover {
      border-color: #D1D5DB;
      background-color: #F3F4F6;
    }

    i {
      font-size: 2rem;
      color: #9CA3AF;
      margin-bottom: 1rem;
    }

    p {
      color: #374151;
      margin-bottom: 0.25rem;
      font-size: 0.875rem;
    }

    small {
      color: #6B7280;
      font-size: 0.75rem;
    }
  }

  // Radio buttons
  .radio-group {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;

    .radio-option {
      position: relative;
      display: flex;
      align-items: center;
      padding: 1rem;
      border: 1px solid #E5E7EB;
      border-radius: 0.5rem;
      cursor: pointer;
      transition: all 0.15s;
      background-color: white;

      &:hover {
        border-color: #D1D5DB;
        background-color: #F9FAFB;
      }

      input[type="radio"] {
        margin-right: 0.75rem;
        width: 1rem;
        height: 1rem;
      }

      .radio-label {
        strong {
          display: block;
          font-size: 0.875rem;
          font-weight: 500;
          color: #111827;
          margin-bottom: 0.25rem;
        }

        small {
          display: block;
          font-size: 0.75rem;
          color: #6B7280;
        }
      }
    }
  }

  // Botão principal
  .btn-primary {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    background-color: #3B82F6;
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    border: none;
    cursor: pointer;
    transition: background-color 0.15s;

    &:hover {
      background-color: #2563EB;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    i {
      margin-right: 0.5rem;
    }
  }
}

// Adiciona os estilos do Bootstrap que são usados na tela de tributação
.form-control {
  display: block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

  &:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  // Adiciona a linha verde para campos válidos
  &.ng-valid[required], &.ng-valid.required {
    border-left: 5px solid #42A948; /* verde */
  }

  // Adiciona linha vermelha para campos inválidos
  &.ng-invalid.ng-touched:not(form) {
    border-left: 5px solid #a94442; /* vermelho */
  }
}

// Remove estilos padrão do Kendo que podem estar interferindo
.k-dropdown {
  border: none;
  background: none;
  
  .k-dropdown-wrap {
    background-image: none;
    box-shadow: none;
  }
}

// Ajuste para o container do dropdown
.form-group, .form-field {
  margin-bottom: 1rem;
  width: 100%;

  label {
    display: block;
    margin-bottom: 0.5rem;
  }

  kendo-dropdownlist {
    display: block;
    width: 100%;
  }
}

.form-group {
  margin-bottom: 1rem;
  width: 100%;

  label {
    display: block;
    font-size: 0.875rem;
    color: #374151;
    margin-bottom: 0.5rem;
  }
}

.form-control {
  display: block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out;

  &.ng-valid[required] {
    border-left: 5px solid #42A948;
  }

  &.ng-invalid.ng-touched:not(form) {
    border-left: 5px solid #a94442;
  }
}

// Estilo específico para o Kendo DropDownList
kendo-dropdownlist.form-control {
  padding: 0;
  border: none;
  height: auto;

  .k-dropdown-wrap {
    height: calc(1.5em + 0.75rem + 2px);
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    padding: 0 0.75rem;
    background-color: #fff;

    &.ng-valid[required] {
      border-left: 5px solid #42A948;
    }

    &.ng-invalid.ng-touched {
      border-left: 5px solid #a94442;
    }
  }
}

.switch-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
  margin-right: 15px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
}

input:checked + .slider {
  background-color: #2196F3;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

.switch-label {
  font-weight: 500;
}

.form-hint {
  display: block;
  color: #6c757d;
  font-size: 0.875rem;
  margin-top: 5px;
}

.alert {
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  
  i {
    margin-right: 8px;
  }
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}
