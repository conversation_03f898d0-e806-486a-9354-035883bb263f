import {Component, Input, OnInit} from '@angular/core';
import {ConfiguracoesNotaFiscalService} from "../services/configuracoes-nota-fiscal.service";
import {FiscalService} from "../services/fiscal.service";

@Component({
  selector: 'app-tributacao-nat-op',
  templateUrl: './tributacao-nat-op.component.html',
  styleUrls: ['./tributacao-nat-op.component.scss']
})
export class TributacaoNatOpComponent implements OnInit {
  @Input()
  propriaOuTerceiros: 'PROPRIA' | 'TERCEIROS';

  configuracoes: any;
  tributacaoNaturezaOperacao: any = {};
  mensagemSucesso: string;
  erro: string;
  salvando: boolean;
  cfops: any;
  tiposDeTributacao: any;
  modalidadesBCICMS: any
  modalidadesBCIMST_ST: any;

  constructor(private configuracoesNotaService: ConfiguracoesNotaFiscalService,
              private fiscalService: FiscalService) {
    console.log('Constructor - propriaOuTerceiros:', this.propriaOuTerceiros);
  }

  ngOnInit(): void {
    console.log('Inicializando componente para:', this.propriaOuTerceiros);

    this.configuracoesNotaService.obtenhaConfiguracoesNotaFiscal().then((configuracoes: any) => {
      if(configuracoes) {
        this.configuracoes = configuracoes;

        console.log('Configurações carregadas:', configuracoes);
        console.log('propriaOuTerceiros no momento da carga:', this.propriaOuTerceiros);

        const tributacao = this.propriaOuTerceiros === 'PROPRIA' ?
          configuracoes.tributacaoVendaProducaoPropria :
          configuracoes.tributacaoVendaProdutosTerceiros;

        console.log('Tributação selecionada:', tributacao);

        if(tributacao) {
          this.tributacaoNaturezaOperacao = {...tributacao};
        }

        Promise.all([
          this.fiscalService.listeCfopsNfce(),
          this.fiscalService.listeTiposDeTributacao(),
          this.fiscalService.listeModalidadesBCICMS(),
          this.fiscalService.listeModalidadesBCICMSST()
        ]).then(([cfops, tiposTributacao, modalidadesBCICMS, modalidadesBCIMST_ST]) => {
          this.cfops = cfops;
          this.tiposDeTributacao = tiposTributacao;
          this.modalidadesBCICMS = modalidadesBCICMS;
          this.modalidadesBCIMST_ST = modalidadesBCIMST_ST;

          if (this.tributacaoNaturezaOperacao.cfop) {
            this.tributacaoNaturezaOperacao.cfop = this.cfops.find(
              (c: any) => c.id === this.tributacaoNaturezaOperacao.cfop.id
            );
          }

          if (this.tributacaoNaturezaOperacao.tipoDeTributacaoICMS) {
            this.tributacaoNaturezaOperacao.tipoDeTributacaoICMS = this.tiposDeTributacao.find(
              (t: any) => t.id === this.tributacaoNaturezaOperacao.tipoDeTributacaoICMS.id
            );
          }

          if (this.tributacaoNaturezaOperacao.modalidadeBaseDeCalculoICMS) {
            this.tributacaoNaturezaOperacao.modalidadeBaseDeCalculoICMS = this.modalidadesBCICMS.find(
              (m: any) => m.id === this.tributacaoNaturezaOperacao.modalidadeBaseDeCalculoICMS.id
            );
          }
        });
      }
    });
  }

  onSubmitConfiguracoes() {
    this.salvando = true;
    console.log('Enviando configurações para:', this.propriaOuTerceiros);
    console.log('Dados da tributação:', this.tributacaoNaturezaOperacao);

    this.configuracoesNotaService.salveTributacaoNaturezaOperacao(this.tributacaoNaturezaOperacao, this.propriaOuTerceiros)
      .then((resposta) => {
        console.log('Resposta do servidor:', resposta);
        this.mensagemSucesso = 'Configurações salvas com sucesso';
        this.erro = null;
        this.salvando = false;
      })
      .catch((erro) => {
        console.error('Erro ao salvar:', erro);
        this.erro = erro.error || 'Erro ao salvar configurações';
        this.mensagemSucesso = null;
        this.salvando = false;
      });
  }

  protected readonly HTMLInputElement = HTMLInputElement;
}
