.contatos {
  .card-body {
    padding: 0;
  }

  .card-box {
    margin-bottom: 0;
    padding: 1em;
  }
}

.linha {
  border-bottom: 5px solid #979AA0;
}

.contato {
  box-shadow: none;
  border-radius: 0;
  margin-left: -16px;
  margin-right: -16px;

  .card-box {
    box-shadow: none;
    border: 1px solid #cccccc59;
  }

  label {
    margin: 0;
    top: -10px;
    position: relative;
  }

  button {
    margin-left: 15px;
  }
}

.text-black{
  color:#000;
}

.font24{
  font-size:24px;
}

.font30{
  font-size: 30px;
}

.badge{
  padding: 0.45em 0.5em;
}

.acoes {
  top: 20%;
  position: relative;
}

.close span {
  opacity: 1.0;
}

.modal-header {
  background: url('/assets/fidelidade/Banner_02.png');
}

a{
  color: inherit;
  display: block;
}

@media (min-width: 481px) and (max-width: 767px) {
  .card {
    margin-left: -12px;
    margin-right: -12px;
  }

  .card-body {
    padding: 1rem;
  }

  .mt-4 {
    margin-top: 10px !important;
  }
}

@media (max-width: 480px) {
  .card {
    margin-left: -12px;
    margin-right: -12px;
  }

  .card-body {
    padding: 1rem 5px;
  }

  .mt-4 {
    margin-top: 10px !important;
  }
}
