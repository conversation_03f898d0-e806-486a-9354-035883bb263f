import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ServerService } from '../../services/ServerService';

@Injectable({
  providedIn: 'root'
})
export class OpcaoRespostaChatbotInstagramService extends ServerService {

  constructor(protected http: HttpClient) {
    super(http);
  }

  // Listar todas as opções de resposta
  listeOpcoes(filtros: any = {}): Promise<any> {
    return this.obtenha('/opcao-resposta-chatbot-instagram', filtros);
  }

  // Listar opções por resposta específica
  listeOpcoesPorResposta(respostaId: number): Promise<any> {
    return this.listeOpcoes({ respostaId });
  }

  // Listar opções por chave de resposta
  listeOpcoesPorChaveResposta(chaveResposta: string): Promise<any> {
    return this.listeOpcoes({ chaveResposta });
  }

  // Obter opção específica por ID
  obtenhaOpcao(id: number): Promise<any> {
    return this.obtenha(`/opcao-resposta-chatbot-instagram/${id}`, {});
  }

  // Criar nova opção
  insiraOpcao(opcao: any): Promise<any> {
    return this.facaPost('/opcao-resposta-chatbot-instagram', opcao);
  }

  // Atualizar opção existente
  atualizeOpcao(id: number, opcao: any): Promise<any> {
    return this.http.put(`/opcao-resposta-chatbot-instagram/${id}`, opcao)
      .toPromise().then(this.retorno).catch(this.handleError);
  }

  // Remover opção específica
  removaOpcao(id: number): Promise<any> {
    return this.http.delete(`/opcao-resposta-chatbot-instagram/${id}`)
      .toPromise().then(this.retorno).catch(this.handleError);
  }

  // Remover todas as opções de uma resposta
  removaOpcoesPorResposta(respostaId: number): Promise<any> {
    return this.http.delete(`/opcao-resposta-chatbot-instagram/resposta/${respostaId}`)
      .toPromise().then(this.retorno).catch(this.handleError);
  }

  // Listar apenas opções ativas
  listeOpcoesAtivas(respostaId?: number): Promise<any> {
    const filtros = { ativo: true };
    if (respostaId) {
      filtros['respostaId'] = respostaId;
    }
    return this.listeOpcoes(filtros);
  }

  // Listar opções por tipo
  listeOpcoesPorTipo(tipo: string, respostaId?: number): Promise<any> {
    const filtros = { tipo };
    if (respostaId) {
      filtros['respostaId'] = respostaId;
    }
    return this.listeOpcoes(filtros);
  }

  // Validar se texto da opção é único para a resposta
  validaTextoOpcao(texto: string, respostaId: number, idOpcao?: number): Promise<any> {
    const params = { texto, respostaId };
    if (idOpcao) {
      params['id'] = idOpcao;
    }
    return this.obtenha('/opcao-resposta-chatbot-instagram/valida-texto', params);
  }

  // Reordenar opções de uma resposta
  reordeneOpcoes(respostaId: number, opcoes: any[]): Promise<any> {
    return this.facaPost(`/opcao-resposta-chatbot-instagram/reordene/${respostaId}`, { opcoes });
  }
} 