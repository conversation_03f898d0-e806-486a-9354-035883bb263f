.footer {
  border-top: solid 1px #eeeeee;
  background-color: #f7f8f8;
  padding: 0px;
  position: fixed;
}

#form:focus-within .footer {
  position: initial;
}

.font11{
  font-size: 11px !important;
}

.container_total {
  margin-left: -12px;
  margin-right: -12px;
}

.preco {
  color: #6db31b;
}

.resgate{
  color: #e63b3b;
}

.produto {
  border-bottom: solid 1px #e8e8e8;
}

.capa_empresa {
  height: 200px;
  background-size: cover;
}

.capa_empresa.centralizada {
  z-index: 10;
  background-repeat: no-repeat;
  background-position-x: center;
  background-size: cover;
}

.cartao {
  background: white;
  margin-left: auto;
  margin-right: auto;
  padding: 15px;
}

.cartao.conteudo {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  box-shadow: 0 4px 10px -2px #E2E3E3;
}

.cartao.conteudo.topo {
  margin-top: 0px;
  width: 100%
}

/*
.cartao.conteudo {
  box-shadow: 0 4px 10px -2px #E2E3E3;
  min-height: 190px;
  border-top: 0;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
*/


.cartao.semborda {
  margin-top: 20px;
  border: 0 none;
  background-color: transparent;
  padding: 0;
}

.bg-content {
  display: none;
}



.imagem_empresa {
  width: 80px;
  height: 80px;
  float: left ;
}

.detalhes_empresa  {
  float:left;
  margin-left: 10px;
  display: inline-block;
  width: calc(100% - 90px);
}

.nome_empresa {
  font-size: 24px;
  color: black;
  font-weight: 500;
  display: block;
  line-height: 1.2;
  max-height: 1.2em;
  overflow: hidden;
}

.endereco {
  font-size: 11px;
}

.whatsapp {
  display: block;
  margin-bottom: 5px;
}

.whatsapp span{
  font-size:12px;
  font-weight: 600;

  color: #199D0F
}

.dados_empresa {
  min-height: 90px;
  overflow: hidden;
}

.linha {
  border-bottom: #EFEFEF solid 1px;
}

.descricao_empresa {
  margin: 10px;
  font-size: 12px;
  font-weight: 400;

}

.menu {
  color: #525252;
  margin-top: 15px;

}

.brinde {
  margin-top: 10px;
}
.valor {
  position: absolute;
  color: white;
  font-size: 20px;
  top: 10px;
  width: 100%;
  text-align: center;
}

.row {
  padding-left: 5px;
  padding-right: 5px;
}

.container_selo {
  position: relative;
}

.brinde {
  text-align: center;
  position: relative;
}

.preco_troca {
  font-weight: 600;
}

.nome_brinde {
  display: inline-block;
  margin-top: 5px;
  font-size: 16px;
  background: #4b4b4b;
  color: white;
  margin-left: 2px;
  padding: 5px 10px 5px 10px;
  border-radius: 50px;
  font-weight: 200;
}

.faltam_selos {
  color: #3e48bc;
  margin-bottom: 10px;
}

.container-scroll {
  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
  margin-right: -20px;
}

.container-scroll .col {
  margin-left: 5px;
  margin-right: 5px;
}


.foto_brinde {
  display: block;
  float:none;
  margin:0 auto;
  border-radius: 30px;
  margin-top: 5px;
  width: 100%;
}


.foto_ambiente {
  display: block;
  float:none;
  margin:0 auto;
  border-radius: 20px;
}


.container-scroll .caixa_brinde {
  border-radius: 5px;
  box-shadow: 0 4px 10px -2px #E2E3E3;
  margin-bottom: 10px;
  height: 250px !important;
  margin-left: -3px;
  margin-right: -3px;
  padding-top: 5px;
  background: white;
}


.container-scroll .foto_brinde {
  width: unset;
  width: 100%;
}

.nome_brinde_pontos {
  font-size: 15px;
  font-weight: 600;

}

.container-scroll .preco_troca {
  font-size: 11px;
  font-weight: 400;
  line-height: 1em;
}

.container-scroll .preco_troca.nao_atingiu {
  color: #F67682;
}

.container-scroll .preco_troca.atingiu {
  color: #6DB31B;
  margin-right: -20px;
}

.botoes {
  margin: 20px;
  text-align: center;
  font-size: 16px;
  font-weight: 400;
}

.botao {
  padding: 15px;
}

.botao.verde{
  background: #6DB31B;
  color:  white;
}

.botao.azul {
  border: #1c95d4 solid 1px;
  margin-top: 10px;
  color: #1c95d4;
}

.icone.whatsapp {
  width: 16px;
  display: inline-block;
  margin: 0
}

.icone_voltar {
  display: inline-block;
  fill: #4a81d4;
  width: 32px;
  height: 32px;
  vertical-align: middle;
}



.float{
  position:fixed;
  width:60px;
  height:60px;
  bottom:40px;
  right:40px;
  background-color:#25d366;
  color:#FFF;
  border-radius:50px;
  text-align:center;
  font-size:30px;
  box-shadow: 2px 2px 3px #999;
  z-index:100;
}

.my-float{
  margin-top:16px;
}

.container_imagem {
  overflow: hidden;
  display: flex;
  max-width: 100%;
}

.fidelidade {
  height: 16px;

  width: 24px;
  background: #3B86FF;
  text-align: center;
  float:left;
  line-height: 1em;
  margin: 2px 3px;
}

.azul .coracao{
  display: inline-block;
  fill: white;
  width: 16px;
  height: 16px;
  vertical-align: middle;
  margin: 0;
}

.azul {
  color: #3B86FF;
  font-weight: bold;
}


.bolinha {
  margin: 5px;
  width: 8px;
  height: 8px;
  background: #6DB31B;
  border-radius: 100px;
  float: left;
}

.horario {
  padding-top: 2px;
}

.bolinha.fechado {
  background: red;
}

.horario .descricao {
  font-size: 11px;
  font-weight: bold;
}

.icon-fechar {
  width: 32px;
  height: 32px;
  float: left;
  margin-left: 5px;
}

.slides-fotos h3, .slides-produtos h3{
  text-align: center;
  line-height: 32px;
}

.slides-fotos, .slides-produtos {
  position: fixed;
  overflow: auto;
  z-index: 1000;
  top:0px;
  background: white;
  height: 100%;
  width: 100%;
}

.slides-produtos {
  padding-top: 60px;
  background: rgba(255, 255, 255, 0.9);
  height: 100%;
}


.container-fotos {
  background: black;
}

.cartao.descricao {
  margin-top: 15px;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-right-radius: 10px;
  box-shadow: 0 4px 10px -2px #E2E3E3;

}

.grande {
  font-size: 18px;
  font-weight: bold;
}

.botao_produto {
  border: 1px solid black;
  padding: 15px;
  border-radius: 30px;
  text-align: center;
  font-weight: 500;
  font-size: 14px;
}

.botao_produto.verde {
  border: 1px solid #3fad36;
  color: #3fad36;

}

.titulo-produtos {
  background: white;
}
.slides-produtos .icon-fechar {
  width: 32px;
  height: 32px;
  float: left;
  margin-left: 0;
  margin-top: 9px;
}


@media screen and (min-width: 768px) {
  .cartao.conteudo, .cartao.semborda, .slides-fotos, .slides-produtos   {
    max-width: 100%;
    /* text-align: center; */
    margin-left: auto;
    margin-right: auto;
  }

  .bg-content {
    z-index: 0;
    position: absolute;
    left: 0px;
    top: 0px;
    right: 0px;
    width: 120%;
    height: 120%;
    margin-left: -10%;
    background-position: 0px 0px, 50% 50%;
    background-size: auto, cover;
    background-repeat: repeat, no-repeat;
    opacity: 1;
    -webkit-filter: blur(24px);
    filter: blur(24px);
    display: block;

  }

  .content {
    position: relative;
    z-index: 10;

  }

  .slides-fotos, .slides-produtos {
    left: 20%;
  }

  .sobre_nos {
    border-radius: 5px;
    box-shadow: 0 4px 10px -2px #E2E3E3;
    padding: 10px;
    padding-bottom: 5px;
    background: white;
    margin-top: 10px;
  }
  .brinde {
    margin-top: 0 ;
  }

  .container-scroll {
    overflow-x: unset;
    overflow-y: unset;
    white-space: unset;
    -webkit-overflow-scrolling: touch;
    margin-right: 0px;
  }

  .capa_empresa.centralizada {
    height: 310px;
    max-width: 100%;
    margin: 0 auto;
    box-shadow: 0 4px 10px -2px #a9aaaa;
  }

  .capa_empresa.desfocada {
    height: 305px;
  }

  .cartao.conteudo {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 30px;
    border-top-right-radius: 0px;
    box-shadow: 0 4px 10px -2px #E2E3E3;
  }

  .cartao.conteudo.topo {
    margin-top: 0px;
  }
}

.icone.insta {
  fill: #525252;
}

.cinza a {
  color: #525252 ;
}

.FlexEmbed {
  display: block;
  overflow: hidden;
  position: relative;
}

.FlexEmbed:before {
  content: "";
  display: block;
  width: 100%;
}


.FlexEmbed--2by1:before {
  padding-bottom: 25%;
}

.FlexEmbed.desfocada {
  background-position: 0px 0px, 50% 50%;
  background-size: auto, cover;
  background-repeat: repeat, no-repeat;
  opacity: 1;
  -webkit-filter: blur(12px);
  filter: blur(12px);
  z-index: -1;
  position: absolute;
  width: 100%;
  max-width: 100%;
  display: block;
  top: 0px;
}

.CoverImage {
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  margin: 0 auto;
  max-height: 300px;
  max-width: 100%;
}

@media (max-width: 992px) {

  .nome_empresa {
    font-size: 16px !important;
  }

  .cartao.conteudo.topo {
    width: 100%;
  }
  .cartao{
    padding: 10px;
  }
}

@media (min-width: 992px) {
  .cartao.conteudo, .cartao.semborda, .slides-fotos, .slides-produtos {
    max-width: 100%;
    /* text-align: center; */
    margin-left: auto;
    margin-right: auto;
  }

  .capa_empresa.centralizada {
    height: 310px;
    max-width: 100%;
    margin: 0 auto;
    box-shadow: 0 4px 10px -2px #a9aaaa;
  }

  .CoverImage {
    max-width: 100%;
  }
}

.font-11{
  font-size: 11px;
}

.nao_mobile {
  display: none;
}

.item_produto {
  h5 {
    font-size: 0.8rem !important;
  }
}

@media (min-width: 1025px) {
  .nao_mobile {
    display: block;
  }

  .mobile {
    display: none;
  }

  .footer {
    position: initial !important;
    margin-top: 0px;
  }

  .item_produto {
    h5 {
      font-size: 1.0rem;
    }
  }
}

.linha {
  border-top: 1px solid #ececec;
  border-bottom: 1px solid #fff;
  margin-left: 0px;
}

.footer .alert{
  border-radius: 0;
  margin-bottom: 0px;
}

.fundo{

}

form{
  padding-bottom: 50px;
}


.dashed{
  border-bottom-style: dashed;
}

.media .badge{
  top: -2px;
  position: relative;
}

.track-order-list ul li {
  position: relative;
  border-left: 2px solid #dee2e6;
  padding: 0 0 14px 21px;
}
.track-order-list ul li:first-child {
  padding-top: 0;
}
.track-order-list ul li {
  position: relative;
  border-left: 2px solid #dee2e6;
  padding: 0 0 14px 21px;
}



.track-order-list ul li:before {
  content: "";
  position: absolute;
  left: -7px;
  top: 0;
  height: 12px;
  width: 12px;
  background-color: #b19ad7f0;
  border-radius: 50%;
  border: 3px solid #fff;
}

.track-order-list ul li.completed:before {
  background-color: #7e57c2 !important;
}


.track-order-list ul li.actived:before {
  background-color: #7e57c2 !important;
}



.track-order-list ul li.completed {
  border-color: #7e57c2;

}





.track-order-list ul li .active-dot.dot {
  top: -9px;
  left: -16px;
  border-color: #7e57c2;
}

.dot {
  border: 4px solid #7e57c2;
  background: 0 0;
  border-radius: 60px;
  height: 30px;
  width: 30px;
  animation: pulse 1.5s ease-out;
  animation-iteration-count: infinite;
  position: absolute;
  top: -15px;
  right: -2px;
  z-index: 1;
  opacity: 0;
}

@keyframes pulse {
  0% {
    -webkit-transform: scale(0);
    opacity: 0;
  }

  25% {
    -webkit-transform: scale(0);
    opacity: .1;
  }

  50% {
    -webkit-transform: scale(.1);
    opacity: .3;
  }

  100% {
    -webkit-transform: scale(1);
    opacity: 0;
  }
}
.riscado{
  text-decoration: line-through;
}

.lh20{
  line-height: 20px;
}

