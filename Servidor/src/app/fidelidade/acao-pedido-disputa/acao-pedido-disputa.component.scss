.disputa{
  max-width: 600px;
  .titulo{
    background: #f1526b;
    color: #fff !important;
    padding: 9px;
    border-radius: 2px 2px;
    font-size: 15px;
  }



  .botoes{
    max-width: 250px;
    margin: 0 auto;
  }


}

.cardbox{
  background-color: #cccccc4a;
  padding: 10px 10px;
  border: 1px solid #fff;
  border-radius: 5px 8px;

  &.resposta{
    background-color: rgb(222 233 210 / 91%);
  }
}

.produtos{
  .produto{
    p{
      padding-bottom: 0;
    }
    .preco{
      color: #6db31b;
    }

  }
}

.evidencias{
  .foto{
    margin-left: 5px; margin-right: 5px;
    width: 100px; height: 100px;
    display: inline-block; position: relative;
    background: #EFEFEF;

    img{
      max-height: 100px;
      max-width: 100px;
    }
  }

}

.cpointer{
  cursor: pointer;
}
