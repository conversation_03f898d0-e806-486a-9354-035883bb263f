import {Component, Input, OnInit} from '@angular/core';
import {DeviceDetectorService} from "ngx-device-detector";

@Component({
  selector: 'app-lista-empresas-multi-marca',
  templateUrl: './lista-empresas-multi-marca.component.html',
  styleUrls: ['./lista-empresas-multi-marca.component.scss']
})
export class ListaEmpresasMultiMarcaComponent implements OnInit {
  @Input()
  grupoDeLojas: any;

  @Input()
  empresas = [];
  isMobile: boolean;

  constructor(private detectorDevice: DeviceDetectorService) {
    this.isMobile = this.detectorDevice.isMobile();
  }

  ngOnInit(): void {

  }

  abraCardapio(circulo: HTMLDivElement, empresaDoGrupo: any) {

  }
}
