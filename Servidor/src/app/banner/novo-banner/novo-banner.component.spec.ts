import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { NovoBannerComponent } from './novo-banner.component';

describe('NovoBannerComponent', () => {
  let component: NovoBannerComponent;
  let fixture: ComponentFixture<NovoBannerComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ NovoBannerComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(NovoBannerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
