import {Directive, forwardRef} from '@angular/core';
import {AbstractControl, NG_VALIDATORS, Validator} from "@angular/forms";

@Directive({
  selector: '[campoTelefone][ngModel]',
  providers: [
    { provide: NG_VALIDATORS, useExisting: forwardRef(() => CampoTelefoneValidator), multi: true }
  ]
})
export class CampoTelefoneValidator  implements  Validator {
  constructor() {   }

  validate(c: AbstractControl): { [key: string]: any } {
    let valido = this.telefoneValido(c.value);

    if(valido) return null;

    return { 'campoTelefone': true }

  }

  telefoneValido(telefone){
    if(!telefone) return false;

    telefone = this.unmask(telefone);

    return telefone.match(/^[1-9]{2}[9]{0,1}[6-9]{1}[0-9]{3}[0-9]{4}$/);
  }

  unmask(val) {
    return val.replace(/\D+/g, '');
  }

}
