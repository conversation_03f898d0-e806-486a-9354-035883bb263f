<div class="mt-2"></div>

<div *ngIf="mensagemSucesso" class="alert alert-success alert-dismissible fade show" role="alert">
  <i class="fas fa-check-circle mr-2"></i>
  {{mensagem<PERSON><PERSON>sso}}
  <button type="button" class="close" data-dismiss="alert" aria-label="Close" (click)="mensagemSucesso = ''">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div *ngIf="mensagemErro" class="alert alert-danger alert-dismissible fade show" role="alert">
  <i class="fas fa-exclamation-triangle mr-2"></i>
  {{mensagemErro}}
  <button type="button" class="close" data-dismiss="alert" aria-label="Close" (click)="mensagemErro = ''">
    <span aria-hidden="true">&times;</span>
  </button>
</div>


<div class="card shadow">
  <div class="card-body">
    <div *ngIf="loading" class="text-center my-4">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Carregando...</span>
      </div>
      <p class="mt-2 text-muted">Carregando notas fiscais...</p>
    </div>

    <div *ngIf="!loading && dataSource.length === 0" class="text-center py-5">
      <img src="/images/empty-notes.svg" alt="Sem notas fiscais" style="max-width: 200px; margin-bottom: 20px; opacity: 0.7;">
      <h3 class="text-muted">Nenhuma Nota Fiscal Encontrada</h3>
      <p class="lead text-muted">Você ainda não possui notas fiscais emitidas. Quando pedidos forem realizados, as notas fiscais serão geradas automaticamente.</p>
    </div>

    <kendo-grid [kendoGridBinding]="filteredDataSource" *ngIf="!loading && filteredDataSource.length > 0" class="border-0">
      <kendo-grid-column title="" [width]="50">
        <ng-template kendoGridCellTemplate let-nfce let-rowIndex="rowIndex">
          <span class="badge badge-pill badge-light">{{rowIndex + 1}}</span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column title="Número" [width]="100">
        <ng-template kendoGridCellTemplate let-nfce>
          <span class="font-weight-medium">{{nfce.numeroNFe}}</span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column title="Série" [width]="70">
        <ng-template kendoGridCellTemplate let-nfce>
          <span>{{nfce.serie}}</span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column title="Destinatário">
        <ng-template kendoGridCellTemplate let-nfce>
          <span class="text-truncate d-inline-block" style="max-width: 100%;" [title]="nfce.nomeDestinatario">
            {{nfce.nomeDestinatario}}
          </span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column title="Data Emissão" [width]="175">
        <ng-template kendoGridCellTemplate let-nfce>
          <span><i class="far fa-calendar-alt text-muted mr-1"></i> {{nfce.dataDeEmissao | date:'dd/MM/yyyy HH:mm'}}</span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column title="Valor Total" [width]="110">
        <ng-template kendoGridCellTemplate let-nfce>
          <span class="font-weight-bold text-success">{{nfce.valorTotalNFe | currency:'BRL'}}</span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column title="Status" [width]="120">
        <ng-template kendoGridCellTemplate let-nfce>
          <span class="badge badge-pill px-2 py-1" [ngClass]="{
            'badge-success': nfce.status === 2,
            'badge-warning': nfce.status === 1 || nfce.status === 3 || nfce.status === 15 || nfce.status === 20,
            'badge-danger': nfce.status === 10 || nfce.status === 11 || nfce.status === 14 || nfce.status === 16 || nfce.status === 17 || nfce.status === 18 || nfce.status === 19 || nfce.status === 22 || nfce.status === 23 || nfce.status === 24,
            'badge-info': nfce.status === 4 || nfce.status === 5 || nfce.status === 6 || nfce.status === 7 || nfce.status === 8 || nfce.status === 9 || nfce.status === 21
          }">
            <ng-container [ngSwitch]="nfce.status">
              <ng-container *ngSwitchCase="1">Nova</ng-container>
              <ng-container *ngSwitchCase="2">Autorizada</ng-container>
              <ng-container *ngSwitchCase="3">Em Processamento</ng-container>
              <ng-container *ngSwitchCase="4">Cancelada</ng-container>
              <ng-container *ngSwitchCase="5">Inutilizada</ng-container>
              <ng-container *ngSwitchCase="6">Importada</ng-container>
              <ng-container *ngSwitchCase="7">Contigenciada</ng-container>
              <ng-container *ngSwitchCase="8">De Contigência</ng-container>
              <ng-container *ngSwitchCase="9">Duplicidade</ng-container>
              <ng-container *ngSwitchCase="10">Rejeitada</ng-container>
              <ng-container *ngSwitchCase="11">Dados Inválidos</ng-container>
              <ng-container *ngSwitchCase="12">Sucesso</ng-container>
              <ng-container *ngSwitchCase="13">Falha</ng-container>
              <ng-container *ngSwitchCase="14">Conferência Rejeitou</ng-container>
              <ng-container *ngSwitchCase="15">Presa na SEFAZ</ng-container>
              <ng-container *ngSwitchCase="16">Problemas no Envio</ng-container>
              <ng-container *ngSwitchCase="17">Envio Interrompido</ng-container>
              <ng-container *ngSwitchCase="18">Problema Comunicação SEFAZ</ng-container>
              <ng-container *ngSwitchCase="19">Denegada</ng-container>
              <ng-container *ngSwitchCase="20">Recebida</ng-container>
              <ng-container *ngSwitchCase="21">Pendente de Conciliação</ng-container>
              <ng-container *ngSwitchCase="22">Rejeitada e Pendente de Conciliação</ng-container>
              <ng-container *ngSwitchCase="23">Problemas no Envio em Contingência</ng-container>
              <ng-container *ngSwitchCase="24">Problema ao Cancelar NFe</ng-container>
              <ng-container *ngSwitchDefault>{{nfce.status}}</ng-container>
            </ng-container>
          </span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column title="Chave de Acesso" [width]="390">
        <ng-template kendoGridCellTemplate let-nfce>
          <span class="text-monospace small text-nowrap">
            {{nfce.chaveDeAcesso | chaveAcesso}}
          </span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column title="Ações" [width]="270">
        <ng-template kendoGridCellTemplate let-nfce>
          <div class="btn-group">
            <!--
            <button type="button" class="btn btn-sm btn-light mr-1" (click)="visualizar(nfce)" kendoTooltip title="Visualizar">
              <i class="fas fa-eye text-primary"></i>
            </button>
            -->
            <button type="button"
                    class="btn btn-sm btn-light mr-1"
                    (click)="downloadXml(nfce)"
                    kendoTooltip
                    title="Baixar XML"
                    *ngIf="podeDownloadXml(nfce)">
              <i class="fas fa-download text-info"></i>
            </button>
            <!--
            <a class="btn btn-sm btn-light mr-1" [href]="getPdfUrl(nfce.id)" target="_blank" kendoTooltip title="Baixar PDF">
              <i class="fas fa-file-pdf text-danger"></i>
            </a>
            -->
            <button type="button"
                    class="btn btn-sm btn-light mr-1"
                    (click)="imprimir(nfce)"
                    kendoTooltip
                    title="Imprimir"
                    *ngIf="podeImprimir(nfce)">
              <i class="fas fa-print text-success"></i>
            </button>
            <button type="button"
                    class="btn btn-sm btn-light"
                    (click)="reenviarNota(nfce)"
                    kendoTooltip
                    title="Reenviar Nota"
                    *ngIf="podeReenviar(nfce.status)"
                    [disabled]="loading">
              <span *ngIf="loading" class="k-icon k-i-loading"></span>
              <i *ngIf="!loading" class="fas fa-paper-plane text-warning"></i>
            </button>
            <button type="button"
                    class="btn btn-sm btn-light"
                    (click)="cancelarNota(nfce, $event)"
                    *ngIf="podeCancelar(nfce)"
                    kendoTooltip
                    title="Cancelar Nota">
              <i class="fas fa-ban text-danger"></i>
            </button>
          </div>
        </ng-template>
      </kendo-grid-column>
    </kendo-grid>

    <div *ngIf="!loading && dataSource.length > 0 && filteredDataSource.length === 0" class="text-center py-5">
      <img src="/images/empty-notes.svg" alt="Sem notas fiscais" style="max-width: 200px; margin-bottom: 20px; opacity: 0.7;">
      <h3 class="text-muted">Nenhuma Nota Fiscal com Status Válido</h3>
      <p class="lead text-muted">Todas as notas fiscais encontradas não possuem status definido.</p>
    </div>
  </div>
  <div class="card-footer bg-white d-flex justify-content-between" *ngIf="!loading && filteredDataSource.length > 0">
    <div>
      <span class="text-muted">Total de notas: <strong>{{filteredDataSource.length}}</strong></span>
    </div>
    <button class="btn btn-outline-primary btn-sm" (click)="carregarNFCes()">
      <i class="fas fa-sync-alt mr-1"></i> Atualizar
    </button>
  </div>
</div>
<kendo-dialog title="Cancelar Nota Fiscal"
              *ngIf="dialogCancelamento">
  <div class="p-3">
    <label class="form-label mb-2">Justificativa do cancelamento:</label>
    <textarea kendoTextArea
              [(ngModel)]="justificativaCancelamento"
              rows="5"
              style="width: 100%; min-height: 120px;"
              [disabled]="processandoCancelamento"
              placeholder="Digite a justificativa do cancelamento (mínimo 15 caracteres)">
    </textarea>
  </div>
  <kendo-dialog-actions>
    <button kendoButton
            [disabled]="processandoCancelamento"
            (click)="fecharDialogCancelamento()">
      Cancelar
    </button>
    <button kendoButton
            [disabled]="processandoCancelamento"
            (click)="confirmarCancelamento()"
            [primary]="true">
      <span *ngIf="processandoCancelamento" class="k-icon k-i-loading"></span>
      Confirmar
    </button>
  </kendo-dialog-actions>
</kendo-dialog>
