<div *ngIf="carregou"  class="container_total">
  <a [href]="'http://wa.me/55' + empresa?.whatsapp" class="float" target="_blank">
    <i class="fab fa-whatsapp my-float"></i>
  </a>

  <div class="CoverImage FlexEmbed FlexEmbed--2by1"
       [style.background-image]='"url(\"\/images\/empresa\/" + empresa.capa +"\")" '
  ></div>

  <div class="CoverImage FlexEmbed desfocada FlexEmbed--2by1"
       [style.background-image]='"url(\"\/images\/empresa\/" + empresa.capa +"\")" '
  ></div>

  <div class="cartao conteudo topo">
    <div class="dados_empresa linha">
      <img class="imagem_empresa" src="/images/empresa/{{empresa.logo}}"/>
      <div class="detalhes_empresa">
      <span class="nome_empresa">
        {{empresa.nome}}
      </span>
        <span class="endereco">
        {{empresa.endereco}}
      </span>
        <a class="whatsapp" [href]="'http://wa.me/55' +empresa.numeroWhatsapp?.whatsapp">
          <img class="icone tam1" src='/assets/fidelidade/icones/icon-whatsapp-mini.png'>
          <span>
          {{empresa.numeroWhatsapp?.whatsapp | mask: '(99) 9-9999-9999'}}
        </span>
        </a>
      </div>

    </div>
    <div class="descricao_empresa">
      {{empresa.descricao}}
    </div>
    <div class="row">
      <div class="col horario"><div class="bolinha" [ngClass]="{
        'fechado': !estaAberto
      }"></div><div class="descricao">{{descricaoHorario}}</div></div>
      <a routerLink="/cliente" routerLinkActive="active">
        <div class="col azul">
          <div class="fidelidade">
            <div class="coracao" [inlineSVG]="'/assets/fidelidade/icones/icon-fidelidade-pink.svg'" [removeSVGAttributes]="['fill', 'width', 'height']" style="width: 12px; height: auto"></div>
          </div>
          <span class="texto_azul">
          Meu Cartão
        </span>

        </div>

      </a>
    </div>

  </div>
  <div class="cartao semborda">
    <div class="row">
      <div class="col-12" [ngClass]="{'col-md-8': empresa.instagram}" >
        <div class="menu" *ngIf="empresa.destaques && empresa.destaques.length > 0">
          <h4>
            {{empresa.tituloDestaques}}
          </h4>

          <div class="container-scroll" *ngIf="carregou">
            <div class="row flex-row flex-nowrap flex-md-wrap">
              <div class="col-8 " [ngClass]="{'col-md-4': empresa.instagram, 'col-md-3': !empresa.instagram}"  *ngFor="let destaque of empresa.destaques"  style="height: 300px;" >
                <div class="brinde linha" >
                  <div class="row h-100 justify-content-center align-items-center caixa_brinde">
                    <div class="nome_brinde_pontos">{{destaque.nome}}</div>
                    <div class="container_imagem" (click)="exibaProdutoSelecionado(destaque)">
                      <div class="" *ngIf="destaque.imagens && destaque.imagens.length > 0">
                        <img class="foto_brinde" src="https://www.promokit.com.br/images/empresa/{{destaque.imagens[0].linkImagem}}">
                      </div>
                    </div>
                    <div class="preco_troca">
                      <h3 class="mt-1" *ngIf="destaque.exibirPrecoNoSite && destaque.preco > 0.0">
                        R$ {{destaque.preco.toFixed(2).replace('.', ',')}}
                      </h3>
                      <h4 class="mt-1" *ngIf="!destaque.exibirPrecoNoSite || destaque.preco == 0.0">
                        Super Oferta
                      </h4>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="menu pt-3" *ngIf="empresa.ambiente && empresa.ambiente.length > 0">
          <h4>
            {{empresa.tituloFotos}}
          </h4>

          <div class="container-scroll" *ngIf="carregou">
            <div class="row flex-row flex-nowrap flex-md-wrap">
              <div class="col-8" *ngFor="let foto of empresa.ambiente; let i = index;" [ngClass]="{'col-md-4': empresa.instagram, 'col-md-3': !empresa.instagram}">
                <div class="brinde linha" >

                  <div class="row h-100 caixa_brinde justify-content-center">
                    <div class="container_imagem justify-content-center align-items-center"  style="height: 180px;">
                      <div class="">
                        <img class="foto_ambiente img-fluid" src="https://www.promokit.com.br/images/empresa/{{foto.link}}" (click)="exibirSlide(i)">
                      </div>
                    </div>
                    <div class="preco_troca">
                      <h4 class="mt-1">
                        <div class="nome_brinde_pontos">{{foto.titulo}}</div>
                      </h4>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12 col-md-4" *ngIf="empresa.instagram">
        <div class="menu">
          <h4>
            Mais sobre nós
          </h4>
          <div class="sobre_nos">
            <div class="pb-1 linha">
              <iframe [src]="urlMaps" frameborder="0" style="border:0; width: 100%; height: 100%" allowfullscreen></iframe>
            </div>
            <div class="linha">
              <div class="row mt-1 mb-1">
                <div *ngFor="let post of postsInstagram" class="col-6 p-0">
                  <a href="{{post.link}}" target="_blank"><img class="img-fluid p-1" src="{{post.imagem}}" alt="Foto instagram"/></a>
                </div>
              </div>
            </div>
            <div class="mt-1 mb-1 text-center cinza">
              <a href="https://instagram.com/{{empresa.instagram}}" target="blank">
                <div class="icone insta tam1" [inlineSVG]="'/assets/fidelidade/icones/instagram.svg'" [removeSVGAttributes]="['fill']" style="display: inline-block"></div>
                Veja mais no Instagram

              </a>
            </div>

          </div>


        </div>

      </div>
    </div>

  </div>
</div>
<div [hidden]="!carregou || !exibirSlideShow" class="slides-fotos">
  <div *ngIf="carregou">

    <div class="icon-fechar" [inlineSVG]="'/assets/fidelidade/icones/icon-fechar.svg'" [removeSVGAttributes]="['width', 'height']" style="width: 32px; height: auto" (click)="fecharSlides()" ></div>
    <h3>{{empresa.tituloFotos}}</h3>
  </div>
  <div *ngIf="carregou" class="cartao descricao">
    <kendo-scrollview
      [data]="fotosDaEmpresa"
      style="background: #000;text-align: center;"
      [width]="'100%'"
      [endless]="true"
      [height]="alturaSlide"
      [arrows]="true"
      [pageable]="true">
      <ng-template let-item="item">
        <h2 class="demo-title">{{item.titulo}}</h2>
        <img class="foto-brinde img-fluid" src='{{item.url}}' alt='{{item.titulo}}' [ngStyle]="{minWidth: 400}" draggable="false" />
      </ng-template>
    </kendo-scrollview>
  </div>
</div>

<div *ngIf="carregou && produtoSelecionado" class="slides-produtos">
  <div class="container-itens">
    <div class="cartao descricao">
      <div class="icon-fechar" [inlineSVG]="'/assets/fidelidade/icones/icon-fechar.svg'" [removeSVGAttributes]="['width', 'height']" style="width: 32px; height: auto" (click)="fecharSlidesItens()" ></div>
      <h3>Detalhes do Item</h3>
      <DIV class="linha" *ngIf="produtoSelecionado.imagens && produtoSelecionado.imagens.length > 0">
        <img class="foto_brinde" src="/images/empresa/{{produtoSelecionado.imagens[0].linkImagem}}">
      </DIV>
      <h4>
        {{produtoSelecionado.nome}}
      </h4>
      <span>{{produtoSelecionado.descricao}}</span>
      <span class="preco_troca azul grande mt-1" style="display: block" *ngIf="produtoSelecionado.exibirPrecoNoSite && produtoSelecionado.preco > 0.0">
        {{produtoSelecionado.preco | currency:"BRL"}}
      </span>
      <span class="preco_troca azul grande mt-1" style="display: block" *ngIf="!produtoSelecionado.exibirPrecoNoSite || produtoSelecionado.preco == 0.0">
        Super Oferta
      </span>
      <a [href]="'http://wa.me/55' + empresa?.whatsapp + '?text=' + produtoSelecionado.mensagemPedido">
        <div class="botao_produto verde mt-3">
          <img class="icone tam1" src='/assets/fidelidade/icones/icon-whatsapp-mini.png'> Pedir pelo WhatsApp
        </div>
      </a>
    </div>
  </div>
</div>
