import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { ActivatedRoute, Router } from '@angular/router';
import { InstagramDataService } from '../services/instagram-data.service';
import { LeadService } from '../services/lead.service';
import { CrmEmpresaService } from '../services/crm-empresa.service';
// import Lead, { ConcorrenteLead } from '../../../server/domain/crm/Lead'; // Removido - frontend não deve referenciar objetos de domínio

@Component({
  selector: 'app-novo-lead',
  templateUrl: './novo-lead.component.html',
  styleUrls: ['./novo-lead.component.scss'],
  animations: [
    trigger('slideDown', [
      state('closed', style({
        height: '0px',
        opacity: 0,
        overflow: 'hidden'
      })),
      state('open', style({
        height: '*',
        opacity: 1,
        overflow: 'visible'
      })),
      transition('closed => open', [
        animate('300ms ease-in-out')
      ]),
      transition('open => closed', [
        animate('300ms ease-in-out')
      ])
    ])
  ]
})
export class NovoLeadComponent implements OnInit {

  // Dados do Instagram
  dadosInstagram: any = null;
  username: string = '';

  // Lead processado pela API dadosig2
  leadProcessadoAPI: any = null;

  // Estado do componente
  carregando = false;
  carregandoWebsite = false;
  carregandoCnpj = false;
  erro = '';
  mostrarFormulario = false;
  sincronizarBitrix = false;


  // Links encontrados do website
  linksEncontrados: any[] = [];

  // Telefones múltiplos
  telefonesEncontrados: any[] = [];

  // CNPJs encontrados
  cnpjsEncontrados: any[] = [];

  // Sócios detalhados
  sociosDetalhados: any[] = [];
  carregandoSocios = false;
  sociosBuscados = false;

  // CNPJ manual
  cnpjManual = '';
  carregandoCnpjManual = false;

  // Wizard state
  currentStep = 1;
  totalSteps = 5;
  wizardData = {
    dadosBasicos: {},
    linksEncontrados: [],
    cnpjSelecionado: null,
    sociosEncontrados: [],
    configuracoes: {}
  };

  // Controle de validação das etapas
  websiteAnalisado = false;
  cnpjBuscado = false;
  etapaFoiPulada = false;
  cnpjRecusadoExplicitamente = false; // Usuário confirmou que não quer escolher nenhum CNPJ
  parceirSelecionado = false; // Usuário selecionou um sócio como contato principal

  // Dados do formulário
  lead: any = {
    nomeResponsavel: '',
    empresa: '',
    cidade: '',
    endereco: '',
    cnpj: '',
    telefone: '',
    instagramHandle: '',
    website: '',
    linkCardapio: '',
    bioInsta: '',
    biografia: '',
    observacoes: '',
    observacoesSocios: '',
    origem: 'Instagram',
    etapa: 'Prospecção',
    segmento: 'Alimentação',
    concorrente: '',
    crmEmpresaId: null
  };

  // Lista de empresas CRM para seleção
  crmEmpresas: any[] = [];

  // Campo formatado do telefone (para exibição)
  telefoneFormatado: string = '';

  // Razão social do CNPJ selecionado (para exibição no resumo)
  razaoSocialSelecionada: string = '';

  // Nome do sócio manual
  nomeSocioManual: string = '';

  // Propriedade computed para busca no Google
  get searchTerm(): string {
    const empresa = this.lead?.empresa?.trim() || '';
    const cidade = this.lead?.cidade?.trim() || '';

    if (empresa && cidade) {
      return `${empresa} - ${cidade}`;
    } else if (empresa) {
      return empresa;
    } else {
      return '';
    }
  }

  set searchTerm(value: string) {
    if (!value) {
      this.lead.empresa = '';
      this.lead.cidade = '';
      return;
    }

    // Se contém " - ", divide em empresa e cidade
    if (value.includes(' - ')) {
      const parts = value.split(' - ');
      this.lead.empresa = parts[0].trim();
      this.lead.cidade = parts.slice(1).join(' - ').trim(); // Em caso de múltiplos " - "
    } else {
      // Se não contém " - ", considera tudo como empresa
      this.lead.empresa = value.trim();
      // Mantém a cidade atual se já existir
    }
  }

  // Opções para dropdowns
  etapas = [
    { valor: 'Prospecção', texto: 'Prospecção' },
    { valor: 'Qualificação', texto: 'Qualificação' },
    { valor: 'Objeção', texto: 'Objeção' },
    { valor: 'Fechamento', texto: 'Fechamento' },
    { valor: 'Ganho', texto: 'Ganho' },
    { valor: 'Perdido', texto: 'Perdido' }
  ];

  origens = [
    { valor: 'Instagram', texto: 'Instagram' },
    { valor: 'Site/Landing Page', texto: 'Site/Landing Page' },
    { valor: 'WhatsApp Direto', texto: 'WhatsApp Direto' },
    { valor: 'Indicação', texto: 'Indicação' },
    { valor: 'Evento/Feira', texto: 'Evento/Feira' },
    { valor: 'Outros', texto: 'Outros' }
  ];

  segmentos = [
    { valor: 'Alimentação', texto: 'Alimentação' },
    { valor: 'Varejo', texto: 'Varejo' },
    { valor: 'Serviços', texto: 'Serviços' },
    { valor: 'Saúde', texto: 'Saúde' },
    { valor: 'Educação', texto: 'Educação' },
    { valor: 'Outros', texto: 'Outros' }
  ];

  tiposTelefone = [
    { valor: 'WhatsApp', texto: 'WhatsApp', icone: 'fa-whatsapp', cor: '#25d366' },
    { valor: 'Telefone Fixo', texto: 'Telefone Fixo', icone: 'fa-phone', cor: '#6c757d' },
    { valor: 'Celular', texto: 'Celular', icone: 'fa-mobile-alt', cor: '#007bff' },
    { valor: 'Comercial', texto: 'Comercial', icone: 'fa-briefcase', cor: '#28a745' },
    { valor: 'Emergência', texto: 'Emergência', icone: 'fa-exclamation-triangle', cor: '#dc3545' }
  ];

  concorrentes = [
    { valor: 'Não descobri', texto: 'Não descobri' },
    { valor: 'Não tem sistema', texto: 'Não tem sistema' },
    { valor: 'Accon', texto: 'Accon' },
    { valor: 'Amo Delivery', texto: 'Amo Delivery' },
    { valor: 'Anota Ai', texto: 'Anota Ai' },
    { valor: 'App Para Delivery', texto: 'App Para Delivery' },
    { valor: 'Beetech', texto: 'Beetech' },
    { valor: 'Bigdim', texto: 'Bigdim' },
    { valor: 'By App Food', texto: 'By App Food' },
    { valor: 'By Food', texto: 'By Food' },
    { valor: 'Cardapio.co', texto: 'Cardapio.co' },
    { valor: 'Cardápio Fácil', texto: 'Cardápio Fácil' },
    { valor: 'Cardápio Pronto', texto: 'Cardápio Pronto' },
    { valor: 'Cardapioweb', texto: 'Cardapioweb' },
    { valor: 'Cardapius', texto: 'Cardapius' },
    { valor: 'CCMPedidoOnline', texto: 'CCMPedidoOnline' },
    { valor: 'Cinndi', texto: 'Cinndi' },
    { valor: 'Delivery Direto', texto: 'Delivery Direto' },
    { valor: 'Delivery Seguro', texto: 'Delivery Seguro' },
    { valor: 'Delyver', texto: 'Delyver' },
    { valor: 'Ecta', texto: 'Ecta' },
    { valor: 'Eita.delivery', texto: 'Eita.delivery' },
    { valor: 'Expresso Delivery', texto: 'Expresso Delivery' },
    { valor: 'Expresso Menu', texto: 'Expresso Menu' },
    { valor: 'Glow Delivery', texto: 'Glow Delivery' },
    { valor: 'Go2Go Solutions', texto: 'Go2Go Solutions' },
    { valor: 'GoEntrega', texto: 'GoEntrega' },
    { valor: 'Goomer Gratuito', texto: 'Goomer Gratuito' },
    { valor: 'Goomer Pago', texto: 'Goomer Pago' },
    { valor: 'Grand Chef', texto: 'Grand Chef' },
    { valor: 'Instabuy', texto: 'Instabuy' },
    { valor: 'Instadelivery', texto: 'Instadelivery' },
    { valor: 'Jotaja', texto: 'Jotaja' },
    { valor: 'Kyte', texto: 'Kyte' },
    { valor: 'Kuppi', texto: 'Kuppi' },
    { valor: 'LadDelivery', texto: 'LadDelivery' },
    { valor: 'Magnata App', texto: 'Magnata App' },
    { valor: 'MenuIntegrado', texto: 'MenuIntegrado' },
    { valor: 'Menuvem', texto: 'Menuvem' },
    { valor: 'Meu Cardápio Digital', texto: 'Meu Cardápio Digital' },
    { valor: 'MeuPedido', texto: 'MeuPedido' },
    { valor: 'Menap', texto: 'Menap' },
    { valor: 'Menu Dino', texto: 'Menu Dino' },
    { valor: 'Menu Ifood', texto: 'Menu Ifood' },
    { valor: 'Miller Delivery', texto: 'Miller Delivery' },
    { valor: 'Neemo', texto: 'Neemo' },
    { valor: 'Ola Click', texto: 'Ola Click' },
    { valor: 'Pedefacil UOL', texto: 'Pedefacil UOL' },
    { valor: 'Pediaki', texto: 'Pediaki' },
    { valor: 'Pedir Delivery', texto: 'Pedir Delivery' },
    { valor: 'Pedir Online', texto: 'Pedir Online' },
    { valor: 'Pedyun', texto: 'Pedyun' },
    { valor: 'PedZap', texto: 'PedZap' },
    { valor: 'PodePedir', texto: 'PodePedir' },
    { valor: 'PopSales', texto: 'PopSales' },
    { valor: 'Prefiro Delivery', texto: 'Prefiro Delivery' },
    { valor: 'Rvpedidos', texto: 'Rvpedidos' },
    { valor: 'Saipos Cardápio', texto: 'Saipos Cardápio' },
    { valor: 'StayApp', texto: 'StayApp' },
    { valor: 'StarFood', texto: 'StarFood' },
    { valor: 'Vtto', texto: 'Vtto' },
    { valor: 'Zappedis', texto: 'Zappedis' },
    { valor: 'Wabiz', texto: 'Wabiz' },
    { valor: 'Webcardapio', texto: 'Webcardapio' },
    { valor: 'Whats Menu', texto: 'Whats Menu' },
    { valor: 'Alloy', texto: 'Alloy' },
    { valor: 'Cardápio Digital Totvs', texto: 'Cardápio Digital Totvs' },
    { valor: 'Pedir Agora', texto: 'Pedir Agora' },
    { valor: 'StiloWeb Delivery', texto: 'StiloWeb Delivery' },
    { valor: 'Tuigo Eats', texto: 'Tuigo Eats' },
    { valor: 'Hubt', texto: 'Hubt' }
  ];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private instagramDataService: InstagramDataService,
    private leadService: LeadService,
    private crmEmpresaService: CrmEmpresaService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    // DEBUG: Log initial state
    console.log('🐛 [ngOnInit] Starting - lead.empresa:', this.lead.empresa, 'Type:', typeof this.lead.empresa);

    // Pega o username da URL
    this.route.queryParams.subscribe(params => {
      this.username = params['username'] || '';
      console.log('Username da URL:', this.username);
    });

    // Recupera dados do Instagram do service
    const dadosService = this.instagramDataService.getDados();
    if (dadosService) {
      this.dadosInstagram = dadosService.dados;
      this.username = dadosService.username;
      this.preencherFormularioComDadosInstagram();
    } else {
      console.warn('Nenhum dado do Instagram encontrado no service');

      // Se tem username, configura listener mas NÃO busca automaticamente
      if (this.username) {
        console.log('Username presente, aguardando ação do usuário para buscar dados');
        this.setupInstagramDataListener();
      } else {
        // Se não tem username, redireciona para home
        this.router.navigate(['/crm/home']);
      }
    }

    // Carrega lista de empresas CRM
    this.carregarCrmEmpresas();

    // Inicializa campo de telefone formatado se existir
    if (this.lead.telefone) {
      this.telefoneFormatado = this.formatarTelefone(this.lead.telefone);
    }

    // DEBUG: Log final state
    console.log('🐛 [ngOnInit] Finished - lead.empresa:', this.lead.empresa, 'Type:', typeof this.lead.empresa);
  }

  /**
   * Preenche o formulário com dados do Instagram
   */
  private preencherFormularioComDadosInstagram(): void {
    if (!this.dadosInstagram || !this.dadosInstagram.user) {
      console.warn('Dados do Instagram não encontrados ou estrutura incorreta:', this.dadosInstagram);
      return;
    }

    const user = this.dadosInstagram.user;

    this.lead.nomeResponsavel = user.full_name || user.username || '';
    this.lead.empresa = user.full_name || user.username || '';
    this.lead.instagramHandle = user.username || '';
    this.lead.website = user.external_url || '';
    this.lead.bioInsta = user.biography || '';
    this.lead.biografia = user.biography || '';
    this.lead.telefone = user.business_phone_number || '';
    
    // Atualiza telefone formatado
    if (this.lead.telefone) {
      this.telefoneFormatado = this.formatarTelefone(this.lead.telefone);
    }

    console.log('Formulário preenchido com dados do Instagram:', this.lead);

    // DEBUG: Log empresa change
    console.log('🐛 [preencherFormularioComDadosInstagram] lead.empresa set to:', this.lead.empresa, 'Type:', typeof this.lead.empresa);
  }

  /**
   * Preenche o formulário com dados do lead processado pela API
   * Agora recebe um objeto Lead completo com CrmEmpresa
   */
  private preencherFormularioComLeadProcessado(leadProcessado: any): void {
    if (!leadProcessado) {
      console.warn('Lead processado não encontrado:', leadProcessado);
      return;
    }

    console.log('Preenchendo formulário com lead processado (objeto Lead completo):', leadProcessado);

    // Armazena o lead processado para uso posterior
    this.leadProcessadoAPI = leadProcessado;

    // Preenche campos do formulário com dados do Lead
    this.lead.nomeResponsavel = leadProcessado.nomeResponsavel || '';
    this.lead.empresa = leadProcessado.empresa || '';
    this.lead.cidade = leadProcessado.cidade || '';
    this.lead.endereco = leadProcessado.endereco || '';
    this.lead.telefone = leadProcessado.telefone || '';
    
    // Atualiza telefone formatado
    if (this.lead.telefone) {
      this.telefoneFormatado = this.formatarTelefone(this.lead.telefone);
    }

    // DEBUG: Log empresa change
    console.log('🐛 [preencherFormularioComLeadProcessado] lead.empresa set to:', this.lead.empresa, 'Type:', typeof this.lead.empresa);

    // Log de endereço extraído
    if (leadProcessado.endereco) {
      console.log('Endereço extraído e preenchido no formulário:', leadProcessado.endereco);
    }
    this.lead.instagramHandle = leadProcessado.instagramHandle || '';
    this.lead.website = leadProcessado.website || (leadProcessado.instagramData?.website || '');
    this.lead.bioInsta = leadProcessado.bioInsta || '';
    this.lead.biografia = leadProcessado.bioInsta || '';
    this.lead.origem = leadProcessado.origem || 'Instagram';
    this.lead.etapa = leadProcessado.etapa || 'Prospecção';
    this.lead.score = leadProcessado.score || 0;

    // Mapear segmento baseado na categoria do negócio
    if (leadProcessado.instagramData?.businessCategory) {
      this.lead.segmento = this.mapearSegmento(leadProcessado.instagramData.businessCategory);
    } else {
      this.lead.segmento = 'Alimentação'; // padrão
    }

    // Define empresa CRM se disponível
    if (leadProcessado.crmEmpresaId) {
      this.lead.crmEmpresaId = leadProcessado.crmEmpresaId;
    }

    // Processa telefones múltiplos se disponíveis
    if (leadProcessado.telefones && Array.isArray(leadProcessado.telefones)) {
      this.telefonesEncontrados = leadProcessado.telefones.map((telefone: any, index: number) => ({
        id: `temp_${index}`,
        tipo: telefone.tipo,
        numero: telefone.numero,
        descricao: telefone.descricao || '',
        numeroFormatado: this.formatarTelefone(telefone.numero),
        icone: this.getIconeTelefone(telefone.tipo),
        cor: this.getCorTelefone(telefone.tipo)
      }));
      console.log('Telefones processados no frontend:', this.telefonesEncontrados);
    }

    // Processa links múltiplos se disponíveis
    if (leadProcessado.links && Array.isArray(leadProcessado.links)) {
      this.linksEncontrados = leadProcessado.links.map((link: any, index: number) => ({
        id: link.id || `temp_${index}`,
        tipo: link.tipo,
        url: link.url,
        descricao: link.descricao || '',
        ordem: link.ordem || index + 1,
        ativo: link.ativo !== false
      }));
      console.log('Links processados no frontend:', this.linksEncontrados);
      
      // Atualizar wizard data para manter consistência
      this.wizardData.linksEncontrados = [...this.linksEncontrados];
      
      // Marcar que já tem links analisados para pular análise automática
      this.websiteAnalisado = true;
      
      console.log('Total de links carregados do backend:', this.linksEncontrados.length);
    }

    // Adiciona informações extras do Instagram nas notas se não estiverem presentes
    if (leadProcessado.notas) {
      this.lead.observacoes = leadProcessado.notas;
    }

    // Simula dados do Instagram para o template (se necessário)
    if (leadProcessado.instagramData) {
      this.dadosInstagram = {
        user: {
          username: leadProcessado.instagramHandle,
          full_name: leadProcessado.empresa,
          biography: leadProcessado.bioInsta,
          business_phone_number: leadProcessado.telefone,
          edge_followed_by: { count: leadProcessado.instagramData.followers },
          edge_follow: { count: leadProcessado.instagramData.following },
          is_business_account: leadProcessado.instagramData.accountType === 'Business',
          business_category_name: leadProcessado.instagramData.businessCategory,
          external_url: leadProcessado.instagramData.website,
          profile_pic_url: leadProcessado.avatarUrl
        }
      };
    }

    console.log('Formulário preenchido com lead processado:', this.lead);
    console.log('Dados do Instagram simulados para template:', this.dadosInstagram);
  }

  /**
   * Mapeia categoria de negócio para segmento
   */
  private mapearSegmento(categoria: string): string {
    if (!categoria) return 'Outros';

    const categoriaLower = categoria.toLowerCase();

    if (categoriaLower.includes('restaurante') ||
        categoriaLower.includes('comida') ||
        categoriaLower.includes('food') ||
        categoriaLower.includes('pizza') ||
        categoriaLower.includes('lanche') ||
        categoriaLower.includes('café') ||
        categoriaLower.includes('bar') ||
        categoriaLower.includes('japonês') ||
        categoriaLower.includes('delivery')) {
      return 'Alimentação';
    }

    if (categoriaLower.includes('loja') ||
        categoriaLower.includes('varejo') ||
        categoriaLower.includes('shop')) {
      return 'Varejo';
    }

    if (categoriaLower.includes('serviço') ||
        categoriaLower.includes('service')) {
      return 'Serviços';
    }

    if (categoriaLower.includes('saúde') ||
        categoriaLower.includes('health') ||
        categoriaLower.includes('médico') ||
        categoriaLower.includes('clínica')) {
      return 'Saúde';
    }

    if (categoriaLower.includes('educação') ||
        categoriaLower.includes('education') ||
        categoriaLower.includes('escola') ||
        categoriaLower.includes('curso')) {
      return 'Educação';
    }

    return 'Outros';
  }

  /**
   * Carrega lista de empresas CRM
   */
  private async carregarCrmEmpresas(): Promise<void> {
    try {
      const response = await this.crmEmpresaService.liste();
      if (response.sucesso) {
        this.crmEmpresas = response.dados || [];
      }
    } catch (error) {
      console.error('Erro ao carregar empresas CRM:', error);
    }
  }

  /**
   * Salva o novo lead
   */
  async salvarLead(): Promise<void> {
    if (!this.validarFormulario()) {
      return;
    }

    this.carregando = true;
    this.erro = '';

    // Garante que o telefone está limpo antes de salvar
    if (this.telefoneFormatado) {
      this.lead.telefone = this.limparTelefone(this.telefoneFormatado);
    }

    try {
      // Se temos um lead processado pela API dadosig2, usa ele como base
      let leadParaSalvar: any;

      if (this.leadProcessadoAPI) {
        // Usa o lead processado pela API como base e aplica modificações do formulário
        leadParaSalvar = { ...this.leadProcessadoAPI };

        // Atualiza com dados modificados no formulário
        leadParaSalvar.nomeResponsavel = this.lead.nomeResponsavel;
        leadParaSalvar.empresa = this.lead.empresa;
        leadParaSalvar.cidade = this.lead.cidade;
        leadParaSalvar.endereco = this.lead.endereco;
        leadParaSalvar.telefone = this.lead.telefone;
        leadParaSalvar.instagramHandle = this.lead.instagramHandle;
        leadParaSalvar.website = this.lead.website;
        leadParaSalvar.linkCardapio = this.lead.linkCardapio;
        leadParaSalvar.bioInsta = this.lead.bioInsta;
        leadParaSalvar.origem = this.lead.origem;
        leadParaSalvar.etapa = this.lead.etapa;
        leadParaSalvar.segmento = this.lead.segmento;
        leadParaSalvar.crmEmpresaId = this.lead.crmEmpresaId;
        leadParaSalvar.observacoes = this.lead.observacoes;
        leadParaSalvar.observacoesSocios = this.lead.observacoesSocios;
        leadParaSalvar.concorrente = this.lead.concorrente;

        // Remove campos que não devem ser enviados na criação
        delete leadParaSalvar.id;
        delete leadParaSalvar.dataCriacao;
        delete leadParaSalvar.createdAt;
        delete leadParaSalvar.updatedAt;

        // Adiciona links categorizados se existirem
        if (this.linksEncontrados && this.linksEncontrados.length > 0) {
          leadParaSalvar.links = this.linksEncontrados;
          console.log('Adicionando links categorizados ao lead processado:', this.linksEncontrados);
        }

        // Adiciona telefones múltiplos se existirem
        if (this.telefonesEncontrados && this.telefonesEncontrados.length > 0) {
          leadParaSalvar.telefones = this.telefonesEncontrados.map(tel => ({
            tipo: tel.tipo,
            numero: tel.numero,
            descricao: tel.descricao
          }));
          console.log('Adicionando telefones ao lead processado:', leadParaSalvar.telefones);
        }

        // Adiciona sócios detalhados se existirem
        if (this.sociosDetalhados && this.sociosDetalhados.length > 0) {
          leadParaSalvar.sociosDetalhados = this.sociosDetalhados;
          console.log('Adicionando sócios detalhados ao lead:', this.sociosDetalhados.length);
        }

      } else {
        // Fallback: cria lead do zero (modo manual)
        leadParaSalvar = { ...this.lead };

        // Adiciona dados do Instagram de forma estruturada (se disponível)
        if (this.dadosInstagram && this.dadosInstagram.user) {
          const user = this.dadosInstagram.user;

          // Dados básicos
          leadParaSalvar.avatarUrl = user.profile_pic_url;

          // Estrutura instagramData
          leadParaSalvar.instagramData = {
            bio: user.biography,
            followers: user.edge_followed_by?.count,
            following: user.edge_follow?.count,
            accountType: user.is_business_account ? 'Business' : 'Pessoal',
            businessCategory: user.business_category_name || user.category_name,
            location: user.business_address_json ? JSON.stringify(user.business_address_json) : undefined,
            website: user.external_url
          };
        }

        // Adiciona links categorizados se existirem (modo manual)
        if (this.linksEncontrados && this.linksEncontrados.length > 0) {
          leadParaSalvar.links = this.linksEncontrados;
          console.log('Adicionando links categorizados ao lead manual:', this.linksEncontrados);
        }

        // Adiciona telefones múltiplos se existirem (modo manual)
        if (this.telefonesEncontrados && this.telefonesEncontrados.length > 0) {
          leadParaSalvar.telefones = this.telefonesEncontrados.map(tel => ({
            tipo: tel.tipo,
            numero: tel.numero,
            descricao: tel.descricao
          }));
          console.log('Adicionando telefones ao lead manual:', leadParaSalvar.telefones);
        }

        // Adiciona sócios detalhados se existirem (modo manual)
        if (this.sociosDetalhados && this.sociosDetalhados.length > 0) {
          leadParaSalvar.sociosDetalhados = this.sociosDetalhados;
          console.log('Adicionando sócios detalhados ao lead manual:', this.sociosDetalhados.length);
        }
      }

      console.log('Salvando lead:', leadParaSalvar);
      console.log('Sincronizar com Bitrix:', this.sincronizarBitrix);
      console.log('Total de links categorizados a serem enviados:', leadParaSalvar.links?.length || 0);

      if (leadParaSalvar.links && leadParaSalvar.links.length > 0) {
        console.log('Links categorizados detalhados:');
        leadParaSalvar.links.forEach((link: any, index: number) => {
          console.log(`  ${index + 1}. ${link.tipo}: ${link.url} (${link.descricao})`);
        });
      }

      // Adicionar flag de sincronização no objeto a ser salvo
      if (this.sincronizarBitrix) {
        leadParaSalvar.sincronizarBitrix = true;
      }

      const leadCriado = await this.leadService.salveLead(leadParaSalvar);

      console.log('Lead criado com sucesso:', leadCriado);

      // Limpa dados do service
      this.instagramDataService.clearDados();

      // Redireciona para a home do lead criado
      this.router.navigate(['/crm/home', this.username]);
    } catch (error) {
      console.error('Erro ao salvar lead:', error);
      this.erro = 'Erro ao criar lead. Tente novamente.';
    } finally {
      this.carregando = false;
    }
  }

  /**
   * Valida o formulário
   */
  private validarFormulario(): boolean {
    if (!this.lead.nomeResponsavel) {
      this.erro = 'Nome do responsável é obrigatório';
      return false;
    }
    if (!this.lead.empresa) {
      this.erro = 'Nome da empresa é obrigatório';
      return false;
    }
    if (!this.lead.cidade) {
      this.erro = 'Cidade da empresa é obrigatória';
      return false;
    }
    if (!this.lead.instagramHandle) {
      this.erro = 'Username do Instagram é obrigatório';
      return false;
    }
    return true;
  }

  /**
   * Cancela e volta para a home
   */
  cancelar(): void {
    this.instagramDataService.clearDados();
    this.router.navigate(['/crm/home', this.username]);
  }

  /**
   * Retorna a URL da foto do perfil do Instagram
   */
  getFotoPerfilInstagram(): string {
    if (this.dadosInstagram?.user?.profile_pic_url) {
      return this.dadosInstagram.user.profile_pic_url;
    }
    return '/assets/images/default-avatar.png'; // fallback
  }

  /**
   * Retorna o número de seguidores formatado
   */
  getSeguidoresFormatado(): string {
    const count = this.dadosInstagram?.user?.edge_followed_by?.count;
    if (!count) return '0';

    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  }

  /**
   * Solicita dados do Instagram via content script
   */
  solicitarDadosInstagram(): void {
    // Enviar mensagem para content script via iframe
    const message = {
      tipo: 'REQUEST_INSTAGRAM_DATA',
      username: this.username
    };

    // Comunicação via postMessage para parent window (content script)
    if (window.parent && window.parent !== window) {
      window.parent.postMessage({tipo: "NOVA_MENSAGEM", text: message}, "*");
    }

    console.log('Solicitação de dados enviada para content script:', message);
  }

  /**
   * Configura listener para eventos do Instagram
   */
  private setupInstagramDataListener(): void {
    window.addEventListener('message', (event) => {
      // Verifica se é um evento de resposta do Instagram
      if (event.data && event.data.tipo === 'INSTAGRAM_DATA_RESPONSE') {
        console.log('Dados do Instagram recebidos:', event.data);
        this.processarDadosInstagram(event.data);
      }

      // Verifica se houve erro na busca
      if (event.data && event.data.tipo === 'INSTAGRAM_DATA_ERROR') {
        console.error('Erro ao buscar dados do Instagram:', event.data);
        this.processarErroInstagram(event.data);
      }
    });
  }

  /**
   * Processa os dados recebidos do Instagram
   */
  private async processarDadosInstagram(dadosInstagram: any): Promise<void> {
    console.log('Processando dados do Instagram para username:', dadosInstagram.username);

    try {
      // Primeiro, envia texto do Instagram para a API dadosig2 para processamento
      console.log('Enviando texto do Instagram para API dadosig2...');
      this.carregando = true;

      const leadProcessado = await this.leadService.enviarDadosInstagram(dadosInstagram.textoInsta, null, dadosInstagram.username);

      console.log('Lead processado pela API dadosig2:', leadProcessado);

      if (leadProcessado) {
        // ServerService já extraiu o 'dados', então leadProcessado é o objeto Lead completo
        console.log('Objeto Lead processado com sucesso:', leadProcessado);

        // Define username
        this.username = dadosInstagram.username;

        // Preenche formulário com dados do lead processado pela API
        // A função preencherFormularioComLeadProcessado agora simula dadosInstagram para o template
        this.preencherFormularioComLeadProcessado(leadProcessado);

        // Salva dados do Instagram no service (se necessário para outras funcionalidades)
        this.instagramDataService.setDados(this.dadosInstagram, this.username);

        console.log('Lead processado e formulário preenchido:', this.lead);
        console.log('Dados simulados para template:', this.dadosInstagram);
      } else {
        console.error('Erro: resposta vazia da API dadosig2');
        this.erro = 'Erro ao processar dados do Instagram na API.';
      }
    } catch (error) {
      console.error('Erro ao enviar dados para API dadosig2:', error);
      this.erro = 'Erro ao processar dados do Instagram. Tente novamente.';
    } finally {
      this.carregando = false;
    }
  }

  /**
   * Envia dados do Instagram para a API (caso necessário futuramente)
   */
  private async enviarDadosInstagramParaAPI(dadosInstagram: any): Promise<void> {
    try {
      console.log('Enviando dados do Instagram para API:', dadosInstagram.data);

      const response = await this.leadService.enviarDadosInstagram(dadosInstagram.data, null, dadosInstagram.username);

      if (response.sucesso) {
        console.log('Lead criado/atualizado com sucesso:', response.dados);
      } else {
        console.error('Erro na resposta da API:', response.erro);
      }
    } catch (error) {
      console.error('Erro ao enviar dados do Instagram para API:', error);
    }
  }

  /**
   * Processa erros na busca de dados do Instagram
   */
  private processarErroInstagram(errorData: any): void {
    console.error('Erro ao buscar dados do Instagram:', errorData.error);
    this.erro = 'Erro ao buscar dados do Instagram. Tente novamente.';
  }

  /**
   * Exibe o formulário para criação manual de lead
   */
  mostrarFormularioManual(): void {
    this.mostrarFormulario = true;
    console.log('Formulário manual ativado');
  }

  /**
   * Normaliza URL do website quando o usuário sai do campo
   */
  onWebsiteBlur(): void {
    if (this.lead.website) {
      const urlOriginal = this.lead.website;
      this.lead.website = this.normalizarUrl(this.lead.website);

      if (urlOriginal !== this.lead.website) {
        console.log('URL normalizada:', urlOriginal, '->', this.lead.website);
      }
    }
  }

  /**
   * Processa o telefone formatado quando o usuário sai do campo
   */
  onTelefoneBlur(): void {
    // Remove a formatação e armazena apenas os números no objeto lead
    this.lead.telefone = this.limparTelefone(this.telefoneFormatado);
    console.log('Telefone formatado:', this.telefoneFormatado, '-> Limpo:', this.lead.telefone);
  }

  /**
   * Processa mudanças no campo de telefone em tempo real
   */
  onTelefoneChange(value: string): void {
    // Atualiza o valor limpo do telefone enquanto o usuário digita
    this.lead.telefone = this.limparTelefone(value || '');
  }

  /**
   * Processa quando o usuário cola um telefone
   */
  onTelefonePaste(event: ClipboardEvent): void {
    event.preventDefault();
    
    const clipboardData = event.clipboardData?.getData('text') || '';
    console.log('Telefone colado:', clipboardData);
    
    // Processa o número removendo código do país se necessário
    const numeroProcessado = this.processarTelefoneColado(clipboardData);
    
    // Define o valor limpo e formatado
    this.lead.telefone = numeroProcessado;
    this.telefoneFormatado = this.formatarTelefone(numeroProcessado);
    
    console.log('Telefone processado:', this.telefoneFormatado);
    
    // Força atualização
    this.cdr.detectChanges();
  }

  /**
   * Remove formatação do telefone mantendo apenas números
   */
  private limparTelefone(telefone: string): string {
    if (!telefone) return '';
    return telefone.replace(/\D/g, '');
  }

  /**
   * Processa telefone colado removendo código do país se necessário
   */
  private processarTelefoneColado(valor: string): string {
    // Remove tudo que não é número
    let numeroLimpo = valor.replace(/\D/g, '');
    
    // Se tem código do país do Brasil (55) no início, remove
    if (numeroLimpo.startsWith('55') && numeroLimpo.length > 11) {
      numeroLimpo = numeroLimpo.substring(2);
      console.log('Removido código do país 55, telefone:', numeroLimpo);
    }
    
    // Se ainda tem mais de 11 dígitos, pega apenas os 11 últimos
    if (numeroLimpo.length > 11) {
      numeroLimpo = numeroLimpo.substring(numeroLimpo.length - 11);
      console.log('Telefone tinha mais de 11 dígitos, pegando últimos 11:', numeroLimpo);
    }
    
    return numeroLimpo;
  }

  /**
   * Carrega dados extras do website informado
   */
  async carregarDadosDoLink(): Promise<void> {
    if (!this.lead.website) {
      this.erro = 'Informe o website para carregar os dados';
      return;
    }

    // Normalizar URL antes da validação
    this.lead.website = this.normalizarUrl(this.lead.website);

    // Validar se é uma URL válida
    try {
      new URL(this.lead.website);
    } catch {
      this.erro = 'Informe uma URL válida (ex: https://exemplo.com.br)';
      return;
    }

    this.carregandoWebsite = true;
    this.erro = '';
    this.marcarWebsiteAnalisado(); // Marcar como tentativa executada

    try {
      console.log('Carregando dados do website:', this.lead.website);

      // Enviar URL para a API para análise
      const dadosWebsite = await this.leadService.analisarWebsite(this.lead.website);

      if (dadosWebsite) {
        console.log('Dados do website recebidos:', dadosWebsite);

        // Armazenar links categorizados
        if (dadosWebsite.linksCategorized && Array.isArray(dadosWebsite.linksCategorized)) {
          this.linksEncontrados = dadosWebsite.linksCategorized.sort((a: any, b: any) => a.ordem - b.ordem);
          console.log('Links categorizados recebidos da API:', this.linksEncontrados);
          console.log('Total de links categorizados:', this.linksEncontrados.length);

          // Log detalhado de cada link
          this.linksEncontrados.forEach((link: any, index: number) => {
            console.log(`Link ${index + 1}: ${link.tipo} = ${link.url} (${link.descricao})`);
          });
        } else {
          console.warn('Nenhum link categorizado recebido da API ou formato inválido:', dadosWebsite.linksCategorized);
          this.linksEncontrados = [];
        }

        // Preencher automaticamente telefone do primeiro WhatsApp encontrado
        const whatsappLink = this.linksEncontrados.find((link: any) => link.tipo === 'WhatsApp');
        if (whatsappLink && !this.lead.telefone) {
          // Extrair número do WhatsApp
          const numeroMatch = whatsappLink.url.match(/(\d{10,15})/);
          if (numeroMatch) {
            this.lead.telefone = numeroMatch[1];
            // Atualiza o campo formatado também
            this.telefoneFormatado = this.formatarTelefone(this.lead.telefone);
          }
        }

        // Preencher automaticamente Instagram handle
        const instagramLink = this.linksEncontrados.find((link: any) => link.tipo === 'Instagram');
        if (instagramLink && !this.lead.instagramHandle) {
          // Extrair username do Instagram
          const usernameMatch = instagramLink.url.match(/instagram\.com\/([^\/\?]+)/);
          if (usernameMatch) {
            this.lead.instagramHandle = usernameMatch[1];
          }
        }

        // Preencher automaticamente link do cardápio (Site do Cardápio ou Concorrente)
        const cardapioLink = this.linksEncontrados.find((link: any) =>
          link.tipo === 'Site do Cardápio' || link.tipo === 'Concorrente'
        );
        if (cardapioLink) {
          this.lead.linkCardapio = cardapioLink.url;
          console.log(`Link de cardápio/concorrente encontrado: ${cardapioLink.tipo} = ${cardapioLink.url}`);
        }

        console.log('Formulário atualizado com dados dos links:', this.lead);
      } else {
        this.erro = 'Não foi possível extrair dados do website informado';
      }
    } catch (error) {
      console.error('Erro ao carregar dados do website:', error);
      this.erro = 'Erro ao analisar o website. Verifique a URL e tente novamente.';
    } finally {
      this.carregandoWebsite = false;
    }
  }

  /**
   * Normaliza URL adicionando https:// se necessário
   */
  private normalizarUrl(url: string): string {
    if (!url) return url;

    // Remove espaços extras
    url = url.trim();

    // Se já tem protocolo, retorna como está
    if (url.match(/^https?:\/\//i)) {
      return url;
    }

    // Se não tem protocolo, adiciona https://
    return `https://${url}`;
  }

  /**
   * Abre um link em nova aba (sempre com https://)
   */
  abrirLink(url: string): void {
    const urlNormalizada = this.normalizarUrl(url);
    window.open(urlNormalizada, '_blank');
    console.log('Abrindo link:', url, '-> normalizado:', urlNormalizada);
  }

  /**
   * Descobre múltiplos CNPJs da empresa usando busca no Google + IA
   */
  async descobrirCnpj(): Promise<void> {
    if (!this.lead.empresa) {
      this.erro = 'Informe o nome da empresa para descobrir o CNPJ';
      return;
    }

    if (!this.lead.cidade) {
      this.erro = 'Informe a cidade da empresa para descobrir o CNPJ';
      return;
    }

    this.carregandoCnpj = true;
    this.erro = '';
    this.cnpjsEncontrados = []; // Limpar resultados anteriores
    this.cnpjRecusadoExplicitamente = false; // Reset da flag ao buscar novos CNPJs
    this.marcarCnpjBuscado(); // Marcar como tentativa executada

    try {
      console.log('Descobrindo CNPJs para empresa:', this.lead.empresa, 'em', this.lead.cidade);

      // ServerService retorna diretamente os dados (response.data) quando sucesso
      const response = await this.leadService.descobrirCnpj(this.lead.empresa, this.lead.cidade);

      console.log('Resposta completa do servidor:', response);
      console.log('Tipo da resposta:', typeof response);
      console.log('Estrutura da resposta:', Object.keys(response || {}));

      // ServerService já extraiu .data, então response É os dados
      const dados = response;

      console.log('Dados extraídos:', dados);
      console.log('CNPJs encontrados:', dados?.cnpjsEncontrados);
      console.log('Total encontrados:', dados?.totalEncontrados);

      // Debug detalhado
      console.log('dados existe?', !!dados);
      console.log('dados.cnpjsEncontrados existe?', !!dados?.cnpjsEncontrados);
      console.log('dados.cnpjsEncontrados é array?', Array.isArray(dados?.cnpjsEncontrados));
      console.log('length do array:', dados?.cnpjsEncontrados?.length);

      if (dados && dados.cnpjsEncontrados && dados.cnpjsEncontrados.length > 0) {
        this.cnpjsEncontrados = dados.cnpjsEncontrados.map((cnpj: any, index: number) => ({
          ...cnpj,
          id: `cnpj_${index}`,
          selecionado: false
        }));

        console.log('CNPJs encontrados:', dados.totalEncontrados);
        console.log('Lista de CNPJs processada:', this.cnpjsEncontrados);
        console.log('Propriedade cnpjsEncontrados.length:', this.cnpjsEncontrados.length);
        console.log('Primeiro CNPJ:', this.cnpjsEncontrados[0]);

        // Forçar detecção de mudanças
        this.cdr.detectChanges();

        setTimeout(() => {
          console.log('Após timeout - cnpjsEncontrados.length:', this.cnpjsEncontrados.length);
          this.cdr.detectChanges();
        }, 100);

        // Se encontrou apenas 1 CNPJ com alta confiança, pode selecionar automaticamente
        if (dados.totalEncontrados === 1 && dados.cnpjsEncontrados[0].confianca === 'alta') {
          this.selecionarCnpj(this.cnpjsEncontrados[0]);
          console.log('CNPJ único com alta confiança selecionado automaticamente');
        }
      } else {
        console.warn('Condição de CNPJs falhou:', {
          dados: !!dados,
          cnpjsEncontrados: !!dados?.cnpjsEncontrados,
          isArray: Array.isArray(dados?.cnpjsEncontrados),
          length: dados?.cnpjsEncontrados?.length,
          dadosCompletos: dados
        });

        this.erro = `Nenhum CNPJ encontrado para "${this.lead.empresa}" em ${this.lead.cidade}. Tente com um nome mais específico ou verifique se a empresa possui CNPJ.`;
        console.warn('Nenhum CNPJ encontrado para a busca realizada');
      }
    } catch (error) {
      console.error('Erro ao descobrir CNPJs:', error);
      this.erro = `Erro ao buscar CNPJs: ${error}`;
    } finally {
      this.carregandoCnpj = false;
    }
  }

  /**
   * Seleciona um CNPJ da lista de opções encontradas
   */
  selecionarCnpj(cnpjSelecionado: any): void {
    // Marcar apenas o selecionado
    this.cnpjsEncontrados.forEach(cnpj => {
      cnpj.selecionado = cnpj.id === cnpjSelecionado.id;
    });

    // Preencher dados no formulário
    this.lead.cnpj = cnpjSelecionado.cnpj;

    // Armazenar razão social para exibição no resumo
    this.razaoSocialSelecionada = cnpjSelecionado.razaoSocial || '';

    // Reset da flag de recusa já que selecionou um CNPJ
    this.cnpjRecusadoExplicitamente = false;
    this.erro = ''; // Limpa qualquer erro

    // NÃO alteramos o nome da empresa - mantemos o que veio do Instagram
    // O nome do Instagram é mais relevante (como o mercado conhece a empresa)
    console.log('Mantendo nome da empresa do Instagram:', this.lead.empresa);
    if (cnpjSelecionado.nomeFantasia && cnpjSelecionado.nomeFantasia !== this.lead.empresa) {
      console.log('Nome fantasia do CNPJ é diferente:', cnpjSelecionado.nomeFantasia);
      console.log('Mas mantemos o nome original:', this.lead.empresa);
    }

    // Adicionar razão social nas observações se disponível
    if (cnpjSelecionado.razaoSocial) {
      const observacaoExtra = `Razão Social: ${cnpjSelecionado.razaoSocial}`;
      if (!this.lead.observacoes) {
        this.lead.observacoes = observacaoExtra;
      } else if (!this.lead.observacoes.includes('Razão Social:')) {
        this.lead.observacoes = `${observacaoExtra}\n\n${this.lead.observacoes}`;
      }
    }

    // Adicionar endereço se disponível e campo estiver vazio
    if (cnpjSelecionado.endereco && !this.lead.endereco) {
      this.lead.endereco = cnpjSelecionado.endereco;
      console.log('Endereço preenchido automaticamente:', cnpjSelecionado.endereco);
    }

    console.log('CNPJ selecionado:', cnpjSelecionado.cnpj);
    console.log('Dados atualizados no formulário:', {
      cnpj: this.lead.cnpj,
      empresa: this.lead.empresa + ' (mantido do Instagram)',
      endereco: this.lead.endereco
    });
  }

  /**
   * Remove a seleção de CNPJ
   */
  removerSelecaoCnpj(): void {
    this.cnpjsEncontrados.forEach(cnpj => {
      cnpj.selecionado = false;
    });
    this.lead.cnpj = '';
    this.razaoSocialSelecionada = ''; // Limpar razão social
    this.cnpjRecusadoExplicitamente = false; // Reset da confirmação
    console.log('Seleção de CNPJ removida');
  }

  /**
   * Formatar input de CNPJ em tempo real
   */
  formatarCnpjInput(event: any): void {
    let valor = event.target.value.replace(/\D/g, ''); // Remove tudo que não é número

    // Aplica a máscara XX.XXX.XXX/XXXX-XX
    if (valor.length <= 14) {
      valor = valor.replace(/(\d{2})(\d)/, '$1.$2');
      valor = valor.replace(/(\d{2})\.(\d{3})(\d)/, '$1.$2.$3');
      valor = valor.replace(/\.(\d{3})(\d)/, '.$1/$2');
      valor = valor.replace(/(\d{4})(\d)/, '$1-$2');
    }

    // Atualiza o valor do input
    event.target.value = valor;
    this.cnpjManual = valor;
  }

  /**
   * Buscar dados da empresa pelo CNPJ informado manualmente
   */
  async buscarCnpjManual(): Promise<void> {
    if (!this.cnpjManual) {
      this.erro = 'Informe o CNPJ para buscar os dados da empresa';
      return;
    }

    // Remove formatação para validação
    const cnpjLimpo = this.cnpjManual.replace(/\D/g, '');
    if (cnpjLimpo.length !== 14) {
      this.erro = 'CNPJ deve ter 14 dígitos';
      return;
    }

    this.carregandoCnpjManual = true;
    this.erro = '';

    try {
      console.log('Buscando dados para CNPJ manual:', this.cnpjManual);

      // Usar a mesma API que busca detalhes dos sócios, mas só para obter dados da empresa
      const response = await this.leadService.buscarDetalhesSocios(cnpjLimpo);

      if (response && response.empresa) {
        // Criar um objeto CNPJ simulado para adicionar à lista
        const cnpjEncontrado = {
          id: 'cnpj_manual',
          cnpj: cnpjLimpo,
          cnpjFormatado: this.cnpjManual,
          nomeFantasia: response.empresa.nomeFantasia || response.empresa.razaoSocial || 'Nome não informado',
          razaoSocial: response.empresa.razaoSocial || '',
          endereco: response.empresa.endereco || '',
          capitalSocial: response.empresa.capitalSocial || '',
          porte: response.empresa.porte || '',
          situacao: response.empresa.situacao || 'ATIVA',
          atividadePrincipal: response.empresa.naturezaJuridica || response.empresa.atividadePrincipal || '',
          socios: response.socios ? response.socios.map((s: any) => s.nome).slice(0, 3) : [],
          confianca: 'manual',
          fonte: 'Informado manualmente',
          selecionado: false
        };

        // Limpar resultados anteriores e adicionar o CNPJ manual
        this.cnpjsEncontrados = [cnpjEncontrado];

        // Marcar automaticamente como selecionado
        this.selecionarCnpj(cnpjEncontrado);

        console.log('Dados do CNPJ manual encontrados:', cnpjEncontrado);

        // Limpar o campo manual após sucesso
        this.cnpjManual = '';

        // Forçar detecção de mudanças
        this.cdr.detectChanges();

      } else {
        this.erro = 'CNPJ não encontrado ou dados indisponíveis. Verifique o número informado.';
        console.warn('Nenhum dado encontrado para o CNPJ manual:', cnpjLimpo);
      }
    } catch (error) {
      console.error('Erro ao buscar CNPJ manual:', error);
      this.erro = 'Erro ao buscar dados do CNPJ. Verifique o número e tente novamente.';
    } finally {
      this.carregandoCnpjManual = false;
    }
  }

  /**
   * Abrir Google com busca específica para o CNPJ informado
   */
  abrirGoogleCnpj(): void {
    if (!this.cnpjManual) {
      console.warn('CNPJ manual não informado para busca no Google');
      return;
    }

    const cnpjLimpo = this.cnpjManual.replace(/\D/g, '');
    const termoBusca = `CNPJ ${this.cnpjManual}`;
    const urlGoogle = `https://www.google.com/search?q=${encodeURIComponent(termoBusca)}`;

    console.log('Abrindo busca Google para CNPJ:', this.cnpjManual);
    window.open(urlGoogle, '_blank');
  }

  /**
   * Abrir Google com busca para CNPJ da empresa
   */
  abrirGoogleEmpresaCnpj(): void {
    if (!this.lead.empresa) {
      console.warn('Nome da empresa não informado para busca no Google');
      return;
    }

    const termoBusca = `${this.lead.empresa} CNPJ`;
    const urlGoogle = `https://www.google.com/search?q=${encodeURIComponent(termoBusca)}`;

    console.log('Abrindo busca Google para empresa:', termoBusca);
    window.open(urlGoogle, '_blank');
  }

  /**
   * Abrir Google com busca mais específica (empresa + cidade + CNPJ)
   */
  abrirGoogleEmpresaCompleta(): void {
    if (!this.lead.empresa) {
      console.warn('Nome da empresa não informado para busca no Google');
      return;
    }

    let termoBusca = `${this.lead.empresa}`;

    // Adicionar cidade se disponível
    if (this.lead.cidade) {
      termoBusca += ` ${this.lead.cidade}`;
    }

    termoBusca += ' CNPJ';

    const urlGoogle = `https://www.google.com/search?q=${encodeURIComponent(termoBusca)}`;

    console.log('Abrindo busca Google completa:', termoBusca);
    window.open(urlGoogle, '_blank');
  }

  /**
   * Abrir consulta direta da Receita Federal
   */
  abrirGoogleReceitaFederal(): void {
    const urlReceita = 'https://solucoes.receita.fazenda.gov.br/servicos/cnpjreva/cnpjreva_solicitacao.asp';

    console.log('Abrindo consulta Receita Federal');
    window.open(urlReceita, '_blank');
  }

  /**
   * Abrir consulta de CNPJ na Receita Federal (usado quando não encontra sócios)
   */
  abrirConsultaCNPJReceita(): void {
    const urlReceita = 'https://solucoes.receita.fazenda.gov.br/servicos/cnpjreva/cnpjreva_solicitacao.asp';
    
    // Se tiver CNPJ, copia para área de transferência para facilitar
    if (this.lead.cnpj) {
      navigator.clipboard.writeText(this.lead.cnpj)
        .then(() => {
          console.log('CNPJ copiado para área de transferência:', this.lead.cnpj);
          // Poderia mostrar um toast/notificação aqui
        })
        .catch(err => {
          console.error('Erro ao copiar CNPJ:', err);
        });
    }
    
    console.log('Abrindo consulta Receita Federal para buscar sócios');
    window.open(urlReceita, '_blank');
  }

  /**
   * Adicionar sócio manualmente quando não encontrar na busca
   */
  adicionarSocioManual(): void {
    if (!this.nomeSocioManual?.trim()) {
      return;
    }
    
    const socioManual = {
      nome: this.nomeSocioManual.trim(),
      cpf: null,
      participacao: null,
      cargo: 'Sócio',
      dataEntrada: null,
      qualificacao: null,
      principal: true,
      observacoes: 'Informado manualmente',
      scoreAnalise: 50,
      motivoSelecao: 'Informado manualmente pelo usuário',
      manual: true
    };
    
    // Limpa lista anterior e adiciona o sócio manual
    this.sociosDetalhados = [socioManual];
    
    // Define como responsável do lead
    this.lead.nomeResponsavel = socioManual.nome;
    this.parceirSelecionado = true;
    
    // Limpa o campo de input
    this.nomeSocioManual = '';
    
    // Força detecção de mudanças
    this.cdr.detectChanges();
    
    console.log('Sócio adicionado manualmente:', socioManual.nome);
    console.log('Nome responsável atualizado para:', this.lead.nomeResponsavel);
  }

  /**
   * Abrir Google para verificar um CNPJ específico
   */
  abrirGoogleVerificarCnpj(cnpj: any): void {
    const nomeEmpresa = cnpj.nomeFantasia || cnpj.razaoSocial || 'empresa';
    const termoBusca = `${nomeEmpresa} CNPJ ${cnpj.cnpj}`;
    const urlGoogle = `https://www.google.com/search?q=${encodeURIComponent(termoBusca)}`;

    console.log('Abrindo busca Google para verificar CNPJ:', termoBusca);
    window.open(urlGoogle, '_blank');
  }


  /**
   * Confirma que o usuário não quer escolher nenhum dos CNPJs encontrados
   */
  confirmarNenhumCnpj(): void {
    this.cnpjRecusadoExplicitamente = true;
    this.removerSelecaoCnpj();
    this.erro = ''; // Limpa qualquer erro
    console.log('Usuário confirmou que não quer escolher nenhum CNPJ');
  }

  /**
   * Copia CNPJ para a área de transferência
   */
  async copiarCnpj(cnpj: string): Promise<void> {
    try {
      await navigator.clipboard.writeText(cnpj);
      console.log('CNPJ copiado:', cnpj);
    } catch (error) {
      console.error('Erro ao copiar CNPJ:', error);
    }
  }

  /**
   * Abre detalhes do CNPJ no site CNPJ.biz
   */
  abrirCnpjBiz(cnpj: string): void {
    // Remove formatação do CNPJ para usar na URL
    const cnpjLimpo = cnpj.replace(/[^\d]/g, '');
    const url = `https://cnpj.biz/${cnpjLimpo}`;
    window.open(url, '_blank');
    console.log('Abrindo CNPJ.biz para:', cnpj, '-> URL:', url);
  }

  /**
   * Obter cor do badge de confiança
   */
  getCorConfianca(confianca: string): string {
    switch (confianca) {
      case 'alta': return 'success';
      case 'media': return 'warning';
      case 'baixa': return 'danger';
      default: return 'secondary';
    }
  }

  /**
   * Obter ícone de confiança
   */
  getIconeConfianca(confianca: string): string {
    switch (confianca) {
      case 'alta': return 'fa-check-circle';
      case 'media': return 'fa-exclamation-circle';
      case 'baixa': return 'fa-times-circle';
      default: return 'fa-question-circle';
    }
  }

  /**
   * Copia link para a área de transferência (sempre com https://)
   */
  async copiarLink(url: string): Promise<void> {
    try {
      const urlNormalizada = this.normalizarUrl(url);
      await navigator.clipboard.writeText(urlNormalizada);
      console.log('Link copiado:', url, '-> normalizado:', urlNormalizada);
      // Opcional: mostrar notificação de sucesso
    } catch (error) {
      console.error('Erro ao copiar link:', error);
      // Fallback para navegadores antigos
      const urlNormalizada = this.normalizarUrl(url);
      const textArea = document.createElement('textarea');
      textArea.value = urlNormalizada;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
    }
  }

  /**
   * Formatar número de telefone
   */
  formatarTelefone(numero: string): string {
    if (!numero) return '';

    const numeroLimpo = numero.replace(/\D/g, '');

    if (numeroLimpo.length === 11) {
      // Celular: (XX) 9XXXX-XXXX
      return numeroLimpo.replace(/^(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
    } else if (numeroLimpo.length === 10) {
      // Fixo: (XX) XXXX-XXXX
      return numeroLimpo.replace(/^(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
    }

    return numero;
  }

  /**
   * Obter ícone por tipo de telefone
   */
  getIconeTelefone(tipo: string): string {
    const tipoInfo = this.tiposTelefone.find(t => t.valor === tipo);
    return tipoInfo ? tipoInfo.icone : 'fa-phone';
  }

  /**
   * Obter cor por tipo de telefone
   */
  getCorTelefone(tipo: string): string {
    const tipoInfo = this.tiposTelefone.find(t => t.valor === tipo);
    return tipoInfo ? tipoInfo.cor : '#6c757d';
  }

  /**
   * Abrir telefone (WhatsApp ou discador)
   */
  abrirTelefone(telefone: any): void {
    if (telefone.tipo === 'WhatsApp' || telefone.tipo === 'Celular') {
      const numeroLimpo = telefone.numero.replace(/\D/g, '');
      const whatsappUrl = `https://wa.me/55${numeroLimpo}`;
      window.open(whatsappUrl, '_blank');
    } else {
      // Para telefones fixos, apenas copiar o número
      this.copiarTelefone(telefone.numero);
    }
  }

  /**
   * Copiar telefone para a área de transferência
   */
  async copiarTelefone(numero: string): Promise<void> {
    try {
      const numeroFormatado = this.formatarTelefone(numero);
      await navigator.clipboard.writeText(numeroFormatado);
      console.log('Telefone copiado:', numeroFormatado);
      // Opcional: mostrar notificação de sucesso
    } catch (error) {
      console.error('Erro ao copiar telefone:', error);
    }
  }

  /**
   * Buscar detalhes dos sócios da empresa
   */
  async buscarDetalhesSocios(): Promise<void> {
    if (!this.lead.cnpj) {
      this.erro = 'CNPJ não selecionado';
      return;
    }

    this.carregandoSocios = true;
    this.erro = '';
    this.sociosBuscados = true;
    this.parceirSelecionado = false; // Reset da seleção anterior

    try {
      console.log('Buscando sócios para CNPJ:', this.lead.cnpj);

      // Chamar API para buscar sócios
      // IMPORTANTE: ServerService já extrai .data quando sucesso=true
      const response = await this.leadService.buscarDetalhesSocios(this.lead.cnpj);

      console.log('Resposta do leadService (já extraída):', response);

      // Como ServerService já extraiu .data, response É os dados
      if (response && response.socios) {
        console.log('Dados recebidos:', response);
        console.log('Array de sócios:', response.socios);

        // Garantir que é um array
        if (Array.isArray(response.socios)) {
          this.sociosDetalhados = response.socios;
        } else {
          console.error('response.socios não é um array:', response.socios);
          this.sociosDetalhados = [];
        }

        console.log('Sócios atribuídos a this.sociosDetalhados:', this.sociosDetalhados);
        console.log('Quantidade de sócios:', this.sociosDetalhados.length);
        console.log('sociosBuscados:', this.sociosBuscados);

        // Verificar cada sócio
        this.sociosDetalhados.forEach((socio, index) => {
          console.log(`Sócio ${index}:`, socio);
        });

        // Aplicar seleção inteligente do sócio principal (vem do backend)
        if (this.sociosDetalhados.length > 0) {
          const socioPrincipal = this.sociosDetalhados.find(socio => socio.principal);

          if (socioPrincipal) {
            // Atualizar responsável com base na análise inteligente
            this.lead.nomeResponsavel = socioPrincipal.nome;
            this.parceirSelecionado = true;

            console.log('🎯 Sócio principal identificado automaticamente:', socioPrincipal.nome);
            console.log(`   Score: ${socioPrincipal.scoreAnalise}/100`);
            console.log(`   Motivo: ${socioPrincipal.motivoSelecao}`);
            console.log('   Nome responsável atualizado para:', this.lead.nomeResponsavel);
          } else {
            // Fallback: se por algum motivo não tiver principal, define o primeiro
            this.sociosDetalhados[0].principal = true;
            this.lead.nomeResponsavel = this.sociosDetalhados[0].nome;
            this.parceirSelecionado = true;
            console.log('⚠️ Fallback: primeiro sócio definido como principal:', this.sociosDetalhados[0].nome);
          }

          // Forçar detecção de mudanças imediatamente após definir
          this.cdr.detectChanges();
        }

        // Forçar detecção de mudanças
        this.cdr.detectChanges();

        // Timeout para garantir renderização
        setTimeout(() => {
          console.log('Após timeout - sociosDetalhados:', this.sociosDetalhados);
          console.log('Após timeout - length:', this.sociosDetalhados.length);
          console.log('Após timeout - lead.nomeResponsavel:', this.lead.nomeResponsavel);
          this.cdr.detectChanges();
        }, 100);

        // Armazenar informações extras da empresa se disponíveis
        if (response.empresa) {
          console.log('Informações extras da empresa:', response.empresa);

          // Adicionar informações da empresa nas observações se não existirem
          const infoEmpresa = response.empresa;
          let observacoesExtras = '';

          if (infoEmpresa.razaoSocial && infoEmpresa.razaoSocial !== this.lead.empresa) {
            observacoesExtras += `Razão Social: ${infoEmpresa.razaoSocial}\n`;
          }
          if (infoEmpresa.capitalSocial) {
            observacoesExtras += `Capital Social: ${infoEmpresa.capitalSocial}\n`;
          }
          if (infoEmpresa.porte) {
            observacoesExtras += `Porte: ${infoEmpresa.porte}\n`;
          }
          if (infoEmpresa.naturezaJuridica) {
            observacoesExtras += `Natureza Jurídica: ${infoEmpresa.naturezaJuridica}\n`;
          }
          if (infoEmpresa.mei === 'Sim') {
            observacoesExtras += `MEI: Sim\n`;
          }

          if (observacoesExtras && !this.lead.observacoes?.includes('Razão Social:')) {
            this.lead.observacoes = observacoesExtras + (this.lead.observacoes || '');
          }
        }

        // Se não encontrou detalhes dos sócios mas tem sócios no CNPJ selecionado
        if (this.sociosDetalhados.length === 0) {
          // Verificar se tem sócios básicos do passo anterior
          const cnpjSelecionado = this.cnpjsEncontrados.find(cnpj => cnpj.cnpj === this.lead.cnpj);
          if (cnpjSelecionado && cnpjSelecionado.socios && cnpjSelecionado.socios.length > 0) {
            // Converter lista de nomes para formato detalhado
            const sociosBasicos = cnpjSelecionado.socios;
            this.sociosDetalhados = sociosBasicos.map((nome: string, index: number) => ({
              nome: nome,
              cpf: null,
              participacao: null,
              cargo: 'Sócio',
              dataEntrada: null,
              qualificacao: null,
              principal: index === 0,
              observacoes: '',
              scoreAnalise: index === 0 ? 60 : 30, // Score simples baseado na posição
              motivoSelecao: index === 0 ? (sociosBasicos.length === 1 ? 'Sócio único da empresa' : 'Primeiro na lista') : 'Sócio secundário'
            }));

            // Definir o primeiro sócio como responsável
            if (this.sociosDetalhados.length > 0) {
              this.lead.nomeResponsavel = this.sociosDetalhados[0].nome;
              this.parceirSelecionado = true; // Marcar como selecionado automaticamente

              if (this.sociosDetalhados.length === 1) {
                console.log('🎯 Sócio único (do CNPJ) definido automaticamente como responsável:', this.sociosDetalhados[0].nome);
                console.log('   Score: 60/100 (sócio único)');
              } else {
                console.log('🎯 Primeiro sócio (do CNPJ) definido como responsável:', this.sociosDetalhados[0].nome);
                console.log('   Score: 60/100 (primeiro na lista)');
              }
              console.log('   Nome responsável atualizado para:', this.lead.nomeResponsavel);

              // Forçar detecção de mudanças imediatamente
              this.cdr.detectChanges();
            }

            console.log('Usando sócios básicos do CNPJ selecionado:', this.sociosDetalhados);
          }
        }
      } else {
        console.warn('Nenhum sócio encontrado na busca detalhada. Response:', response);
        this.sociosDetalhados = [];
        this.parceirSelecionado = false; // Não há parceiro para selecionar

        // Se a resposta for um erro (string), mostrar mensagem
        if (typeof response === 'string') {
          this.erro = response;
        }
      }
    } catch (error) {
      console.error('Erro ao buscar sócios:', error);
      this.erro = 'Erro ao buscar informações dos sócios. Tente novamente.';
      this.sociosDetalhados = [];
      this.parceirSelecionado = false; // Reset em caso de erro
    } finally {
      this.carregandoSocios = false;
    }
  }

  /**
   * Extrair porcentagem numérica de uma string de participação
   */
  private extrairPorcentagem(participacao: string): number {
    if (!participacao) return 0;
    const match = participacao.match(/(\d+(?:\.\d+)?)/);
    return match ? parseFloat(match[1]) : 0;
  }

  /**
   * Identificar automaticamente o sócio principal baseado em critérios
   */
  identificarSocioPrincipalAutomaticamente(): void {
    if (!this.sociosDetalhados || this.sociosDetalhados.length === 0) {
      console.log('Nenhum sócio encontrado para identificação automática');
      return;
    }

    let socioPrincipal = null;
    let criterioUsado = '';

    // 1. Buscar por cargo (prioridade alta)
    const cargosPrioritarios = [
      'presidente',
      'diretor presidente',
      'ceo',
      'diretor',
      'administrador',
      'sócio administrador',
      'gerente',
      'representante legal'
    ];

    for (const cargo of cargosPrioritarios) {
      socioPrincipal = this.sociosDetalhados.find(s =>
        s.cargo?.toLowerCase().includes(cargo)
      );
      if (socioPrincipal) {
        criterioUsado = `cargo: ${socioPrincipal.cargo}`;
        break;
      }
    }

    // 2. Se não encontrou por cargo, buscar por maior participação
    if (!socioPrincipal) {
      const socioComMaiorParticipacao = this.sociosDetalhados.reduce((prev, current) => {
        const prevParticipacao = this.extrairPorcentagem(prev.participacao) || 0;
        const currentParticipacao = this.extrairPorcentagem(current.participacao) || 0;
        return currentParticipacao > prevParticipacao ? current : prev;
      });

      const maiorParticipacao = this.extrairPorcentagem(socioComMaiorParticipacao.participacao);
      if (maiorParticipacao > 0) {
        socioPrincipal = socioComMaiorParticipacao;
        criterioUsado = `maior participação: ${socioComMaiorParticipacao.participacao}`;
      }
    }

    // 3. Se ainda não encontrou, buscar por qualificação
    if (!socioPrincipal) {
      const qualificacoesPrioritarias = ['administrador', 'diretor', 'representante'];
      for (const qualificacao of qualificacoesPrioritarias) {
        socioPrincipal = this.sociosDetalhados.find(s =>
          s.qualificacao?.toLowerCase().includes(qualificacao)
        );
        if (socioPrincipal) {
          criterioUsado = `qualificação: ${socioPrincipal.qualificacao}`;
          break;
        }
      }
    }

    // 4. Se ainda não encontrou, pegar o primeiro da lista
    if (!socioPrincipal) {
      socioPrincipal = this.sociosDetalhados[0];
      criterioUsado = 'primeiro da lista';
    }

    // Marcar automaticamente como principal
    if (socioPrincipal) {
      console.log(`Sócio principal identificado automaticamente: ${socioPrincipal.nome} (critério: ${criterioUsado})`);
      this.marcarComoPrincipal(socioPrincipal);

      // Adicionar propriedade para mostrar que foi selecionado automaticamente
      socioPrincipal.selecionadoAutomaticamente = true;
      socioPrincipal.criterioSelecao = criterioUsado;
    }
  }

  /**
   * Marcar sócio como contato principal
   */
  marcarComoPrincipal(socioSelecionado: any): void {
    // Desmarcar todos os outros e limpar flags de seleção automática
    this.sociosDetalhados.forEach(socio => {
      socio.principal = false;
      if (socio !== socioSelecionado) {
        socio.selecionadoAutomaticamente = false;
        socio.criterioSelecao = null;
      }
    });

    // Marcar o selecionado
    socioSelecionado.principal = true;

    // Se não foi seleção automática, marcar como seleção manual
    if (!socioSelecionado.selecionadoAutomaticamente) {
      socioSelecionado.selecionadoManualmente = true;
    }

    // Atualizar o nome do responsável do lead
    this.lead.nomeResponsavel = socioSelecionado.nome;

    // Marcar que um parceiro foi selecionado
    this.parceirSelecionado = true;
    this.erro = ''; // Limpa qualquer erro de validação

    console.log('Sócio marcado como principal:', socioSelecionado.nome);
    console.log('Nome responsável atualizado para:', this.lead.nomeResponsavel);

    // Log da seleção inteligente
    if (socioSelecionado.scoreAnalise) {
      console.log(`Score do sócio selecionado: ${socioSelecionado.scoreAnalise}/100`);
      console.log(`Motivo da seleção: ${socioSelecionado.motivoSelecao}`);
    }
  }

  /**
   * Retorna o sócio marcado como principal
   */
  getSocioPrincipal(): any {
    return this.sociosDetalhados?.find(socio => socio.principal);
  }

  /**
   * Formatar score de confiança para exibição
   */
  getScoreClasse(score: number): string {
    if (score >= 80) return 'badge-success';
    if (score >= 60) return 'badge-warning';
    if (score >= 40) return 'badge-info';
    return 'badge-secondary';
  }

  /**
   * Formatar motivo de seleção para exibição mais amigável
   */
  formatarMotivoSelecao(motivo: string): string {
    if (!motivo) return '';

    // Capitalizar primeira letra e melhorar formatação
    return motivo.charAt(0).toUpperCase() + motivo.slice(1);
  }


  /**
   * Buscar empresa no Google para encontrar informações adicionais
   */
  buscarEmpresaNoGoogle(): void {
    console.log('🔍 buscarEmpresaNoGoogle called - lead.empresa:', this.lead.empresa);

    if (!this.lead.empresa?.trim()) {
      console.warn('Nome da empresa não informado para busca no Google');
      return;
    }

    let termoBusca = this.lead.empresa.trim();

    // Adicionar cidade se disponível para busca mais precisa
    if (this.lead.cidade?.trim()) {
      termoBusca += ` ${this.lead.cidade.trim()}`;
    }

    // URL otimizada para Google Business (mostra telefone, endereço, etc.)
    const termoCodificado = encodeURIComponent(termoBusca);
    const urlGoogle = `https://www.google.com/search?q=${termoCodificado}`;

    console.log('Abrindo busca Google para:', termoBusca);
    console.log('URL:', urlGoogle);

    // Abrir em nova aba para não perder dados do formulário
    window.open(urlGoogle, '_blank');
  }

  // ===== MÉTODOS DO WIZARD =====

  /**
   * Navegar para próximo passo
   */
  nextStep(): void {
    // Sempre valida antes de tentar avançar
    if (!this.canAdvance()) {
      this.mostrarMensagemValidacao();
      return;
    }
    
    // Se passou na validação, avança
    this.saveCurrentStepData();
    this.currentStep++;
    this.etapaFoiPulada = false; // Reset flag de pular
    this.erro = ''; // Limpar erro ao avançar com sucesso
    console.log('Avançando para passo:', this.currentStep);
  }

  /**
   * Voltar para passo anterior
   */
  prevStep(): void {
    if (this.currentStep > 1) {
      this.saveCurrentStepData();
      this.currentStep--;
      console.log('Voltando para passo:', this.currentStep);
    }
  }

  /**
   * Ir para passo específico
   */
  goToStep(step: number): void {
    if (step >= 1 && step <= this.totalSteps) {
      this.saveCurrentStepData();
      this.currentStep = step;
      console.log('Indo para passo:', this.currentStep);
    }
  }

  /**
   * Obter lista de campos obrigatórios faltantes
   */
  getCamposFaltantes(): string[] {
    const faltantes = [];
    
    if (!this.lead.nomeResponsavel?.trim()) {
      faltantes.push('Nome do Responsável');
    }
    if (!this.lead.empresa?.trim()) {
      faltantes.push('Nome da Empresa');
    }
    if (!this.lead.cidade?.trim()) {
      faltantes.push('Cidade');
    }
    if (!this.lead.instagramHandle?.trim()) {
      faltantes.push('Instagram');
    }
    
    // Para o telefone, verifica tanto o campo formatado quanto o limpo
    if (this.telefoneFormatado) {
      this.lead.telefone = this.limparTelefone(this.telefoneFormatado);
    }
    if (!this.lead.telefone?.trim()) {
      faltantes.push('Telefone');
    }
    
    return faltantes;
  }

  /**
   * Verificar se pode avançar para próximo passo
   */
  canAdvance(): boolean {
    switch (this.currentStep) {
      case 1: // Extrair Dados
        // Garante que o telefone está atualizado antes de validar
        if (this.telefoneFormatado) {
          this.lead.telefone = this.limparTelefone(this.telefoneFormatado);
        }
        return !!(this.lead.nomeResponsavel && this.lead.empresa && this.lead.cidade && this.lead.instagramHandle && this.lead.telefone?.trim());
      case 2: // Buscar Links
        // Deve ter analisado website OU não ter website OU ter pulado a etapa
        return this.websiteAnalisado || !this.lead.website || this.etapaFoiPulada;
      case 3: // Descobrir CNPJ
        // Se encontrou CNPJs, deve ter selecionado um OU confirmado explicitamente que não quer nenhum
        if (this.cnpjsEncontrados.length > 0) {
          return !!this.lead.cnpj || this.cnpjRecusadoExplicitamente;
        }
        // Se não encontrou CNPJs ou não buscou, pode avançar normalmente
        return this.cnpjBuscado || !this.lead.empresa || !this.lead.cidade || this.etapaFoiPulada;
      case 4: // Buscar Sócios
        // Pode avançar se selecionou um sócio OU se não tem CNPJ OU se pulou a etapa
        return this.parceirSelecionado || !this.lead.cnpj || this.etapaFoiPulada;
      case 5: // Finalizar
        return true;
      default:
        return false;
    }
  }

  /**
   * Verificar se passo foi completado
   */
  isStepCompleted(step: number): boolean {
    switch (step) {
      case 1:
        return !!(this.lead.nomeResponsavel && this.lead.empresa && this.lead.cidade);
      case 2:
        return this.linksEncontrados.length > 0;
      case 3:
        return !!this.lead.cnpj;
      case 4:
        return this.parceirSelecionado; // Completo se um parceiro foi selecionado
      default:
        return false;
    }
  }

  /**
   * Obter classe CSS do passo
   */
  getStepClass(step: number): string {
    if (step === this.currentStep) return 'active';
    if (step < this.currentStep || this.isStepCompleted(step)) return 'completed';
    return 'pending';
  }

  /**
   * Salvar dados do passo atual
   */
  saveCurrentStepData(): void {
    switch (this.currentStep) {
      case 1:
        this.wizardData.dadosBasicos = {
          nomeResponsavel: this.lead.nomeResponsavel,
          empresa: this.lead.empresa,
          cidade: this.lead.cidade,
          telefone: this.lead.telefone,
          instagramHandle: this.lead.instagramHandle,
          website: this.lead.website,
          bioInsta: this.lead.bioInsta
        };
        break;
      case 2:
        this.wizardData.linksEncontrados = [...this.linksEncontrados];
        break;
      case 3:
        this.wizardData.cnpjSelecionado = this.lead.cnpj ? {
          cnpj: this.lead.cnpj,
          empresa: this.lead.empresa,
          endereco: this.lead.endereco
        } : null;
        break;
      case 4:
        this.wizardData.sociosEncontrados = [...this.sociosDetalhados];
        break;
      case 5:
        this.wizardData.configuracoes = {
          etapa: this.lead.etapa,
          origem: this.lead.origem,
          segmento: this.lead.segmento,
          observacoes: this.lead.observacoes,
          observacoesSocios: this.lead.observacoesSocios,
          sincronizarBitrix: this.sincronizarBitrix
        };
        break;
    }
    console.log('Dados salvos do passo', this.currentStep, ':', this.wizardData);
  }

  /**
   * Pular etapa atual
   */
  skipStep(): void {
    if (this.currentStep < this.totalSteps && this.canSkipCurrentStep()) {
      console.log('Pulando passo:', this.currentStep);
      this.etapaFoiPulada = true;
      this.nextStep();
    }
  }

  /**
   * Obter título do passo atual
   */
  getCurrentStepTitle(): string {
    switch (this.currentStep) {
      case 1: return 'Extrair Dados do Instagram';
      case 2: return 'Buscar Links do Website';
      case 3: return 'Descobrir CNPJ da Empresa';
      case 4: return 'Buscar Sócios da Empresa';
      case 5: return 'Finalizar Lead';
      default: return 'Passo Desconhecido';
    }
  }

  /**
   * Verificar se pode pular passo atual
   */
  canSkipCurrentStep(): boolean {
    return this.currentStep === 2 || this.currentStep === 3 || this.currentStep === 4; // Links, CNPJ e Sócios são opcionais
  }

  /**
   * Mostrar mensagem de validação específica
   */
  mostrarMensagemValidacao(): void {
    switch (this.currentStep) {
      case 1: // Extrair Dados
        const camposFaltantes = this.getCamposFaltantes();
        if (camposFaltantes.length > 0) {
          this.erro = `Preencha os campos obrigatórios: ${camposFaltantes.join(', ')}`;
        }
        break;
      case 2:
        if (this.lead.website && !this.websiteAnalisado) {
          this.erro = 'Você deve analisar o website antes de continuar ou pular esta etapa.';
        }
        break;
      case 3:
        if (this.cnpjsEncontrados.length > 0 && !this.lead.cnpj && !this.cnpjRecusadoExplicitamente) {
          this.erro = 'Foram encontrados CNPJs para esta empresa. Selecione um ou confirme que nenhum corresponde ao lead.';
        } else if (this.lead.empresa && this.lead.cidade && !this.cnpjBuscado) {
          this.erro = 'Você deve buscar o CNPJ da empresa antes de continuar ou pular esta etapa.';
        }
        break;
      case 4:
        if (this.lead.cnpj && this.sociosBuscados && !this.parceirSelecionado) {
          this.erro = 'Você deve selecionar um sócio como contato principal antes de continuar ou pular esta etapa.';
        } else if (this.lead.cnpj && !this.sociosBuscados) {
          this.erro = 'Você deve buscar os sócios da empresa antes de continuar ou pular esta etapa.';
        }
        break;
      default:
        this.erro = 'Complete os dados obrigatórios para continuar.';
    }
  }

  /**
   * Marcar website como analisado
   */
  marcarWebsiteAnalisado(): void {
    this.websiteAnalisado = true;
    this.erro = ''; // Limpa erro se houver
  }

  /**
   * Marcar CNPJ como buscado
   */
  marcarCnpjBuscado(): void {
    this.cnpjBuscado = true;
    this.erro = ''; // Limpa erro se houver
  }

}
