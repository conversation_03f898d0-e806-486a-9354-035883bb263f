import { ComponentFixture, TestBed } from '@angular/core/testing';

import { FormUploadKmlComponent } from './form-upload-kml.component';

describe('FormUploadKmlComponent', () => {
  let component: FormUploadKmlComponent;
  let fixture: ComponentFixture<FormUploadKmlComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ FormUploadKmlComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FormUploadKmlComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
