.ativar-pagamento-meucadapio {
  background: linear-gradient(to right, #ffffff, #f8f9fa);
  border: none;
  transition: all 0.3s ease;
  max-width: 920px;
}

.ativar-pagamento-meucadapio:hover {
  transform: translateY(-2px);
  box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
}

.ativar-pagamento-meucadapio .btn-primary {
  padding: 12px 30px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.ativar-pagamento-meucadapio .btn-primary:hover {
  transform: scale(1.05);
}

.badge {
  padding: 6px 12px;
  font-weight: 500;
}



.meucardapio-pay-ativo {
  background: #ffffff;
  border: none;
  transition: all 0.3s ease;
  border-radius: 12px;
}

.meucardapio-pay-ativo:hover {
  transform: translateY(-2px);
  box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
}

.status-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(40, 167, 69, 0.1);
}

.info-box {
  padding: 1.25rem;
  background: #f8f9fa;
  border-radius: 10px;
  height: 100%;
  border: 1px solid rgba(0,0,0,.05);
  transition: all 0.3s ease;
}

.info-box:hover {
  background: #fff;
  box-shadow: 0 .125rem .25rem rgba(0,0,0,.075);
}

.info-label {
  font-size: 0.875rem;
  color: #6c757d;
  margin-bottom: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.payment-method-box {
  padding: 1.25rem;
  background: #f8f9fa;
  border-radius: 10px;
  transition: all 0.3s ease;
  border: 1px solid rgba(0,0,0,.05);
}

.payment-method-box:hover {
  background: #fff;
  box-shadow: 0 .125rem .25rem rgba(0,0,0,.075);
}

.payment-icon {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.badge {
  padding: 6px 12px;
  font-weight: 500;
  font-size: 0.875rem;
  border-radius: 6px;
}

.btn-light {
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 6px;
}

.btn-light:hover {
  background: #f8f9fa;
}

.btn-outline-primary,
.btn-outline-secondary {
  border-radius: 6px;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.spinner-border {
  width: 1rem;
  height: 1rem;
}

.alert {
  border-radius: 8px;
}

.card-body {
  padding: 1rem;
}

@media (max-width: 768px) {
  .btn-group {
    margin-top: 0.5rem;
    display: flex;
    width: 100%;
  }

  .btn-group .btn {
    flex: 1;
  }
}
