.lojas-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.loja-item {
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 1rem;
  cursor: pointer;
  min-width: 200px;
  transition: border-color 0.2s;
}

.loja-item.selected {
  border-color: #007bff;
  background: #e9f5ff;
}


.card{
  width: 700px; margin: 0 auto;
}

// Container principal
.container-fluid {
  max-width: 900px;
  margin: 0 auto;
}

// Wizard Header
.wizard-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.wizard-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.wizard-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  transition: all 0.3s ease;

  &.clickable {
    cursor: pointer;

    &:hover {
      transform: translateY(-2px);

      .step-number {
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
      }
    }
  }

  .step-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
    transition: all 0.3s ease;
    border: 3px solid #e9ecef;
    margin-bottom: 0.75rem;

    i {
      font-size: 20px;
    }
  }

  .step-label {
    font-size: 14px;
    font-weight: 500;
    color: #6c757d;
    text-align: center;
    transition: color 0.3s ease;
    white-space: nowrap;
  }

  // Estados do passo
  &.pending {
    .step-number {
      background: #f8f9fa;
      color: #adb5bd;
      border-color: #dee2e6;
    }

    .step-label {
      color: #adb5bd;
    }
  }

  &.active {
    .step-number {
      background: #007bff;
      color: white;
      border-color: #007bff;
      box-shadow: 0 0 0 4px rgba(0, 123, 255, 0.25);
      transform: scale(1.1);
    }

    .step-label {
      color: #007bff;
      font-weight: 600;
    }
  }

  &.completed {
    .step-number {
      background: #28a745;
      color: white;
      border-color: #28a745;
    }

    .step-label {
      color: #28a745;
      font-weight: 600;
    }
  }
}

.step-connector {
  flex: 1;
  height: 3px;
  background: #dee2e6;
  margin: 0 1rem;
  transition: background 0.3s ease;
  max-width: 100px;

  &.completed {
    background: #28a745;
  }
}

// Wizard Content
.wizard-content {
  min-height: 400px;
}

.wizard-pane {
  animation: fadeIn 0.3s ease-in-out;
  padding-bottom: 20px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Wizard Navigation
.wizard-navigation {
  background: rgba(248, 249, 250, 0.8);
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem 1.5rem;

  .btn {
    min-width: 120px;
    font-weight: 600;

    &:disabled {
      opacity: 0.5;
    }
  }

  .step-indicator {
    font-size: 14px;
    font-weight: 500;

    span {
      background: #ffffff;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      border: 1px solid #dee2e6;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
  }
}

// Cards melhorados
.card {
  border: none;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  }

  .card-header {
    border-bottom: none;
    padding: 0.8rem;

    h5 {
      font-size: 1.1rem;
      font-weight: 600;
      color: #fff;
    }
  }

  .card-body {
    padding: 2rem;
  }
}

// Cards de estatísticas
.stat-card {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  border-radius: 12px;
  color: white;
  margin-bottom: 1rem;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.02);
  }

  .stat-icon {
    font-size: 2.5rem;
    margin-right: 1rem;
    opacity: 0.9;
    color: rgba(255, 255, 255, 0.9);
  }

  .stat-content {
    h3 {
      font-size: 2rem;
      font-weight: bold;
      margin: 0;
      line-height: 1;
      color: #ffffff;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    p {
      margin: 0;
      font-size: 1rem;
      color: rgba(255, 255, 255, 0.95);
      font-weight: 500;
      text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
    }
  }

  &.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
  }

  &.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
  }
}

// Grid de categorias
.categorias-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.categoria-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.2s ease;

  &:hover {
    background: #e9ecef;
    border-color: #007bff;
    transform: translateY(-1px);
  }

  .categoria-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;

    .categoria-nome {
      font-weight: 600;
      color: #495057;
    }
  }

  .categoria-produtos {
    text-align: right;

    .badge {
      font-size: 0.8rem;
      padding: 0.4rem 0.8rem;
      border-radius: 15px;
    }
  }
}

// Seção de confirmação unificada
.confirmation-section {
  .confirmation-instructions {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid rgba(255, 193, 7, 0.3);

    code {
      font-size: 1.2rem;
      padding: 0.8rem 1.5rem;
      border-radius: 8px;
      border: 2px dashed #dc3545;
      background: #fff;
      display: inline-block;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  .input-group-lg .form-control {
    font-size: 1.1rem;
    padding: 0.75rem 1rem;
  }

  .input-group-text {
    background: #f8f9fa;
    border-color: #ced4da;
  }
}

// Alerta unificado melhorado
.alert-warning {
  .alert-heading {
    margin-bottom: 1rem;
    font-size: 1.1rem;

    i {
      color: #856404;
    }
  }

  .text-danger {
    color: #721c24 !important;
  }

  hr {
    border-color: rgba(133, 100, 4, 0.3);
  }
}

// Progress bar personalizada
.import-progress {
  .progress {
    height: 8px;
    border-radius: 4px;
    background: #f8f9fa;
    overflow: hidden;

    .progress-bar {
      border-radius: 4px;
      background: linear-gradient(90deg, #007bff, #0056b3);
    }
  }
}

// Loading containers
.loading-container {
  text-align: center;

  .spinner-border {
    width: 2rem;
    height: 2rem;
  }
}

// Alertas melhorados
.alert {
  border: none;
  border-radius: 8px;

  &.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
  }

  &.alert-success {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
  }

  &.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;

    .alert-heading {
      color: #856404;
    }
  }
}

// Responsividade
@media (max-width: 768px) {
  .container-fluid {
    padding: 0 1rem;
  }

  .wizard-header {
    padding: 1rem;
  }

  .wizard-steps {
    flex-direction: column;

    .step-connector {
      width: 3px;
      height: 40px;
      margin: 0.5rem 0;
      max-width: none;
    }
  }

  .wizard-navigation {
    .d-flex {
      flex-direction: column;
      gap: 1rem;

      .step-indicator {
        order: -1;
      }
    }
  }

  .stat-card {
    flex-direction: column;
    text-align: center;

    .stat-icon {
      margin-right: 0;
      margin-bottom: 1rem;
    }
  }

  .categorias-grid {
    grid-template-columns: 1fr;
  }
}

// Estados de validação melhorados
.form-control {
  border-radius: 6px;
  transition: all 0.2s ease;

  &:focus {
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
    border-color: #007bff;
  }

  &.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.25);
  }

  &.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.25);
  }
}

.invalid-feedback, .valid-feedback {
  font-size: 0.9rem;
  margin-top: 0.5rem;

  i {
    margin-right: 0.25rem;
  }
}

// Labels melhorados
.form-label {
  font-size: 0.95rem;
  margin-bottom: 0.5rem;
  color: #495057;

  .text-danger {
    color: #dc3545 !important;
  }
}

// Seção de Sucesso
.success-section {
  padding: 2rem 0;
  text-align: center;
}

.success-container {
  max-width: 800px;
  margin: 0 auto;
}

.success-animation {
  margin-bottom: 2rem;
}

.checkmark-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  margin: 0 auto;
  position: relative;
  animation: scaleIn 0.6s ease-out;
  box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);

  .checkmark {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;

    &::before {
      content: '';
      position: absolute;
      width: 25px;
      height: 12px;
      border: 4px solid white;
      border-top: none;
      border-right: none;
      transform: rotate(-45deg);
      top: 50%;
      left: 50%;
      margin-top: -10px;
      margin-left: -12px;
      animation: checkmarkDraw 0.6s ease-out 0.3s both;
    }
  }
}

@keyframes scaleIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes checkmarkDraw {
  0% {
    width: 0;
    height: 0;
  }
  50% {
    width: 25px;
    height: 0;
  }
  100% {
    width: 25px;
    height: 12px;
  }
}

.success-content {
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.success-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #28a745;
  margin-bottom: 1rem;

  i {
    font-size: 2rem;
    animation: bounce 2s infinite;
  }
}

.success-subtitle {
  font-size: 1.2rem;
  color: #6c757d;
  margin-bottom: 2rem;

  strong {
    color: #495057;
  }
}

// Card de resumo da importação
.import-summary {
  text-align: left;
  margin-bottom: 2rem;
}

.summary-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: slideInUp 0.6s ease-out 0.6s both;
}

.summary-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;

  h5 {
    margin: 0;
    font-weight: 600;
    color: #495057;
  }
}

.summary-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.stat-item {
  text-align: center;
  flex: 1;

  .stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    line-height: 1;
    margin-bottom: 0.5rem;
  }

  .stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
  }
}

.stat-divider {
  width: 1px;
  height: 40px;
  background: #dee2e6;
  margin: 0 1rem;
}

// Tags de categorias
.categories-imported {
  animation: slideInUp 0.6s ease-out 0.8s both;

  h6 {
    font-weight: 600;
    color: #495057;
  }
}

.categories-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.category-tag {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  animation: fadeInScale 0.4s ease-out both;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
  }

  .category-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    margin-left: 0.5rem;
    font-size: 0.8rem;
    font-weight: 600;
  }
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// Próximos passos
.next-steps {
  animation: slideInUp 0.6s ease-out 1s both;

  .alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border: none;
    border-radius: 12px;

    .alert-heading {
      color: #0c5460;
      font-weight: 600;
    }

    ul {
      color: #0c5460;

      li {
        margin-bottom: 0.5rem;
        padding-left: 0.5rem;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// Detalhes da importação
.import-details {
  animation: fadeIn 0.6s ease-out 1.2s both;

  small {
    background: #f8f9fa;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    border: 1px solid #e9ecef;
    display: inline-block;
  }
}

// Animações gerais
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(50px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

// Responsividade para sucesso
@media (max-width: 768px) {
  .success-title {
    font-size: 2rem;
  }

  .checkmark-circle {
    width: 80px;
    height: 80px;

    .checkmark {
      width: 40px;
      height: 40px;

      &::before {
        width: 18px;
        height: 9px;
        margin-top: -7px;
        margin-left: -9px;
      }
    }
  }

  .summary-stats {
    flex-direction: column;
    gap: 1rem;

    .stat-divider {
      width: 40px;
      height: 1px;
    }
  }

  .categories-tags {
    justify-content: center;
  }
}
