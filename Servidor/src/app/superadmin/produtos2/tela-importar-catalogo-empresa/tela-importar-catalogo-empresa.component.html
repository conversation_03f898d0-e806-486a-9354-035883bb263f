<kendo-dialog-titlebar (close)="feche()">
  <h4 class="modal-title d-flex align-items-center">
    <i class="fas fa-file-import fa-lg mr-2"></i>
    Importar Catálogo da Rede <strong class="ml-1">{{empresa.rede}}</strong>
  </h4>
</kendo-dialog-titlebar>

<div class="container-fluid p-0">

  <!-- Sucesso da importação -->
  <div class="success-section" *ngIf="importou">
    <div class="success-container">
      <!-- Animação de sucesso -->
      <div class="success-animation">
        <div class="checkmark-circle">
          <div class="checkmark"></div>
        </div>
      </div>

      <!-- Título principal -->
      <div class="success-content">
        <h2 class="success-title">
          <i class="fas fa-check-circle text-success mr-3"></i>
          Importação Finalizada com Sucesso!
        </h2>

        <p class="success-subtitle">
          O catálogo da loja <strong>{{lojaSelecionada?.nome}}</strong> foi importado completamente.
        </p>

        <!-- Resumo da importação -->
        <div class="import-summary">
          <div class="summary-card">
            <div class="summary-header">
              <i class="fas fa-chart-line text-primary mr-2"></i>
              <h5>Resumo da Importação</h5>
            </div>
            <div class="summary-content">
              <div class="summary-stats">
                <div class="stat-item">
                  <div class="stat-number text-primary">{{resumo.totalCategorias}}</div>
                  <div class="stat-label">Categorias importadas</div>
                </div>
                <div class="stat-divider"></div>
                <div class="stat-item">
                  <div class="stat-number text-success">{{resumo.totalProdutos}}</div>
                  <div class="stat-label">Produtos importados</div>
                </div>
                <div class="stat-divider" *ngIf="resumo.quantidadeProdutosDaLoja > 0"></div>
                <div class="stat-item" *ngIf="resumo.quantidadeProdutosDaLoja > 0">
                  <div class="stat-number text-warning">{{resumo.quantidadeProdutosDaLoja}}</div>
                  <div class="stat-label">Produtos removidos</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Detalhes das categorias importadas -->
          <div class="categories-imported mt-4" *ngIf="resumo.categorias?.length > 0">
            <h6 class="mb-3">
              <i class="fas fa-folder-open text-info mr-2"></i>
              Categorias Importadas:
            </h6>
            <div class="categories-tags">
              <span class="category-tag" *ngFor="let categoria of resumo.categorias; let i = index"
                    [style.animation-delay]="(i * 0.1) + 's'">
                <i class="fas fa-folder mr-1"></i>
                {{categoria.nome}}
                <span class="category-count">{{categoria.produtos}}</span>
              </span>
            </div>
          </div>
        </div>

        <!-- Próximos passos -->
        <div class="next-steps mt-4">
          <div class="alert alert-info d-flex align-items-start">
            <i class="fas fa-lightbulb fa-2x mr-3 text-info"></i>
            <div>
              <h5 class="alert-heading">Próximos Passos</h5>
              <ul class="mb-0 text-left">
                <li>O catálogo já está disponível para os clientes</li>
                <li>Você pode revisar os produtos importados na seção "Produtos"</li>
                <li>Verifique se as categorias estão organizadas corretamente</li>
                <li>Configure preços e disponibilidade se necessário</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Stepper horizontal navegável -->
  <div class="wizard-header mb-0" *ngIf="!importou">
    <div class="wizard-steps">
      <div class="wizard-step"
           [class]="getStatusPasso(1)"
           [class.clickable]="podeClicarPasso(1)"
           (click)="podeClicarPasso(1) ? irParaPasso(1) : null">
        <div class="step-number">
          <i class="fas fa-store" *ngIf="getStatusPasso(1) === 'completed'"></i>
          <span *ngIf="getStatusPasso(1) !== 'completed'">1</span>
        </div>
        <div class="step-label">Selecionar Loja</div>
      </div>

      <div class="step-connector" [class.completed]="getStatusPasso(2) === 'completed' || getStatusPasso(2) === 'active'"></div>

      <div class="wizard-step"
           [class]="getStatusPasso(2)"
           [class.clickable]="podeClicarPasso(2)"
           (click)="podeClicarPasso(2) ? irParaPasso(2) : null">
        <div class="step-number">
          <i class="fas fa-clipboard-list" *ngIf="getStatusPasso(2) === 'completed'"></i>
          <span *ngIf="getStatusPasso(2) !== 'completed'">2</span>
        </div>
        <div class="step-label">Revisar Catálogo</div>
      </div>

      <div class="step-connector" [class.completed]="getStatusPasso(3) === 'completed' || getStatusPasso(3) === 'active'"></div>

      <div class="wizard-step"
           [class]="getStatusPasso(3)"
           [class.clickable]="podeClicarPasso(3)"
           (click)="podeClicarPasso(3) ? irParaPasso(3) : null">
        <div class="step-number">
          <i class="fas fa-shield-alt" *ngIf="getStatusPasso(3) === 'completed'"></i>
          <span *ngIf="getStatusPasso(3) !== 'completed'">3</span>
        </div>
        <div class="step-label">Confirmar</div>
      </div>
    </div>
  </div>

  <!-- Botões de navegação -->
  <div class="wizard-navigation mb-4" *ngIf="!importou">
    <div class="d-flex justify-content-between align-items-center">
      <button
        type="button"
        class="btn btn-outline-secondary"
        [disabled]="!podeVoltar()"
        (click)="passoAnterior()">
        <i class="fas fa-arrow-left mr-2"></i>
        Anterior
      </button>

      <div class="step-indicator">
        <span class="text-muted">Passo {{passoAtual}} de {{totalPassos}}</span>
      </div>

      <button
        type="button"
        class="btn btn-primary"
        [disabled]="!podeAvancar()"
        (click)="proximoPasso()">
        Próximo
        <i class="fas fa-arrow-right ml-2"></i>
      </button>
    </div>
  </div>

  <!-- Conteúdo dos passos -->
  <div class="wizard-content" *ngIf="!importou">

    <!-- Passo 1: Seleção da Loja -->
    <div class="wizard-pane" *ngIf="passoAtual === 1">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0 d-flex align-items-center">
            <i class="fas fa-store mr-2"></i>
            Passo 1: Selecione a Loja de Origem
          </h5>
        </div>
        <div class="card-body">
          <form [ngClass]="{'needs-validation': !frmLoja.submitted, 'was-validated': frmLoja.submitted}"
                novalidate #frmLoja="ngForm">
            <div class="row">
              <div class="col-md-8">
                <div class="form-group">
                  <label class="form-label font-weight-bold">
                    <i class="fas fa-building mr-1"></i>
                    Loja da Rede
                    <span class="text-danger">*</span>
                  </label>
                  <kendo-dropdownlist
                    [data]="lojas"
                    [readonly]="carregando || importando"
                    [textField]="'nome'"
                    [valueField]="'id'"
                    [filterable]="true"
                    [(ngModel)]="lojaSelecionada"
                    name="lojaSelecionada"
                    (selectionChange)="onSelecionarLoja($event)"
                    required
                    #loja="ngModel"
                    [ngClass]="{'is-invalid': frmLoja.submitted && loja.invalid}"
                    class="form-control">

                    <!-- Template para os itens da lista -->
                    <ng-template kendoDropDownListItemTemplate let-dataItem>
                      <div>
                        <p class="mb-0 text-primary">  {{ dataItem.nome }}</p>
                        <span class="text-muted ml-3"> Última atualização: <b>{{ dataItem.catalogo.ultimaAtualizacaoProdutos | date:'shortDate' }}</b></span>
                      </div>
                    </ng-template>

                    <!-- Template para o item selecionado -->
                    <ng-template kendoDropDownListValueTemplate let-selected>
                      <span *ngIf="selected">
                         {{ selected?.nome }} - Atualizado em: {{ selected?.catalogo.ultimaAtualizacaoProdutos | date:'shortDate' }}
                      </span>

                      <span *ngIf="!selected">
                        Selecione uma loja
                      </span>

                    </ng-template>

                  </kendo-dropdownlist>

                  <div class="invalid-feedback">
                    <p *ngIf="loja.errors?.required">
                      <i class="fas fa-exclamation-circle mr-1"></i>
                      Selecione uma loja para continuar
                    </p>
                  </div>
                  <small class="form-text text-muted mt-2" *ngIf="lojas.length > 0">
                    {{lojas.length}} loja(s) disponível(is) na rede
                  </small>
                </div>
              </div>
              <div class="col-md-4 d-flex align-items-end">
                <div class="loading-container" *ngIf="carregando">
                  <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Carregando...</span>
                  </div>
                  <p class="text-muted mt-2 mb-0">Carregando lojas...</p>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Passo 2: Resumo do Catálogo -->
    <div class="wizard-pane" *ngIf="passoAtual === 2 && !importou">
      <div class="card shadow-sm">
        <div class="card-header bg-info text-white">
          <h5 class="mb-0 d-flex align-items-center">
            <i class="fas fa-clipboard-list mr-2"></i>
            Passo 2: Resumo do Catálogo
          </h5>
        </div>
        <div class="card-body">
          <!-- Loading do resumo -->
          <h3 class="mb-3">Catalogo <strong class="ml-1">{{lojaSelecionada?.nome}}</strong></h3>
          <div class="text-center py-4" *ngIf="carregandoResumo">
            <div class="spinner-border text-info" role="status">
              <span class="sr-only">Carregando resumo...</span>
            </div>
            <p class="text-muted mt-2">Analisando catálogo...</p>
          </div>

          <!-- Conteúdo do resumo -->
          <div *ngIf="!carregandoResumo && resumo.totalProdutos > 0">
            <!-- Estatísticas gerais -->
            <div class="row mb-4">
              <div class="col-md-6">
                <div class="stat-card bg-primary">
                  <div class="stat-icon">
                    <i class="fas fa-layer-group"></i>
                  </div>
                  <div class="stat-content">
                    <h3>{{resumo.totalCategorias}}</h3>
                    <p>Categorias</p>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="stat-card bg-success">
                  <div class="stat-icon">
                    <i class="fas fa-shopping-bag"></i>
                  </div>
                  <div class="stat-content">
                    <h3>{{resumo.totalProdutos}}</h3>
                    <p>Produtos</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Detalhes das categorias -->
            <div class="mb-4">
              <h4 class="font-weight-bold mb-3">
                <i class="fas fa-list mr-2"></i>
                Categorias e Produtos:
              </h4>
              <div class="categorias-grid">
                <div class="categoria-card" *ngFor="let categoria of resumo.categorias; trackBy: trackByCategoria">
                  <div class="categoria-header">
                    <i class="fas fa-folder text-primary mr-2"></i>
                    <span class="categoria-nome">{{categoria.nome}}</span>
                  </div>
                  <div class="categoria-produtos">
                    <span class="badge badge-primary">{{categoria.produtos}} produtos</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Mensagem se não há produtos -->
          <div class="text-center py-4" *ngIf="!carregandoResumo && resumo.totalProdutos === 0 && lojaSelecionada">
            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Catálogo Vazio</h5>
            <p class="text-muted">A loja selecionada não possui produtos em seu catálogo.</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Passo 3: Confirmação e Importação -->
    <div class="wizard-pane" *ngIf="passoAtual === 3 && !importou">
      <div class="card shadow-sm">
        <div class="card-header bg-warning text-dark">
          <h5 class="mb-0 d-flex align-items-center">
            <i class="fas fa-shield-alt mr-2"></i>
            Passo 3: Confirmação da Importação
          </h5>
        </div>
        <div class="card-body">
          <!-- Alerta unificado de substituição e confirmação -->
          <div class="alert alert-warning d-flex align-items-start mb-4">
            <i class="fas fa-exclamation-triangle fa-2x mr-3 text-warning"></i>
            <div class="w-100">
              <h6 class="alert-heading d-flex align-items-center">
                <i class="fas fa-shield-alt mr-2"></i>
                Atenção: Confirmação Necessária
              </h6>

              <!-- Aviso sobre substituição (se aplicável) -->
              <div *ngIf="resumo.quantidadeProdutosDaLoja > 0" class="mb-3">
                <p class="mb-2 font-weight-bold text-danger">
                  <i class="fas fa-trash-alt mr-1"></i>
                  Substituição de Catálogo
                </p>
                <p class="mb-1">
                  O catálogo atual desta loja possui <strong>{{resumo.quantidadeProdutosDaLoja}} produtos</strong>
                  que serão <strong>removidos permanentemente</strong> durante a importação.
                </p>
              </div>

              <!-- Instruções de confirmação -->
              <div class="confirmation-instructions">
                <p class="mb-2">
                  <i class="fas fa-keyboard mr-1"></i>
                  Para prosseguir com a importação, digite exatamente:
                </p>
                <div class="text-center my-3">
                  <code class="bg-light px-3 py-2 text-danger font-weight-bold border rounded">{{getTextoImportar()}}</code>
                </div>
              </div>

              <hr class="my-3">
              <p class="mb-0  text-danger">
                <i class="fas fa-info-circle mr-1"></i>
                Esta ação não pode ser desfeita. Certifique-se de que deseja prosseguir.
              </p>
            </div>
          </div>

          <form (ngSubmit)="importar()" #frmImportar="ngForm" novalidate
               [ngClass]="{'needs-validation': !frmImportar.submitted, 'was-validated': frmImportar.submitted}">

            <!-- Alerta de erro global -->
            <div class="alert alert-danger alert-dismissible" *ngIf="erroImportacao" role="alert">
              <i class="fas fa-exclamation-triangle mr-2"></i>
              <strong>Erro:</strong> {{erroImportacao}}
            </div>


            <div class="confirmation-section">
              <div class="form-group">
                <label class="form-label font-weight-bold">
                  Confirmação:
                  <span class="text-danger">*</span>
                </label>
                <div class="input-group">
                  <div class="input-group-prepend">
                    <span class="input-group-text">
                      <i class="fas fa-keyboard"></i>
                    </span>
                  </div>
                  <input
                    type="text"
                    class="form-control form-control-lg"
                    [(ngModel)]="textoConfirmacao"
                    name="textoConfirmacao"
                    placeholder="Digite a confirmação..."
                    [class.is-invalid]="frmImportar.submitted && !digitouImportar()"
                    [class.is-valid]="digitouImportar()"
                    required
                    #txtConf="ngModel"
                    [disabled]="importando"
                    autocomplete="off">
                  <div class="input-group-append" *ngIf="digitouImportar()">
                    <span class="input-group-text text-success">
                      <i class="fas fa-check"></i>
                    </span>
                  </div>
                </div>
                <div class="invalid-feedback" *ngIf="frmImportar.submitted && !digitouImportar()">
                  <i class="fas fa-times-circle mr-1"></i>
                  Digite exatamente "{{getTextoImportar()}}" para continuar
                </div>
                <div class="valid-feedback" *ngIf="digitouImportar()">
                  <i class="fas fa-check-circle mr-1"></i>
                  Confirmação válida
                </div>
              </div>

              <div class="text-center mt-4">
                <button
                  class="btn btn-danger btn-lg"
                  [disabled]="importando || !digitouImportar()"
                  type="submit">
                  <span *ngIf="!importando">
                    <i class="fas fa-download mr-2"></i>
                    {{getTextoImportar()}}
                  </span>
                  <span *ngIf="importando">
                    <div class="spinner-border spinner-border-sm mr-2" role="status">
                      <span class="sr-only">Importando...</span>
                    </div>
                    Importando Catálogo...
                  </span>
                </button>
              </div>
            </div>
          </form>


          <!-- Progress bar durante importação -->
          <div class="import-progress mt-4" *ngIf="importando">
            <div class="progress">
              <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary"
                   style="width: 100%" role="progressbar">
              </div>
            </div>
            <p class="text-center mt-2 text-muted">
              <i class="fas fa-cog fa-spin mr-2"></i>
              Processando importação... Por favor, aguarde.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Botão de fechar quando finalizado -->
  <div class="text-center mt-4 mb-4" *ngIf="importou">
    <button type="button" class="btn btn-success btn-lg" (click)="feche()">
      <i class="fas fa-check mr-2"></i>
      Concluir
    </button>
  </div>
</div>
