import { NgModule } from '@angular/core';
import {RouterModule, Route} from '@angular/router';
import {SuperadminComponent} from "./superadmin.component";
import {CadEmpresasComponent} from "./cad-empresas/cad-empresas.component";
import {ListaEmpresasComponent} from "./lista-empresas/lista-empresas.component";
import {TelaListaLeadsComponent} from "./tela-lista-leads/tela-lista-leads.component";
import {ListaPlanosComponent} from "./lista-planos/lista-planos.component";
import {CadEmpresaPagamentoComponent} from "./cad-empresa-pagamento/cad-empresa-pagamento.component";
import {TelaGerenciarRecebimentosComponent} from "./tela-gerenciar-recebimentos/tela-gerenciar-recebimentos.component";
import {CadastroNumeroWhatsappComponent} from "../numero-whatsapp/cadastro-numero-whatsapp/cadastro-numero-whatsapp.component";
import {ListaDeCampanhasComponent} from "./lista-de-campanhas/lista-de-campanhas.component";
import {GridNfseComponent} from "./grid-nfse/grid-nfse.component";
import {CadGrupoLojasComponent} from "./cad-grupo-lojas/cad-grupo-lojas.component";
import {ListaGrupoLojasComponent} from "./cad-grupo-lojas/lista-grupo-lojas/lista-grupo-lojas.component";
import {TelaResumoFinanceiroEcleticaComponent} from "./tela-resumo-financeiro-ecletica/tela-resumo-financeiro-ecletica.component";
import {PedidosIntegradosComponent} from "./pedidos-integrados/pedidos-integrados.component";
import {ListaFormasPagamentosComponent} from "./lista-formas-pagamentos/lista-formas-pagamentos.component";
import {AuthLojaGuard, AuthSuperAdminGuard} from "../guards/auth-loja.guard";
import {ListaTemplatesPromptsDbComponent} from "../ia/lista-templates-prompts-db/lista-templates-prompts-db.component";
import {TelaTemplatesPromptsDbComponent} from "../ia/tela-templates-prompts-db/tela-templates-prompts-db.component";
import {UserResolver} from "../services/user.resolver";
import { CadCepsCustomizadosComponent } from './cad-ceps-customizados/cad-ceps-customizados.component';
import {ModulosComponent} from "./modulos/modulos.component";
import {FormModuloComponent} from "./modulos/cadastro/form-modulo.component";


const routes: Route[] = [
  {
    path: '', component: SuperadminComponent, canActivate: [AuthSuperAdminGuard],
    resolve: { user: UserResolver },
    children: [
      {path: 'empresas', component: ListaEmpresasComponent},
      {path: 'empresas/nova', component: CadEmpresasComponent},
      {path: 'empresas/nova/pagamento', component: CadEmpresaPagamentoComponent},
      {path: 'empresas/:idEmpresa', component: CadEmpresasComponent },
      {path: 'empresas/:idEmpresa/whatsapp', component: CadastroNumeroWhatsappComponent },
      {path: 'leads', component: TelaListaLeadsComponent},
      {path: 'planos', component: ListaPlanosComponent},
      {path: 'formaspagamento', component: ListaFormasPagamentosComponent },
      {path: 'recebimentos', component: TelaGerenciarRecebimentosComponent },
      {path: 'recebimentos/ecletica/:ano/:mes', component: TelaResumoFinanceiroEcleticaComponent },
      {path: 'grupos-de-lojas', component: ListaGrupoLojasComponent },
      {path: 'prompts-mia/global', component: ListaTemplatesPromptsDbComponent },
      {path: 'prompts-mia/global/nova',  canActivate: [AuthLojaGuard], component: TelaTemplatesPromptsDbComponent},
      {path: 'prompts-mia/global/nova/:id',  canActivate: [AuthLojaGuard], component: TelaTemplatesPromptsDbComponent},
      {path: 'grupos-de-lojas/novo', component: CadGrupoLojasComponent },
      {path: 'grupos-de-lojas/:idGrupo', component: CadGrupoLojasComponent },
      {path: 'nfse', component: GridNfseComponent},
      {path: 'campanhas', component: ListaDeCampanhasComponent},
      {path: 'pedidosintegrados', component: PedidosIntegradosComponent},
      {path: 'ceps-customizados', component: CadCepsCustomizadosComponent },
      {path: 'modulos', component: ModulosComponent },
      {path: 'modulos/novo', component: FormModuloComponent },
      {path: 'modulos/editar/:id', component: FormModuloComponent }
    ]
  },
];


@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SuperAdminRoutingModule { }
