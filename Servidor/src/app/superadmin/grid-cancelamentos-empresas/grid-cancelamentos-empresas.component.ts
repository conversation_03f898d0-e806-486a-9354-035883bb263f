import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {orderBy, process, SortDescriptor, State} from "@progress/kendo-data-query";
import {Router} from "@angular/router";
import {EmpresasService} from "../services/empresas.service";
import {PageChangeEvent} from "@progress/kendo-angular-pager";
declare var moment;
@Component({
  selector: 'app-grid-cancelamentos-empresas',
  templateUrl: './grid-cancelamentos-empresas.component.html',
  styleUrls: ['./grid-cancelamentos-empresas.component.scss']
})
export class GridCancelamentosEmpresasComponent implements OnInit {
  @Input() cancelamentos = [];
  estados: [] = []
  cancelaemntosResultSet: any ;
  public pageSizes = true;
  public previousNext = true;
  confirmarRemocao = false;
  cancelando = false;
  motivoCancelamento: any;
  empresaBloquear: any;
  empresaDesativar: any;
  gridView: any;
  state: State = {
    sort: [],
    skip: 0,
    take: 100,
    filter: {
      logic: 'or',
      filters: []
    }
  };
  loading = true;
  public type: 'numeric' | 'input' = 'numeric';
  filtro: any = {};
  erroBloqueio: boolean;
  erroRemover: string;
  sort: SortDescriptor[] = [{
    field: 'nome'
  }];
  private filtroAtual: any;
  timeoutBusca: any;

  constructor(private router: Router, private empresaService: EmpresasService) { }
  ngOnInit(): void {
    if(!this.estados.length)
      this.empresaService.obtenhaEstados().then( (estados) =>  this.estados = estados)

  }

  exibaCancelamentos() {

    if(!this.cancelamentos || !this.cancelamentos.length){
      this.busqueCancelamentos();
    } else {
      this.loading = false;
      this.filtreCancelamentos(null)
    }
  }

  busqueCancelamentos(dataCancelamento: any = null){
    this.loading = true;
    let params: any = {};
    if(dataCancelamento){
      let mes = (dataCancelamento.getMonth() +  1).toString().padStart(2, '0'),
        dia =  dataCancelamento.getDate().toString().padStart(2, '0');

      params.dt =  String(`${dataCancelamento.getFullYear()}${mes}${dia}`)
    }

   if(this.filtro.exibirOportunidades) params.opt = true;

    this.empresaService.listeCanceladas(params).then((cancelamentos: any) => {
      this.loading = false;
      this.cancelamentos = cancelamentos || [];
      this.cancelamentos.forEach((item: any) => {
        if( item.motivosCancelamento &&  item.motivosCancelamento.length)
          item.motivosCancelamentoTexto = item.motivosCancelamento.map((m: any) => m.codigo).join(', ');
      })


      this.filtreCancelamentos(null)
    })

  }

  onFilter(event: any) {
    if(this.timeoutBusca) clearTimeout(this.timeoutBusca);

    this.timeoutBusca = setTimeout(() => {
      this.filtreCancelamentos(event.target.value)
    }, 500)

  }

  onPageChange($event: PageChangeEvent) {

  }

  private filtreCancelamentos(valor: any, estado: any = null) {
    this.state.filter.filters = [];

    if(valor){
      this.state.filter.filters.push({
        field: 'empresa.nome',
        operator: 'contains',
        value: valor
      })
    }

    if(estado){
      this.state.filter.filters.push({
        field: 'empresa.enderecoCompleto.estado.id',
        operator: 'equals',
        value: estado.id
      })
    }

    this.filtroAtual = valor;

    this.loadData()
  }

  // Atualiza a grid conforme o estado muda (filtros/paginação)
  public loadData(): void {
    //this.cancelaemntosResultSet = orderBy(process(this.cancelamentos,  this.state).data, this.sort);
    this.gridView = process(this.cancelamentos, this.state);
  }

  sortChange($event: any) {
    this.sort = $event;
    this.filtreCancelamentos(this.filtroAtual)
  }

  // Atualiza os filtros, paginação e ordenação
  public onStateChange(state: State): void {
    this.state = state;  // Atualiza o estado geral
    this.loadData();
  }

  mudouEstado(estado: any) {
    this.filtreCancelamentos(this.filtroAtual, estado)
  }

  // Atualiza os filtros
  public onFilterChange(state: State): void {
    this.state = state;
    this.loadData();
  }

  desativarCancelamento(cancelamento: any) {
    cancelamento.processando = true;

    this.empresaService.canceleDesativacao(cancelamento).then( () => {
      this.router.navigateByUrl(String(`/superadmin/empresas/${cancelamento.empresa.id}`));
    }).catch( erro => {
      cancelamento.processando = false;
      alert(erro)
    })

  }

  mudouDataCadastro(data: any){
    this.busqueCancelamentos(data)
  }

  obtenhaNomeArquivoDownload(nome: string) {
    let sufixo = '', horario = new Date().getTime();

    if(this.filtro.rede)
      nome = String(`${nome}-${this.filtro.rede.nome.toLowerCase()}`)

    return String(`${nome}(${sufixo}).xls`)
  }
}
