.col.foto-produto{
  max-width: 371px !important;
  overflow: hidden;
}

::ng-deep .nav-tabs .nav-link.disabled {
  color: #6c757d66 !important;
}

::ng-deep  .foto-produto label{
  text-align: center;
  display: block;
}
fieldset legend {
  color: #656565;
  font-size: 1.1rem;
  background: #eeeeee94;
  padding: 5px 10px;
}




.tamanho-pizza{
  height: 90px;
  display: block;
  img{
    margin: 0 auto;
    display: block;
    position: relative;
  }
  &.x1{
    font-size: 3em !important;
    position: relative;

    img{
      height: 45px;
      top: 15px;

    }
  }

  &.x2{
    position: relative;
    font-size: 3.5em !important;
    top: 10px;

    img{
      height: 55px;
    }
  }

  &.x3{
    font-size: 4em !important;

    img{
      height: 65px;
      top:  5px;
    }
  }

  &.x4{
    font-size: 4.5em !important;

    img{
      height: 75px;
    }
  }
}

.ribbon-light{
  span{
    color: #9E9E9E !important;
  }
}


.container-adicionais{
  max-height: 500px;
  overflow-x: hidden;
  overflow-y: scroll;
}

.titulo-adicional{
  background: #cccccc75;
  line-height: 30px;
  padding-left: 15px;
}

.checkboxlg{
  height: 20px;
  width: 20px;
}


.desativado{
  .tamanho-pizza {
    color: #eee;
  }


}
.form-control:disabled{
  background-color: #9e9e9e30;
}

.preco-antigo{
  ::ng-deep .k-numerictextbox .k-numeric-wrap {
    .k-input{
      text-decoration: line-through;
      color: #aaa;
    }
  }

}

.novo-preco{
  ::ng-deep .k-numerictextbox .k-numeric-wrap {
    border-color: #72747b9c;
    .k-input{
      font-weight: bold !important;
    }
  }

}
.modal-footer p{
  padding: 0;
  margin: 0;
  margin-top: 0px;
  margin-right: 15px;
  font-size: 15px;
}


.row.removido{
  input, ::ng-deep input , p{
    color: #ccc !important;
    border-color:  #ccc !important;
    text-decoration: line-through;
  }

  ::ng-deep .k-numeric-wrap, ::ng-deep  .k-dropdown-wrap{
    border-color: #cccccc5c !important;
  }
}

.disponibilidade{
  .input-group{
    float: left;
    width: auto;
    display: inline-block;
  }
}

.icon-restricao-alimentar{
  width: 25px;
  height: 25px;
  background-size: 25px;
  margin-left: 10px;
  top: -5px;
  position: relative;
  &.organico{
    background-image: url("/assets/icons/icon-produto-organico.png");
  }

  &.vegetariano{
    background-image: url('/assets/icons/icon-produto-vegetariano.png');
  }

  &.vegano{
    background-image: url('/assets/icons/icon-produto-vegano.png');
  }

  &.semacucar{
    background-image:url('/assets/icons/icon-produto-semacucar.png');
  }

  &.semgluten{
    background-image: url('/assets/icons/icon-produto-semgluten.png');
  }

  &.zerolactose{
    background-image:url('/assets/icons/icon-produto-semlactose.png');
  }
}

:host {
  display: block;

  @media (max-width: 768px) {
    .modal-body {
      padding: 8px !important;
    }

    .k-tabstrip {
      .k-tabstrip-items {
        display: flex;
        overflow-x: auto;
        white-space: nowrap;
        -webkit-overflow-scrolling: touch;

        .k-item {
          flex: 0 0 auto;
          font-size: 14px;
          padding: 6px 12px;
        }
      }
    }

    .form-group {
      margin-bottom: 10px;

      label {
        font-size: 14px;
        margin-bottom: 4px;
      }

      .form-control {
        font-size: 14px;
        height: 36px;
      }
    }

    .row {
      margin-left: -8px;
      margin-right: -8px;

      [class*="col-"] {
        padding-left: 8px;
        padding-right: 8px;
      }

      .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
      }

      .col-sm-4, .col-sm-6 {
        flex: 0 0 50%;
        max-width: 50%;
      }
    }

    .card-box {
      margin-bottom: 10px;
      padding: 10px;

      .ribbon {
        font-size: 12px;
        padding: 3px 8px;
      }

      .tamanho-pizza {
        height: 70px;

        img {
          height: 40px !important;
        }
      }
    }

    .foto-produto {
      flex: 0 0 100% !important;
      max-width: 100% !important;
      margin-bottom: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      padding: 16px;

      ::ng-deep {
        .upload-container {
          min-height: 200px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border: 2px dashed #ddd;
          border-radius: 8px;
          padding: 16px;

          img {
            max-width: 100%;
            height: auto;
            margin-bottom: 12px;
          }

          label {
            width: 100%;
            text-align: center;
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
          }

          .btn {
            width: 100%;
            max-width: 200px;
            margin: 8px 0;
          }
        }

        .preview-container {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
          gap: 8px;
          margin-top: 12px;

          img {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 4px;
          }
        }
      }
    }

    .btn {
      font-size: 14px;
      padding: 6px 12px;

      &.btn-block {
        margin-bottom: 8px;
      }
    }

    .k-dropdown {
      width: 100% !important;

      .k-dropdown-wrap {
        height: 36px;

        .k-input {
          font-size: 14px;
        }
      }
    }

    .k-numerictextbox {
      width: 100% !important;

      .k-numeric-wrap {
        height: 36px;

        .k-input {
          font-size: 14px;
        }
      }
    }

    .container-adicionais {
      max-height: 400px;
      padding: 8px;

      .titulo-adicional {
        font-size: 14px;
        padding: 8px;
      }
    }

    .modal-footer {
      padding: 10px;
      flex-wrap: wrap;

      button {
        margin: 4px;
        min-width: 80px;
      }

      p {
        width: 100%;
        margin-bottom: 8px;
        font-size: 13px;
      }
    }

    // Ajusta o layout após o upload
    .col:not(.foto-produto) {
      flex: 0 0 100%;
      max-width: 100%;
      padding: 0 8px;
    }

    fieldset {
      margin: 0 -8px;
      padding: 12px;
      background: #fff;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);

      legend {
        width: auto;
        margin: -24px auto 12px;
        padding: 4px 12px;
        font-size: 16px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 20px;
        color: #495057;
      }
    }
  }

  .k-animation-container {
    transition: all 0.3s ease-in-out;
  }
}

// Estilos específicos para mobile
.mobile-tabs {
  .k-tabstrip-items {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0 8px;
  }
}

.mobile-title {
  font-size: 18px;
  margin: 12px 0;
  padding: 0 8px;
}

.mobile-buttons {
  padding: 8px;

  .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 44px;

    i {
      font-size: 16px;
    }
  }
}

.mobile-form {
  padding: 8px;

  .foto-produto {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
  }
}

.mobile-pricing {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px 8px;
  margin: 0 -4px 16px;
}

.mobile-footer {
  position: sticky;
  bottom: 0;
  background: white;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.1);

  .messages {
    width: 100%;
    margin-bottom: 8px;
  }

  .buttons {
    display: flex;
    gap: 8px;
    width: 100%;

    button {
      flex: 1;
      min-height: 44px;
    }
  }
}

// Melhorias para cards de pizza no mobile
@media (max-width: 768px) {
  .card-box.ribbon-box {
    position: relative;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    margin: 8px 4px 16px;
    padding: 0;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.08);

    // Novo estilo do ribbon
    .ribbon {
      position: absolute;
      left: -5px;
      top: 0;
      z-index: 1;
      overflow: hidden;
      width: auto;
      height: 30px;
      text-align: center;
      font-size: 13px;
      line-height: 30px;
      padding: 0 12px;
      background: #02a8b5;
      color: #fff;
      border-radius: 0 3px 3px 0;

      &:before {
        position: absolute;
        content: "";
        top: 100%;
        left: 0px;
        border: 3px solid #019aa6;
        border-right-color: transparent;
        border-bottom-color: transparent;
      }

      &.ribbon-info {
        background: #02a8b5;
        &:before {
          border-color: #019aa6;
          border-right-color: transparent;
          border-bottom-color: transparent;
        }
      }

      &.ribbon-primary {
        background: #5b69bc;
        &:before {
          border-color: #4a59b4;
          border-right-color: transparent;
          border-bottom-color: transparent;
        }
      }

      &.ribbon-success {
        background: #35b8e0;
        &:before {
          border-color: #21a5cd;
          border-right-color: transparent;
          border-bottom-color: transparent;
        }
      }

      &.ribbon-warning {
        background: #f9c851;
        &:before {
          border-color: #f8bc2c;
          border-right-color: transparent;
          border-bottom-color: transparent;
        }
      }

      &.ribbon-light {
        background: #9e9e9e;
        &:before {
          border-color: #919191;
          border-right-color: transparent;
          border-bottom-color: transparent;
        }
      }

      span {
        font-size: 13px;
        color: #fff;
        text-transform: uppercase;
        white-space: nowrap;
      }
    }

    .ribbon-content {
      padding: 40px 12px 12px; // Espaço para o ribbon no topo
    }

    .tamanho-pizza {
      height: auto;
      min-height: 80px;
      padding: 12px 0;
      margin: 0 -12px;
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        height: 48px !important;
        margin: 0 !important;
        position: static !important;
      }

      &.x1 img { height: 36px !important; }
      &.x2 img { height: 42px !important; }
      &.x3 img { height: 46px !important; }
      &.x4 img { height: 48px !important; }
    }

    .form-group {
      margin-bottom: 12px;

      label {
        font-size: 13px;
        color: #6c757d;
        margin-bottom: 4px;
      }

      .form-control {
        height: 36px;
        font-size: 14px;
      }
    }

    // Checkbox de disponibilidade
    .checkboxlg {
      width: 24px;
      height: 24px;
      margin: 8px auto;
      display: block;
    }

    // Área de desconto
    .mt-2 {
      &.preco-antigo {
        position: relative;
        padding-bottom: 8px;

        &:after {
          content: '';
          position: absolute;
          left: 0;
          right: 0;
          bottom: 4px;
          height: 1px;
          background: #dee2e6;
        }
      }
    }

    .novo-preco {
      background: #f8f9fa;
      padding: 8px;
      border-radius: 4px;
      margin-top: 12px;

      label {
        color: #495057;
        font-weight: 500;
      }
    }

    // Botões
    .btn-xs {
      width: 100%;
      padding: 8px;
      font-size: 13px;
      margin: 4px 0;
    }
  }

  // Status de indisponível
  .desativado {
    opacity: 0.7;

    .tamanho-pizza {
      background: #f1f3f5;

      img {
        opacity: 0.5;
      }
    }

    .ribbon {
      background: #adb5bd !important;
    }
  }

  // Grid de tamanhos
  [class*="col-3"] {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0 8px;

    @media (min-width: 480px) {
      flex: 0 0 50%;
      max-width: 50%;
    }
  }
}

.upload-title {
  font-size: 16px;
  color: #495057;
  margin-bottom: 12px;
  padding-left: 4px;
}

@media (max-width: 768px) {
  .foto-produto {
    background: #fff;
    border: 1px solid #e9ecef;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);

    .upload-title {
      text-align: center;
      padding: 8px 0;
      margin: -16px -16px 16px -16px;
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      border-radius: 8px 8px 0 0;
    }
  }

  .produto-info {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    margin-top: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  }

  // Melhora visualização das imagens
  ::ng-deep .preview-container {
    img {
      border: 2px solid #fff;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }
}

// Estilos base para mobile
@media (max-width: 768px) {
  .modal-body {
    padding: 8px !important;
  }

  // Campos de formulário
  .form-group {
    margin-bottom: 16px;

    label {
      display: block;
      font-size: 14px;
      color: #495057;
      margin-bottom: 6px;
    }

    .form-control,
    .k-dropdown,
    .k-numerictextbox {
      width: 100% !important;
      height: 40px;
      font-size: 14px;
    }

    textarea.form-control {
      height: auto;
      min-height: 80px;
    }
  }

  // Cards de pizza
  .card-box.ribbon-box {
    margin-bottom: 16px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.08);

    .ribbon {
      left: 25px;
      border-radius: 8px 0 8px 0;
      height: 28px;
      line-height: 28px;
      padding: 0 12px;
      font-size: 12px;
    }

    .ribbon-content {
      padding: 36px 12px 12px;
    }

    .tamanho-pizza {
      margin: -12px -12px 12px;
      padding: 16px;
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      text-align: center;

      img {
        height: 40px !important;
        display: block;
        margin: 0 auto !important;
      }
    }
  }

  // Área de upload
  .foto-produto {
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;

    .upload-title {
      text-align: center;
      margin: -16px -16px 16px;
      padding: 12px;
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      border-radius: 8px 8px 0 0;
      font-size: 14px;
      font-weight: 500;
    }
  }

  // Tabs
  .k-tabstrip {
    .k-tabstrip-items {
      display: flex;
      overflow-x: auto;
      white-space: nowrap;
      -webkit-overflow-scrolling: touch;
      background: #f8f9fa;
      border-bottom: 1px solid #dee2e6;
      padding: 0;

      .k-item {
        flex: 0 0 auto;
        font-size: 14px;
        padding: 8px 16px;
      }
    }
  }

  // Footer
  .modal-footer {
    position: sticky;
    bottom: 0;
    background: #fff;
    padding: 12px;
    box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
    margin: 0 -8px -8px;
    border-radius: 0 0 8px 8px;

    .messages {
      margin-bottom: 12px;
    }

    .buttons {
      display: flex;
      gap: 8px;

      button {
        flex: 1;
        min-height: 40px;
        font-size: 14px;
      }
    }
  }

  // Campos de preço e desconto
  .mobile-pricing {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;

    .form-group {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // Checkbox e radio
  .k-checkbox-label,
  .k-radio-label {
    font-size: 14px;
    margin: 8px 0;
    display: block;
  }

  .checkboxlg {
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }
}

@media (max-width: 768px) {
  ::ng-deep {
    .k-dialog {
      width: 100vw !important;
      max-width: 100vw !important;
      height: 100vh !important;
      max-height: 100vh !important;
      margin: 0 !important;
      padding: 0 !important;
      border-radius: 0 !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      transform: none !important;
    }

    .k-dialog-wrapper {
      padding: 0 !important;
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100% !important;
      height: 100% !important;
      transform: none !important;
      background: #fff !important;
      z-index: 10000000 !important;
    }

    .k-window-content {
      padding: 0 !important;
      height: 100vh !important;
      max-height: 100vh !important;
      display: flex !important;
      flex-direction: column !important;
    }

    kendo-dialog-titlebar {
      position: sticky !important;
      top: 0 !important;
      z-index: 10 !important;
      background: #5f2cb6 !important;
      padding: 10px !important;
      border-bottom: 1px solid #dee2e6 !important;

      .k-window-title {
        margin: 0 !important;
        font-size: 16px !important;
        color: white !important;

        h4 {
          color: white !important;
        }
      }
    }

    .modal-body {
      flex: 1 !important;
      overflow-y: auto !important;
      padding: 10px !important;
      -webkit-overflow-scrolling: touch !important;
    }

    .modal-footer {
      position: sticky !important;
      bottom: 0 !important;
      background: white !important;
      padding: 10px !important;
      border-top: 1px solid #dee2e6 !important;
      box-shadow: 0 -2px 10px rgba(0,0,0,0.1) !important;
      z-index: 10 !important;

      .buttons {
        display: flex !important;
        flex-direction: column !important;
        gap: 8px !important;
        width: 100% !important;

        button {
          width: 100% !important;
          margin: 0 !important;
          min-height: 44px !important;
        }
      }
    }
  }
}

.mobile-title {
  margin: 15px 0;
  font-size: 18px;
}

.mobile-buttons {
  padding: 0 10px;

  button {
    margin-bottom: 10px;
  }
}


.k-tabstrip-items-wrapper {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: inherit;
  position: relative;
  z-index: 1 !important;
}
