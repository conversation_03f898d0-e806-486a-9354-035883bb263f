<div class="container-fluid ">
  <div class="row">
    <div class="col-12 ">
      <div class="card mt-4">
        <div class="card-header">
          <h4 class="card-title mb-0">
            <i class="fas fa-cubes mr-2"></i>
            Gerenciar Módulos
          </h4>
        </div>
        <div class="card-body">

          <div class="row mb-3">
            <div class="col-12">
              <button type="button" class="btn btn-primary" (click)="novoModulo()">
                <i class="fas fa-plus mr-1"></i>
                Novo Módulo
              </button>
            </div>
          </div>

          <div class="row" *ngIf="carregando">
            <div class="col-12 text-center">
              <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Carregando...</span>
              </div>
            </div>
          </div>

          <div class="row" *ngIf="!carregando">
            <div class="col-12">
              <kendo-grid
                [data]="modulos"

                [pageable]="true"
                [sortable]="true"
                [filterable]="true">

                <kendo-grid-column field="id" title="ID" [width]="80">
                </kendo-grid-column>

                <kendo-grid-column field="nome" title="Nome" [width]="200">
                  <ng-template kendoGridCellTemplate let-dataItem>
                      <span class="text-primary"><b>{{dataItem.nome}}</b></span>
                  </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="descricao" title="Descrição" [width]="300">
                </kendo-grid-column>

                <kendo-grid-column field="valorMensalidade" title="Valor Mensalidade" [width]="150">
                  <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem.valorMensalidade | currency:'BRL':'symbol':'1.2-2' }}
                  </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="valorAtivacao" title="Valor Ativação" [width]="150">
                  <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem.valorAtivacao | currency:'BRL':'symbol':'1.2-2' }}
                  </ng-template>
                </kendo-grid-column>

                <kendo-grid-column title="Ações" [width]="150">
                  <ng-template kendoGridCellTemplate let-dataItem>
                    <button type="button"
                            class="btn btn-sm btn-outline-primary mr-1"
                            (click)="editarModulo(dataItem)"
                            title="Editar">
                      <i class="fas fa-edit"></i>
                    </button>
                    <button type="button"  [hidden]="true"
                            class="btn btn-sm btn-outline-danger"
                            (click)="removerModulo(dataItem)"
                            title="Remover">
                      <i class="fas fa-trash"></i>
                    </button>
                  </ng-template>
                </kendo-grid-column>

              </kendo-grid>
            </div>
          </div>

          <div class="row" *ngIf="!carregando && modulos.length === 0">
            <div class="col-12 text-center">
              <div class="alert alert-info">
                <i class="fas fa-info-circle mr-2"></i>
                Nenhum módulo cadastrado.
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>
