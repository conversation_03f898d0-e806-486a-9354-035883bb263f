h5{
  color: #fff;
  font-size: 17px;
}

// Estilos para a tela de ativação de módulos

.coluna-modulos{
  max-width: 550px !important;
}

.card {
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  background: #ffffff;

  h6{
    font-size: 1.2em;
    color: #495057;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border-color: #dee2e6;
  }

  &.h-100 {
    height: 100% !important;

    .card-body {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
  }

  // Headers dos painéis principais mantêm as cores
  .card-header {
    &.bg-success {
      background: linear-gradient(135deg, #28a745, #20c997) !important;
      border-bottom: none;
    }

    &.bg-primary {
      background: linear-gradient(135deg, #007bff, #6610f2) !important;
      border-bottom: none;
    }

    &.bg-warning {
      background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
      border-bottom: none;
    }
  }

  .card-body {
    padding: 1rem;
  }

  .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }

  .btn-outline-primary {
    &:hover {
      color: #fff;
    }
  }
}

// Estilos para os cards de módulos individuais
.modulo-card {
  &.ativo,
  &.disponivel {
    .card {
      // Usar estilo padrão neutro
      border-color: #e9ecef;
      background: #ffffff;

      &:hover {
        border-color: #adb5bd;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }
}

// Botões com animações
.btn {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;

    &:hover {
      transform: none;
      box-shadow: none;
    }
  }

  &.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
    font-weight: 600;

    .badge {
      animation: pulse 2s infinite;
    }
  }

  &.btn-outline-primary {
    border-width: 2px;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #007bff, #0056b3);
      border-color: #0056b3;
    }
  }

  &.btn-outline-danger {
    border-width: 2px;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #dc3545, #c82333);
      border-color: #c82333;
    }
  }
}

// Badges e indicadores
.badge {
  font-size: 0.75rem;
  padding: 0.4rem 0.6rem;

  &.badge-success {
    background: linear-gradient(135deg, #28a745, #20c997);
  }

  &.badge-secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
  }

  &.badge-info {
    background: linear-gradient(135deg, #6c757d, #495057);
    font-size: 0.65rem;
    padding: 0.25rem 0.4rem;
    border-radius: 0.375rem;
  }

  &.badge-light {
    color: #495057;
    background: rgba(255, 255, 255, 0.8);
  }
}

// Modal customizado
.modal {
  .modal-header {
    &.bg-primary {
      background: linear-gradient(135deg, #007bff, #6610f2) !important;
      border-bottom: none;
    }
  }

  .modal-content {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  }

  .close {
    &.text-white {
      color: #fff !important;
      opacity: 0.8;

      &:hover {
        opacity: 1;
      }
    }
  }
}

// Cards com seleção no modal
.card.border-primary.bg-light {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb) !important;
  border-color: #007bff !important;
  border-width: 2px;

  .custom-control-label {
    font-weight: 600;
    color: #0056b3;
  }
}

// Alertas personalizados
.alert {
  border: none;
  border-radius: 0.5rem;
  padding: 1rem 1.25rem;

  &.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border-left: 4px solid #28a745;
  }

  &.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f1b0b7);
    color: #721c24;
    border-left: 4px solid #dc3545;

    .fas.fa-lock {
      color: #dc3545;
      font-size: 1.1rem;
    }
  }

  &.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
    border-left: 4px solid #17a2b8;
  }

  &.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
    border-left: 4px solid #ffc107;
  }
}

// Spinner de loading
.spinner-border {
  &.text-primary {
    color: #007bff !important;
  }
}

// Estados de carregamento
.loading-overlay {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
    z-index: 10;
  }
}

// Ícones com animações
.fa, .fas {
  transition: all 0.3s ease;

  &.fa-spin {
    animation: fa-spin 1s infinite linear;
  }
}

// Animações personalizadas
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Aplicar fadeIn aos cards
.modulo-card {
  animation: fadeIn 0.5s ease-in-out;

  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }
  &:nth-child(5) { animation-delay: 0.5s; }
  &:nth-child(6) { animation-delay: 0.6s; }
}

// Responsividade aprimorada
@media (max-width: 768px) {
  .btn-lg {
    font-size: 1rem;
    padding: 0.5rem 1rem;
  }

  .card-title {
    font-size: 1rem;
  }

  .modal-dialog {
    margin: 0.5rem;
  }
}

// Estados hover para melhor UX
.custom-control-input:checked ~ .custom-control-label::before {
  background: linear-gradient(135deg, #007bff, #0056b3);
  border-color: #007bff;
}

.custom-control-label::before {
  border-width: 2px;
  transition: all 0.3s ease;
}

.custom-control-label:hover::before {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

// Estilos para valores monetários
.valor-monetario {
  font-family: 'Roboto Mono', monospace;
  font-weight: 600;
  color: #495057;

  &.valor-gratis {
    color: #28a745;
    text-shadow: 0 1px 2px rgba(40, 167, 69, 0.2);
  }

  &.valor-pago {
    color: #495057;
  }
}

// Estilos para inputs editáveis de valores
.input-group-sm {
  .form-control {
    font-family: 'Roboto Mono', monospace;
    font-size: 0.8rem;

    &:focus {
      border-color: #007bff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  }

  .input-group-text {
    font-size: 0.8rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-color: #ced4da;
    color: #495057;
  }
}

// Informações de contrato
.contrato-info {
  background: linear-gradient(135deg, #e8f4fd, #d1ecf1);
  border-radius: 0.375rem;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  border-left: 3px solid #17a2b8;

  .fas {
    color: #17a2b8;
  }
}

// Comparação de valores (original vs editado)
.valor-comparacao {
  font-size: 0.75rem;
  font-style: italic;

  &.valor-alterado {
    color: #fd7e14;
    font-weight: 600;

    &::before {
      content: "✏️ ";
      margin-right: 0.25rem;
    }
  }
}

// Melhorias no layout do resumo da fatura
.resumo-fatura {
  background: linear-gradient(135deg, #fff8e1, #fff3c4);
  border: 2px solid #ffc107;
  border-radius: 0.75rem;

  .card-header {
    &.bg-light {
      background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
      border-bottom: 1px solid #dee2e6;
      border-radius: 0.375rem 0.375rem 0 0;
    }
  }

  .total-fatura {
    font-size: 1.25rem;
    font-weight: 700;
    color: #007bff;
    text-shadow: 0 1px 2px rgba(0, 123, 255, 0.2);
  }

  // Cards individuais dos módulos no resumo
  .card.border-light {
    border: 1px solid #e9ecef !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 1rem;

    &:hover {
      border-color: #007bff !important;
      box-shadow: 0 2px 6px rgba(0, 123, 255, 0.15);
      transform: translateY(-1px);
    }

    .card-header {
      border-bottom: 1px solid #dee2e6;

      h6 {
        margin-bottom: 0.25rem;
        color: #495057;
      }
    }

    .input-group {
      max-width: 150px;

      .form-control {
        font-family: 'Roboto Mono', monospace;
        font-weight: 600;
        font-size: 0.9rem;

        &:focus {
          border-color: #0056b3;
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
      }

      .input-group-text {
        font-weight: 600;
        font-size: 0.8rem;

        &.text-success {
          background: linear-gradient(135deg, #28a745, #20c997);
          color: white;
          border-color: #28a745;
        }

        &.text-primary {
          background: linear-gradient(135deg, #007bff, #0056b3);
          color: white;
          border-color: #007bff;
        }
      }
    }

    // Indicadores de alteração de valor
    .text-muted small {
      font-style: italic;

      .fas {
        &.fa-percentage {
          color: #28a745;
        }
      }
    }

    // Separador entre mensalidade e ativação
    hr {
      margin: 0.75rem 0;
      border-color: #dee2e6;
    }
  }

  // Valores comparativos
  .h6 {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;

    &.text-primary {
      color: #007bff !important;
      font-weight: 600;
    }

    &.text-success {
      color: #28a745 !important;
      font-weight: 600;
    }

    &.text-muted {
      color: #6c757d !important;
      opacity: 0.7;
    }
  }

  // Card de resumo total
  .card.bg-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7) !important;
    border: 2px solid #ffc107;
    border-radius: 0.5rem;
    margin-top: 1rem;

    .card-body {
      padding: 1.25rem;
    }

    .h5 {
      margin-bottom: 0.5rem;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

      &.text-success {
        color: #28a745 !important;
      }

      &.text-primary {
        color: #007bff !important;
      }
    }

    hr {
      border-color: #ffc107;
      margin: 1rem 0;
    }
  }
}

// Estilos para controle de visualização de módulos
.controle-visualizacao {
  .alert {
    border-radius: 0.5rem;
    border: 1px solid #bee5eb;

    .btn {
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }
}

// Contador de módulos no header
.badge-contador {
  background: linear-gradient(135deg, #6c757d, #495057);
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
  animation: fadeIn 0.3s ease-in-out;
}

// Botões de alternar visualização
.btn-toggle-modulos {
  transition: all 0.3s ease;
  border-radius: 0.375rem;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
  }

  .fas {
    transition: transform 0.3s ease;
  }

  &:hover .fas {
    transform: scale(1.1);
  }
}

// Mensagem de módulos ocultos
.modulos-ocultos-info {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px dashed #6c757d;

  .btn {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    color: white;

    &:hover {
      background: linear-gradient(135deg, #0056b3, #004085);
      transform: translateY(-1px);
    }
  }
}

.configuracoes-modulo {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 1rem;
  margin-top: 1rem;

  .form-group {
    margin-bottom: 1rem;
  }

  .k-radio-label {
    margin-bottom: 0.5rem;
  }

  .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
  }

  .invalid-feedback {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
  }
}

// Gradientes e cores
.bg-gradient-primary {
  background: linear-gradient(45deg, #007bff, #0056b3) !important;
}

.border-left-danger {
  border-left: 4px solid #dc3545 !important;
}

.border-left-info {
  border-left: 4px solid #17a2b8 !important;
}

.border-left-success {
  border-left: 4px solid #28a745 !important;
}

// Cards de resumo
.card {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  }
}

// Módulos ativos
.modulo-ativo {
  .card {
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 20px rgba(40, 167, 69, 0.2);
    }
  }

  .card-header {
    background: linear-gradient(90deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05)) !important;
  }
}

// Módulos disponíveis
.modulo-disponivel {
  .card {
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      box-shadow: 0 4px 20px rgba(0, 123, 255, 0.2);
      transform: translateY(-2px);
    }
  }

  .pricing-info {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #e9ecef;
  }
}

// Configurações de módulos
.configuracoes-modulo {
  background: rgba(0, 123, 255, 0.02);
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;

  .form-group {
    margin-bottom: 1.2rem;
  }

  .form-label {
    color: #495057;
    margin-bottom: 0.5rem;
  }

  .custom-control-label {
    padding-left: 0.5rem;

    i {
      color: #6c757d;
    }
  }

  .form-text {
    font-size: 0.8rem;
    line-height: 1.4;
  }
}

// Badge personalizado
.badge-outline-success {
  color: #28a745;
  border: 1px solid #28a745;
  background: transparent;
}

.badge-outline-primary {
  color: #007bff;
  border: 1px solid #007bff;
  background: transparent;
}

// Faturas pendentes
.modulos-fatura {
  .badge {
    margin-right: 5px;
    margin-bottom: 5px;
  }
}

// Estados vazios
.text-center {
  i {
    opacity: 0.6;
  }
}

// Loading melhorado
.spinner-border {
  width: 3rem;
  height: 3rem;
}

// Botões
.btn {
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  }

  &.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
  }
}

// Alertas
.alert {
  border-radius: 8px;
  border: none;

  &.alert-info {
    background: linear-gradient(90deg, rgba(23, 162, 184, 0.1), rgba(23, 162, 184, 0.05));
    color: #0c5460;
  }

  &.alert-success {
    background: linear-gradient(90deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
    color: #155724;
  }

  &.alert-warning {
    background: linear-gradient(90deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
    color: #856404;
  }

  &.alert-danger {
    background: linear-gradient(90deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05));
    color: #721c24;
  }
}

// Tabelas
.table {
  th {
    background: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.9rem;
    color: #495057;
  }

  td {
    vertical-align: middle;
  }
}

// Scrollbar personalizada
.card-body {
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// Responsividade
@media (max-width: 991.98px) {
  .col-lg-6 {
    margin-bottom: 1.5rem;
  }

  .btn-lg {
    font-size: 1rem;
    padding: 0.6rem 1.2rem;
  }
}

@media (max-width: 575.98px) {
  .card-header {
    .card-title {
      font-size: 1.1rem;
    }

    .badge {
      font-size: 0.7rem;
    }
  }

  .btn-group {
    flex-direction: column;

    .btn {
      margin-bottom: 0.25rem;
    }
  }
}

// Animações
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modulo-ativo,
.modulo-disponivel {
  animation: fadeIn 0.5s ease forwards;
}

// Estados de hover
.btn-outline-danger:hover {
  background-color: #dc3545;
  border-color: #dc3545;
  color: #fff;
}

.btn-outline-primary:hover {
  background-color: #007bff;
  border-color: #007bff;
  color: #fff;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  border-color: #6c757d;
  color: #fff;
}

// Layout horizontal para módulos
.modulos-horizontal-container {
  position: relative;

  .modulos-horizontal-scroll {
    display: flex;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 10px 0;
    gap: 20px;
    scroll-behavior: smooth;

    // Scrollbar personalizada
    &::-webkit-scrollbar {
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

// Cards horizontais dos módulos
.modulo-card-horizontal {
  min-width: 320px;
  max-width: 320px;
  flex-shrink: 0;
  height: auto;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  }

  .card-body {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}

// Módulos ativos horizontais
.modulo-ativo-horizontal {
  .modulo-card-horizontal {
    border-color: #28a745 !important;

    &:hover {
      box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    }
  }
}

// Módulos disponíveis horizontais
.modulo-disponivel-horizontal {
  .modulo-card-horizontal {
    border-color: #007bff !important;

    &:hover {
      box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
    }
  }
}

// Configurações compactas
.configuracoes-modulo-compacta {
  .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
  }

  .configuracoes-expandidas {
    background: rgba(0, 123, 255, 0.05);
    border-radius: 6px;
    padding: 10px;
    border: 1px solid rgba(0, 123, 255, 0.1);

    .form-group {
      margin-bottom: 0.8rem;
    }

    .form-label {
      font-size: 0.75rem;
      margin-bottom: 0.25rem;
    }

    .form-control-sm {
      font-size: 0.8rem;
      padding: 0.25rem 0.5rem;
    }

    .custom-control-label {
      font-size: 0.8rem;
      padding-left: 0.25rem;
    }

    .custom-control {
      margin-bottom: 0.25rem;
    }

    kendo-switch {
      transform: scale(0.8);
    }

    kendo-dropdownlist {
      font-size: 0.8rem;
    }
  }
}

// Controles de scroll
.scroll-controls {
  opacity: 0.8;
  font-size: 0.8rem;

  i {
    opacity: 0.7;
  }
}

// Cards de resumo mantêm o layout original
.card {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  }
}

// Responsividade para layout horizontal
@media (max-width: 768px) {
  .modulo-card-horizontal {
    min-width: 280px;
    max-width: 280px;
  }

  .scroll-controls {
    display: none;
  }

  .btn-lg {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
  }
}

@media (max-width: 576px) {
  .modulo-card-horizontal {
    min-width: 250px;
    max-width: 250px;
  }

  .modulos-horizontal-scroll {
    gap: 15px;
  }

  .configuracoes-modulo-compacta {
    .configuracoes-expandidas {
      padding: 8px;

      .form-label {
        font-size: 0.7rem;
      }

      .form-control-sm {
        font-size: 0.75rem;
      }
    }
  }
}

// Animação para entrada dos cards
.modulo-ativo-horizontal,
.modulo-disponivel-horizontal {
  animation: slideInRight 0.5s ease forwards;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Gradientes mantidos
.bg-gradient-primary {
  background: linear-gradient(45deg, #007bff, #0056b3) !important;
}

.border-left-danger {
  border-left: 4px solid #dc3545 !important;
}

// Botões de controle das configurações
.btn-group-sm {
  .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;

    &:hover {
      transform: translateY(-1px);
    }
  }
}

// Animação para expandir/recolher configurações
.configuracoes-expandidas {
  animation: slideDown 0.3s ease-out;
  overflow: hidden;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    max-height: 500px;
    transform: translateY(0);
  }
}

.text-white {
  color: #fff !important;
}
