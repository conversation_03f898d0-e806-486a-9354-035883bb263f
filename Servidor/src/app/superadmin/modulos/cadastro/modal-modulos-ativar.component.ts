import {Component, OnInit} from "@angular/core";
import {ModalKendo} from "../../../lib/ModalKendo";
import {ModulosService} from "../../../services/modulos.service";

@Component({
  selector: 'app-modal-modulos-ativar',
  templateUrl: './modal-modulos-ativar.component.html',
  styleUrls: ['./modal-modulos-ativar.component.scss']
})
export class ModalModulosEmpresaComponent extends ModalKendo implements OnInit {
  empresa: any = {};

  modulosDisponiveis: any[] = [];
  modulosSelecionados: any[] = [];
  carregando = true;
  processandoAtivacao = false;
  totalAtivacao = 0;
  totalMensalidades = 0;
  mostrarTodosModulos = true;
  moduloInicialSelecionado: any = null;
  numeroParcelas = 1;
  mensagemErro: string;
  mensagemSucesso: string;
   constructor(    private modulosService: ModulosService) {
     super()
   }

    ngOnInit() {
      // Reset seleções
      this.modulosDisponiveis.forEach(modulo => modulo.selecionado = false);
      this.modulosSelecionados = [];
      this.totalAtivacao = 0;
      this.totalMensalidades = 0;
      this.mostrarTodosModulos = true;
      this.moduloInicialSelecionado = null;
      this.numeroParcelas = this.modulosDisponiveis[0].numeroParcelas
      this.limpeMensagens();
    }

  confirmarAtivacao(): void {
    if (this.modulosSelecionados.length === 0) {
      this.mensagemErro = 'Selecione pelo menos um módulo para ativar.';
      return;
    }

    this.processandoAtivacao = true;
    this.limpeMensagens();

    // Se tem valor de ativação, gerar fatura
    if (this.totalPagar) {
      this.gerarFaturaAtivacao();
    } else {
      // Ativação gratuita
      this.ativarModulosGratuitos();
    }
  }

  gerarFaturaAtivacao(): void {

    const dadosFatura = {
      numeroParcelas: this.numeroParcelas || 1,
      modulos:  this.modulosSelecionados,
      valor: this.totalPagar
    };

    this.modulosService.gerarFaturaAtivacaoModulos(this.empresa.id, dadosFatura).then((resposta: any) => {
      this.processandoAtivacao = false;
      this.fecheModal({ modulosAtivados: this.modulosSelecionados});
    }).catch((erro) => {
      this.processandoAtivacao = false;
      console.error('Erro ao gerar fatura:', erro);
      this.mensagemErro = (erro.message || erro);
    });
  }

  ativarModulosGratuitos(): void {
    const modulosGratuitos = this.modulosSelecionadosGratuitos;

    this.modulosService.ativeModulosGratuitos(this.empresa.id, modulosGratuitos).then((resposta: any) => {
      this.mensagemSucesso = 'Módulos ativados com sucesso!';
      this.fecheModal({ modulosAtivados: modulosGratuitos});

    }).catch((erro) => {
      this.processandoAtivacao = false;
      console.error('Erro ao ativar módulos:', erro);
      this.mensagemErro = 'Erro ao ativar módulos: ' + (erro.message || erro);
    });
  }


  // Método para atualizar valor da mensalidade de um módulo
  atualizarValorMensalidade(modulo: any, novoValor: number): void {
    // Recalcular total se o módulo estiver selecionado
    if (modulo.selecionado) {
      this.calcularTotal();
    }

  }

  // Método para atualizar valor de ativação de um módulo
  atualizarValorAtivacao(modulo: any, novoValor: number): void {
    if (modulo.selecionado) {
      this.calcularTotal();
    }

  }

  calcularTotal(): void {
    this.modulosSelecionados = this.modulosDisponiveis.filter(modulo => modulo.selecionado);

    this.totalAtivacao = 0;
    this.totalMensalidades = 0;

    this.modulosSelecionados.forEach((modulo: any) => {
       modulo.totalPagar = 0;
       modulo.itensFaturaveis.forEach((item: any) => {
          if(!item.taxa){
            this.totalMensalidades += item.total
          } else {
            this.totalAtivacao += item.total;
          }

          modulo.totalPagar += item.total;
       })
    })
  }


  toggleExibirTodosModulos(): void {
    this.mostrarTodosModulos = !this.mostrarTodosModulos;

    if (!this.mostrarTodosModulos) {
      // Se está ocultando outros módulos, manter apenas o inicial selecionado
      this.modulosDisponiveis.forEach(m => {
        if (m.id !== this.moduloInicialSelecionado?.id) {
          m.selecionado = false;
        }
      });
      this.calcularTotal();
    }
  }

  get modulosParaExibir(): any[] {
     let modulosExibir: any = this.modulosDisponiveis.filter((modulo: any) => !modulo.aguardandoAtivacao);

    if (this.mostrarTodosModulos) {
      return modulosExibir;
    } else {
      return modulosExibir.filter(m => m.id === this.moduloInicialSelecionado?.id);
    }
  }

  limpeMensagens(): void {
    this.mensagemErro = '';
    this.mensagemSucesso = '';
  }

  get modulosSelecionadosGratuitos(): any[] {
    return this.modulosSelecionados.filter(m => (m.totalPagar) === 0);
  }

  get modulosSelecionadosComTaxa(): any[] {
    return this.modulosSelecionados.filter(m => (m.totalPagar) > 0);
  }

  get totalPagar(){
     return this.totalAtivacao + this.totalMensalidades;
  }


}
