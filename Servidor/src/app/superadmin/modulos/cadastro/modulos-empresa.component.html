<div class="container-fluid">
  <div class="row">
     <div class="col-12">
       <div class="card">
         <div class="card-header bg-gradient-primary text-white">
           <h4 class="card-title mb-0 text-white">
             <i class="fas fa-cubes mr-2"></i>
             Gestão de Módulos da Empresa
           </h4>
           <p class="mb-0 mt-1 opacity-8">
             <small>Gerencie os módulos ativos e ative novos recursos para sua empresa</small>
           </p>
         </div>
         <div class="card-body">
           <!-- Mensagens -->
           <div class="alert alert-danger alert-dismissible fade show" role="alert" *ngIf="mensagemErro">
             <i class="fas fa-exclamation-circle mr-2"></i>
             {{ mensagemErro }}
             <button type="button" class="close" (click)="limpeMensagens()" aria-label="Close">
               <span aria-hidden="true">&times;</span>
             </button>
           </div>

           <div class="alert alert-success alert-dismissible fade show" role="alert" *ngIf="mensagemSucesso">
             <i class="fas fa-check-circle mr-2"></i>
             <span [innerHTML]="mensagemSucesso"></span>
             <button type="button" class="close" (click)="limpeMensagens()" aria-label="Close">
               <span aria-hidden="true">&times;</span>
             </button>
           </div>

           <!-- Loading -->
           <div class="text-center py-5" *ngIf="carregando">
             <div class="spinner-border text-primary mb-3" role="status">
               <span class="sr-only">Carregando...</span>
             </div>
             <h5 class="text-muted">Carregando módulos...</h5>
             <p class="text-muted">Aguarde enquanto buscamos as informações dos módulos</p>
           </div>

           <!-- Alerta de Empresa Bloqueada -->
           <div class="alert alert-danger border-left-danger" *ngIf="empresaBloqueada">
             <div class="d-flex align-items-center">
               <i class="fas fa-lock fa-2x mr-3 text-danger"></i>
               <div>
                 <h5 class="alert-heading mb-1">Empresa Bloqueada</h5>
                 <p class="mb-0">Esta empresa está bloqueada e não pode ativar novos módulos. Entre em contato com o suporte para mais informações.</p>
               </div>
             </div>
           </div>

           <div *ngIf="!carregando">
             <!-- Faturas Pendentes -->
             <div class="card border-warning mb-4" *ngIf="faturasPendentes.length">
               <div class="card-header bg-warning text-dark">
                 <h5 class="card-title mb-0">
                   <i class="fas fa-exclamation-triangle mr-2"></i>
                   Faturas Pendentes de Ativação
                 </h5>
                 <p class="mb-0 mt-1">
                   <small>Faturas que aguardam pagamento para ativação dos módulos</small>
                 </p>
               </div>
               <div class="card-body">
                 <div class="table-responsive">
                   <table class="table table-hover mb-0">
                     <thead class="thead-light">
                       <tr>
                         <th><i class="fas fa-cubes mr-1"></i>Módulos</th>
                         <th><i class="fas fa-dollar-sign mr-1"></i>Valor</th>
                         <th><i class="fas fa-calendar mr-1"></i>Vencimento</th>
                         <th><i class="fas fa-info-circle mr-1"></i>Status</th>
                         <th><i class="fas fa-cogs mr-1"></i>Ações</th>
                       </tr>
                     </thead>
                     <tbody>
                       <tr *ngFor="let fatura of faturasPendentes">
                         <td>
                           <div class="modulos-fatura">
                             <div *ngFor="let modulo of fatura.modulos" class="badge badge-light mr-1 mb-1">
                               <i class="fas fa-cube mr-1"></i>{{modulo.nome}}
                             </div>
                           </div>
                         </td>
                         <td>
                           <span class="font-weight-bold text-success">
                             {{ fatura.valor | currency:'BRL':'symbol':'1.2-2' }}
                           </span>
                         </td>
                         <td>
                           <span class="badge badge-outline-primary">
                             {{ fatura.dataVencimentoTexto }}
                           </span>
                         </td>
                         <td>
                           <span class="badge" [ngClass]="{
                             'badge-warning': !fatura.paga,
                             'badge-danger': fatura.cancelada
                           }">
                             {{ fatura.status | titlecase }}
                           </span>
                         </td>
                         <td>
                           <div class="btn-group" role="group">
                             <a [href]="fatura.linkPagamento" target="_blank"
                                class="btn btn-sm btn-outline-primary" *ngIf="fatura.linkPagamento">
                               <i class="fas fa-external-link-alt mr-1"></i>Ver Fatura
                             </a>
                             <button type="button" [disabled]="fatura.sincronizando" [hidden]="fatura.cancelada"
                                     *ngIf="usuario.superadmin" class="btn btn-sm btn-outline-secondary"
                                     (click)="verificarStatusFatura(fatura)">
                               <i class="fas fa-sync-alt mr-1" [class.fa-spin]="fatura.sincronizando"></i>
                               {{ fatura.sincronizando ? 'Sincronizando...' : 'Sincronizar' }}
                             </button>
                           </div>
                         </td>
                       </tr>
                     </tbody>
                   </table>
                 </div>
               </div>
             </div>

             <!-- Resumo Rápido -->
             <div class="row mb-4">
               <div class="col-md-4">
                 <div class="card border-success h-100">
                   <div class="card-body text-center">
                     <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                     <h4 class="text-success mb-1">{{ modulosAtivos.length }}</h4>
                     <p class="text-muted mb-0">Módulos Ativos</p>
                   </div>
                 </div>
               </div>
               <div class="col-md-4">
                 <div class="card border-primary h-100">
                   <div class="card-body text-center">
                     <i class="fas fa-plus-circle fa-2x text-primary mb-2"></i>
                     <h4 class="text-primary mb-1">{{ modulosDisponiveis.length }}</h4>
                     <p class="text-muted mb-0">Disponíveis</p>
                   </div>
                 </div>
               </div>
               <div class="col-md-4">
                 <div class="card border-warning h-100">
                   <div class="card-body text-center">
                     <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                     <h4 class="text-warning mb-1">{{ faturasPendentes.length }}</h4>
                     <p class="text-muted mb-0">Faturas Pendentes</p>
                   </div>
                 </div>
               </div>
             </div>

             <!-- Módulos Ativos - Linha Horizontal -->
             <div class="row mb-4">
               <div class="col-12">
                 <div class="card">
                   <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                     <h5 class="card-title mb-0">
                       <i class="fas fa-check-circle mr-2"></i>
                       Módulos Ativos
                       <span class="badge badge-light text-success ml-2">{{ modulosAtivos.length }}</span>
                     </h5>
                     <div class="scroll-controls">
                       <i class="fas fa-arrows-alt-h mr-2"></i>
                       <small>Role horizontalmente para ver todos</small>
                     </div>
                   </div>
                   <div class="card-body p-3">
                     <!-- Adicione isso logo após o header dos módulos ativos, antes do container de scroll -->
                     <div class="mb-3" *ngIf="modulosAtivos.length > 0">
                       <div class="btn-group btn-group-sm" role="group">
                         <button type="button" class="btn btn-outline-secondary" (click)="expandirTodasConfiguracoes()">
                           <i class="fas fa-expand-arrows-alt mr-1"></i>
                           Expandir Todas Configurações
                         </button>
                         <button type="button" class="btn btn-outline-secondary" (click)="recolherTodasConfiguracoes()">
                           <i class="fas fa-compress-arrows-alt mr-1"></i>
                           Recolher Todas
                         </button>
                       </div>
                     </div>

                     <!-- Container com scroll horizontal -->
                     <div class="modulos-horizontal-container">
                       <div class="modulos-horizontal-scroll" *ngIf="modulosAtivos.length > 0">
                         <div class="modulo-ativo-horizontal" *ngFor="let modulo of modulosAtivos">
                           <div class="card border-success modulo-card-horizontal">
                             <div class="card-header bg-light border-success d-flex justify-content-between align-items-center">
                               <div class="d-flex align-items-center">
                                 <i class="fas fa-cube text-success mr-2"></i>
                                 <h6 class="mb-0 font-weight-bold">{{ modulo.nome }}</h6>
                               </div>
                               <div class="d-flex align-items-center">
                                 <span class="badge badge-success mr-2">Ativo</span>
                                 <button type="button" class="btn btn-sm btn-outline-danger"
                                         (click)="desativarModulo(modulo)" [disabled]="salvando"
                                         title="Desativar módulo">
                                   <i class="fas fa-times"></i>
                                 </button>
                               </div>
                             </div>
                             <div class="card-body">
                               <p class="text-muted small mb-2">{{ modulo.descricao }}</p>
                               <div class="mb-2">
                                 <span class="badge badge-outline-success">
                                   <i class="fas fa-dollar-sign mr-1"></i>
                                   {{ modulo.valorMensalidade | currency:'BRL':'symbol':'1.2-2' }}/mês
                                 </span>
                               </div>

                               <!-- Configurações do Módulo (versão compacta) -->
                               <div class="configuracoes-modulo-compacta" *ngIf="temConfiguracoes(modulo)">
                                 <button type="button" class="btn btn-sm btn-outline-primary btn-block"
                                         (click)="toggleConfiguracoes(modulo)">
                                   <i class="fas fa-cog mr-1"></i>
                                   {{ modulo.mostrarConfiguracoes ? 'Ocultar' : 'Configurar' }}
                                 </button>

                                 <!-- Configurações Expandidas -->
                                 <div class="configuracoes-expandidas mt-2" *ngIf="modulo.mostrarConfiguracoes">
                                   <!-- Configurações do Módulo Pedidos -->
                                   <div *ngIf="modulo.nome === 'pedidos'">
                                     <div class="form-group mb-2">
                                       <label class="form-label small font-weight-bold">Aceitar pedidos automaticamente</label>
                                       <div class="mt-1">
                                         <kendo-switch name="aceitarPedidoAutomatico"
                                                       [(ngModel)]="empresa.aceitarPedidoAutomatico"
                                                       [onLabel]="'Sim'" [offLabel]="'Não'">
                                         </kendo-switch>
                                       </div>
                                     </div>

                                     <div class="form-group mb-2" *ngIf="!empresa.aceitarPedidoAutomatico">
                                       <label class="form-label small font-weight-bold">Status inicial do pedido</label>
                                       <kendo-dropdownlist name="statusAutomatico"
                                                          [(ngModel)]="empresa.objetoStatusPedidoAoAceitar"
                                                          [data]="listaStatus" class="form-control"
                                                          textField="nome" valueField="status" required
                                                          (valueChange)="escolheuStatusPedidoAoAceitar()">
                                       </kendo-dropdownlist>
                                     </div>
                                   </div>

                                   <!-- Configurações do Módulo Controle de Estoque -->
                                   <div *ngIf="modulo.nome === 'controle de estoque'">
                                     <div class="form-group mb-2">
                                       <label class="form-label small font-weight-bold">Vincular estoque ao produto</label>
                                       <div class="mt-1">
                                         <kendo-switch name="estoqueVinculadoProduto"
                                                       [(ngModel)]="empresa.estoqueVinculadoProduto"
                                                       [onLabel]="'Sim'" [offLabel]="'Não'">
                                         </kendo-switch>
                                       </div>
                                     </div>
                                   </div>

                                   <!-- Configurações do Módulo Cardápio -->
                                   <div *ngIf="modulo.nome === 'cardápio'">
                                     <div class="form-group mb-2">
                                       <label class="form-label small font-weight-bold">Tipo de Loja</label>
                                       <div class="mt-1">
                                         <div class="custom-control custom-radio mb-1">
                                           <input type="radio" id="cardapio_{{ modulo.id }}" name="group1_{{ modulo.id }}" class="custom-control-input"
                                                  [(ngModel)]="empresa.tipoDeLoja" value="CARDAPIO" kendoRadioButton required/>
                                           <label class="custom-control-label small" for="cardapio_{{ modulo.id }}">
                                             <i class="fas fa-utensils mr-1"></i>Cardápio Digital
                                           </label>
                                         </div>
                                         <div class="custom-control custom-radio mb-1">
                                           <input type="radio" id="catalogo_{{ modulo.id }}" name="group1_{{ modulo.id }}" class="custom-control-input"
                                                  [(ngModel)]="empresa.tipoDeLoja" value="CATALOGO" kendoRadioButton required/>
                                           <label class="custom-control-label small" for="catalogo_{{ modulo.id }}">
                                             <i class="fas fa-book mr-1"></i>Catálogo Digital
                                           </label>
                                         </div>
                                         <div class="custom-control custom-radio mb-1">
                                           <input type="radio" id="mercado_{{ modulo.id }}" name="group1_{{ modulo.id }}" class="custom-control-input"
                                                  [(ngModel)]="empresa.tipoDeLoja" value="MERCADO" kendoRadioButton required/>
                                           <label class="custom-control-label small" for="mercado_{{ modulo.id }}">
                                             <i class="fas fa-shopping-cart mr-1"></i>Mercado e Supermercado
                                           </label>
                                         </div>
                                         <div class="custom-control custom-radio mb-1">
                                           <input type="radio" id="ecommerce_{{ modulo.id }}" name="group1_{{ modulo.id }}" class="custom-control-input"
                                                  [(ngModel)]="empresa.tipoDeLoja" value="ECOMMERCE" kendoRadioButton required/>
                                           <label class="custom-control-label small" for="ecommerce_{{ modulo.id }}">
                                             <i class="fas fa-globe mr-1"></i>E-Commerce
                                           </label>
                                         </div>
                                       </div>
                                     </div>
                                   </div>

                                   <!-- Configurações do Módulo App -->
                                   <div *ngIf="modulo.nome === 'app'">
                                     <div class="form-group mb-2">
                                       <label class="form-label small font-weight-bold">Link App iOS</label>
                                       <input type="text" class="form-control form-control-sm" autocomplete="off" name="appIOS"
                                              [(ngModel)]="empresa.appIos" placeholder="Link do App na App Store">
                                     </div>
                                     <div class="form-group mb-2">
                                       <label class="form-label small font-weight-bold">Link App Android</label>
                                       <input type="text" class="form-control form-control-sm" disabled name="appAndroid"
                                              [value]="'https://play.google.com/store/apps/details?id=ai.meucardapio.' + empresa.dominio"
                                              readonly placeholder="Link do App na Play Store"/>
                                     </div>
                                   </div>

                                   <!-- Configurações do Módulo Google Maps -->
                                   <div *ngIf="modulo.id === 10">
                                     <div class="form-group mb-2">
                                       <label class="form-label small font-weight-bold">Chave API Google Maps</label>
                                       <input kendoTextBox name="chaveGoogle" [(ngModel)]="empresa.googleMapsKey"
                                              class="form-control form-control-sm" required/>
                                     </div>
                                   </div>

                                   <!-- Botão Salvar Configurações -->
                                   <div class="text-center mt-2">
                                     <button type="button" [disabled]="salvando" class="btn btn-sm btn-primary"
                                             (click)="salvarConfiguracoesModulo(modulo)">
                                       <i class="fas fa-spinner fa-spin mr-1" *ngIf="salvando"></i>
                                       <i class="fas fa-save mr-1" *ngIf="!salvando"></i>
                                       {{ salvando ? 'Salvando...' : 'Salvar' }}
                                     </button>
                                   </div>
                                 </div>
                               </div>
                             </div>
                           </div>
                         </div>
                       </div>

                       <!-- Estado Vazio Módulos Ativos -->
                       <div class="text-center py-4" *ngIf="modulosAtivos.length === 0">
                         <i class="fas fa-cube text-muted mb-3" style="font-size: 3rem; opacity: 0.3;"></i>
                         <h5 class="text-muted">Nenhum módulo ativo</h5>
                         <p class="text-muted">Ative módulos para começar a usar os recursos da plataforma.</p>
                         <button type="button" class="btn btn-primary" (click)="abrirModalAtivacao()"
                                 [disabled]="carregando || salvando || empresaBloqueada">
                           <i class="fas fa-plus mr-2"></i>Ativar Primeiro Módulo
                         </button>
                       </div>
                     </div>
                   </div>
                 </div>
               </div>
             </div>

             <!-- Módulos Disponíveis - Linha Horizontal -->
             <div class="row mb-4">
               <div class="col-12">
                 <div class="card">
                   <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                     <h5 class="card-title mb-0">
                       <i class="fas fa-plus-circle mr-2"></i>
                       Módulos Disponíveis
                       <span class="badge badge-light text-primary ml-2">{{ modulosDisponiveis.length }}</span>
                     </h5>
                     <div class="scroll-controls" *ngIf="modulosDisponiveis.length > 0">
                       <i class="fas fa-arrows-alt-h mr-2"></i>
                       <small>Role horizontalmente para ver todos</small>
                     </div>
                   </div>
                   <div class="card-body p-3">
                     <!-- Botão Ativar Múltiplos -->
                     <div class="mb-3" *ngIf="modulosDisponiveis.length > 0 && !empresaBloqueada">
                       <button type="button" class="btn btn-primary btn-lg"
                               (click)="abrirModalAtivacao()" [disabled]="carregando || salvando">
                         <i class="fas fa-cubes mr-2"></i>
                         Ativar Múltiplos Módulos
                         <span class="badge badge-light ml-2">{{ modulosDisponiveis.length }}</span>
                       </button>
                     </div>

                     <!-- Container com scroll horizontal -->
                     <div class="modulos-horizontal-container">
                       <div class="modulos-horizontal-scroll" *ngIf="modulosDisponiveis.length > 0">
                         <div class="modulo-disponivel-horizontal" *ngFor="let modulo of modulosDisponiveis">
                           <div class="card border-primary modulo-card-horizontal">
                             <div class="card-body">
                               <div class="d-flex justify-content-between align-items-start mb-2">
                                 <div>
                                   <h6 class="card-title mb-1">
                                     <i class="fas fa-cube text-primary mr-2"></i>
                                     {{ modulo.nome }}
                                   </h6>
                                   <p class="card-text text-muted small mb-2">{{ modulo.descricao }}</p>
                                 </div>
                                 <button type="button" [hidden]="modulo.aguardandoAtivacao"
                                         class="btn btn-primary btn-sm" (click)="ativarModuloUnico(modulo)"
                                         [disabled]="salvando || empresaBloqueada">
                                   <i class="fas fa-plus mr-1"></i>Ativar
                                 </button>
                                 <span class="badge badge-warning" *ngIf="modulo.aguardandoAtivacao">
                                   <i class="fas fa-clock mr-1"></i>Aguardando
                                 </span>
                               </div>

                               <div class="pricing-info">
                                 <div class="d-flex justify-content-between align-items-center">
                                   <span class="text-success font-weight-bold" *ngIf="modulo.valorMensalidade">
                                     <i class="fas fa-calendar mr-1"></i>
                                     {{ modulo.valorMensalidade | currency:'BRL':'symbol':'1.2-2' }}/mês
                                   </span>
                                   <span class="font-weight-bold"
                                         [class]="modulo.valorAtivacao > 0 ? 'text-primary' : 'text-success'">
                                     <i   [class]="modulo.valorAtivacao > 0 ? 'fa-bolt' : 'fa-gift'" class="fas mr-1"></i>
                                     {{ modulo.valorAtivacao > 0 ? (modulo.valorAtivacao | currency:'BRL':'symbol':'1.2-2') : 'Grátis' }}
                                   </span>
                                 </div>
                               </div>
                             </div>
                           </div>
                         </div>
                       </div>

                       <!-- Estado quando todos módulos estão ativos -->
                       <div class="text-center py-4" *ngIf="modulosDisponiveis.length === 0 && modulosAtivos.length > 0">
                         <i class="fas fa-check-circle text-success mb-3" style="font-size: 3rem;"></i>
                         <h5 class="text-success">Parabéns!</h5>
                         <p class="text-muted">Todos os módulos disponíveis já estão ativos para esta empresa.</p>
                       </div>

                       <!-- Estado quando empresa está bloqueada -->
                       <div class="text-center py-4" *ngIf="empresaBloqueada">
                         <i class="fas fa-lock text-danger mb-3" style="font-size: 3rem; opacity: 0.3;"></i>
                         <h5 class="text-danger">Empresa Bloqueada</h5>
                         <p class="text-muted">Não é possível ativar novos módulos.</p>
                       </div>
                     </div>
                   </div>
                 </div>
               </div>
             </div>
           </div>
         </div>
       </div>
     </div>
  </div>
</div>





