import {Component, ElementRef, Inject, Input, OnInit, Renderer2, ViewChild} from '@angular/core';
import {NgForm} from "@angular/forms";
import {FileRestrictions, SelectEvent} from "@progress/kendo-angular-upload";
import {DOCUMENT} from "@angular/common";
import {FormasDePagamentoService} from "../../services/formas-de-pagamento.service";
import {EnumTipoBandeira} from "../../../../server/lib/integracao/pagamentos/EnumTipoBandeira";
import {ModalKendo} from "../../lib/ModalKendo";
declare var canvg;
@Component({
  selector: 'app-cad-bandeira',
  templateUrl: './cad-bandeira.component.html',
  styleUrls: ['./cad-bandeira.component.scss']
})
export class CadBandeiraComponent extends ModalKendo implements OnInit {
  @ViewChild('frm', {static: true}) frm: NgForm;
  bandeira: any = {}
  salvando = false;
  mensagemSucesso: any;
  mensagemErro: any;
  tipos = [EnumTipoBandeira.Cartao, EnumTipoBandeira.Ticket, EnumTipoBandeira.Outros]
  @Input() restricoes:  FileRestrictions = {
    allowedExtensions: ['.svg', '.webp', '.png']
  };
  files: any = [];
  editarImagem = false;
  backupImagem: string;
  fezUpload: boolean;
  erroSalvar: string;
  erroUpload: string;
  uploadUrl: any = '/upload/imagem/bandeira/svg';
  uploadRemoveUrl: any;
  public imagePreview: any;
  public svgResult ;
  @ViewChild('fileInput', { static: true }) fileInput: ElementRef;
  constructor(  private formasDePagamentoService: FormasDePagamentoService,
              @Inject(DOCUMENT) private _document: Document,
              private _renderer2: Renderer2) {
    super()
  }

  ngOnInit(): void { }

  selecionouArquivo(e: SelectEvent){
    e.files.forEach((file) => {
      console.log(`File selected: ${file.name}`);
      if (!file.validationErrors) {
        const reader = new FileReader();
        reader.onload = (ev) => {
          const image = {
            src: ev.target['result'],
            uid: file.uid
          };
          this.imagePreview = image;
        };
        reader.readAsDataURL(file.rawFile);
      }
    });
  }


  onErroUpload($event: any) {
    console.log($event)
    if($event.response && $event.response.error)
      alert($event.response.error)

  }

  successUpload(e: any){
    const respUpload = e.response.body;
    this.backupImagem =  this.bandeira.imagem;
    this.bandeira.imagem =   respUpload.data;
    this.editarImagem = false;
    this.fezUpload = true;
    //this.onEnviou.emit(this.getLinkImagem());
  }

  canceleImagem(){
    if(this.backupImagem)
      this.bandeira.imagem = this.backupImagem;
    this.files = [];

  }

  salveBandeira(){
    if(this.frm.invalid) return;

    if(!this.bandeira.imagem){
      this.mensagemErro  = 'Icone da bandeira é obrigatorio'
      return;
    }

    this.salvando = true;
    this.formasDePagamentoService.salveBandeira(this.bandeira).then((bandeira: any) => {
      this.salvando = false;
      this.bandeira.id = bandeira.id;
      this.fecheModal(this.bandeira)
    }).catch((erro) => {
      this.salvando = false;
      this.mensagemErro = erro;
    })
  }


  fecheMensagemErro() {
    delete this.mensagemErro
  }


}
