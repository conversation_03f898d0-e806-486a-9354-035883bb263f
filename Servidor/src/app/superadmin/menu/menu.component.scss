.left-side-menu{
  padding-top: 30px;
}

.nav-user{
  padding-bottom: 10px !important;
}

.empresa{
  font-size: 11px;
}



:host-context(.enlarged) .dadosUsuario {
  display: none;
}

:host-context(.enlarged) .nav-user {
  padding-bottom: 30px !important;
}

.nav-user {
  padding-left:18px !important;

}
.nav-link {
  padding-left: 0;
}



.text-primary{
  color:#3a44b9 !important;
}

.text-bold{
  font-weight: bold;
}

.bg-primary{
  background-color:#3a44b9  !important;
}

:host-context(.sidebar-enable) .pro-user-name {
  display: initial !important;
}

.nav-second-level li a, .nav-thrid-level li a {
  padding: 5px 10px !important;
}

.nav-second-level {
  padding-left: 30px !important;
  background-color: rgba(0,0,0,0.05);
  margin: 0;

  li {
    list-style: none;

    a {
      display: block;
      padding: 8px 15px;
      color: #6c757d;
      text-decoration: none;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(0,0,0,0.1);
        color: #3a44b9;
      }

      i {
        margin-right: 8px;
        width: 16px;
        text-align: center;
      }
    }
  }
}

#side-menu li a {
  display: block;
  padding: 12px 20px;
  color: #6c757d;
  text-decoration: none;
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(0,0,0,0.05);
    color: #3a44b9;
  }

  i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
  }
}
