::ng-deep .custom-grid.k-grid tr:hover {
  background-color: inherit !important;
}

:host ::ng-deep .custom-grid.k-grid tr {
  background-color: inherit !important;
}

::ng-deep .custom-grid.k-grid tr.ativo {
  background-color: #fff !important;
}

::ng-deep .custom-grid.k-grid tr.inativo {
  background-color: #f2f2f2  !important;
  color: #999 !important;
}

::ng-deep .custom-grid.k-grid tr td {
  border-left: none;
  padding: 2px 3px;
  border-bottom: solid 1px #d9d9d9; /* altera a cor da borda para uma cor mais clara */
}

::ng-deep .custom-grid.k-grid tr:last-child td {
  border-bottom: none; /* remove a borda inferior da última linha */
}

::ng-deep .wndNovaInstrucao .k-window-titlebar {
  background-color: #f1556c !important;
}

.k-listview.lst-trechos-Prompt {
  border: none;

  .k-listview-item {
    padding: 30px !important;
  }

  .highlight {
    background: #333;
    color: #fff;
  }
}

::ng-deep .highlight {
  background: #333;
  color: #fff;
}

.item {
  border-left: solid 4px transparent;
}

.hoverable {
  display: none;
}

.pl-1:hover .hoverable {
  display: inline-block;
}

.pl-1:hover .tipo {
  display: none;
}

.modificado {
  border-left: solid 4px red;
}

.texto-desativado {
  color: #ccc;
  text-decoration: line-through;
}

.loader-container {
  display: flex;
  height: 300px;
  align-items: center;
  justify-content: center;
}

.personalizado {
  background: #EAFBF6 !important;
  color: #444;
}

.global {
  background: #FFF9E9 !important;
  color: #444;
}


.tela_lateral {
  position: fixed;
  top: 0;
  right: 0;
  height: 100%;
  width: 500px;
  background-color: #fff;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}


.tela_lateral.open {
  transform: translateX(0);
  z-index: 999999;
}

.content {
  padding: 20px;
}

textarea::placeholder {
  color: #999;
}
.button-wrapper {
  position: sticky;
  bottom: 0;
  background: #FFF; /* Mudar dependendo do seu design */
  padding-bottom: 16px;
}

::ng-deep .my-dialog .k-dialog-content {
  padding: 16px !important;
  padding-bottom: 0px !important;
}

.item:hover {
  filter: brightness(95%); /* Escurece a cor em 25% ao passar o mouse */
}

@keyframes blink {
  0% { background-color: red;padding-left: 3px; }
  50% { background-color: transparent;padding-left: 3px; }
  100% { background-color: red;padding-left: 3px; }
}

.blink {
  animation: blink .5s ease-in-out 3; /* Blinks 3 times */
}
