.row.removida{

  input {
    color: #ccc !important;
    border-color: #ccc !important;
    text-decoration: line-through;
  }
}

.acao .btn{
  margin-top: 2rem !important;
}


.row.impressora{
  border: 1px solid #eee;
  padding-top: 10px;
  margin-bottom: 10px;

  >div{
    padding-right: 0 !important;
    max-width: 150px !important;
  }
}

.card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  .card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #eee;

    h5 {
      margin-bottom: 0.25rem;
    }

    small {
      color: #6c757d;
    }
  }
}

.form-group {
  label.font-weight-bold {
    color: #344767;
    margin-bottom: 1rem;

    i {
      color: #5e72e4;
    }
  }
}

.form-check {
  margin-left: 1rem;

  .form-check-label {
    color: #344767;

    small {
      color: #6c757d;
      font-size: 0.875rem;
    }
  }
}

.btn-primary {
  background: #5e72e4;
  border: none;

  &:hover {
    background: #4454c3;
  }
}

.alert {
  border-radius: 8px;

  i {
    font-size: 1.1rem;
  }
}

:host {
  display: block;
  padding: 20px;
}

h4 {
  margin: 0;
  color: #344767;
}

.config-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);

  .section-title {
    display: flex;
    align-items: center;
    font-weight: 500;
    color: #344767;
    margin-bottom: 20px;

    i {
      margin-right: 8px;
      color: #5e72e4;
    }
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.radio-group {
  .radio-option {
    margin: 12px 0;
    padding-left: 24px;
    position: relative;

    input[type="radio"] {
      position: absolute;
      left: 0;
      top: 4px;
      width: 16px;
      height: 16px;
      visibility: visible;
      opacity: 1;
    }

    label {
      display: block;
      color: #344767;
      cursor: pointer;

      small {
        display: block;
        color: #6c757d;
        font-size: 0.875rem;
      }
    }
  }
}

.impressoras-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.impressora-card {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;

  &.removed {
    opacity: 0.5;
  }

  .card-header {
    background: #f8f9fa;
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
  }

  .card-body {
    padding: 16px;
  }
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;

  .form-group {
    flex: 1;
  }
}

.checkbox-group {
  display: grid;
  gap: 8px;

  .checkbox {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    cursor: pointer;

    input[type="checkbox"] {
      margin-top: 4px;
      width: 16px;
      height: 16px;
      visibility: visible;
      opacity: 1;
    }

    label {
      color: #344767;
      cursor: pointer;
    }

    small {
      display: block;
      color: #6c757d;
      font-size: 0.875rem;
    }
  }
}

.btn-add {
  background: #5e72e4;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background: #4454c3;
  }
}

.btn-save {
  background: #5e72e4;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  margin-top: 20px;

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.form-control {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;

  &:focus {
    border-color: #5e72e4;
    outline: none;
  }
}

.config-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-top: 24px;

  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
  }
}

.config-column {
  .config-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 24px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
  }
}

.impressoras-list {
  margin-top: 20px;

  .impressora-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 8px;
    background: white;

    &.removed {
      opacity: 0.5;
      background: #f8f9fa;
    }

    .item-content {
      display: flex;
      align-items: center;
      gap: 12px;

      i {
        color: #5e72e4;
        font-size: 20px;
      }

      .item-info {
        display: flex;
        flex-direction: column;

        strong {
          color: #344767;
        }

        span {
          color: #6c757d;
          font-size: 0.875rem;
        }
      }
    }

    .item-actions {
      display: flex;
      gap: 8px;

      .btn-icon {
        background: none;
        border: none;
        padding: 4px;
        cursor: pointer;
        color: #6c757d;

        &:hover {
          color: #5e72e4;
        }

        &.text-danger:hover {
          color: #dc3545;
        }
      }
    }
  }
}

.config-impressora {
  padding: 24px;

  .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 24px;

    .form-group {
      flex: 1;

      label {
        display: block;
        color: #344767;
        font-weight: 500;
        margin-bottom: 8px;
      }

      input, kendo-dropdownlist {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        color: #344767;
        transition: all 0.2s;

        &:focus {
          border-color: #5e72e4;
          box-shadow: 0 0 0 3px rgba(94,114,228,0.1);
        }
      }
    }
  }

  .checkbox-group {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 24px;

    .checkbox {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 8px;
      border-radius: 6px;
      transition: background 0.2s;

      &:hover {
        background: #fff;
      }

      input[type="checkbox"] {
        width: 18px;
        height: 18px;
        margin-top: 2px;
        accent-color: #5e72e4;
      }

      label {
        color: #344767;
        font-weight: 500;
        cursor: pointer;

        small {
          display: block;
          color: #6c757d;
          font-weight: normal;
          margin-top: 2px;
        }
      }
    }
  }

  .comandos-fim {
    label {
      display: block;
      color: #344767;
      font-weight: 500;
      margin-bottom: 8px;
    }

    input {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      font-family: monospace;

      &:focus {
        border-color: #5e72e4;
        box-shadow: 0 0 0 3px rgba(94,114,228,0.1);
      }
    }

    small {
      display: block;
      color: #6c757d;
      margin-top: 4px;
    }
  }
}

.btn-secondary {
  background: #f8f9fa;
  border: 1px solid #ddd;
  padding: 8px 16px;
  border-radius: 4px;
  margin-right: 8px;

  &:hover {
    background: #e9ecef;
  }
}

.item-configs {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 4px;

  .config-tag {
    font-size: 0.75rem;
    padding: 2px 6px;
    background: #e9ecef;
    border-radius: 4px;
    color: #6c757d;
  }
}

.impressora-item {
  .item-content {
    .item-info {
      strong {
        margin-bottom: 2px;
      }

      span {
        line-height: 1.2;
      }
    }
  }
}

::ng-deep .k-dialog {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0,0,0,0.12);

  .k-dialog-titlebar {
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
    padding: 16px 24px;

    .k-dialog-title {
      color: #344767;
      font-size: 1.1rem;
      font-weight: 500;
    }

    .k-dialog-actions {
      .k-button {
        background: none;
        border: none;
        color: #6c757d;
        padding: 4px;

        &:hover {
          color: #344767;
        }
      }
    }
  }

  .k-dialog-content {
    padding: 0;
  }
}

::ng-deep .k-dialog-actions {
  padding: 16px 24px;
  background: #f8f9fa;
  border-top: 1px solid #eee;
  gap: 12px;

  .k-button, button {
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s;
    color: #6c757d;
    background: #fff;
    border: 1px solid #ddd;

    &.k-button-md {
      padding: 8px 16px;
      color: #6c757d;
      background: #fff;
      border: 1px solid #ddd;
    }

    &:hover {
      background: #f8f9fa;
      border-color: #c1c7cd;
      color: #344767;
    }

    &.btn-primary, &.k-primary {
      background: #5e72e4;
      border: none;
      color: #fff;

      &:hover {
        background: #4454c3;
        color: #fff;
      }
    }
  }
}

.alert-warning {
  background: #fff7ed;
  border: 1px solid #ffedd5;
  color: #c2410c;
  padding: 12px 16px;
  border-radius: 8px;
  margin: 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;

  i {
    font-size: 18px;
  }
}

.qz-error-notification {
  background-color: #FFF3E0;
  border: 1px solid #FFB74D;
  border-radius: 4px;
  padding: 1.5rem;
  margin: 1rem 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  .notification-content {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;

    .notification-icon {
      font-size: 1.5rem;
      color: #FF9800;
      margin-right: 1rem;
    }

    .notification-message {
      flex: 1;

      strong {
        display: block;
        color: #E65100;
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
      }

      p {
        color: #795548;
        margin: 0;
      }
    }
  }

  .notification-actions {
    display: flex;
    justify-content: flex-start;
    gap: 1rem;

    .btn-warning {
      background-color: #FF9800;
      border-color: #FF9800;
      color: white;

      &:hover {
        background-color: #F57C00;
        border-color: #F57C00;
      }
    }

    .btn-primary {
      background-color: #2196F3;
      border-color: #2196F3;

      &:hover {
        background-color: #1976D2;
        border-color: #1976D2;
      }
    }
  }
}
