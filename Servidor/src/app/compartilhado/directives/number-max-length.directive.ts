import {Directive, ElementRef, forwardRef, HostListener, Input} from '@angular/core';
import {ControlValueAccessor, NG_VALUE_ACCESSOR} from "@angular/forms";

@Directive({
  selector: '[appNumberMaxLength]',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => NumberMaxLengthDirective),
      multi: true
    }
  ]
})
export class NumberMaxLengthDirective implements ControlValueAccessor {
  @Input('appNumberMaxLength') maxLength: number;

  private _onChange: (value: any) => void;

  constructor(private el: ElementRef) { }

  @HostListener('input', ['$event']) onInputChange(event: any) {
    const initalValue = this.el.nativeElement.value;

    if (initalValue.length > this.maxLength) {
      this.el.nativeElement.value = initalValue.slice(0, this.maxLength);
      event.stopPropagation();
      this.writeValue(this.el.nativeElement.value);
    }
  }

  writeValue(value: any): void {
    this.el.nativeElement.value = value;
    if (this._onChange) {
      this._onChange(value);
    }
  }

  registerOnChange(fn: any): void {
    this._onChange = fn;
  }

  registerOnTouched(fn: any): void {

  }
}
