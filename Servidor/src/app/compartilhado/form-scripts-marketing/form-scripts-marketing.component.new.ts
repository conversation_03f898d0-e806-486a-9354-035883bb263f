import {Component, Input, OnInit, ViewChild} from '@angular/core';
import {EmpresasService} from "../../superadmin/services/empresas.service";
import {NgForm} from "@angular/forms";

interface ScriptInfo {
  title: string;
  description: string;
  placeholder: string;
  icon: string;
  helpText: string;
  exampleImage?: string;
  learnMoreLink?: string;
}

@Component({
  selector: 'app-form-scripts-marketing',
  templateUrl: './form-scripts-marketing.component.html',
  styleUrls: ['./form-scripts-marketing.component.scss']
})
export class FormScriptsMarketingComponent implements OnInit {
  @Input() empresa: any = {};
  @Input() usuario: any = {};
  @ViewChild('frmScriptsMarketing', {}) frmScriptsMarketing: NgForm;
  
  mensagemSucesso: string;
  mensagemErro: string;
  salvando: boolean;
  
  // Informações detalhadas sobre cada script
  scriptInfos: {[key: string]: ScriptInfo} = {
    pixelFacebook: {
      title: 'Pixel do Facebook',
      description: 'O Pixel do Facebook é um código de rastreamento que permite medir a eficácia de suas campanhas publicitárias, entender as ações que as pessoas realizam em seu site e alcançar públicos relevantes.',
      placeholder: 'Informe a identificação do Pixel do Facebook (ex: 123456789012345)',
      icon: 'assets/images/marketing/facebook-pixel.svg',
      helpText: 'O ID do Pixel do Facebook é um código numérico que identifica seu pixel. Você pode encontrá-lo no Gerenciador de Eventos do Facebook.',
      exampleImage: 'assets/images/marketing/facebook-pixel-example.svg',
      learnMoreLink: 'https://www.facebook.com/business/help/952192354843755'
    },
    accessTokenAPIConversoes: {
      title: 'Token API Conversões do Facebook',
      description: 'O Token da API de Conversões permite enviar eventos diretamente para o Facebook, melhorando a precisão do rastreamento em um cenário com restrições de cookies.',
      placeholder: 'Informe o Token da API de conversões do Facebook',
      icon: 'assets/images/marketing/facebook-api.svg',
      helpText: 'O token da API de Conversões é uma chave de acesso longa que permite enviar eventos diretamente para o Facebook. Você pode gerá-lo no Facebook Business.',
      learnMoreLink: 'https://developers.facebook.com/docs/marketing-api/conversions-api/'
    },
    analytics: {
      title: 'ID de acompanhamento do Analytics',
      description: 'O ID do Google Analytics permite rastrear o tráfego do seu site e entender o comportamento dos usuários através de relatórios detalhados.',
      placeholder: 'UA-XXXXXX-Y ou G-XXXXXXXX',
      icon: 'assets/images/marketing/google-analytics.svg',
      helpText: 'O ID do Google Analytics pode começar com "UA-" (Analytics Universal) ou "G-" (Google Analytics 4). Você pode encontrá-lo nas configurações da sua propriedade no Google Analytics.',
      exampleImage: 'assets/images/marketing/google-analytics-example.svg',
      learnMoreLink: 'https://support.google.com/analytics/answer/9304153'
    },
    gtm: {
      title: 'ID Google Tag Manager',
      description: 'O Google Tag Manager permite gerenciar várias tags (como Analytics, Facebook Pixel) através de uma única interface, sem precisar modificar o código do site.',
      placeholder: 'GTM-XXXXXX',
      icon: 'assets/images/marketing/google-tag-manager.svg',
      helpText: 'O ID do Google Tag Manager começa com "GTM-" seguido por um código alfanumérico. Você pode encontrá-lo nas configurações do seu container no GTM.',
      exampleImage: 'assets/images/marketing/gtm-example.svg',
      learnMoreLink: 'https://support.google.com/tagmanager/answer/6103696'
    },
    gtag: {
      title: 'Código GTAG',
      description: 'A Tag Global do Site (gtag.js) é uma biblioteca de marcação unificada do Google que simplifica a implementação de produtos Google como Analytics, Ads e outros.',
      placeholder: 'Informe o Código da GTAG (Tag Global do Site)',
      icon: 'assets/images/marketing/gtag.svg',
      helpText: 'O código gtag.js é usado para enviar dados para o Google Analytics, Google Ads e outros produtos Google. Ele geralmente é fornecido junto com o ID do Google Analytics.',
      learnMoreLink: 'https://developers.google.com/analytics/devguides/collection/gtagjs'
    }
  };
  
  // Controle de modais
  modalAberto: string | null = null;
  
  constructor(private empresaService: EmpresasService) { }
  
  ngOnInit(): void {
  }
  
  onSubmitScriptsMarketing() {
    this.mensagemSucesso = '';
    this.salvando = true;

    this.empresaService.salveScriptsMarketing({
      id: this.empresa.id,
      dominio: this.empresa.dominio,
      pixelFacebook: this.empresa.pixelFacebook?.trim(),
      accessTokenAPIConversoes: this.empresa.accessTokenAPIConversoes?.trim(),
      gtm: this.empresa.gtm?.trim(),
      gtag: this.empresa.gtag?.trim(),
      analytics: this.empresa.analytics?.trim()
    }).then((_) => {
      this.salvando = false;
      this.mensagemSucesso = "Scripts de Marketing atualizados";
    }).catch((erro) => {
      this.salvando = false;
      this.mensagemErro = erro;
    });
  }

  fecheMensagemSucesso() {
    this.mensagemSucesso = '';
  }

  fecheMensagemErro() {
    this.mensagemErro = '';
  }

  testarToken() {
    if(!this.empresa.pixelFacebook) {
      alert('Para testar o token é necessário salvar o pixel do facebook antes.');
      return;
    }

    this.mensagemErro = '';

    this.empresaService.testeTokenAPIConversoes(this.empresa).then((resposta) => {
      alert(resposta);
    }).catch((erro) => {
      this.mensagemErro = erro;
    });
  }
  
  abrirModal(scriptKey: string) {
    this.modalAberto = scriptKey;
  }
  
  fecharModal() {
    this.modalAberto = null;
  }
  
  verificarScript(scriptKey: string) {
    // Simulação de verificação de script
    const script = this.empresa[scriptKey];
    if (!script) {
      alert('Nenhum código informado para verificar.');
      return;
    }
    
    alert('Verificação simulada: O código parece estar correto e funcionando!');
  }
}
