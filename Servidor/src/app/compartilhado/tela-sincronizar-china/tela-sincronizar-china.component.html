<kendo-dialog-titlebar (close)="fecheModal()"  >
  <h4 class="modal-title" id="myModalLabel">
    Sincronizar com catálogo Site
  </h4>

</kendo-dialog-titlebar>


<div #pnl   class="modal-body"  >
  <form [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"  novalidate #frm="ngForm" (ngSubmit)="carregueProdutosNovos()"
        *ngIf="buscarProdutos" >

    <div class="col-12 col-sm-7 centralizado ">
      <div class="card card-box">
        <div class="card-body">
          <ng-container  *ngIf="ehSiteChina()">
            <img src="https://www.chinainbox.com.br/ccstore/v1/images/?source=/file/general/logo-lg-china.png&height=69&width=100">

            <div class="form-group mb-3 mt-2 text-center"  >
              <label  >
                <span class="font-18 text-success ml-2"><b>{{empresa.integracaoDelivery?.unidadeChina}}</b></span><br>
                <b>Loja China configurada:</b>
              </label>
            </div>
          </ng-container>

          <ng-container  *ngIf="ehSiteGendai()">
            <img src="https://www.gendai.com.br/ccstore/v1/images/?source=/file/general/gen_mob_logo.png&height=60&width=74" >

            <div class="form-group mb-3 mt-2 text-center"  >
              <label  >
                <span class="font-18 text-success ml-2"><b>{{empresa.integracaoDelivery?.unidadeGendai}}</b></span>
                <br>
                <b>Loja Gendai configurada</b>
              </label>
            </div>
          </ng-container>

          <div class="row">
            <div class="col">
              <div class="form-group mt-2 mb-3   "  >
                <label class="mt-2 k-checkbox-label"  >
                  <input type="checkbox" id="agendarEntrega" name="agendarEntrega" class="k-checkbox " kendoCheckBox
                         [(ngModel)]="opcoes.sincronizarPrecos"/>

                  Sincronizar Preços

                </label>
              </div>
            </div>
            <div class="col">
              <div class="form-group mt-2 mb-3   "  >
                <label class="mt-2 k-checkbox-label"  >
                  <input type="checkbox" id="atualizarFotos" name="atualizarFotos" class="k-checkbox " kendoCheckBox
                         [(ngModel)]="opcoes.sincronizarImagens"
                  />

                  Atualizar Fotos

                </label>
              </div>
            </div>
          </div>

          <div class="alert alert-danger alert-dismissible fade show mt-2" role="alert" *ngIf="mensagemErro">
            <i class="mdi mdi-check-all mr-2"></i> {{mensagemErro}}
            <button type="button" class="close" data-dismiss="alert" aria-label="Fechar" (click)="fecheMensagemErro()">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>

          <h4 *ngIf="carregandoProdutos" >
            Buscando o catálogo de produtos ...
            <div class="k-i-loading ml-1 mr-1" style="font-size: 40px;    height: 90px;" ></div>

          </h4>

          <div class="alert alert-info" *ngIf="buscouProdutos && tudoSincronizado">
            <h6>
              Não há nenhum produto novo no catálogo
            </h6>
            <button type="button" class="close" aria-label="Close" (click)="fecheMensagemErro()">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>

        </div>
      </div>
    </div>

  </form>
  <app-painel-importacao-produtos #painelImportacao [hidden]="buscarProdutos"  ></app-painel-importacao-produtos>
</div>

<div class="modal-footer"    >

  <button type="button" class="btn btn-primary waves-effect waves-light"
          [disabled]="carregandoProdutos"   *ngIf="buscarProdutos" (click)="submitFormBusca()">
    <i class="k-icon k-i-loading" *ngIf="carregandoProdutos"></i>  Buscar Produtos</button>

  <button class="btn btn-danger mr-2" *ngIf="!buscarProdutos"  (click)="painelImportacao.sincronizeTodos()"
          [disabled]="painelImportacao && painelImportacao.importacao.sincronizando"
          [hidden]="painelImportacao && painelImportacao.importacao.finalizou"   >

    <i class="k-icon k-i-loading" *ngIf="painelImportacao && painelImportacao.importacao.sincronizando"></i>
    Sincronizar todos</button>

  <button type="button" class="btn btn-light waves-effect" data-dismiss="modal"
          (click)="feche()">Cancelar</button>
</div>
