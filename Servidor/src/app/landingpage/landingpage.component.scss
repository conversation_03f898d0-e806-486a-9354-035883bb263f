
.container-fluid {
  padding-right: 0px !important;
  padding-left: 0px !important;
  overflow: hidden;
}
h1{
  font-weight: bold;
}
p{
  font-size: 14px;
}
.base-logo{
  position: absolute;
  background: url("/assets/fidelidade/ladingpage/base-logo.png") no-repeat;
  width: 250px;
  height: 250px;
  top:0;
  left: 0;
  z-index: 100;
  img {
    width: 150px;
    position: relative;
    top: 35px;
    left: 35px;
  }
}

.bg1,.bg2, .bg3, .bg4, .bg5, .bg6{
  background-color: #efefef !important;
  >div{
    margin:  0 auto;
    max-width: 860px;
  }

}
.bg1 {
  background: url("/assets/fidelidade/ladingpage/bg-01.jpg") repeat-x;
  color: #fff;
  height: 700px;
  padding-top: 50px;
}
.bg2 {
  background: url("/assets/fidelidade/ladingpage/bg-02.jpg");
  color: rgba(0, 0, 0, 0.88);
  height: 430px;
}
.bg3 {
  background: url("/assets/fidelidade/ladingpage/bg-03.jpg");
  color: #26502a;
  height: 875px;
  padding-top: 200px;
  font-size: 15px;
}
.bg4 {
  background: url("/assets/fidelidade/ladingpage/bg-04.jpg");
  color: rgba(0, 0, 0, 0.88);
  height: 510px;
  overflow: hidden;
}

.bg5 {
  background: url("/assets/fidelidade/ladingpage/bg-05.jpg");
  color: #fff;
  padding-top: 120px;
  height: 720px;
  background-color: #fff;
  background-position: 0px -10px;
  color: rgba(0, 0, 0, 0.88);
}
.bg6 {
  background-color: #fff !important;
  padding-top: 150px;
  padding-bottom: 150px;

}
.bg1  h1,  .bg3  h1, .bg5  h1{
  color: #fff;
}

.bg2 h1, .bg4 h1, .bg6 h1 {
  color:#2c4499;
}

.promo-info{
  width: 400px;
  padding-top: 100px;
}

.bg2 .promo-info, .bg4 .promo-info{
  float: right;
  right: 160px;
  position: relative;
}

.bg2 .promo-info{
  width: 350px;
}
.bg3 .promo-info{
  width: 415px;
}
.bg4 .promo-info{
  right: 80px;
}
.bg3 img{
  right: 100px;
  position: relative;
}

.btn-blue {
  color: #fff;
  background-color: #2c449c;
  border-color: #4a81d4;
  color: #49b8ff !important;
  font-weight: bold;
}

.btn-success{
  background-color: #51ba5b;
  border-color: #4da854;
}

.check-group{
  background: #4d9e3f;
  width: 25px;
  border-radius: 15px;
  height: 25px;
  display: inline-block;
  padding-left: 7px;
  padding-top: 2px;
  margin-right: 10px;
}

.check {
  --borderWidth: 5px;
  --height: 16px;
  --width: 10px;
  --borderColor: #72c83f ;
  display: inline-block;
  transform: rotate(45deg);
  height: var(--height);
  width: var(--width);
  border-bottom: var(--borderWidth) solid var(--borderColor);
  border-right: var(--borderWidth) solid var(--borderColor);
}

ul li{display: block;    padding-bottom: 12px;}

form{
  width: 600px;
  margin: 0 auto;
}

.form-control{
  padding: 1.5rem 1rem;
  border-top: 0px;
  border-left: 0px;
  border-right: 0px;
  border-radius: 0;
  color: rgba(0, 0, 0, 0.88);

}

input:focus{
  border-color: rgba(0, 0, 0, 0.88);
}

@media (max-width: 768px){
  h1{
    font-size: 24px;
  }

  .base-logo{
    width: 150px;
    height: 150px;
    background-size: 150px;
    img{
      width: 100px;
      top: 20px;
      left: 30px;
    }
  }

  form{
    width: 100%;
  }

  .promo-info{
    width: 100% !important;
    float: none !important;
    right: initial !important;
    text-align: center;
    padding-top: 20px !important;
  }

  .bg1>div,.bg2>div, .bg3>div, .bg4>div, .bg5>div , .bg6>div   {
    padding-right: 10px;
    padding-left: 10px;
    width: 100%;
  }
  .bg1,.bg2,.bg3, .bg4, .bg5, .bg6{
    position: relative;
    overflow: hidden;
    img{
      width: 280px;
      position: absolute;
      top: 365px;
    }
  }
  .bg1{
    height: 600px;
    padding-top: 30px;
    .promo-info{
      padding-top: 100px  !important;;
    }
  }
  .bg2{
    height: 575px;
    img{
      top: 285px;
      width: 60%;
      left: 17%;
    }
  }
  .bg3{
    height: 670px;
    background-position-y: -165px;
    padding-top: 0px;

    img{
      width: 60%;
      top: 415px;
    }
    ul{
      text-align: left;
      padding-left: 10px;
    }
    .check-group{
      margin-right: 10px;
    }
  }
  .bg4{
    height: 500px;
    img{
      top: 290px;
      width: 70%;
      left: 11%;
    }
  }
  .bg5{
    padding-top: 0;
    background-position-y: -100px;
    height: 540px;
    img{
      top: 285px;
      width: 70%;
      left: 10%;
    }
  }
  .bg6{
    padding-top: 15px;
    padding-bottom: 30px;
    h1{
      font-size: 18px;
    }
    button{
      margin: 0 auto;
      width: 245px;
      display: block;
    }
  }
}
