<div class="container-fluid  ">
  <div class="row">
    <div class="col">
      <div  class="base-logo">
        <img  src="/assets/fidelidade/ladingpage/logo-promokit.svg">
      </div>

      <div class="bg1">
        <div>
          <img src="/assets/fidelidade/ladingpage/img-01.png" align="right">
          <div class="promo-info">
            <h1>Programa de fidelidade digital, focado na sua relação com o cliente</h1>
            <p>De um jeito simples e moderno, você fideliza seus clientes e ainda utiliza as features para
              alavancar suas vendas</p>

            <button class="btn btn-blue btn-rounded btn-lg shadow-lg " (click)="solicitarContato()">EXPERIMENTAR</button>
          </div>
        </div>
      </div>
      <div class="bg2">
        <div>
          <img src="/assets/fidelidade/ladingpage/img-02.png" align="left">
          <div class="promo-info">
            <h1>Diga adeus ao cartão de papel</h1>
            <p>Implantar um programa de fidelidade agora é simples e barato. Além disso,
              você pode utilizar diversos recursos exclusivos para envolver seus clientes e aumentar suas vendas.</p>
            <button class="btn btn-blue btn-rounded btn-lg shadow-lg" (click)="solicitarContato()">EXPERIMENTAR</button>
          </div>
        </div>
      </div>
      <div class="bg3">
        <div>
          <img src="/assets/fidelidade/ladingpage/img-03.png" align="right">
          <div class="promo-info">
            <h1>Utilize nossas features e aumente suas vendas</h1>
            <ul>
              <li> <div class="check-group"><i class="check"></i></div> Gestão de contatos</li>
              <li><div class="check-group"><i class="check"></i></div>  Relatórios inteligentes</li>
              <li><div class="check-group"><i class="check"></i></div>  Notificações Automáticas</li>
              <li><div class="check-group"><i class="check"></i></div>  Página personalizada mobile</li>
              <li><div class="check-group"><i class="check"></i></div>  Campanhas via SMS e WhatsApp</li>
              <li><div class="check-group"><i class="check"></i></div>  Link da usa empresa para WhatsApp</li>
            </ul>

            <button class="btn btn-blue btn-rounded btn-lg shadow-lg" (click)="solicitarContato()">EXPERIMENTAR</button>
          </div>

        </div>
      </div>
      <div class="bg4">
        <div>
          <img src="/assets/fidelidade/ladingpage/img-04.png" align="left">
          <div class="promo-info">
            <h1>Campanhas via SMS e Whatsapp conforme o perfil do cliente</h1>
            <p>Com os filtros, é possível enviar mensagens para clientes de um determinado perfil, com avisos
              ou promoções especiais</p>
            <button class="btn btn-blue btn-rounded btn-lg shadow-lg" (click)="solicitarContato()">EXPERIMENTAR</button>
          </div>

        </div>


      </div>
      <div class="bg5">
        <div>
          <img src="/assets/fidelidade/ladingpage/img-05.png" align="right">
          <div class="promo-info">
            <h1>Planos flexíveis que cabem no seu bolso.</h1>
            <p> A partir de R$ 99,00 você pode ter uma solução completa para fidelizar seus clientes e gerar mais resultados para sua empresa.</p>

            <button class="btn btn-blue btn-rounded btn-lg shadow-lg" (click)="solicitarContato()">EXPERIMENTAR</button>
          </div>

        </div>

      </div>
      <div class="bg6" #bgContato>
        <div>
          <form   novalidate #frm="ngForm" (ngSubmit)="onSubmit()" [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}">
            <h1 class="text-center">Solicite um representante e começe agora a fidelizar seus clientes e vender mais</h1>
            <br>
            <div class="form-group">
              <input #nome [(ngModel)]="contato.nome"  name='nome' type="text" class="form-control" placeholder="Informe seu nome" required>
              <div class="invalid-feedback">
                <p  >Nome é obrigatório</p>
              </div>
            </div>
            <div class="form-group">
              <input #empresa [(ngModel)]="contato.empresa" name='empresa'  type="text" class="form-control" placeholder="Informe o nome da Empresa" required>
              <div class="invalid-feedback">
                <p  >Empresa é obrigatório</p>
              </div>
            </div>
            <div class="form-group">
              <input #email [(ngModel)]="contato.email"  name='email' type="email" class="form-control" placeholder="Informe seu email" required>
              <div class="invalid-feedback">
                <p  >E-mail é obrigatório</p>
              </div>
            </div>
            <div class="form-group">
              <input  #telefone="ngModel"  [(ngModel)]="contato.telefone"  campoTelefone  name='telefone' minlength="10"
                     type="tel" class="form-control" placeholder="Informe seu telefone" required>
              <div class="invalid-feedback" *ngIf="telefone.errors">
                <div *ngIf="telefone.errors.required">
                  Telefone é obrigatório
                </div>
                <div *ngIf="telefone.errors.campoTelefone && !telefone.errors.required">
                 Telefone somente números com DD, Ex.: 62981000001
                </div>
              </div>

            </div>
            <br>

            <p *ngIf="erro" class="text-danger pb-2 pb-2"><b>{{erro}}</b></p>

            <p *ngIf="contato.salvo" class="text-success  pb-2 pb-2">
               <b>Contato salvo com sucesso! Aguarde que um representante entrará em contato.</b>
            </p>
            <button type="submit" class="btn btn-success btn-lg btn-rounded shadow-lg " [disabled]="salvando" *ngIf="!contato.salvo">Solicitar contato</button>




          </form>
        </div>
      </div>
    </div>
  </div>
</div>
