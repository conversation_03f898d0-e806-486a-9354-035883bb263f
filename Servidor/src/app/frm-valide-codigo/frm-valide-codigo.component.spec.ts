import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { FrmValideCodigoComponent } from './frm-valide-codigo.component';

describe('FrmValideCodigoComponent', () => {
  let component: FrmValideCodigoComponent;
  let fixture: ComponentFixture<FrmValideCodigoComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ FrmValideCodigoComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FrmValideCodigoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
