import { Component, OnInit, OnD<PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CloudWhatsappService } from '../../services/cloud-whatsapp.service';
import { FacebookSdkService } from './facebook-sdk.service';
import { WhatsAppIntegracao, WhatsAppSignupResponse, WhatsAppTelefone } from './whatsapp.interface';
import { NotificationService } from '@progress/kendo-angular-notification';
import { ActivatedRoute } from '@angular/router';
import { WhatsappTrialService } from '../services/whatsapp-trial.service';

@Component({
  selector: 'app-config-whatsapp',
  templateUrl: './config-whatsapp.component.html',
  styleUrls: ['./config-whatsapp.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ConfigWhatsappComponent implements OnInit, OnDestroy {
  loading = false;
  carregando = false;
  testando = false; // Propriedade para controlar o estado de teste da conexão
  telefones: WhatsAppTelefone[] = [];
  dadosIntegracao: WhatsAppIntegracao = {
    associado: false,
    telefones: []
  };
  fbError = false; // Flag para indicar erro com o SDK do Facebook
  
  // Propriedades para o trial
  trialAtivadoRecentemente = false;
  dataInicioTrial: Date | null = null;
  dataFimTrial: Date | null = null;
  diasRestantesTrial = 0;

  // Propriedades para estatísticas
  carregandoEstatisticas = false;
  dadosEstatisticas: any = null;
  periodoSelecionado = '30dias';
  periodos = [
    { valor: '7dias', texto: 'Últimos 7 dias' },
    { valor: '30dias', texto: 'Últimos 30 dias' },
    { valor: 'mes', texto: 'Este mês' }
  ];
  
  // Propriedade para armazenar o total de conversas
  totalConversasPeriodo = 0;
  
  // Propriedade para armazenar o custo total
  custoTotalPeriodo = 0;
  
  private destroy$ = new Subject<void>();

  constructor(
    private fbService: FacebookSdkService,
    private cloudWhatsapp: CloudWhatsappService,
    private notificationService: NotificationService,
    private cdr: ChangeDetectorRef,
    private route: ActivatedRoute,
    private whatsappTrialService: WhatsappTrialService
  ) {}

  ngOnInit(): void {
    this.inicializar();
    this.setupMessageListener();
    this.verificarTrialAtivado();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private async inicializar(): Promise<void> {
    try {
      console.log('[WhatsApp] Iniciando carregamento dos dados');
      await this.carregueIntegracao();
      
      // Se tiver WhatsApp conectado, carregar estatísticas
      if (this.dadosIntegracao.associado) {
        this.carregueEstatisticas();
      }
    } catch (erro) {
      console.error('[WhatsApp] Erro ao inicializar componente:', erro);
      // Esconde o spinner de carregamento, mas não exibe erro para melhorar UX
      this.carregando = false;
      this.cdr.markForCheck();
    }
  }

  private verificarTrialAtivado(): void {
    // Verificar se veio da página de paywall com trial ativado
    this.route.queryParams.subscribe(params => {
      if (params['trialAtivado'] === 'true') {
        // Buscar informações do trial
        this.carregarInformacoesTrial();
      }
    });
  }

  private carregarInformacoesTrial(): void {
    this.whatsappTrialService.obterStatusTrial()
      .then((resposta: any) => {
        if (resposta) {
          this.trialAtivadoRecentemente = true;
          this.dataInicioTrial = new Date(resposta.dataInicio);
          this.dataFimTrial = new Date(resposta.dataFim);
          this.diasRestantesTrial = resposta.diasRestantes || 30;
          this.cdr.markForCheck();
        }
      })
      .catch(erro => {
        console.error('Erro ao carregar informações do trial:', erro);
      });
  }

  fecharNotificacaoTrial(): void {
    this.trialAtivadoRecentemente = false;
    this.cdr.markForCheck();
  }

  iniciarConfiguracao(): void {
    // Rolar para a seção de configuração
    const container = document.querySelector('.whatsapp-signup-container');
    if (container) {
      container.scrollIntoView({ behavior: 'smooth' });
    }
    this.fecharNotificacaoTrial();
  }

  verDocumentacao(): void {
    // Abrir documentação em nova aba
    window.open('https://meucardapio.com/docs/whatsapp-api', '_blank');
  }

  private async carregueIntegracao(): Promise<void> {
    try {
      this.carregando = true;
      this.cdr.markForCheck();
      
      console.log('[WhatsApp] Carregando dados da integração');

      try {
        const dados = await this.cloudWhatsapp.carregueDados();
        console.log('[WhatsApp] Dados da integração recebidos:', JSON.stringify(dados));
        
        this.dadosIntegracao = dados;
        
        // Converter dataCriacao de string para Date em cada telefone
        if (dados.telefones && dados.telefones.length > 0) {
          this.telefones = dados.telefones.map(telefone => {
            if (telefone.dataCriacao) {
              // Converte a string para objeto Date
              telefone.dataCriacao = new Date(telefone.dataCriacao);
            }
            return telefone;
          });
        } else {
          this.telefones = [];
        }
        
        console.log('[WhatsApp] Status de associação:', dados.associado);
        console.log('[WhatsApp] Quantidade de telefones:', dados.telefones?.length || 0);
      } catch (erroCarregamento) {
        console.error('[WhatsApp] Falha ao carregar dados:', erroCarregamento);
        
        // Em caso de erro, define valores padrão
        this.dadosIntegracao = {
          associado: false,
          telefones: []
        };
        this.telefones = [];
      }
      
      this.cdr.markForCheck();
    } catch (erro) {
      console.error('[WhatsApp] Erro ao carregar dados da integração:', erro);
      this.notificationService.show({
        content: 'Erro ao carregar dados da integração',
        cssClass: 'error',
        animation: { type: 'slide', duration: 400 },
        position: { horizontal: 'right', vertical: 'top' },
        type: { style: 'error', icon: true }
      });
    } finally {
      this.carregando = false;
      this.cdr.markForCheck();
    }
  }

  private setupMessageListener(): void {
    this.fbService.messages$
      .pipe(takeUntil(this.destroy$))
      .subscribe(this.handleSignupResponse.bind(this));
  }

  private handleSignupResponse(response: WhatsAppSignupResponse): void {
    switch(response.event) {
      case 'FINISH':
        this.handleSignupSuccess(response.data);
        break;
      case 'CANCEL':
        this.handleSignupCancel(response.data.current_step);
        break;
      case 'ERROR':
        this.handleSignupError(response.data);
        break;
    }
  }

  private handleSignupSuccess(data: WhatsAppSignupResponse['data']): void {
    this.loading = false;
    this.notificationService.show({
      content: 'WhatsApp conectado com sucesso!',
      cssClass: 'success',
      animation: { type: 'slide', duration: 400 },
      position: { horizontal: 'right', vertical: 'top' },
      type: { style: 'success', icon: true }
    });
    this.carregueIntegracao();
  }

  private handleSignupCancel(step?: string): void {
    this.loading = false;
    this.notificationService.show({
      content: 'Processo de cadastro cancelado',
      cssClass: 'info',
      animation: { type: 'slide', duration: 400 },
      position: { horizontal: 'right', vertical: 'top' },
      type: { style: 'info', icon: true }
    });
    this.cdr.markForCheck();
  }

  private handleSignupError(data: WhatsAppSignupResponse['data']): void {
    this.loading = false;
    this.notificationService.show({
      content: data.error_message || 'Erro ao conectar WhatsApp',
      cssClass: 'error',
      animation: { type: 'slide', duration: 400 },
      position: { horizontal: 'right', vertical: 'top' },
      type: { style: 'error', icon: true }
    });
    this.cdr.markForCheck();
  }

  public async launchWhatsAppSignup(): Promise<void> {
    try {
      this.loading = true;
      this.fbError = false;
      this.cdr.markForCheck();
      
      try {
        const response = await this.fbService.launchWhatsAppSignup().toPromise();
        
        if (response?.authResponse?.code) {
          try {
            // NOVO: Trocar o código de autorização por um access token
            const accessToken = await this.cloudWhatsapp.exchangeCodeForToken(response.authResponse.code);
            
            // Agora usar o token para obter os telefones
            const telefones = await this.cloudWhatsapp.obtenhaTelefones(accessToken);
            
            if (telefones && telefones.length > 0) {
              // Associar o primeiro número disponível
              try {
                // Usar o access token, não o código
                const resultadoAssociacao = await this.associarWhatsapp(telefones[0], accessToken);
                
                this.notificationService.show({
                  content: 'WhatsApp conectado com sucesso!',
                  cssClass: 'success',
                  animation: { type: 'slide', duration: 400 },
                  position: { horizontal: 'right', vertical: 'top' },
                  type: { style: 'success', icon: true }
                });
                
                // Aguardar um momento antes de recarregar para o banco ter tempo de processar
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Recarregar os dados da integração
                await this.carregueIntegracao();
              } catch (erroAssociacao) {
                console.error('[WhatsApp] Erro ao associar número:', erroAssociacao);
                this.notificationService.show({
                  content: 'Erro ao associar WhatsApp. Verifique se você está autenticado no sistema.',
                  cssClass: 'error',
                  animation: { type: 'slide', duration: 400 },
                  position: { horizontal: 'right', vertical: 'top' },
                  type: { style: 'error', icon: true }
                });
              }
            } else {
              console.error('[WhatsApp] Nenhum número de WhatsApp encontrado na conta');
              this.notificationService.show({
                content: 'Nenhum número de WhatsApp Business encontrado na sua conta. Certifique-se de ter uma conta WhatsApp Business configurada.',
                cssClass: 'error',
                animation: { type: 'slide', duration: 400 },
                position: { horizontal: 'right', vertical: 'top' },
                type: { style: 'error', icon: true }
              });
            }
          } catch (erroToken) {
            console.error('[WhatsApp] Erro ao trocar código por token:', erroToken);
            
            // Extrair mensagem detalhada do erro, se disponível
            let mensagemErro = 'Erro ao obter token de acesso.';
            
            if (erroToken.error && erroToken.error.mensagem) {
              mensagemErro += ' ' + erroToken.error.mensagem;
            } else if (erroToken.message) {
              mensagemErro += ' ' + erroToken.message;
            }
            
            console.error('[WhatsApp] Mensagem de erro detalhada:', mensagemErro);
            
            this.notificationService.show({
              content: mensagemErro + ' Por favor, tente novamente.',
              cssClass: 'error',
              animation: { type: 'slide', duration: 400 },
              position: { horizontal: 'right', vertical: 'top' },
              type: { style: 'error', icon: true }
            });
          }
        } else {
          console.error('[WhatsApp] Não foi possível obter o código de autorização', response);
          this.notificationService.show({
            content: 'Não foi possível obter autorização do Facebook. Por favor, tente novamente.',
            cssClass: 'error',
            animation: { type: 'slide', duration: 400 },
            position: { horizontal: 'right', vertical: 'top' },
            type: { style: 'error', icon: true }
          });
        }
      } catch (errorFB) {
        console.error('[WhatsApp] Erro com SDK do Facebook:', errorFB);
        this.fbError = true;
        this.notificationService.show({
          content: 'Erro ao conectar com o Facebook. Verifique se bloqueadores de anúncios estão desativados.',
          cssClass: 'error',
          animation: { type: 'slide', duration: 400 },
          position: { horizontal: 'right', vertical: 'top' },
          type: { style: 'error', icon: true }
        });
      }
    } catch (error) {
      console.error('[WhatsApp] Erro no processo de signup:', error);
      this.notificationService.show({
        content: 'Erro ao conectar WhatsApp. Por favor, tente novamente.',
        cssClass: 'error',
        animation: { type: 'slide', duration: 400 },
        position: { horizontal: 'right', vertical: 'top' },
        type: { style: 'error', icon: true }
      });
    } finally {
      this.loading = false;
      this.cdr.markForCheck();
    }
  }

  public async associarWhatsapp(telefone: WhatsAppTelefone, accessToken?: string): Promise<any> {
    try {
      this.loading = true;
      this.cdr.markForCheck();
      
      console.log('[WhatsApp] Iniciando associação do número:', telefone.telefone);
      console.log('[WhatsApp] Access Token para associação:', accessToken ? 'Token presente (comprimento: ' + accessToken.length + ')' : 'Token ausente');
      console.log('[WhatsApp] Dados completos do telefone:', JSON.stringify(telefone));
      
      if (!accessToken) {
        console.error('[WhatsApp] Tentativa de associar sem token de acesso');
        throw new Error('Token de acesso é obrigatório para associar WhatsApp');
      }

      const resultado = await this.cloudWhatsapp.associe(accessToken, telefone);
      console.log('[WhatsApp] Resultado da associação:', JSON.stringify(resultado));
      
      this.notificationService.show({
        content: 'WhatsApp associado com sucesso!',
        cssClass: 'success',
        animation: { type: 'slide', duration: 400 },
        position: { horizontal: 'right', vertical: 'top' },
        type: { style: 'success', icon: true }
      });
      
      console.log('[WhatsApp] Recarregando dados após associação');
      await this.carregueIntegracao();
      
      return resultado;
    } catch (error) {
      console.error('[WhatsApp] Erro ao associar WhatsApp:', error);
      this.notificationService.show({
        content: 'Erro ao associar WhatsApp. Por favor, tente novamente.',
        cssClass: 'error',
        animation: { type: 'slide', duration: 400 },
        position: { horizontal: 'right', vertical: 'top' },
        type: { style: 'error', icon: true }
      });
      throw error;
    } finally {
      this.loading = false;
      this.cdr.markForCheck();
    }
  }

  public async desconectar(): Promise<void> {
    try {
      this.loading = true;
      this.cdr.markForCheck();

      await this.cloudWhatsapp.desassocie();
      
      this.notificationService.show({
        content: 'WhatsApp desconectado com sucesso!',
        cssClass: 'success',
        animation: { type: 'slide', duration: 400 },
        position: { horizontal: 'right', vertical: 'top' },
        type: { style: 'success', icon: true }
      });
      
      this.carregueIntegracao();
    } catch (error) {
      console.error('Erro ao desconectar WhatsApp:', error);
      this.notificationService.show({
        content: 'Erro ao desconectar WhatsApp',
        cssClass: 'error',
        animation: { type: 'slide', duration: 400 },
        position: { horizontal: 'right', vertical: 'top' },
        type: { style: 'error', icon: true }
      });
    } finally {
      this.loading = false;
      this.cdr.markForCheck();
    }
  }

  // Método para recarregar a página
  public reloadPage(): void {
    window.location.reload();
  }

  // Método para testar a conexão com o WhatsApp
  public async testarConexao(): Promise<void> {
    try {
      this.testando = true;
      this.cdr.markForCheck();
      
      // Simular uma verificação de conexão (pode ser substituída por uma API real)
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Verificar se há telefones conectados
      if (this.telefones && this.telefones.length > 0) {
        this.notificationService.show({
          content: 'Conexão com WhatsApp está funcionando corretamente!',
          cssClass: 'success',
          animation: { type: 'slide', duration: 400 },
          position: { horizontal: 'right', vertical: 'top' },
          type: { style: 'success', icon: true }
        });
      } else {
        throw new Error('Nenhum número de WhatsApp encontrado.');
      }
    } catch (error) {
      console.error('[WhatsApp] Erro ao testar conexão:', error);
      this.notificationService.show({
        content: 'Erro ao testar conexão com WhatsApp. ' + (error.message || ''),
        cssClass: 'error',
        animation: { type: 'slide', duration: 400 },
        position: { horizontal: 'right', vertical: 'top' },
        type: { style: 'error', icon: true }
      });
    } finally {
      this.testando = false;
      this.cdr.markForCheck();
    }
  }

  public carregueEstatisticas(): void {
    if (!this.dadosIntegracao.associado) {
      console.log('[WhatsApp] Não há WhatsApp associado, pulando carregamento de estatísticas');
      return;
    }
    
    this.carregandoEstatisticas = true;
    this.cdr.markForCheck();
    
    console.log(`[WhatsApp] Carregando estatísticas para período: ${this.periodoSelecionado}`);
    
    this.cloudWhatsapp.obtenhaResumoConversas(this.periodoSelecionado)
      .then(response => {
        this.dadosEstatisticas = response;
        
        // Calcular o total de conversas e custo somando todos os dataPoints
        this.totalConversasPeriodo = 0;
        this.custoTotalPeriodo = 0;
        if (this.dadosEstatisticas && this.dadosEstatisticas.dataPoints && this.dadosEstatisticas.dataPoints.length > 0) {
          this.dadosEstatisticas.dataPoints.forEach(ponto => {
            this.totalConversasPeriodo += (ponto.conversas || 0);
            this.custoTotalPeriodo += (ponto.cost || 0);
          });
        }
        console.log(`[WhatsApp] Total de conversas calculado: ${this.totalConversasPeriodo}, Custo total: R$ ${this.custoTotalPeriodo.toFixed(2)}`);
        
        // Verificar se há erros retornados pela API
        if (this.dadosEstatisticas && this.dadosEstatisticas.erro) {
          console.warn('[WhatsApp] API retornou erro (usando dados de fallback):', this.dadosEstatisticas.erro);
          this.notificationService.show({
            content: `As estatísticas podem estar incompletas: ${this.dadosEstatisticas.erro}`,
            cssClass: 'warning',
            animation: { type: 'slide', duration: 400 },
            position: { horizontal: 'right', vertical: 'top' },
            type: { style: 'warning', icon: true },
            closable: true,
            hideAfter: 8000
          });
        } else {
          console.log('[WhatsApp] Estatísticas carregadas com sucesso');
        }
      })
      .catch(erro => {
        console.error('[WhatsApp] Erro ao carregar estatísticas:', erro);
        
        // Extrair mensagem de erro detalhada, se disponível
        let mensagemErro = 'Erro ao carregar estatísticas do WhatsApp';
        if (erro.error && erro.error.mensagem) {
          mensagemErro += ': ' + erro.error.mensagem;
        } else if (erro.message) {
          mensagemErro += ': ' + erro.message;
        }
        
        this.notificationService.show({
          content: mensagemErro,
          cssClass: 'error',
          animation: { type: 'slide', duration: 400 },
          position: { horizontal: 'right', vertical: 'top' },
          type: { style: 'error', icon: true },
          closable: true
        });
        
        // Definir dadosEstatisticas como null para mostrar mensagem de "sem dados"
        this.dadosEstatisticas = null;
      })
      .finally(() => {
        this.carregandoEstatisticas = false;
        this.cdr.markForCheck();
      });
  }
  
  public atualizarPeriodoEstatisticas(evento: any): void {
    this.periodoSelecionado = evento.target.value;
    this.carregueEstatisticas();
  }
  
  public abrirEstatisticasFacebook(): void {
    if (!this.telefones || this.telefones.length === 0) {
      this.notificationService.show({
        content: 'Não foi possível abrir as estatísticas: nenhum telefone conectado',
        cssClass: 'warning',
        animation: { type: 'slide', duration: 400 },
        position: { horizontal: 'right', vertical: 'top' },
        type: { style: 'warning', icon: true }
      });
      return;
    }

    // Usar o primeiro telefone da lista
    const telefone = this.telefones[0];
    
    // Formatar o número para o formato esperado pelo Facebook
    // Remover caracteres não numéricos e adicionar espaço após o código do país
    let numeroFormatado = telefone.telefone;
    if (numeroFormatado) {
      // Manter somente os dígitos
      numeroFormatado = numeroFormatado.replace(/\D/g, '');
      
      // Verificar se começa com o código do país, senão adicionar 55 (Brasil)
      if (!numeroFormatado.startsWith('55')) {
        numeroFormatado = '55' + numeroFormatado;
      }
      
      // Formatar como "55 99 99999-9999"
      if (numeroFormatado.length >= 12) {
        // Inserir espaço após o código do país (55)
        numeroFormatado = numeroFormatado.substring(0, 2) + ' ' + 
                          numeroFormatado.substring(2, 4) + ' ' + 
                          numeroFormatado.substring(4);
      }
    }
    
    // Usar valor padrão para business_id e obter waba_id do telefone
    const businessId = '718978485158330'; // ID fixo do business
    const wabaId = telefone.waba_id;
    
    // Construir a URL dinamicamente
    const url = `https://business.facebook.com/latest/whatsapp_manager/overview?business_id=${businessId}&tab=phone-numbers&childRoute=PHONE_PROFILE%2FINSIGHTS&phone_number=${encodeURIComponent(numeroFormatado)}&nav_ref=whatsapp_manager&asset_id=${wabaId}`;
  
    console.log('[WhatsApp] Abrindo estatísticas do Facebook para o número:', numeroFormatado);
    window.open(url, '_blank');
  }
}
