// Variáveis
$whatsapp-green: #25D366;
$whatsapp-green-dark: #128C7E;
$whatsapp-blue: #34B7F1;
$body-bg: #f8f9fe;
$card-bg: #fff;
$text-color: #2c3e50;
$text-muted: #6c757d;
$border-color: #eef2f7;
$shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 16px rgba(0, 0, 0, 0.08);
$border-radius: 12px;
$danger-color: #e74c3c;
$highlight-color: #ff9500;
$shield-color: #27ae60;

// Estilos gerais
.whatsapp-paywall-container {
  background: $body-bg;
  padding: 2rem 0 4rem;
  min-height: 100vh;
  font-family: 'Segoe UI', Roboto, sans-serif;
  color: $text-color;
}

// Cabeçalho
.paywall-header {
  margin-bottom: 2.5rem;
  
  .header-icon {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 80px;
    height: 80px;
    background: $whatsapp-green;
    border-radius: 20px;
    box-shadow: $shadow-md;

    i {
      font-size: 48px;
      color: white;
    }
  }
  
  h2 {
    color: $text-color;
    font-weight: 700;
    font-size: 2.2rem;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
  }
  
  p.lead {
    color: $text-muted;
    font-size: 1.1rem;
    max-width: 700px;
    margin: 0 auto;
  }
}

// Banner de destaque
.highlight-banner {
  background: linear-gradient(135deg, #ffffff 0%, #f2f7ff 100%);
  border-radius: $border-radius;
  padding: 2rem;
  margin-bottom: 2.5rem;
  box-shadow: $shadow-sm;
  border: 1px solid $border-color;
  
  .highlight-image {
    max-height: 200px;
    margin-bottom: 1.5rem;
  }
  
  .highlight-list {
    margin-bottom: 1.5rem;
    
    .highlight-item {
      display: flex;
      align-items: center;
      margin-bottom: 0.75rem;
      
      i {
        color: $whatsapp-green;
        font-size: 1.5rem;
        margin-right: 0.75rem;
      }
      
      span {
        font-weight: 700;
        font-size: 1.1rem;
        letter-spacing: 0.5px;
      }
    }
  }
  
  .cta-button-large {
    .btn-primary {
      background-color: $whatsapp-green;
      border-color: $whatsapp-green;
      font-weight: 700;
      font-size: 1.1rem;
      padding: 0.75rem 1.5rem;
      border-radius: 10px;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      
      &:hover, &:focus {
        background-color: $whatsapp-green-dark;
        border-color: $whatsapp-green-dark;
        box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
        transform: translateY(-2px);
      }
      
      i {
        font-size: 1.2rem;
      }
    }
    
    .cta-subtitle {
      color: $whatsapp-green-dark;
      font-weight: 600;
      margin-top: 0.75rem;
      font-size: 0.9rem;
    }
  }
}

// Banner de Coexistência
.coexistence-banner {
  background: linear-gradient(135deg, #f8fcff 0%, #f0f9ea 100%);
  border-radius: $border-radius;
  padding: 1.5rem;
  margin-bottom: 2.5rem;
  display: flex;
  align-items: center;
  box-shadow: $shadow-sm;
  border: 1px solid rgba($whatsapp-green, 0.2);
  overflow: hidden;
  position: relative;
  
  &:before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: linear-gradient(45deg, rgba($whatsapp-green, 0.03) 0%, rgba($whatsapp-blue, 0.03) 100%);
    z-index: 0;
  }
  
  .coexistence-icon {
    position: relative;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, $whatsapp-green 0%, $whatsapp-blue 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    flex-shrink: 0;
    box-shadow: $shadow-md;
    z-index: 1;
    
    i {
      color: white;
      font-size: 28px;
    }
  }
  
  .coexistence-content {
    position: relative;
    z-index: 1;
    
    h4 {
      font-weight: 700;
      margin-bottom: 0.5rem;
      font-size: 1.4rem;
      color: $text-color;
    }
    
    p {
      margin-bottom: 0;
      font-size: 1rem;
      line-height: 1.5;
      color: $text-color;
    }
  }
}

// Animação Pulse para ícones
.pulse-animation {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.pulse-slow {
  animation: pulse 3s infinite;
}

// Highlight text
.highlight-text {
  color: $highlight-color;
  font-weight: 700;
  position: relative;
  
  &:after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: rgba($highlight-color, 0.3);
    border-radius: 2px;
  }
}

// Tabela Comparativa
.comparison-section {
  background: #fff;
  border-radius: $border-radius;
  padding: 2rem;
  margin-bottom: 2.5rem;
  box-shadow: $shadow-sm;
  border: 1px solid $border-color;

  .current-solution-alert {
    background-color: rgba($whatsapp-blue, 0.1);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    
    i {
      color: $whatsapp-blue;
      font-size: 1.5rem;
      margin-right: 1rem;
    }
    
    span {
      color: $text-color;
      font-size: 1rem;
      
      strong {
        font-weight: 600;
      }
    }
  }
  
  .comparison-title {
    font-weight: 700;
    font-size: 1.4rem;
    margin-bottom: 1.5rem;
    color: $text-color;
    text-align: center;
  }
  
  .comparison-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 1.5rem;
    
    .comparison-row {
      display: flex;
      border-bottom: 1px solid $border-color;
      
      &:last-child {
        border-bottom: none;
      }
      
      &.header-row {
        background-color: #f8f9fa;
        font-weight: 700;
        
        .comparison-cell {
          padding: 1rem 0.5rem;
        }
      }
      
      &.highlighted-row {
        background-color: rgba($whatsapp-green, 0.05);
        font-weight: 600;
        
        .feature-cell {
          color: $whatsapp-green-dark;
        }
      }
      
      &:nth-child(odd):not(.header-row):not(.highlighted-row) {
        background-color: rgba($body-bg, 0.5);
      }
    }
    
    .comparison-cell {
      flex: 1;
      padding: 0.75rem 0.5rem;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &.feature-cell {
        flex: 3;
        justify-content: flex-start;
        padding-left: 1rem;
        font-weight: 500;
      }
      
      &.api-cell {
        background-color: rgba($whatsapp-green, 0.03);
        border-left: 2px solid rgba($whatsapp-green, 0.1);
      }
      
      .check-icon {
        color: $whatsapp-green;
        font-size: 1.2rem;
      }
      
      .x-icon {
        color: $danger-color;
        font-size: 1.2rem;
      }
      
      .plus-icon {
        color: $highlight-color;
        font-size: 1.2rem;
        margin-right: 0.3rem;
      }
      
      .new-feature {
        font-size: 0.7rem;
        font-weight: 700;
        background: $highlight-color;
        color: white;
        padding: 2px 6px;
        border-radius: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }
  
  .ban-protection-notice {
    background-color: rgba($whatsapp-green, 0.1);
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    align-items: center;
    
    i {
      color: $whatsapp-green-dark;
      font-size: 1.5rem;
      margin-right: 1rem;
      flex-shrink: 0;
    }
    
    p {
      margin: 0;
      color: $text-color;
      font-size: 0.95rem;
      line-height: 1.5;
    }
  }
  
  // Botão CTA adicional na seção de comparação
  .comparison-cta {
    margin-top: 2rem;
    text-align: center;
    
    .btn-primary {
      background-color: $whatsapp-green;
      border-color: $whatsapp-green;
      font-weight: 700;
      font-size: 1.1rem;
      padding: 0.75rem 2rem;
      border-radius: 10px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 10px rgba($whatsapp-green, 0.2);
      
      &:hover, &:focus {
        background-color: $whatsapp-green-dark;
        border-color: $whatsapp-green-dark;
        box-shadow: 0 6px 15px rgba($whatsapp-green, 0.3);
        transform: translateY(-2px);
      }
      
      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 5px rgba($whatsapp-green, 0.2);
      }
      
      i {
        font-size: 1.2rem;
      }
    }
  }
}

// Benefícios e Preço
.benefits-pricing-section {
  margin-bottom: 2.5rem;
  
  .benefits-card, .pricing-card {
    background: $card-bg;
    border-radius: $border-radius;
    padding: 1.5rem;
    height: 100%;
    box-shadow: $shadow-sm;
    border: 1px solid $border-color;
    
    h4 {
      color: $text-color;
      font-weight: 700;
      font-size: 1.2rem;
      margin-bottom: 1.5rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      padding-bottom: 0.75rem;
      border-bottom: 2px solid $border-color;
    }
  }
  
  // Cartão de benefícios
  .benefits-card {
    .benefits-list {
      list-style-type: none;
      padding: 0;
      margin: 0;
      
      li {
        display: flex;
        margin-bottom: 1.25rem;
        
        i {
          color: $whatsapp-green;
          font-size: 1.5rem;
          margin-right: 1rem;
          margin-top: 0.15rem;
          
          &.highlight-icon {
            color: $highlight-color;
          }
        }
        
        div {
          flex: 1;
          
          h6 {
            font-weight: 600;
            margin-bottom: 0.25rem;
            font-size: 1rem;
          }
          
          p {
            color: $text-muted;
            margin-bottom: 0;
            font-size: 0.9rem;
          }
        }
        
        &.featured-benefit {
          background: rgba($shield-color, 0.08);
          margin: 1.5rem -1.5rem; 
          padding: 1rem 1.5rem;
          border-left: 4px solid $shield-color;
          
          i {
            color: $shield-color;
            font-size: 1.8rem;
            animation: pulse-shield 2s infinite;
          }
          
          div h6 {
            color: $shield-color;
            font-size: 1.1rem;
            font-weight: 700;
            letter-spacing: 0.5px;
          }
          
          div p {
            color: darken($text-muted, 10%);
          }
        }
      }
    }
  }
  
  // Cartão de preço
  .pricing-card {
    text-align: center;
    position: relative;
    overflow: visible;
    padding-top: 2.5rem;
    
    .trial-badge {
      position: absolute;
      top: -15px;
      left: 50%;
      transform: translateX(-50%);
      background: $highlight-color;
      color: white;
      font-weight: 700;
      padding: 0.5rem 1.5rem;
      border-radius: 30px;
      font-size: 0.9rem;
      box-shadow: $shadow-sm;
      white-space: nowrap;
      letter-spacing: 0.5px;
      z-index: 2;
    }
    
    .cta-container {
      margin: 1rem 0 1.5rem;
      
      .btn-primary {
        background-color: $whatsapp-green;
        border-color: $whatsapp-green;
        font-weight: 700;
        font-size: 1rem;
        padding: 0.75rem 1rem;
        border-radius: 10px;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        
        &:hover, &:focus {
          background-color: $whatsapp-green-dark;
          border-color: $whatsapp-green-dark;
          box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
          transform: translateY(-2px);
        }
        
        i {
          font-size: 1.2rem;
        }
      }
      
      .price-info {
        margin-top: 0.75rem;
        font-size: 0.9rem;
        color: $text-muted;
        
        strong {
          color: $text-color;
          font-weight: 700;
        }
        
        .cancel-anytime {
          display: block;
          font-size: 0.8rem;
          margin-top: 0.2rem;
          font-style: italic;
        }
      }
    }
    
    .pricing-features {
      list-style-type: none;
      padding: 0;
      margin: 0 0 1rem;
      text-align: left;
      
      li {
        padding: 0.5rem 0;
        color: $text-color;
        
        i {
          color: $whatsapp-green;
          margin-right: 0.5rem;
        }
        
        &.featured-item {
          padding: 0.6rem 0;
          
          i {
            color: $highlight-color;
          }
          
          strong {
            color: $text-color;
            font-weight: 700;
            font-size: 1.05rem;
          }
        }
      }
    }
  }
}

@keyframes pulse-shield {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// Depoimento
.testimonial-section {
  margin-bottom: 2.5rem;
  
  .testimonial-card {
    background: linear-gradient(135deg, #ffffff 0%, #f7fdfa 100%);
    border-radius: $border-radius;
    padding: 2rem;
    position: relative;
    box-shadow: $shadow-sm;
    border: 1px solid $border-color;
    
    .testimonial-icon {
      position: absolute;
      top: -20px;
      right: 30px;
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: $whatsapp-green;
      border-radius: 50%;
      box-shadow: $shadow-md;
      
      i {
        color: white;
        font-size: 1.5rem;
      }
    }
    
    .testimonial-text {
      font-size: 1.1rem;
      font-style: italic;
      color: $text-color;
      line-height: 1.6;
      margin-bottom: 1rem;
    }
    
    .testimonial-author {
      display: flex;
      flex-direction: column;
      
      .author-name {
        font-weight: 600;
        color: $text-color;
      }
      
      .author-location {
        color: $text-muted;
        font-size: 0.9rem;
      }
    }
  }
}

// Nota de rodapé
.footer-note {
  .alert-info {
    background-color: #e8f4fd;
    border-color: #d1e7f9;
    color: #0c5460;
    border-radius: 8px;
  }
  
  .payment-info {
    background-color: #fff9e6;
    border: 1px solid #ffe5b4;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    display: flex;
    align-items: flex-start;
    
    i {
      color: #f1c40f;
      font-size: 1.5rem;
      margin-right: 1rem;
      margin-top: 0.25rem;
      flex-shrink: 0;
    }
    
    .payment-info-content {
      h6 {
        color: #7d6608;
        font-weight: 700;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
      }
      
      p {
        color: #6c5328;
        font-size: 0.9rem;
        line-height: 1.5;
        margin-bottom: 0;
        
        strong {
          font-weight: 700;
        }
      }
    }
  }
  
  .footer-cta {
    text-align: center;
    margin: 2rem 0 1rem;
    
    .btn-primary {
      background-color: #25D366;
      border-color: #25D366;
      font-weight: 700;
      font-size: 1.1rem;
      padding: 0.75rem 2rem;
      border-radius: 10px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 10px rgba(37, 211, 102, 0.2);
      
      &:hover, &:focus {
        background-color: $whatsapp-green-dark;
        border-color: $whatsapp-green-dark;
        box-shadow: 0 6px 15px rgba(37, 211, 102, 0.3);
        transform: translateY(-2px);
      }
      
      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 5px rgba(37, 211, 102, 0.2);
      }
      
      i {
        font-size: 1.2rem;
      }
    }
  }
  
  p {
    margin-top: 1rem;
    
    a {
      color: $whatsapp-green-dark;
      text-decoration: none;
      font-weight: 500;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

// Responsivo
@media (max-width: 767.98px) {
  .paywall-header {
    h2 {
      font-size: 1.8rem;
    }
  }
  
  .highlight-banner {
    padding: 1.5rem;
    
    .highlight-list .highlight-item span {
      font-size: 1rem;
    }
    
    .cta-button-large .btn-primary {
      font-size: 1rem;
      padding: 0.6rem 1rem;
    }
  }
  
  .comparison-section {
    padding: 1.5rem;
    
    .comparison-table {
      .comparison-row {
        font-size: 0.9rem;
      }
      
      .feature-cell {
        padding-left: 0.5rem;
      }
    }
    
    .ban-protection-notice {
      flex-direction: column;
      text-align: center;
      
      i {
        margin-right: 0;
        margin-bottom: 0.5rem;
      }
    }
  }
  
  .benefits-pricing-section {
    .benefits-card, .pricing-card {
      margin-bottom: 1.5rem;
    }
  }
  
  .testimonial-section .testimonial-card {
    padding: 1.5rem;
    
    .testimonial-text {
      font-size: 1rem;
    }
  }
}

// Estilos para o exemplo de botão interativo
.interactive-button-example {
  background-color: #f8f9fa;
  border-radius: $border-radius;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid $border-color;
  
  h5 {
    color: $text-color;
    font-weight: 600;
    margin-bottom: 1rem;
    text-align: center;
  }
  
  .whatsapp-example {
    max-width: 400px;
    margin: 0 auto;
    
    .chat-container {
      background-image: url('/assets/fidelidade/whatsapp-bg.png');
      background-color: #e5ddd5;
      border-radius: 10px;
      padding: 1rem;
      box-shadow: $shadow-sm;
      
      .client-message {
        background-color: #dcf8c6;
        border-radius: 10px;
        padding: 0.5rem 1rem;
        margin-bottom: 1rem;
        margin-left: auto;
        max-width: 80%;
        position: relative;
        
        p {
          margin: 0;
          font-size: 0.9rem;
        }
        
        .message-time {
          font-size: 0.7rem;
          color: rgba(0,0,0,0.5);
          position: absolute;
          bottom: 5px;
          right: 10px;
        }
      }
      
      .store-message {
        background-color: white;
        border-radius: 10px;
        padding: 0.5rem 1rem 1rem;
        max-width: 85%;
        
        .message-title {
          font-weight: bold;
          font-size: 1rem;
          margin-bottom: 0.5rem;
        }
        
        p {
          margin: 0 0 0.5rem;
          font-size: 0.9rem;
        }
        
        .action-button {
          border: 1px solid #3478f5;
          color: #3478f5;
          border-radius: 4px;
          padding: 0.5rem;
          text-align: center;
          font-weight: 500;
          position: relative;
          margin-top: 0.75rem;
          
          i {
            margin-right: 0.5rem;
          }
          
          .button-time {
            font-size: 0.7rem;
            color: rgba(0,0,0,0.5);
            position: absolute;
            bottom: 5px;
            right: 10px;
          }
        }
      }
    }
    
    .example-caption {
      text-align: center;
      font-style: italic;
      margin-top: 1rem;
      font-size: 0.9rem;
      color: $text-muted;
    }
  }
} 