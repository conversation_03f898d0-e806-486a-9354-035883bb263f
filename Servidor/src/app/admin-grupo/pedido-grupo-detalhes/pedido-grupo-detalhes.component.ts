import { Component, OnInit } from '@angular/core';
import {ActivatedRoute} from "@angular/router";

@Component({
  selector: 'app-pedido-grupo-detalhes',
  templateUrl: './pedido-grupo-detalhes.component.html',
  styleUrls: ['./pedido-grupo-detalhes.component.scss']
})
export class PedidoGrupoDetalhesComponent implements OnInit {
  idEmpresa: any
  constructor(private activatedRoute: ActivatedRoute) {
    this.idEmpresa = this.activatedRoute.snapshot.params['idEmpresa'];
  }

  ngOnInit(): void {
  }

}
