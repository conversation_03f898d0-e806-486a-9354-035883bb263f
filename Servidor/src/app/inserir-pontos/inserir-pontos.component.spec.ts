import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { InserirPontosComponent } from './inserir-pontos.component';

describe('InserirPontosComponent', () => {
  let component: InserirPontosComponent;
  let fixture: ComponentFixture<InserirPontosComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ InserirPontosComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InserirPontosComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
