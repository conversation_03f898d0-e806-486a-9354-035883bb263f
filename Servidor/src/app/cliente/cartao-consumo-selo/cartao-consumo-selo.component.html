<a routerLink="{{cartao.link}}"><h4>{{cartao.plano?.tituloCartao}}</h4></a>

<div class="slide">
  <div class="pontuacao "  >
    <div class="row" [ngClass]="{
          'mb-1': i < obtenhaQuantidadeDeLinhas() - 1,
          'mb-2': i == obtenhaQuantidadeDeLinhas() -1
      }" *ngFor="let selos of listaMatrizSelos[0]; let i = index">

      <div class="col" *ngFor="let selo of selos" style="padding-left: 0px;padding-right: 0px;">
        <div class="container_selo" [ngClass]="{
                'on': selo.status == 1,
                'off': selo.status == 0,
                'sem': selo.status == -1
              }">
          <div class="icone selo" [inlineSVG]="'/assets/fidelidade/icones/icon-selo.svg'" [removeSVGAttributes]="['fill']"></div>
          <div class="valor"><span>{{selo.valor}}</span></div>
        </div>
      </div>
    </div>
  </div>


  <kendo-tabstrip class="nav-bordered mt-2"   #tabs  >

    <kendo-tabstrip-tab [title]="'Brindes'"  [id]="'brindes'" [selected]="true">
      <ng-template kendoTabContent>

        <div class="brinde linha"   *ngFor="let brinde of cartao.plano?.brindes; let indice = index">
          <span class="preco_troca">{{brinde.valorEmPontos}}  {{brinde.valorEmPontos <= 1 ? 'selo' : 'selos' }}</span>
          <div>
            <div class="nome_brinde">{{brinde.nome}}</div>
            <img class="foto_brinde" src="/images/empresa/{{brinde.linkImagem}}">


          </div>
        </div>

      </ng-template>
    </kendo-tabstrip-tab>

    <kendo-tabstrip-tab [title]=" 'Extrato' "  [id]="'extrato'">
      <ng-template kendoTabContent>

        <app-extrato-pontos [cartao]="cartao"  ></app-extrato-pontos>

      </ng-template>
    </kendo-tabstrip-tab>

    <kendo-tabstrip-tab [title]="'Expirando' " *ngIf="true" [id]="'expirando'">
      <ng-template kendoTabContent>

        <app-cartao-proximos-vencimentos [cartao]="cartao"  ></app-cartao-proximos-vencimentos>

      </ng-template>
    </kendo-tabstrip-tab>

  </kendo-tabstrip>

</div>
