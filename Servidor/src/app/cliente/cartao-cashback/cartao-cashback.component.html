<a routerLink="{{cartao.link}}"><h4><PERSON><PERSON><PERSON> {{cartao.plano?.nome}}</h4></a>

<div class="   mb-2">
  <div class="row">
    <div class="col">
      <div class="pontos">

        <div class="pontos-interno">
          <div class="icone estrela" [inlineSVG]="'/assets/fidelidade/icones/star-icone.svg'" [removeSVGAttributes]="['fill']"></div>
          {{cartao.pontos |  currency: "BRL"}}
        </div>
      </div>


    </div>
  </div>
  <div class="row margem-fim">
    <div class="col">
      <div class="minimo-troca" *ngIf="cartao.plano.brindes.length && cartao.plano.brindes[0].valorEmPontos">
        Utilize seus créditos a partir de {{cartao.plano.brindes[0].valorEmPontos | currency: "BRL"}}  .
      </div>
    </div>

  </div>
</div>

<kendo-tabstrip class="nav-bordered mt-2"  >
  <kendo-tabstrip-tab [title]=" 'Extrato' " [selected]="true">
    <ng-template kendoTabContent>

      <app-extrato-pontos [cartao]="cartao"  ></app-extrato-pontos>

    </ng-template>
  </kendo-tabstrip-tab>

  <kendo-tabstrip-tab [title]=" 'Expirando' " *ngIf="cartao.plano.validade || cartao.plano.vencimento" >
    <ng-template kendoTabContent>

      <app-cartao-proximos-vencimentos [cartao]="cartao"  ></app-cartao-proximos-vencimentos>

    </ng-template>
  </kendo-tabstrip-tab>

</kendo-tabstrip>
