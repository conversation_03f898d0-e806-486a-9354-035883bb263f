import { Injectable } from '@angular/core';
import {ServerService} from "./ServerService";
import {HttpClient} from "@angular/common/http";


@Injectable({
  providedIn: 'root'
})
export class ModulosService extends ServerService {

  constructor(public   http: HttpClient) {
    super(http);
  }

  listeModulos(query: any = {}) {
    return this.obtenha('/modulos', query)
  }

  selecioneModulo(id: number) {
    return this.obtenha(`/modulos/${id}`, {});
  }

  insiraModulo(dados: any) {
    return this.facaPost('/modulos', dados);
  }

  atualizeModulo(modulo: any) {
    return this.facaPut(`/modulos/${modulo.id}`, modulo);
  }

  removaModulo(modulo: any) {
    return this.remova(`/modulos/${modulo.id}`, {});
  }

  // Método para ativar módulos (compatível com o sistema anterior)
  ativeModulosGratuitos(empresaId: any, modulos: any) {
    // Se empresaId é um objeto (empresa), usar o id
    const idEmpresa = typeof empresaId === 'object' ? empresaId.id : empresaId;

    return this.facaPost(`/modulos/${idEmpresa}/gratuitos/ativar`, { modulos: modulos });
  }

  desativarModuloNaEmpresa(empresaId: number, moduloId: number) {
    return this.facaPost(`/modulos/${empresaId}/desativar/${moduloId}`, {});
  }

  gerarFaturaAtivacaoModulos(empresaId: any, dadosFatura: any) {
    return this.facaPost(`/modulos/${empresaId}/fatura-ativacao`, dadosFatura);
  }

  sincronizeFaturaAtivacao(fatura: any) {
    return this.obtenha(`/modulos/faturas/${fatura.id}/status`, {});
  }

  // Novo método para buscar valores calculados dos módulos
  obtenhaModulosComValoresCalculados(empresaId: number) {
    return this.obtenha(`/modulos/empresa/${empresaId}/valores`, {});
  }

  atualizeConfigModulo(empresaId: number, dados: any){
    return this.facaPost(`/modulos/${empresaId}/config`, dados);
  }
}
