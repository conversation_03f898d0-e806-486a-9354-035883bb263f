import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ServerService } from './ServerService';

@Injectable({
  providedIn: 'root'
})
export class NfceService extends ServerService {

  constructor(http: HttpClient) {
    super(http);
  }

  liste(): Promise<any> {
    return this.obtenha('/nfce', {});
  }

  obtenhaDadosImpressao(id: number): Promise<any> {
    return this.obtenha(`/nfce/${id}/impressao`, {});
  }

  obtenhaProximasParaImpressao(): Promise<any> {
    return this.obtenha('/nfce/liste/nao-impressas', { });
  }

  marqueImpressa(nfce): Promise<any> {
    return this.facaPut('/nfce/'  + nfce.id + '/impressa', nfce);
  }

  enviarNota(nfceId: number): Promise<any> {
    return this.facaPost('/nfce/enviar', { id: nfceId });
  }

  cancelarNota(idNota: number, justificativa: string): Promise<any> {
    return this.facaPut('/nfce/cancelar', { idNota, justificativa });
  }

  obtenhaXmlDistribuicao(id: number): Promise<Blob> {
    return this.obtenhaArquivo(`/nfce/${id}/distribuicao`, {});
  }

  obtenha(url: string, params: any): Promise<any> {
    if (url.includes('/xml') || url.includes('/pdf')) {
      return this.obtenhaArquivo(url, params);
    }
    return super.obtenha(url, params);
  }

  reenvieNota(idNota: number): Promise<any> {
    return this.facaPost(`/nfce/reenviar/${idNota}`, {});
  }

  private obtenhaArquivo(url: string, params: any): Promise<Blob> {
    return new Promise((resolve, reject) => {
      this.http.get(url, { params, responseType: 'blob' })
        .subscribe(
          (response: Blob) => resolve(response),
          (error) => reject(error)
        );
    });
  }
}
