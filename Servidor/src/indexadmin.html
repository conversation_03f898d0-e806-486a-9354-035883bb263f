<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- Tell the browser to be responsive to screen width -->
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="">
  <meta name="author" content="">
  <!-- Favicon icon -->
  <link rel="icon" type="image/x-icon" href="favicon.ico">

  <title>Admin</title>
  <base href="/">
  <!-- Bootstrap Core CSS -->
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
  <!-- Custom CSS -->
  <link href="assets/admin/css/helper.css" rel="stylesheet">
  <link href="assets/admin/css/style.css" rel="stylesheet">

  <link href="https://use.fontawesome.com/releases/v5.0.8/css/all.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css?family=Lato" rel="stylesheet">
  <![endif]-->
  <style type="text/css">
    iframe.fb_iframe_widget_lift  {
      width: 100% !important;
    }
  </style>
</head>

<body class="fix-header fix-sidebar">
<script>
  (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
    (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
    m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

  ga('create', 'UA-21156662-2', 'none');
  //ga('send', 'pageview');
</script>

<!-- Preloader - style you can find in spinners.css -->
<div class="preloader">
  <svg class="circular" viewBox="25 25 50 50">
    <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="2" stroke-miterlimit="10" /> </svg>
</div>
<!-- Main wrapper  -->

<app-root></app-root>
<!-- End Wrapper -->
<!-- All Jquery -->
<script src="assets/admin/js/lib/jquery/jquery.min.js"></script>
<!-- Bootstrap tether Core JavaScript -->
<script src="assets/admin/js/lib/bootstrap/js/popper.min.js"></script>
<!-- slimscrollbar scrollbar JavaScript -->
<script src="assets/admin/js/jquery.slimscroll.js"></script>
<!--Menu sidebar -->
<script src="assets/admin/js/sidebarmenu.js"></script>
<!--stickey kit -->
<script src="assets/admin/js/lib/sticky-kit-master/dist/sticky-kit.min.js"></script>
<!--Custom JavaScript -->

<!-- Amchart -->


<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.21.0/moment.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.21.0/locale/pt-br.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>

<script src="assets/admin/js/lib/owl-carousel/owl.carousel.min.js"></script>
<script src="assets/admin/js/lib/owl-carousel/owl.carousel-init.js"></script>
<script src="assets/admin/js/scripts.js"></script>
<!-- scripit init-->

<script src="assets/admin/js/custom.min.js"></script>

<script type="text/javascript">
  function Deferred() {
    var self = this;
    this.promise = new Promise(function(resolve, reject) {
      self.reject = reject
      self.resolve = resolve
    })
  }
  window.fbLoaded = (new Deferred());

  window.fbAsyncInit = function() {
    FB.init({
      appId            : '442557376163532',
      status           : true,
      autoLogAppEvents : true,
      xfbml            : true,
      cookie           : true,
      version          : 'v2.0'
    });

    fbLoaded.resolve();
  };
</script>

<script type="text/javascript" src="inline.bundle.js"></script><script type="text/javascript" src="polyfills.bundle.js"></script><script type="text/javascript" src="styles.bundle.js"></script><script type="text/javascript" src="vendor.bundle.js"></script><script type="text/javascript" src="main.bundle.js"></script></body>

<script src="//cdnjs.cloudflare.com/ajax/libs/ckeditor/4.8.0/full/ckeditor.js"></script>

<script src="//connect.facebook.net/pt_BR/sdk.js"></script>

<script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function(event) {
    /*VANILLA JAVASCRIPT*/
    setTimeout(function() {
      var element = document.getElementById("page-loading");

      if( element != null && element.parentNode != null ) {
        element.parentNode.removeChild(element);
      }
    }, 300);
  });
</script>
</html>
