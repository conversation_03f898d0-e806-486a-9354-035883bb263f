{"version": 3, "file": "555.e4de9e6acdab0dc6.js", "mappings": "0RASYA,qDACAA,+FAaIA,wCAAMA,oHAASA,0CAAoB,GAEnCA,gDAAcA,4HAAaA,2DAAsC,EAAnDA,CAA4C,qGAAkBA,iDAA2B,GAEtEA,0EAFnBA,4FAA4C,gBAA5CA,CAA4C,4CAS5DA,qCACEA,gDACFA,2FAGAA,qCAAIA,+HAASA,uCAAiB,GAE5BA,uCAA2B,cACQA,sBAAkBA,6BAGrDA,qCACEA,sBAEFA,2BACAA,qCAAoC,WAC3BA,kDAAiCA,6BAG1CA,sCAAgC,YACvBA,yDAAkCA,2DAZRA,yEAIjCA,gFAIOA,wGAIAA,2HA0GCA,wCACEA,2BAACA,kCAAGA,oDAA2BA,uDAFjCA,oCACAA,0CAEEA,sCACEA,mDAA2BA,kCAAGA,sBAA8CA,0EAHrDA,uHAGOA,sIAGlCA,oCAAwD,WAEpDA,sBAAkEA,wEAAlEA,2JAMFA,sCACEA,gDAAsBA,kCAAIA,sBAAmDA,2BAAKA,yCACpFA,sEAD4BA,yIAMJA,qCAAsBA,2BAAEA,qDAD9CA,qCACIA,sBAAkBA,yCACtBA,8DADIA,0EAAyBA,qFAH/BA,sCACEA,uEAA6CA,kCAC7CA,0CAGFA,wEAH2BA,6HAR7BA,oCAEEA,wCAIAA,wCASFA,sEAboBA,2GAIAA,yKAapBA,0CAEQA,mHAASA,sEAA0C,IAAG,GAC5DA,sBACFA,wEADEA,oMAGFA,0CAEQA,mHAASA,4EAAgD,IAAG,GAClEA,qCACFA,2FAEAA,0CAAmDA,mHAASA,uCAAY,GACJA,oCAAWA,kDAUvFA,gCACAA,yDAA+BA,sDAqB/CA,iKA5OFA,uCAEEA,sCAAkB,UAAlBA,CAAkB,SAAlBA,CAAkB,SAAlBA,CAAkB,SAORA,sCACAA,sCACAA,uCAGAA,sBAAgDA,6BAGlDA,uCAA2D,YAA3DA,CAA2D,YAIrDA,mDAEAA,2CAOFA,6BAEFA,wCAEEA,wCAKAA,yCAmBFA,+BAIJA,uCAAuC,WAEnCA,mCACAA,yCACoBA,uBAAiBA,iCACrBA,6BAElBA,wCAAiE,YAAjEA,CAAiE,YAG/BA,oCAAUA,6BAI1CA,sCACEA,iCACAA,sCAAMA,qCAAWA,6BAEnBA,mCAGAA,sCAA+BA,kHAASA,mCAAQ,GAC9CA,iCACAA,sCAAMA,gCAAMA,mCASpBA,uCAA2D,WAA3DA,CAA2D,aAA3DA,CAA2D,YAA3DA,CAA2D,aAKjDA,qCACAA,wCAAgC,gBAE5BA,iCACFA,uCAOZA,wCAAsB,UAAtBA,CAAsB,cAGAA,mCAEJA,2BACdA,yCAEkBA,mCACJA,+BAKlBA,kCAUFA,2BAMAA,wCAAmD,YAAnDA,CAAmD,YAAnDA,CAAmD,eAAnDA,CAAmD,YAAnDA,CAAmD,YAAnDA,CAAmD,YAAnDA,CAAmD,YAAnDA,CAAmD,YAAnDA,CAAmD,YAAnDA,CAAmD,aAgB7BA,iCACAA,uCAAmBA,wCAAcA,2BAEjCA,yCAOAA,yCAMAA,yCAiBAA,2CAA8CA,kHAASA,sEAA2C,GAAGA,4BAAEA,2BAEvGA,6CAMAA,6CAMAA,6CAIFA,mCAMRA,0FAKAA,0CACFA,mCAURA,uCAGFA,6BAIFA,0CAEAA,0CAAsBA,sCAA4DA,2BAElFA,mCACFA,gFAxO0CA,yFACIA,0FAE5BA,8LAENA,+GAS2CA,wFAWrCA,6FAMmBA,wFA0BLA,gFA6FAA,sGAOAA,mGAMAA,yGAoBGA,sJAMAA,sJAMAA,yGAgCVA,qECjOlB,IAAMC,EAAoB,MAA3B,MAAOA,EAIXC,YAAoBC,EAAwBC,EACzBC,EACCC,GAFAC,cAAwBA,0BACzBA,0BACCA,yBAClBA,KAAKH,mBAAmBI,eAAeC,UAAYC,KAC5CA,IAELH,KAAKG,QAAUA,KAGjBH,KAAKD,kBAAkBK,SAASC,KAAOC,IACrCN,KAAKM,WAAaA,IAGpBN,KAAKF,mBAAmBS,+BAC1B,CAEAC,WACA,CAGAC,SAC0C,OAArCT,KAAKH,mBAAmBY,UACzBT,KAAKJ,OAAOc,SAAS,CAAC,qBAAsB,IAAKL,KAAM,OAE3D,CAEAM,oBAAoBC,GAClBZ,KAAKF,mBAAmBe,mBAC1B,CAEAC,aAAaC,GACXA,EAAOC,kBAAmBD,EAAOE,gBACnC,CAEAC,UAAUC,GACRnB,KAAKF,mBAAmBsB,yBACxBpB,KAAKJ,OAAOyB,cAAgBC,OAAO,iCAAiCH,EAAOI,QAAQC,MAAML,EAAOM,QAAS,CAAEC,MAAOP,GAEpH,CAEAQ,aACE3B,KAAKF,mBAAmBsB,yBACxBpB,KAAKJ,OAAOyB,cAAgB,qBAC9B,+CA/CW3B,GAAoBD,+LAApBC,EAAoBkC,ozGDXjCnC,wDAAeA,0JCWFC,CAAoB,uJCuDjCD,uCACEA,gCAAuCA,sBACvCA,0CAA6EA,kHAASA,iDAAsB,GAC1GA,wCAAyBA,6BAAOA,6BAElCA,qCAA2CA,kHAASA,sDAA8B,GAAEA,yEAJ7CA,mJAkB7BA,0CAAuDA,mHAASA,sCAAgB,GAAEA,gCAA9DA,uCAAgB,iBCzEzC,IAAMoC,EAA+B,MAAtC,MAAOA,EAqBXlC,YAAoBI,EAA8CH,EAC9CkC,EAA+BjC,GAD/BG,yBAA8CA,cAC9CA,aAA+BA,0BApBnDA,aAAU,GACVA,kBAAe,GACfA,eAAY,CAAE+B,MAAO,KACrB/B,iBAAkB,EAElBA,mBAAqB,CAAE+B,MAAO,EAAIC,aAAc,EAAGC,UAAW,EAAIC,MAAO,GACzElC,UAAY,EAOZA,aAAU,KAEVA,eAAiB,CACfmC,EAAG,GAML,CAEA3B,WACER,KAAKG,QAAUH,KAAKH,mBAAmBuC,UACvCpC,KAAKqC,iBACP,CAEQA,kBACN,GAAGrC,KAAKsC,WAAY,OAEpBtC,KAAKsC,YAAa,EAClB,IAAIC,EAASC,YACTxC,KAAKyC,OACPF,EAASC,aAGXxC,KAAKD,kBAAkB2C,aAAa,EAAG1C,KAAK2C,UAAUZ,MACpD,CAAEI,EAAGnC,KAAK4C,UAAUT,EAAGU,UAAU,GAAQN,GAAQlC,KAAOyC,IACxD9C,KAAK+C,QAAUD,EAASC,SAAW,GACnC/C,KAAKgD,kBAAoBF,EAASE,kBAClChD,KAAKiD,mBACLjD,KAAKkD,YAAYC,oBAAoBnD,KAAK+C,QAAQ,aAClD/C,KAAKsC,YAAa,IAEjBc,MAAO,KACRpD,KAAKsC,YAAa,GAEtB,CAEAW,mBACEjD,KAAKqD,cAActB,MAAQ/B,KAAK+C,QAAQO,OACxCtD,KAAKqD,cAAcrB,aAAehC,KAAK+C,QAAQQ,OAAQpC,GAAkC,wBAAlBA,EAAOqC,QAA4BF,OAC1GtD,KAAKqD,cAAcpB,UAAYjC,KAAK+C,QAAQQ,OAAQpC,GAAkC,sBAAlBA,EAAOqC,QAAgCF,OAC3GtD,KAAKqD,cAAcnB,MAASlC,KAAK+C,QAAQQ,OAAQpC,GAAkC,SAAlBA,EAAOqC,QAAmBF,MAC7F,CAGAG,SAAS1C,GACPf,KAAKqC,iBACP,CAEAqB,cAAcvC,GACZwC,OAAOC,KAAK,oBAAsBzC,EAAOM,KAC3C,CAGAoC,8BACU7D,KAAK8D,eACf,+CAxEWjC,GAA+BpC,gMAA/BoC,EAA+BD,m2CDZ5CnC,sCAAiB,UAAjBA,CAAiB,UAAjBA,CAAiB,UAAjBA,CAAiB,UAIHA,8BAAKA,6BAEbA,qCACEA,qDACFA,2BAEAA,sCACEA,8BACFA,+BAIJA,uCAA4B,WAA5BA,CAA4B,WAA5BA,CAA4B,WAGhBA,6CAAaA,6BAErBA,uCACEA,uDACFA,2BAEAA,uCACEA,8BACFA,+BAIJA,uCAA4B,WAA5BA,CAA4B,WAA5BA,CAA4B,WAGhBA,oCAAUA,6BAGlBA,uCACEA,uDACFA,2BAEAA,uCACEA,8BACFA,+BAIJA,uCAA4B,WAA5BA,CAA4B,WAA5BA,CAA4B,WAGhBA,+BAAKA,6BAEbA,uCACEA,uDACFA,2BAGAA,uCACEA,8BACFA,iCAONA,0CAUAA,wCAAkB,WAAlBA,CAAkB,YAAlBA,CAAkB,qBAAlBA,CAAkB,uBAKsBA,4EAAyB,iCAEdsE,aAAgB,GACzDtE,kDAGFA,+BAGJA,mCACFA,6BAEFA,mCAAkC,4BAAlCA,CAAkC,oBAtF1BA,qHAeAA,8HAgBAA,2HAeAA,uHAaFA,0EAekCA,yEAAyB,kBAclCA,qEAAmB,0BAAnBA,CAAmB,cAAnBA,CAAmB,umBCnFrCoC,CAA+B,2GC+C9BpC,0CAAuDA,mHAASA,sCAAgB,GAAEA,gCAA9DA,uCAAgB,iBC9C3C,IAAMuE,EAAyB,MAAhC,MAAOA,EAYXrE,YAAoBI,4BATpBC,eAAY,CAAE+B,MAAO,KACrB/B,iBAAkB,EAClBA,YAAc,CAAEiE,OAAQ,KAAMC,IAAK,MACnClE,mBAAqB,CAAEmE,KAAM,EAAGpC,MAAO,EAAGqC,WAAY,GAC7CpE,WAAO,EAChBA,eAAiB,CACfmC,EAAG,IAELnC,aAAU,IAGV,CACAQ,WACER,KAAKuC,OAAO0B,OAASI,SAASC,KAAI,EAAI,KAAKC,SAC3CvE,KAAKuC,OAAO2B,IAAM,IAAIM,KAEtBxE,KAAKqC,iBACP,CACAA,kBACE,GAAGrC,KAAKsC,WAAY,OAEpB,IAAImC,EAAWJ,OAAOrE,KAAKuC,OAAO0B,QAAQS,OAAO,YAC7CC,EAAQN,OAAOrE,KAAKuC,OAAO2B,KAAKQ,OAAO,YAE3C1E,KAAKsC,YAAa,EAElB,IAAIC,EAASC,YACTxC,KAAKyC,OACPF,EAASC,aAEXxC,KAAKD,kBAAkB2C,aAAa,EAAG1C,KAAK2C,UAAUZ,MACpD,CAAE6C,kBAAkB,EAAMH,SAAUA,EAAUE,MAAOA,EAAOxC,EAAGnC,KAAK4C,UAAUT,GAAKI,GAAQlC,KAAOyC,IAClG9C,KAAK6E,WAAW/B,EAASC,SAAW,IACpC/C,KAAKkD,YAAYC,oBAAoBnD,KAAK+C,QAAQ,SAClD/C,KAAKsC,YAAa,IACjBc,MAAO,KACRpD,KAAKsC,YAAa,GAEtB,CAEQuC,WAAW/B,GACjB9C,KAAK+C,QAAUD,GAAY,GAC3B9C,KAAKqD,cAAcc,KAAOnE,KAAK+C,QAAQO,OACvCtD,KAAKqD,cAAce,WAAapE,KAAK+C,QAAQ+B,OAAQ,CAACC,EAAO5D,IAAgB4D,EAAQ5D,EAAO6D,YAAa,GACzGhF,KAAKqD,cAActB,MAAS/B,KAAK+C,QAAQ+B,OAAO,CAACC,EAAO5D,IAAgB4D,EAAQ5D,EAAOY,MAAO,GAC9F/B,KAAKqD,cAActB,OAAY/B,KAAKqD,cAAce,UAEpD,CAEAX,SAAS1C,GACPf,KAAKqC,iBACP,+CAtDW2B,GAAyBvE,qFAAzBuE,EAAyBpC,0kCDbtCnC,sCAAiB,UAAjBA,CAAiB,UAAjBA,CAAiB,UAAjBA,CAAiB,UAIHA,8BAAKA,6BAEbA,qCACEA,qDACFA,2BAEAA,sCACEA,8BACFA,+BAIJA,uCAA4B,WAA5BA,CAA4B,WAA5BA,CAA4B,WAGhBA,gCAAMA,6BAEdA,uCACEA,yDACFA,2BAEAA,uCACEA,8BACFA,+BAKJA,uCAA4B,WAA5BA,CAA4B,WAA5BA,CAA4B,WAGhBA,gCAAMA,6BAEdA,uCACEA,yDACFA,2BAEAA,uCACEA,8BACFA,iCAMNA,uCAAiB,WAAjBA,CAAiB,WAAjBA,CAAiB,YAAjBA,CAAiB,qBAAjBA,CAAiB,uBAMyBA,4EAAyB,iCAEdsE,aAAgB,GACzDtE,kDAGFA,+BAGJA,wCAAiB,eACKA,sCAASA,2BAC7BA,iDAAoB,YAEhBA,oCACAA,oDAA0CA,8EAA2B,kCAAmBsE,mBAAiB,GAAEtE,6BAE7GA,uCAAO,cACyBA,iCAAIA,2BAClCA,oDAAwCA,2EAAwB,kCAAkBsE,mBAAiB,GAAGtE,uCASlHA,0DA3EQA,oHAeAA,6HAgBAA,kIAiBkCA,yEAAyB,kBAcbA,2EAIFA,wEASrBA,qEAAmB,0BAAnBA,CAAmB,cAAnBA,CAAmB,2kBCrErCuE,CAAyB,wHCa5BvE,0CAAuDA,mHAASA,sCAAgB,GAAEA,gCAA9DA,uCAAgB,iBCfvC,IAAMwF,GAA+B,MAAtC,MAAOA,EAaXtF,YAAoBI,4BATpBC,eAAY,CAAE+B,MAAO,KACrB/B,iBAAkB,EAClBA,YAAc,CAAEiE,OAAQ,KAAMC,IAAK,MACnClE,mBAAqB,CAAEmE,KAAM,EAAGpC,MAAO,EAAGqC,WAAY,GAC7CpE,WAAO,EAChBA,eAAiB,CACfmC,EAAG,GAIL,CAEA3B,WACER,KAAKuC,OAAO0B,OAASI,MAASC,KAAI,EAAK,KAAKC,SAC5CvE,KAAKuC,OAAO2B,IAAM,IAAIM,KACtBxE,KAAKqC,iBACP,CAEAA,kBACE,GAAGrC,KAAKsC,WAAY,OAEpB,IAAImC,EAAWJ,IAAOrE,KAAKuC,OAAO0B,QAAQS,OAAO,YAC7CC,EAAQN,IAAOrE,KAAKuC,OAAO2B,KAAKQ,OAAO,YAE3C1E,KAAKsC,YAAa,EAClB,IAAIC,EAASC,YACTxC,KAAKyC,OACPF,EAASC,aAEXxC,KAAKD,kBAAkBmF,uBAAuB,EAAGlF,KAAK2C,UAAUZ,MAC9D,KAAS0C,EAAUE,EAAOpC,EAAQvC,KAAK4C,UAAUT,GAAG9B,KAAOyC,IAC3D9C,KAAK6E,WAAW/B,EAASC,SAAW,IAEpC/C,KAAKkD,YAAYC,oBAAoBnD,KAAK+C,QAAQ,cAClD/C,KAAKsC,YAAa,IACjBc,MAAO,KACRpD,KAAKsC,YAAa,GAEtB,CAEQuC,WAAW/B,GACjB9C,KAAK+C,QAAUD,GAAY,GAC3B9C,KAAKqD,cAAcc,KAAOnE,KAAK+C,QAAQO,MACzC,CAEAG,SAAS1C,GACPf,KAAKqC,iBACP,+CAnDW4C,GAA+BxF,qFAA/BwF,EAA+BrD,o9BDX5CnC,sCAAiB,UAAjBA,CAAiB,UAAjBA,CAAiB,UAAjBA,CAAiB,UAIHA,8BAAKA,6BAEbA,qCACEA,qDACFA,2BAEAA,sCACEA,8BACFA,iCAMNA,uCAAiB,WAAjBA,CAAiB,qBAAjBA,CAAiB,sBAIqBA,4EAAyB,iCAEfsE,aAAgB,GACxDtE,kDAGFA,+BAGJA,uCAAiB,eACKA,sCAASA,2BAC7BA,iDAAoB,YAEhBA,oCACAA,oDAA0CA,8EAA2B,kCAAmBsE,mBAAiB,GAAEtE,6BAE7GA,uCAAO,cACyBA,iCAAIA,2BAClCA,oDAAwCA,2EAAwB,kCAAkBsE,mBAAiB,GAAGtE,mCAM9GA,0DAvCQA,mHAe8BA,yEAAyB,kBAcbA,2EAIFA,wEAMjBA,qEAAmB,0BAAnBA,CAAmB,cAAnBA,CAAmB,4jBCnCrCwF,CAA+B,2BCHlCxF,8EAMEA,wEAMAA,wDCZL,IAAM0F,GAAqB,MAA5B,MAAOA,EAGXxF,cAAgB,CAEhBa,WACA,+CANW2E,EAAqB,kDAArBA,EAAqBvD,+ODRlCnC,qCAAyBA,+BAAiCA,iCAAOA,2BAEjEA,sCAAkB,UAAlBA,CAAkB,qBAAlBA,CAAkB,0BAKVA,iDAGFA,2BAEAA,iDACEA,iDAGFA,2BAEAA,kDACEA,kDAGFA,wCAhBoBA,qEAAqB,eAMzBA,uEAMAA,+GCVT0F,CAAqB,qBCA3B,IAAMC,GAA4B,MAAnC,MAAOA,EAEXzF,YAAoB0F,yBAClBrF,KAAKsF,UAAYtF,KAAKqF,eAAeE,SAASC,OAAOF,SACvD,CAEA9E,WACA,+CAPW4E,GAA4B3F,sFAA5B2F,EAA4BxD,qJCNzCnC,wDAAqCA,4CAAuB,gDDM/C2F,CAA4B,KEDzC,MAAMK,GAAkB,CACtB,CACEC,KAAM,GAAIC,UAAWjG,EACrBkG,QAAS,CAAEC,cAAMC,GACjBC,SAAU,CACR,CAACL,KAAM,QAASM,UAAW,OAAQL,UAAWR,IAC9C,CAACO,KAAM,oCAAqCM,UAAW,OAAQL,UAAWP,OASzE,IAAMa,GAAuB,MAA9B,MAAOA,kDAAuB,iDAAvBA,uDAHDC,cAAsBT,IACtBS,QAECD,CAAuB,wDCuB7B,IAAME,GAAiB,MAAxB,MAAOA,kDAAiB,iDAAjBA,uDATLC,eACAC,KACAC,MACAC,KAAcC,KAAqBC,KAAeC,KAAcC,KAChEC,KAAeC,KAAgBC,MAAoBC,KAAmBC,IACtEC,KACAhB,GAAyBiB,KAAcC,QAGlChB,CAAiB", "names": ["i0", "PainelGrupoComponent", "constructor", "router", "autorizacaoService", "monitoradorPedidos", "grupolojasService", "this", "usuarioLogado$", "subscribe", "usuario", "logado", "then", "grupoLojas", "inicieMonitoramentoGrupoLojas", "ngOnInit", "logout", "navigate", "alterouStatus<PERSON><PERSON><PERSON>", "event", "toogleTocarIframe", "clic<PERSON><PERSON><PERSON><PERSON>", "$event", "stopPropagation", "preventDefault", "verPedido", "pedido", "fecheEZerePedidosNovos", "navigateByUrl", "String", "empresa", "id", "guid", "state", "verPedidos", "selectors", "AcompanharPedidosGrupoComponent", "route", "total", "emPreparacao", "emEntrega", "novos", "q", "getUser", "carreguePedidos", "carregando", "filtro", "EnumFiltroDePedidos", "mesa", "listePedidos", "paginacao", "objFiltro", "<PERSON><PERSON><PERSON>", "resposta", "pedidos", "ultimaAtualizacao", "setResumoPedidos", "gridPedidos", "atualizeGridPedidos", "catch", "resumoPedidos", "length", "filter", "status", "onFilter", "imprimaPedido", "window", "open", "fecheMensagemSucesso", "mensagemSucesso", "ctx", "PedidosPagoGrupoComponent", "inicio", "fim", "qtde", "totalTaxas", "moment", "add", "toDate", "Date", "dtInicio", "format", "dtFim", "apenasEncerrados", "setPedidos", "reduce", "valor", "taxaEntrega", "PedidosCanceladosGrupoComponent", "listePedidosCancelados", "PedidosGrupoComponent", "PedidoGrupoDetalhesComponent", "activatedRoute", "idEmpresa", "snapshot", "params", "routes", "path", "component", "resolve", "user", "UserResolver", "children", "pathMatch", "PainelRedeRoutingModule", "RouterModule", "PainelGrupoModule", "CommonModule", "FormsModule", "IntlModule", "InputsModule", "MaskedTextBoxModule", "ButtonsModule", "SwitchModule", "DateInputsModule", "TextBoxModule", "CheckBoxModule", "CurrencyMaskModule", "RadioButtonModule", "DropDownButtonModule", "CompartilhadoModule", "LayoutModule", "ApplicationPipesModule"], "sourceRoot": "webpack:///", "sources": ["./src/app/admin-grupo/painel-grupo/painel-grupo.component.html", "./src/app/admin-grupo/painel-grupo/painel-grupo.component.ts", "./src/app/admin-grupo/acompanhar-pedidos-grupo/acompanhar-pedidos-grupo.component.html", "./src/app/admin-grupo/acompanhar-pedidos-grupo/acompanhar-pedidos-grupo.component.ts", "./src/app/admin-grupo/pedidos-pago-grupo/pedidos-pago-grupo.component.html", "./src/app/admin-grupo/pedidos-pago-grupo/pedidos-pago-grupo.component.ts", "./src/app/admin-grupo/pedidos-cancelados-grupo/pedidos-cancelados-grupo.component.html", "./src/app/admin-grupo/pedidos-cancelados-grupo/pedidos-cancelados-grupo.component.ts", "./src/app/admin-grupo/pedidos-grupo/pedidos-grupo.component.html", "./src/app/admin-grupo/pedidos-grupo/pedidos-grupo.component.ts", "./src/app/admin-grupo/pedido-grupo-detalhes/pedido-grupo-detalhes.component.ts", "./src/app/admin-grupo/pedido-grupo-detalhes/pedido-grupo-detalhes.component.html", "./src/app/admin-grupo/painel-grupo-routing.module.ts", "./src/app/admin-grupo/painel-grupo.module.ts"], "sourcesContent": ["<ng-container *ngIf=\"usuario != null\">\n  <!-- Begin page -->\n  <div id=\"wrapper\">\n    <!-- Topbar Start -->\n    <div class=\"navbar-custom\" >\n      <ul class=\"list-unstyled topnav-menu float-right mb-0\">\n        <li class=\"dropdown notification-list topbar-dropdown\">\n          <a class=\"nav-link dropdown-toggle waves-effect waves-light\" data-toggle=\"dropdown\"\n             href=\"#\" role=\"button\" aria-haspopup=\"false\" aria-expanded=\"false\" >\n            <i class=\"fe-bell noti-icon\" *ngIf=\"monitoradorPedidos.tocarAlerta\"></i>\n            <i class=\"fe-bell-off noti-icon\" *ngIf=\"!monitoradorPedidos.tocarAlerta\"></i>\n            <span class=\"badge  rounded-circle noti-icon-badge\"\n                  [ngClass]=\"{'badge-danger': monitoradorPedidos.totalPedidosMonitorados() >  0,\n                          'badge-light': !monitoradorPedidos.totalPedidosMonitorados() }\">\n            {{monitoradorPedidos.totalPedidosMonitorados()}}</span>\n          </a>\n\n          <div class=\"dropdown-menu dropdown-menu-right dropdown-lg\">\n\n            <div class=\"dropdown-item noti-title\">\n              <h5 class=\"m-0\">\n                Alerta de novos pedidos\n\n                <span (click)=\"clicouAlerta($event)\"  *ngIf=\"monitoradorPedidos.inIframe()\" class=\"ml-1\">\n\n                <kendo-switch [(ngModel)]=\"monitoradorPedidos.tocarAlerta\" (ngModelChange)=\"alterouStatusAlerta($event)\"\n                              [onLabel]=\"'Sim'\"\n                              [offLabel]=\"'Não'\"></kendo-switch>\n              </span>\n\n              </h5>\n            </div>\n            <div class=\"slimscroll noti-scroll\">\n\n              <a *ngIf=\"!monitoradorPedidos.pedidos.length\" class=\"dropdown-item notify-item text-muted text-center \" >\n                Nenhum pedido recente\n              </a>\n\n              <!-- item-->\n              <a  (click)=\"verPedido(pedido)\" class=\"dropdown-item notify-item cpointer \"\n                  *ngFor=\"let pedido of monitoradorPedidos.pedidos\" style=\"position: relative\">\n                <div class=\"notify-icon\"  >\n                  <small class=\"text-blue d-block\">#{{pedido.codigo}}</small>\n\n                </div>\n                <p class=\"notify-details\">\n                  {{pedido.cliente.nome}}\n\n                </p>\n                <p class=\"text-muted mb-0 user-msg\">\n                  <small>{{pedido.cliente.telefone | tel}}</small>\n                </p>\n\n                <p class=\" mb-0 user-msg valor\">\n                  <small>{{pedido.total | currency: \"BRL\"}}</small>\n                </p>\n\n              </a>\n            </div>\n\n          </div>\n        </li>\n        <li class=\"dropdown notification-list\">\n          <a class=\"nav-link dropdown-toggle nav-user mr-0 waves-effect waves-light\" data-toggle=\"dropdown\" href=\"#\" role=\"button\" aria-haspopup=\"false\" aria-expanded=\"false\">\n            <img style=\"width:32px; height: 32px;\" src=\"/assets/fidelidade/icones/user-icon.png\" alt=\"user-image\">\n            <span class=\"pro-user-name ml-1\">\n                                {{usuario.nome}} <i class=\"mdi mdi-chevron-down\"></i>\n                            </span>\n          </a>\n          <div class=\"dropdown-menu dropdown-menu-right profile-dropdown \">\n            <!-- item-->\n            <div class=\"dropdown-header noti-title\">\n              <h6 class=\"text-overflow m-0\">Bem vindo!</h6>\n            </div>\n\n            <!-- item-->\n            <a href=\"javascript:void(0);\" class=\"dropdown-item notify-item\">\n              <i class=\"fe-user\"></i>\n              <span>Minha Conta</span>\n            </a>\n            <div class=\"dropdown-divider\"></div>\n\n            <!-- item-->\n            <a href=\"javascript:void(0);\"  (click)=\"logout()\" class=\"dropdown-item notify-item\">\n              <i class=\"fe-log-out\"></i>\n              <span>Logout</span>\n            </a>\n\n          </div>\n        </li>\n\n      </ul>\n\n      <!-- LOGO -->\n      <ul class=\"list-unstyled topnav-menu topnav-menu-left m-0\">\n        <li class=\"d-none\">\n          <form class=\"app-search\">\n            <div class=\"app-search-box\">\n              <div class=\"input-group\">\n                <input type=\"text\" class=\"form-control\" placeholder=\"Buscar...\">\n                <div class=\"input-group-append\">\n                  <button class=\"btn\" type=\"submit\">\n                    <i class=\"fe-search\"></i>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </form>\n        </li>\n      </ul>\n      <div class=\"logo-box\">\n        <a  routerLink=\"/admin-grupo/index\" class=\"logo text-center\">\n                        <span class=\"logo-lg\">\n                            <img src=\"/assets/fidelidade/promokit-horizontal-branco.png\" alt=\"\" height=\"36\">\n                          <!-- <span class=\"logo-lg-text-light\">UBold</span> -->\n                        </span>\n          <span class=\"logo-sm\">\n                            <!-- <span class=\"logo-sm-text-dark\">U</span> -->\n                            <img src=\"/assets/fidelidade/zapkit-logo.png\" alt=\"\" height=\"36\">\n                        </span>\n        </a>\n      </div>\n\n\n      <ul class=\"list-unstyled topnav-menu topnav-menu-left m-0\">\n        <!--\n        <li>\n          <button class=\"button-menu-mobile waves-effect waves-light\">\n            <i class=\"fe-menu\"></i>\n          </button>\n        </li>\n        -->\n\n      </ul>\n    </div>\n\n    <!-- Left Sidebar End -->\n    <!-- ============================================================== -->\n    <!-- Start Page Content here -->\n    <!-- ============================================================== -->\n    <div class=\"content-page\" style=\"margin-left: 0px\">\n      <div class=\"content\">\n\n        <!-- Start Content-->\n        <div class=\"container-fluid\">\n\n          <!-- start page title -->\n          <div class=\"row\" #appendTo>\n            <div class=\"col-12 conteudo-componente\">\n              <div class=\"page-title-box\">\n\n                <div id=\"alertaNovo\" class=\"modal fade\" tabindex=\"-1\" role=\"dialog\"   aria-modal=\"true\"  data-keyboard=\"false\" >\n                  <div class=\"modal-dialog \">\n                    <div class=\"modal-content\">\n                      <div class=\"modal-body p-4\">\n                        <div class=\"text-center\">\n                          <i class=\"dripicons-information h1 text-info\"></i>\n                          <h2 class=\"mt-2\"  >Alerta Pedidos</h2>\n\n                          <div *ngIf=\"monitoradorPedidos.novosPedidos.length===1\">\n                          <span class=\"text-danger\" *ngIf=\"monitoradorPedidos.novosPedidos[0].aguardandoPagamentoOnline\" >\n                            *<b>Aguardando pagamento online</b></span>\n                            <h4 class=\"mt-3\" >\n                              Voce tem um novo pedido:   <b>#{{monitoradorPedidos.novosPedidos[0].codigo}}</b></h4>\n                          </div>\n\n                          <div *ngIf=\"monitoradorPedidos.novosPedidos.length > 1\">\n                            <h4 class=\"mt-3\" >\n                              Voce tem {{monitoradorPedidos.novosPedidos.length}} novos pedidos </h4>\n\n                          </div>\n\n                          <div *ngIf=\"monitoradorPedidos.pedidosConfirmados.length > 0\">\n\n                            <h4 class=\"mt-3\" *ngIf=\"monitoradorPedidos.pedidosConfirmados.length === 1\" >\n                              O Pagamento do pedido <b> {{monitoradorPedidos.pedidosConfirmados[0].codigo}}</b> foi confirmado\n                            </h4>\n\n                            <h4 class=\"mt-3\" *ngIf=\"monitoradorPedidos.pedidosConfirmados.length >  1\" >\n                              Os Pagamentos dos pedidos foram confirmados: <b>\n                              <span *ngFor=\"let pedido of monitoradorPedidos.pedidosConfirmados; let ultimo = last;\">\n                                  {{pedido.codigo}} <span *ngIf=\"!ultimo\">, </span>\n                              </span>\n                            </b>\n                            </h4>\n\n\n                          </div>\n\n                          <button type=\"button\" class=\"btn btn-light \"  (click)=\"monitoradorPedidos.fecheEZerePedidosNovos()\" >Ok</button>\n\n                          <button type=\"button\" class=\"btn btn-info ml-2  \"\n                                  *ngIf=\"monitoradorPedidos.novosPedidos.length === 1 && !monitoradorPedidos.pedidosConfirmados.length\"\n                                  (click)=\"verPedido(monitoradorPedidos.novosPedidos[0])\" >\n                            {{!monitoradorPedidos.novosPedidos[0].aceito ? 'Aprovar' : 'Ver'}}  Pedido\n                          </button>\n\n                          <button type=\"button\" class=\"btn btn-info ml-2  \"\n                                  *ngIf=\"!monitoradorPedidos.novosPedidos.length && monitoradorPedidos.pedidosConfirmados.length === 1\"\n                                  (click)=\"verPedido(monitoradorPedidos.pedidosConfirmados[0])\" >\n                            Ver Pedido\n                          </button>\n\n                          <button type=\"button\" class=\"btn btn-info ml-2  \"  (click)=\"verPedidos()\"\n                                  *ngIf=\"monitoradorPedidos.totalPedidosMonitorados() > 1  \" >Ver Pedidos</button>\n\n\n                        </div>\n                      </div>\n                    </div><!-- /.modal-content -->\n                  </div><!-- /.modal-dialog -->\n                </div>\n\n                <ng-template #template>\n                  <i class=\"fe-printer fa-lg mr-1\"></i>\n                  Conectando com a impressora... <i class=\"k-icon k-i-loading\"></i>\n                </ng-template>\n\n                <router-outlet></router-outlet>\n              </div>\n            </div>\n          </div>\n          <!-- end page title -->\n\n        </div> <!-- container -->\n\n      </div> <!-- content -->\n\n      <!-- Footer Start -->\n      <app-footer></app-footer>\n      <!-- end Footer -->\n\n    </div>\n  </div>\n\n\n  <div class=\"bloqueio\" *ngIf=\"bloqueado\"> </div>\n\n  <audio id=\"beep\"  >   <source src=\"/Servidor/src/assets/audio/beet-pedido.mp3\" /> </audio>\n\n  <div kendoWindowContainer></div>\n</ng-container>\n", "import { Component, OnInit } from '@angular/core';\nimport {Router} from \"@angular/router\";\nimport {AutorizacaoService} from \"../../services/autorizacao.service\";\nimport {GrupolojasService} from \"../../superadmin/services/grupolojas.service\";\nimport {MonitoradorPedidos} from \"../../fidelidade/MonitoradorPedidos\";\n\n@Component({\n  selector: 'app-painel-grupo',\n  templateUrl: './painel-grupo.component.html',\n  styleUrls: ['./painel-grupo.component.scss']\n})\nexport class PainelGrupoComponent implements OnInit {\n  usuario: any\n  grupoLojas: any;\n  bloqueado: boolean;\n  constructor(private router: Router, private autorizacaoService: AutorizacaoService ,\n              public monitoradorPedidos: MonitoradorPedidos,\n              private grupolojasService: GrupolojasService) {\n    this.autorizacaoService.usuarioLogado$.subscribe( (usuario) => {\n      if( !usuario ) return;\n\n      this.usuario = usuario;\n    });\n\n    this.grupolojasService.logado().then( (grupoLojas) => {\n      this.grupoLojas = grupoLojas;\n    })\n\n    this.monitoradorPedidos.inicieMonitoramentoGrupoLojas();\n  }\n\n  ngOnInit(): void {\n  }\n\n\n  logout() {\n    if(this.autorizacaoService.logout() !== null)  {\n      this.router.navigate(['/admin-rede/login'], { }).then( () => {});\n    }\n  }\n\n  alterouStatusAlerta(event: any) {\n    this.monitoradorPedidos.toogleTocarIframe();\n  }\n\n  clicouAlerta($event: MouseEvent) {\n    $event.stopPropagation(); $event.preventDefault();\n  }\n\n  verPedido(pedido: any) {\n    this.monitoradorPedidos.fecheEZerePedidosNovos();\n    this.router.navigateByUrl(  String(`/admin-grupo/pedidos/detalhes/${pedido.empresa.id}/${pedido.guid}`), { state: pedido });\n\n  }\n\n  verPedidos(){\n    this.monitoradorPedidos.fecheEZerePedidosNovos();\n    this.router.navigateByUrl(  \"/admin-grupo/index\");\n  }\n}\n", "<div class=\"row\">\n  <div class=\"col-6 col-lg-3\">\n    <div class=\"card-box ribbon-box  com-borda\">\n      <div class=\" ribbon ribbon-dark float-left\">\n        <span>Total</span>\n      </div>\n      <h5 class=\"text-info float-right mt-0 total\">\n        {{resumoPedidos.total | number}}\n      </h5>\n\n      <div class=\"ribbon-content\">\n        <p></p>\n      </div>\n\n    </div>\n  </div>\n  <div class=\"col-6 col-lg-3\">\n    <div class=\"card-box ribbon-box \">\n      <div class=\" ribbon ribbon-warning float-left\">\n        <span>Em Preparação</span>\n      </div>\n      <div class=\"text-info float-right mt-0 total\">\n        {{resumoPedidos.emPreparacao | number}}\n      </div>\n\n      <div class=\"ribbon-content\">\n        <p></p>\n      </div>\n\n    </div>\n  </div>\n  <div class=\"col-6 col-lg-3\">\n    <div class=\"card-box ribbon-box  \">\n      <div class=\" ribbon ribbon-success float-left\">\n        <span>Em Entrega</span>\n      </div>\n\n      <div class=\"text-info float-right mt-0 total\">\n        {{resumoPedidos.emEntrega | number}}\n      </div>\n\n      <div class=\"ribbon-content\">\n        <p></p>\n      </div>\n\n    </div>\n  </div>\n  <div class=\"col-6 col-lg-3\">\n    <div class=\"card-box ribbon-box  \">\n      <div class=\" ribbon ribbon-info float-left\">\n        <span>Novos</span>\n      </div>\n      <div class=\"text-info float-right mt-0 total\">\n        {{resumoPedidos.novos | number}}\n      </div>\n\n\n      <div class=\"ribbon-content\">\n        <p></p>\n      </div>\n\n    </div>\n  </div>\n</div>\n\n\n<div *ngIf=\"mensagemSucesso\" class=\"alert alert-success alert-dismissible fade show mb-2\" role=\"alert\">\n  <i class=\"mdi mdi-check-all mr-2\"></i> {{mensagemSucesso}}\n  <button type=\"button\" class=\"close\" data-dismiss=\"alert\" aria-label=\"Fechar\" (click)=\"fecheMensagemSucesso()\">\n    <span aria-hidden=\"true\">&times;</span>\n  </button>\n  <i class=\"fa fa-print fa-lg ml-1 cpointer\" (click)=\"imprimaPedido(this.novoPedido)\"></i>\n</div>\n\n\n\n<div class=\"mb-2\">\n  <div class=\"row\">\n    <div class=\"col-auto\">\n      <kendo-formfield>\n        <kendo-textbox placeholder=\"Busque por nome ou telefone do cliente ou por código\"\n                       name=\"txtFiltro\" [(ngModel)]=\"objFiltro.q\"\n                       [clearButton]=\"true\" style=\"width: 500px\"\n                       appAutoFocus (valueChange)=\"onFilter($event)\">\n          <ng-template kendoTextBoxSuffixTemplate>\n            <button kendoButton [look]=\"'clear'\" [icon]=\"'search'\" (click)=\"onFilter($event)\"></button>\n          </ng-template>\n        </kendo-textbox>\n      </kendo-formfield>\n    </div>\n    <div class=\"col-auto\"> </div>\n  </div>\n</div>\n<div class=\"clearfix mt-2\"> </div>\n\n<app-grid-pedidos #gridPedidos [pedidos]=\"pedidos\" [carregando]=\"carregando\" [mesa]=\"mesa\" [modoGrupoLojas]=\"true\"></app-grid-pedidos>\n\n\n<div class=\"clearfix mt-4\"> </div>\n\n", "import {Component, Input, OnInit, ViewChild} from '@angular/core';\nimport {GridPedidosComponent} from \"../../pedidos/grid-pedidos/grid-pedidos.component\";\nimport {GrupolojasService} from \"../../superadmin/services/grupolojas.service\";\nimport {ActivatedRoute, Router} from \"@angular/router\";\nimport {AutorizacaoService} from \"../../services/autorizacao.service\";\nimport {EnumFiltroDePedidos} from \"../../services/pedidos.service\";\n\n@Component({\n  selector: 'app-acompanhar-pedidos-grupo',\n  templateUrl: './acompanhar-pedidos-grupo.component.html',\n  styleUrls: ['./acompanhar-pedidos-grupo.component.scss']\n})\nexport class AcompanharPedidosGrupoComponent implements OnInit {\n  @ViewChild('gridPedidos', { static: true}) gridPedidos: GridPedidosComponent;\n  pedidos = [];\n  novosPedidos = [];\n  paginacao = { total: 1000};\n  carregando: any = false;\n\n  resumoPedidos: any = { total: 0 , emPreparacao: 0, emEntrega: 0 , novos: 0}\n  page: any = 0;\n  pedidoAlterar: any;\n  timerPedidos;\n\n  mensagemSucesso: string;\n  novoPedido: any;\n  ultimaAtualizacao: any;\n  usuario = null;\n  @Input() mesa: boolean;\n  objFiltro: any = {\n    q: ''\n  };\n\n  constructor(private grupolojasService: GrupolojasService, private router: Router,\n              private route: ActivatedRoute, private autorizacaoService: AutorizacaoService) {\n\n  }\n\n  ngOnInit(): void {\n    this.usuario = this.autorizacaoService.getUser();\n    this.carreguePedidos();\n  }\n\n  private carreguePedidos() {\n    if(this.carregando) return;\n\n    this.carregando = true;\n    let filtro = EnumFiltroDePedidos.pedidos;\n    if( this.mesa ) {\n      filtro = EnumFiltroDePedidos.comanda;\n    }\n\n    this.grupolojasService.listePedidos(0, this.paginacao.total,\n      { q: this.objFiltro.q, emAberto: true }, filtro).then( (resposta) => {\n      this.pedidos = resposta.pedidos || [];\n      this.ultimaAtualizacao = resposta.ultimaAtualizacao;\n      this.setResumoPedidos();\n      this.gridPedidos.atualizeGridPedidos(this.pedidos,'em-aberto');\n      this.carregando = false;\n\n    }).catch( () => {\n      this.carregando = false;\n    });\n  }\n\n  setResumoPedidos(){\n    this.resumoPedidos.total = this.pedidos.length;\n    this.resumoPedidos.emPreparacao = this.pedidos.filter((pedido: any) => pedido.status === 'Em preparação').length;\n    this.resumoPedidos.emEntrega = this.pedidos.filter((pedido: any) => pedido.status === 'Saiu para entrega').length;\n    this.resumoPedidos.novos =  this.pedidos.filter((pedido: any) => pedido.status === 'Novo').length;\n  }\n\n\n  onFilter($event: Event) {\n    this.carreguePedidos();\n  }\n\n  imprimaPedido(pedido: any) {\n    window.open('/imprimir/pedido/' + pedido.guid);\n  }\n\n\n  fecheMensagemSucesso() {\n    delete  this.mensagemSucesso;\n  }\n\n}\n", "<div class=\"row\">\n  <div class=\"col-6 col-lg-3\">\n    <div class=\"card-box ribbon-box  com-borda\">\n      <div class=\" ribbon ribbon-dark float-left\">\n        <span>Total</span>\n      </div>\n      <h5 class=\"text-info float-right mt-0 total\">\n        {{resumoPedidos.qtde | number}}\n      </h5>\n\n      <div class=\"ribbon-content\">\n        <p></p>\n      </div>\n\n    </div>\n  </div>\n  <div class=\"col-6 col-lg-3\">\n    <div class=\"card-box ribbon-box \">\n      <div class=\" ribbon ribbon-success float-left\">\n        <span>Valor </span>\n      </div>\n      <div class=\"text-info float-right mt-0 total\">\n        {{resumoPedidos.total | currency: \"BRL\"}}\n      </div>\n\n      <div class=\"ribbon-content\">\n        <p></p>\n      </div>\n\n    </div>\n  </div>\n\n  <div class=\"col-6 col-lg-3\">\n    <div class=\"card-box ribbon-box \">\n      <div class=\" ribbon ribbon-warning float-left\">\n        <span>Taxas </span>\n      </div>\n      <div class=\"text-info float-right mt-0 total\">\n        {{resumoPedidos.totalTaxas | currency: \"BRL\"}}\n      </div>\n\n      <div class=\"ribbon-content\">\n        <p></p>\n      </div>\n\n    </div>\n  </div>\n</div>\n\n<div class=\"row\">\n  <div class=\"mb-2\">\n    <div class=\"row\">\n      <div class=\"col-auto\">\n        <kendo-formfield>\n          <kendo-textbox placeholder=\"Busque por nome ou telefone do cliente ou por código\"\n                         name=\"txtFiltro\" [(ngModel)]=\"objFiltro.q\"\n                         [clearButton]=\"true\" style=\"width: 500px\"\n                         appAutoFocus (valueChange)=\"onFilter($event)\">\n            <ng-template kendoTextBoxSuffixTemplate>\n              <button kendoButton [look]=\"'clear'\" [icon]=\"'search'\" (click)=\"onFilter($event)\"></button>\n            </ng-template>\n          </kendo-textbox>\n        </kendo-formfield>\n      </div>\n      <div class=\"col\">\n        <label class=\"mr-3\">Período: </label>\n        <kendo-daterange   >\n          <label>\n            <span class=\"label\"></span>\n            <kendo-dateinput kendoDateRangeStartInput [(ngModel)]=\"filtro.inicio\"  (ngModelChange)=\"carreguePedidos()\"></kendo-dateinput>\n          </label>\n          <label>\n            <span class=\"label ml-2 mr-2\">Até </span>\n            <kendo-dateinput kendoDateRangeEndInput [(ngModel)]=\"filtro.fim\" (ngModelChange)=\"carreguePedidos()\" ></kendo-dateinput>\n          </label>\n        </kendo-daterange>\n      </div>\n    </div>\n  </div>\n</div>\n\n\n<app-grid-pedidos #gridPedidos [pedidos]=\"pedidos\" [carregando]=\"carregando\" [mesa]=\"mesa\" [modoGrupoLojas]=\"true\"></app-grid-pedidos>\n", "import {Component, Input, OnInit, ViewChild} from '@angular/core';\nimport {GridPedidosComponent} from \"../../pedidos/grid-pedidos/grid-pedidos.component\";\nimport {EnumFiltroDePedidos} from \"../../services/pedidos.service\";\nimport {GrupolojasService} from \"../../superadmin/services/grupolojas.service\";\n\ndeclare var moment;\n\n\n@Component({\n  selector: 'app-pedidos-pago-grupo',\n  templateUrl: './pedidos-pago-grupo.component.html',\n  styleUrls: ['./pedidos-pago-grupo.component.scss']\n})\nexport class PedidosPagoGrupoComponent implements OnInit {\n  @ViewChild('gridPedidos', { static: false}) gridPedidos: GridPedidosComponent;\n  pedidos: [];\n  paginacao = { total: 1000};\n  carregando: any = false;\n  filtro: any = { inicio: null, fim: null};\n  resumoPedidos: any = { qtde: 0, total: 0, totalTaxas: 0 };\n  @Input() mesa = false;\n  objFiltro: any = {\n    q: ''\n  };\n  usuario = null;\n  constructor(private grupolojasService: GrupolojasService) {\n\n  }\n  ngOnInit(): void {\n    this.filtro.inicio = moment().add(-7, 'd').toDate();\n    this.filtro.fim = new Date();\n\n    this.carreguePedidos();\n  }\n  carreguePedidos(){\n    if(this.carregando) return;\n\n    let dtInicio = moment(this.filtro.inicio).format('YYYYMMDD');\n    let dtFim = moment(this.filtro.fim).format('YYYYMMDD');\n\n    this.carregando = true;\n\n    let filtro = EnumFiltroDePedidos.pedidos;\n    if( this.mesa )\n      filtro = EnumFiltroDePedidos.comanda;\n\n    this.grupolojasService.listePedidos(0, this.paginacao.total,\n      { apenasEncerrados: true, dtInicio: dtInicio, dtFim: dtFim, q: this.objFiltro.q }, filtro).then( (resposta) => {\n      this.setPedidos(resposta.pedidos || []);\n      this.gridPedidos.atualizeGridPedidos(this.pedidos,'pagos');\n      this.carregando = false;\n    }).catch( () => {\n      this.carregando = false;\n    });\n  }\n\n  private setPedidos(resposta: any) {\n    this.pedidos = resposta || [];\n    this.resumoPedidos.qtde = this.pedidos.length;\n    this.resumoPedidos.totalTaxas = this.pedidos.reduce( (valor, pedido: any) => valor + pedido.taxaEntrega, 0);\n    this.resumoPedidos.total =  this.pedidos.reduce((valor, pedido: any) => valor + pedido.total, 0);\n    this.resumoPedidos.total -=    this.resumoPedidos.totalTaxas;\n\n  }\n\n  onFilter($event: any) {\n    this.carreguePedidos();\n  }\n\n\n}\n", "<div class=\"row\">\n  <div class=\"col-6 col-lg-3\">\n    <div class=\"card-box ribbon-box  com-borda\">\n      <div class=\" ribbon ribbon-dark float-left\">\n        <span>Total</span>\n      </div>\n      <h5 class=\"text-info float-right mt-0 total\">\n        {{resumoPedidos.qtde | number}}\n      </h5>\n\n      <div class=\"ribbon-content\">\n        <p></p>\n      </div>\n\n    </div>\n  </div>\n</div>\n\n<div class=\"row\">\n  <div class=\"col-auto\">\n    <kendo-formfield>\n      <kendo-textbox placeholder=\"Busque por nome ou telefone do cliente ou por código\"\n                     name=\"txtFiltro\" [(ngModel)]=\"objFiltro.q\"\n                     [clearButton]=\"true\" style=\"width: 500px\"\n                     appAutoFocus (valueChange)=onFilter($event)>\n        <ng-template kendoTextBoxSuffixTemplate>\n          <button kendoButton [look]=\"'clear'\" [icon]=\"'search'\" (click)=\"onFilter($event)\"></button>\n        </ng-template>\n      </kendo-textbox>\n    </kendo-formfield>\n  </div>\n  <div class=\"col\">\n    <label class=\"mr-3\">Período: </label>\n    <kendo-daterange   >\n      <label>\n        <span class=\"label\"></span>\n        <kendo-dateinput kendoDateRangeStartInput [(ngModel)]=\"filtro.inicio\"  (ngModelChange)=\"carreguePedidos()\"></kendo-dateinput>\n      </label>\n      <label>\n        <span class=\"label ml-2 mr-2\">Até </span>\n        <kendo-dateinput kendoDateRangeEndInput [(ngModel)]=\"filtro.fim\" (ngModelChange)=\"carreguePedidos()\" ></kendo-dateinput>\n      </label>\n    </kendo-daterange>\n  </div>\n</div>\n\n<app-grid-pedidos #gridPedidos [pedidos]=\"pedidos\" [carregando]=\"carregando\" [mesa]=\"mesa\" [modoGrupoLojas]=\"true\">\n\n</app-grid-pedidos>\n\n", "import {Component, Input, OnInit, ViewChild} from '@angular/core';\nimport {GridPedidosComponent} from \"../../pedidos/grid-pedidos/grid-pedidos.component\";\nimport {EnumFiltroDePedidos, PedidosService} from \"../../services/pedidos.service\";\nimport {GrupolojasService} from \"../../superadmin/services/grupolojas.service\";\nimport moment from \"moment\";\n\n@Component({\n  selector: 'app-pedidos-cancelados-grupo',\n  templateUrl: './pedidos-cancelados-grupo.component.html',\n  styleUrls: ['./pedidos-cancelados-grupo.component.scss']\n})\nexport class PedidosCanceladosGrupoComponent implements OnInit {\n\n  @ViewChild('gridPedidos', { static: true}) gridPedidos: GridPedidosComponent;\n  pedidos: [];\n  paginacao = { total: 1000};\n  carregando: any = false;\n  filtro: any = { inicio: null, fim: null};\n  resumoPedidos: any = { qtde: 0, total: 0, totalTaxas: 0 };\n  @Input() mesa = false;\n  objFiltro: any = {\n    q: ''\n  };\n\n  constructor(private grupolojasService: GrupolojasService) {\n  }\n\n  ngOnInit(): void {\n    this.filtro.inicio = moment().add(-7 , 'd').toDate();\n    this.filtro.fim = new Date();\n    this.carreguePedidos();\n  }\n\n  carreguePedidos(){\n    if(this.carregando) return;\n\n    let dtInicio = moment(this.filtro.inicio).format('YYYYMMDD');\n    let dtFim = moment(this.filtro.fim).format('YYYYMMDD');\n\n    this.carregando = true;\n    let filtro = EnumFiltroDePedidos.pedidos;\n    if( this.mesa ) {\n      filtro = EnumFiltroDePedidos.comanda;\n    }\n    this.grupolojasService.listePedidosCancelados(0, this.paginacao.total,\n      null,    dtInicio, dtFim, filtro, this.objFiltro.q).then( (resposta) => {\n      this.setPedidos(resposta.pedidos || []);\n\n      this.gridPedidos.atualizeGridPedidos(this.pedidos,'cancelados');\n      this.carregando = false;\n    }).catch( () => {\n      this.carregando = false;\n    });\n  }\n\n  private setPedidos(resposta: any) {\n    this.pedidos = resposta || [];\n    this.resumoPedidos.qtde = this.pedidos.length;\n  }\n\n  onFilter($event: any) {\n    this.carreguePedidos();\n  }\n\n}\n", "<h4   class=\"page-title\"><i class=\"fe-shopping-cart\"></i> Pedidos</h4>\n\n<div class=\"card\">\n\n  <div class=\"card-body\">\n    <kendo-tabstrip class=\"nav-bordered mt-3\" >\n      <kendo-tabstrip-tab [title]=\"'Em a<PERSON>'\" [selected]=\"true\">\n        <ng-template kendoTabContent>\n          <app-acompanhar-pedidos-grupo></app-acompanhar-pedidos-grupo>\n        </ng-template>\n      </kendo-tabstrip-tab>\n\n      <kendo-tabstrip [title]=\"'Finalizados'\">\n        <ng-template kendoTabContent>\n            <app-pedidos-pago-grupo></app-pedidos-pago-grupo>\n        </ng-template>\n      </kendo-tabstrip>\n\n      <kendo-tabstrip [title]=\"'Cancelados'\">\n        <ng-template kendoTabContent>\n            <app-pedidos-cancelados-grupo></app-pedidos-cancelados-grupo>\n        </ng-template>\n      </kendo-tabstrip>\n    </kendo-tabstrip>\n  </div>\n\n\n</div>\n\n\n\n", "import { Component, OnInit } from '@angular/core';\n\n\n@Component({\n  selector: 'app-pedidos-grupo',\n  templateUrl: './pedidos-grupo.component.html',\n  styleUrls: ['./pedidos-grupo.component.scss']\n})\nexport class PedidosGrupoComponent implements OnInit {\n\n  pedidos\n  constructor() { }\n\n  ngOnInit(): void {\n  }\n\n}\n", "import { Component, OnInit } from '@angular/core';\nimport {ActivatedRoute} from \"@angular/router\";\n\n@Component({\n  selector: 'app-pedido-grupo-detalhes',\n  templateUrl: './pedido-grupo-detalhes.component.html',\n  styleUrls: ['./pedido-grupo-detalhes.component.scss']\n})\nexport class PedidoGrupoDetalhesComponent implements OnInit {\n  idEmpresa: any\n  constructor(private activatedRoute: ActivatedRoute) {\n    this.idEmpresa = this.activatedRoute.snapshot.params['idEmpresa'];\n  }\n\n  ngOnInit(): void {\n  }\n\n}\n", "\n\n<app-pedido-detalhes #pedidoDetalhes [modoGrupoLojas]=\"true\" [idEmpresa]=\"idEmpresa\"></app-pedido-detalhes>\n", "import {Route, RouterModule} from \"@angular/router\";\nimport {NgModule} from \"@angular/core\";\nimport {PainelGrupoComponent} from \"./painel-grupo/painel-grupo.component\";\nimport {PedidosGrupoComponent} from \"./pedidos-grupo/pedidos-grupo.component\";\nimport {PedidoGrupoDetalhesComponent} from \"./pedido-grupo-detalhes/pedido-grupo-detalhes.component\";\nimport {UserResolver} from \"../services/user.resolver\";\n\nconst routes: Route[] = [\n  {\n    path: '', component: PainelGrupoComponent,\n    resolve: { user: UserResolver },\n    children: [\n      {path: 'index', pathMatch: 'full', component: PedidosGrupoComponent},\n      {path: 'pedidos/detalhes/:idEmpresa/:guid', pathMatch: 'full', component: PedidoGrupoDetalhesComponent},\n    ]\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class PainelRedeRoutingModule{}\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport {PainelRedeRoutingModule} from \"./painel-grupo-routing.module\";\nimport { PainelGrupoComponent } from './painel-grupo/painel-grupo.component';\nimport {PedidoGrupoDetalhesComponent} from \"./pedido-grupo-detalhes/pedido-grupo-detalhes.component\";\nimport {PedidosGrupoComponent} from \"./pedidos-grupo/pedidos-grupo.component\";\nimport {AcompanharPedidosGrupoComponent} from \"./acompanhar-pedidos-grupo/acompanhar-pedidos-grupo.component\";\nimport {\n  CheckBoxModule,\n  InputsModule,\n  MaskedTextBoxModule,\n  RadioButtonModule, SwitchModule,\n  TextBoxModule\n} from '@progress/kendo-angular-inputs';\nimport {ButtonsModule, DropDownButtonModule} from '@progress/kendo-angular-buttons';\nimport {CompartilhadoModule} from \"../compartilhado/compartilhado.module\";\nimport {CurrencyMaskModule} from \"ng2-currency-mask\";\nimport {FormsModule} from \"@angular/forms\";\nimport {IntlModule} from \"@progress/kendo-angular-intl\";\nimport { PedidosPagoGrupoComponent } from './pedidos-pago-grupo/pedidos-pago-grupo.component';\nimport {DateInputsModule} from \"@progress/kendo-angular-dateinputs\";\nimport { PedidosCanceladosGrupoComponent } from './pedidos-cancelados-grupo/pedidos-cancelados-grupo.component';\nimport {LayoutModule} from \"@progress/kendo-angular-layout\";\nimport {ApplicationPipesModule} from \"../pipes/application-pipes.module\";\n\n\n@NgModule({\n  declarations: [\n    PainelGrupoComponent,\n    PedidoGrupoDetalhesComponent,\n    AcompanharPedidosGrupoComponent,\n    PedidosGrupoComponent,\n    PedidosPagoGrupoComponent,\n    PedidosCanceladosGrupoComponent\n  ],\n    imports: [\n        CommonModule,\n        FormsModule,\n        IntlModule,\n        InputsModule, MaskedTextBoxModule, ButtonsModule, SwitchModule, DateInputsModule,\n        TextBoxModule, CheckBoxModule, CurrencyMaskModule, RadioButtonModule, DropDownButtonModule,\n        CompartilhadoModule,\n        PainelRedeRoutingModule, LayoutModule, ApplicationPipesModule\n    ]\n})\nexport class PainelGrupoModule { }\n"], "x_google_ignoreList": []}