"use strict";(self.webpackChunksorteieme_js=self.webpackChunksorteieme_js||[]).push([[630],{17630:(Ba,R,l)=>{l.r(R),l.d(R,{CrmModule:()=>$a});var m=l(94666),d=l(2508),_=l(81798),e=l(22560),P=l(50549);let ue=(()=>{class o{}return o.\u0275fac=function(t){return new(t||o)},o.\u0275mod=e.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=e.\u0275\u0275defineInjector({imports:[_.si,_.BQ,_.BQ]}),o})();var fe=l(29757),O=l(48971),v=l(64139),y=(l(66587),l(54350),l(6957),l(88759),l(86942),l(47418),l(44661),l(24514),l(83910),l(58987));l(34497);let Ae=(()=>{class o{}return o.\u0275fac=function(t){return new(t||o)},o.\u0275mod=e.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=e.\u0275\u0275defineInjector({imports:[_.BQ,_.BQ]}),o})();var S=l(89107);function ze(o,r){if(1&o&&(e.\u0275\u0275namespaceSVG(),e.\u0275\u0275element(0,"circle",3)),2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275styleProp("animation-name","mat-progress-spinner-stroke-rotate-"+t._spinnerAnimationLabel)("stroke-dashoffset",t._getStrokeDashOffset(),"px")("stroke-dasharray",t._getStrokeCircumference(),"px")("stroke-width",t._getCircleStrokeWidth(),"%"),e.\u0275\u0275attribute("r",t._getCircleRadius())}}function Ne(o,r){if(1&o&&(e.\u0275\u0275namespaceSVG(),e.\u0275\u0275element(0,"circle",3)),2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275styleProp("stroke-dashoffset",t._getStrokeDashOffset(),"px")("stroke-dasharray",t._getStrokeCircumference(),"px")("stroke-width",t._getCircleStrokeWidth(),"%"),e.\u0275\u0275attribute("r",t._getCircleRadius())}}class Re{constructor(r){this._elementRef=r}}const $e=(0,_.pj)(Re,"primary"),X=new e.InjectionToken("mat-progress-spinner-default-options",{providedIn:"root",factory:function Be(){return{diameter:100}}});class C extends $e{constructor(r,t,n,a,i){super(r),this._elementRef=r,this._document=n,this._diameter=100,this._value=0,this._fallbackAnimation=!1,this.mode="determinate";const s=C._diameters;this._spinnerAnimationLabel=this._getSpinnerAnimationLabel(),s.has(n.head)||s.set(n.head,new Set([100])),this._fallbackAnimation=t.EDGE||t.TRIDENT,this._noopAnimations="NoopAnimations"===a&&!!i&&!i._forceAnimations,i&&(i.diameter&&(this.diameter=i.diameter),i.strokeWidth&&(this.strokeWidth=i.strokeWidth))}get diameter(){return this._diameter}set diameter(r){this._diameter=(0,O.su)(r),this._spinnerAnimationLabel=this._getSpinnerAnimationLabel(),!this._fallbackAnimation&&this._styleRoot&&this._attachStyleNode()}get strokeWidth(){return this._strokeWidth||this.diameter/10}set strokeWidth(r){this._strokeWidth=(0,O.su)(r)}get value(){return"determinate"===this.mode?this._value:0}set value(r){this._value=Math.max(0,Math.min(100,(0,O.su)(r)))}ngOnInit(){const r=this._elementRef.nativeElement;this._styleRoot=(0,S.kV)(r)||this._document.head,this._attachStyleNode(),r.classList.add(`mat-progress-spinner-indeterminate${this._fallbackAnimation?"-fallback":""}-animation`)}_getCircleRadius(){return(this.diameter-10)/2}_getViewBox(){const r=2*this._getCircleRadius()+this.strokeWidth;return`0 0 ${r} ${r}`}_getStrokeCircumference(){return 2*Math.PI*this._getCircleRadius()}_getStrokeDashOffset(){return"determinate"===this.mode?this._getStrokeCircumference()*(100-this._value)/100:this._fallbackAnimation&&"indeterminate"===this.mode?.2*this._getStrokeCircumference():null}_getCircleStrokeWidth(){return this.strokeWidth/this.diameter*100}_attachStyleNode(){const r=this._styleRoot,t=this._diameter,n=C._diameters;let a=n.get(r);if(!a||!a.has(t)){const i=this._document.createElement("style");i.setAttribute("mat-spinner-animation",this._spinnerAnimationLabel),i.textContent=this._getAnimationText(),r.appendChild(i),a||(a=new Set,n.set(r,a)),a.add(t)}}_getAnimationText(){const r=this._getStrokeCircumference();return"\n @keyframes mat-progress-spinner-stroke-rotate-DIAMETER {\n    0%      { stroke-dashoffset: START_VALUE;  transform: rotate(0); }\n    12.5%   { stroke-dashoffset: END_VALUE;    transform: rotate(0); }\n    12.5001%  { stroke-dashoffset: END_VALUE;    transform: rotateX(180deg) rotate(72.5deg); }\n    25%     { stroke-dashoffset: START_VALUE;  transform: rotateX(180deg) rotate(72.5deg); }\n\n    25.0001%   { stroke-dashoffset: START_VALUE;  transform: rotate(270deg); }\n    37.5%   { stroke-dashoffset: END_VALUE;    transform: rotate(270deg); }\n    37.5001%  { stroke-dashoffset: END_VALUE;    transform: rotateX(180deg) rotate(161.5deg); }\n    50%     { stroke-dashoffset: START_VALUE;  transform: rotateX(180deg) rotate(161.5deg); }\n\n    50.0001%  { stroke-dashoffset: START_VALUE;  transform: rotate(180deg); }\n    62.5%   { stroke-dashoffset: END_VALUE;    transform: rotate(180deg); }\n    62.5001%  { stroke-dashoffset: END_VALUE;    transform: rotateX(180deg) rotate(251.5deg); }\n    75%     { stroke-dashoffset: START_VALUE;  transform: rotateX(180deg) rotate(251.5deg); }\n\n    75.0001%  { stroke-dashoffset: START_VALUE;  transform: rotate(90deg); }\n    87.5%   { stroke-dashoffset: END_VALUE;    transform: rotate(90deg); }\n    87.5001%  { stroke-dashoffset: END_VALUE;    transform: rotateX(180deg) rotate(341.5deg); }\n    100%    { stroke-dashoffset: START_VALUE;  transform: rotateX(180deg) rotate(341.5deg); }\n  }\n".replace(/START_VALUE/g,""+.95*r).replace(/END_VALUE/g,""+.2*r).replace(/DIAMETER/g,`${this._spinnerAnimationLabel}`)}_getSpinnerAnimationLabel(){return this.diameter.toString().replace(".","_")}}C.\u0275fac=function(r){return new(r||C)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(S.t4),e.\u0275\u0275directiveInject(m.DOCUMENT,8),e.\u0275\u0275directiveInject(e.ANIMATION_MODULE_TYPE,8),e.\u0275\u0275directiveInject(X))},C.\u0275cmp=e.\u0275\u0275defineComponent({type:C,selectors:[["mat-progress-spinner"]],hostAttrs:["role","progressbar","tabindex","-1",1,"mat-progress-spinner"],hostVars:10,hostBindings:function(r,t){2&r&&(e.\u0275\u0275attribute("aria-valuemin","determinate"===t.mode?0:null)("aria-valuemax","determinate"===t.mode?100:null)("aria-valuenow","determinate"===t.mode?t.value:null)("mode",t.mode),e.\u0275\u0275styleProp("width",t.diameter,"px")("height",t.diameter,"px"),e.\u0275\u0275classProp("_mat-animation-noopable",t._noopAnimations))},inputs:{color:"color",mode:"mode",diameter:"diameter",strokeWidth:"strokeWidth",value:"value"},exportAs:["matProgressSpinner"],features:[e.\u0275\u0275InheritDefinitionFeature],decls:3,vars:8,consts:[["preserveAspectRatio","xMidYMid meet","focusable","false","aria-hidden","true",3,"ngSwitch"],["cx","50%","cy","50%",3,"animation-name","stroke-dashoffset","stroke-dasharray","stroke-width",4,"ngSwitchCase"],["cx","50%","cy","50%",3,"stroke-dashoffset","stroke-dasharray","stroke-width",4,"ngSwitchCase"],["cx","50%","cy","50%"]],template:function(r,t){1&r&&(e.\u0275\u0275namespaceSVG(),e.\u0275\u0275elementStart(0,"svg",0),e.\u0275\u0275template(1,ze,1,9,"circle",1),e.\u0275\u0275template(2,Ne,1,7,"circle",2),e.\u0275\u0275elementEnd()),2&r&&(e.\u0275\u0275styleProp("width",t.diameter,"px")("height",t.diameter,"px"),e.\u0275\u0275property("ngSwitch","indeterminate"===t.mode),e.\u0275\u0275attribute("viewBox",t._getViewBox()),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngSwitchCase",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngSwitchCase",!1))},dependencies:[m.NgSwitch,m.NgSwitchCase],styles:[".mat-progress-spinner{display:block;position:relative;overflow:hidden}.mat-progress-spinner svg{position:absolute;transform:rotate(-90deg);top:0;left:0;transform-origin:center;overflow:visible}.mat-progress-spinner circle{fill:transparent;transform-origin:center;transition:stroke-dashoffset 225ms linear}._mat-animation-noopable.mat-progress-spinner circle{transition:none;animation:none}.cdk-high-contrast-active .mat-progress-spinner circle{stroke:currentColor;stroke:CanvasText}.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] svg{animation:mat-progress-spinner-linear-rotate 2000ms linear infinite}._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] svg{transition:none;animation:none}.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] circle{transition-property:stroke;animation-duration:4000ms;animation-timing-function:cubic-bezier(0.35, 0, 0.25, 1);animation-iteration-count:infinite}._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] circle{transition:none;animation:none}.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] svg{animation:mat-progress-spinner-stroke-rotate-fallback 10000ms cubic-bezier(0.87, 0.03, 0.33, 1) infinite}._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] svg{transition:none;animation:none}.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] circle{transition-property:stroke}._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] circle{transition:none;animation:none}@keyframes mat-progress-spinner-linear-rotate{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes mat-progress-spinner-stroke-rotate-100{0%{stroke-dashoffset:268.606171575px;transform:rotate(0)}12.5%{stroke-dashoffset:56.5486677px;transform:rotate(0)}12.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(72.5deg)}25%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(72.5deg)}25.0001%{stroke-dashoffset:268.606171575px;transform:rotate(270deg)}37.5%{stroke-dashoffset:56.5486677px;transform:rotate(270deg)}37.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(161.5deg)}50%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(161.5deg)}50.0001%{stroke-dashoffset:268.606171575px;transform:rotate(180deg)}62.5%{stroke-dashoffset:56.5486677px;transform:rotate(180deg)}62.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(251.5deg)}75%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(251.5deg)}75.0001%{stroke-dashoffset:268.606171575px;transform:rotate(90deg)}87.5%{stroke-dashoffset:56.5486677px;transform:rotate(90deg)}87.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(341.5deg)}100%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(341.5deg)}}@keyframes mat-progress-spinner-stroke-rotate-fallback{0%{transform:rotate(0deg)}25%{transform:rotate(1170deg)}50%{transform:rotate(2340deg)}75%{transform:rotate(3510deg)}100%{transform:rotate(4680deg)}}\n"],encapsulation:2,changeDetection:0}),C._diameters=new WeakMap;let He=(()=>{class o{}return o.\u0275fac=function(t){return new(t||o)},o.\u0275mod=e.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=e.\u0275\u0275defineInjector({imports:[_.BQ,m.CommonModule,_.BQ]}),o})();var F=l(25895),Z=l(57199),p=(l(28456),l(83278),l(17520),l(92218),l(85921),l(24851));l(72867);const Ze={provide:new e.InjectionToken("mat-tooltip-scroll-strategy"),deps:[F.aV],useFactory:function Xe(o){return()=>o.scrollStrategies.reposition({scrollThrottle:20})}};let nt=(()=>{class o{}return o.\u0275fac=function(t){return new(t||o)},o.\u0275mod=e.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=e.\u0275\u0275defineInjector({providers:[Ze],imports:[P.rt,m.CommonModule,F.U8,_.BQ,_.BQ,Z.ZD]}),o})();var L=l(85989),ae=l(27209),b=l(72433),f=l(83918),ot=l(84505);let ie=(()=>{class o{constructor(){this.contextoConversaSubject=new ot.X({mensagens:[]}),this.contextoConversa$=this.contextoConversaSubject.asObservable(),this.configurarEventListener()}configurarEventListener(){window.addEventListener("message",t=>{if(t.data&&"crm_conversa_atualizada"===t.data.type&&(console.log("Recebendo atualiza\xe7\xe3o de conversa do WhatsApp:",t.data.payload),this.atualizarContextoConversa(t.data.payload)),t.data&&"SELECIONOU_CONTATO"===t.data.tipo){console.log("Contato selecionado no WhatsApp:",t.data.payload);const n=this.contextoConversaSubject.getValue(),a={...n,contatoAtual:t.data.payload.nome||n.contatoAtual,telefoneAtual:t.data.payload.telefone||n.telefoneAtual};this.contextoConversaSubject.next(a)}})}atualizarContextoConversa(t){const n=this.contextoConversaSubject.getValue();this.contextoConversaSubject.next({mensagens:t.mensagens||n.mensagens,contatoAtual:t.contatoAtual||n.contatoAtual,telefoneAtual:t.telefoneAtual||n.telefoneAtual,etapaFunil:t.etapaFunil||n.etapaFunil})}setContextoConversa(t){this.contextoConversaSubject.next(t)}adicionarMensagem(t){const n=this.contextoConversaSubject.getValue(),a=[...n.mensagens,t];this.contextoConversaSubject.next({...n,mensagens:a})}limparMensagens(){const t=this.contextoConversaSubject.getValue();this.contextoConversaSubject.next({...t,mensagens:[]})}getContextoAtual(){return this.contextoConversaSubject.getValue()}}return o.\u0275fac=function(t){return new(t||o)},o.\u0275prov=e.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})();var re=l(50864);let j=(()=>{class o extends re.N{constructor(t){super(t),this.http=t,this.endpoint="/crm/leads"}liste(t={}){return this.obtenha(this.endpoint,t)}selecione(t){return this.obtenha(`${this.endpoint}/${t}`,{})}salveLead(t){return t.id?this.facaPut(`${this.endpoint}/${t.id}`,t):this.facaPost(this.endpoint,t)}removaLead(t){return this.remova(`${this.endpoint}/${t}`,{})}buscarDadosInstagram(t){return this.obtenha(`${this.endpoint}/dadosig`,{username:t})}enviarDadosInstagram(t,n,a){return this.facaPost(`${this.endpoint}/dadosig2`,{texto:t,username:a,crmEmpresaId:n||null})}analisarWebsite(t){return this.obtenha(`${this.endpoint}/analisar-website`,{url:t})}categorizarLinks(t){return this.facaPost(`${this.endpoint}/categorizar-links`,{links:t})}descobrirCnpj(t,n){return this.obtenha(`${this.endpoint}/descobrir-cnpj`,{nomeEmpresa:t,cidade:n})}buscarDetalhesSocios(t){return this.facaPost(`${this.endpoint}/buscar-detalhes-socios`,{cnpj:t})}listarLinks(t){return this.obtenha(`${this.endpoint}/${t}/links`,{})}adicionarLink(t,n){return this.facaPost(`${this.endpoint}/${t}/links`,n)}atualizarLink(t,n,a){return this.facaPut(`${this.endpoint}/${t}/links/${n}`,a)}removerLink(t,n){return this.remova(`${this.endpoint}/${t}/links/${n}`,{})}removerLinkPorTipo(t,n){return this.remova(`${this.endpoint}/${t}/links/tipo/${n}`,{})}}return o.\u0275fac=function(t){return new(t||o)(e.\u0275\u0275inject(y.eN))},o.\u0275prov=e.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac}),o})();function at(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",14)(1,"div",15)(2,"div",16),e.\u0275\u0275element(3,"div",17),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"h3",18),e.\u0275\u0275text(5,"Carregando dados do lead"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"p",19),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd()()()),2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate1("Buscando informa\xe7\xf5es para @",t.username,"...")}}function it(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",14)(1,"div",20)(2,"div",21),e.\u0275\u0275element(3,"i",22),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"h3",18),e.\u0275\u0275text(5,"Lead n\xe3o encontrado"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"p",19),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd()()()),2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate1("N\xe3o foi poss\xedvel encontrar dados para @",t.username,". Redirecionando...")}}function rt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"a",52),e.\u0275\u0275element(1,"i",53),e.\u0275\u0275elementStart(2,"span"),e.\u0275\u0275text(3,"WhatsApp"),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("href",t.getWhatsAppUrl(null==t.dadosLeadAtual?null:t.dadosLeadAtual.telefone),e.\u0275\u0275sanitizeUrl)}}function st(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"a",54),e.\u0275\u0275element(1,"i",55),e.\u0275\u0275elementStart(2,"span"),e.\u0275\u0275text(3,"Ligar"),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("href","tel:"+(null==t.dadosLeadAtual?null:t.dadosLeadAtual.telefone),e.\u0275\u0275sanitizeUrl)}}function ct(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"a",56),e.\u0275\u0275element(1,"i",57),e.\u0275\u0275elementStart(2,"span"),e.\u0275\u0275text(3,"Email"),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("href","mailto:"+(null==t.dadosLeadAtual?null:t.dadosLeadAtual.email),e.\u0275\u0275sanitizeUrl)}}function lt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",58)(1,"div",59),e.\u0275\u0275element(2,"i",55),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",60)(4,"span",61),e.\u0275\u0275text(5,"Telefone"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"span",62),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd()()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate(null==t.dadosLeadAtual?null:t.dadosLeadAtual.telefone)}}function dt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",58)(1,"div",59),e.\u0275\u0275element(2,"i",57),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",60)(4,"span",61),e.\u0275\u0275text(5,"Email"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"span",62),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd()()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate(null==t.dadosLeadAtual?null:t.dadosLeadAtual.email)}}function mt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",58)(1,"div",63),e.\u0275\u0275element(2,"i",64),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",60)(4,"span",61),e.\u0275\u0275text(5,"Instagram"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"a",65),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd()()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(6),e.\u0275\u0275property("href",t.getInstagramUrl(null==t.dadosLeadAtual?null:t.dadosLeadAtual.instagram),e.\u0275\u0275sanitizeUrl),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(null==t.dadosLeadAtual?null:t.dadosLeadAtual.instagram)}}function pt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",58)(1,"div",59),e.\u0275\u0275element(2,"i",66),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",60)(4,"span",61),e.\u0275\u0275text(5,"Segmento"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"span",62),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd()()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate(null==t.dadosLeadAtual?null:t.dadosLeadAtual.segmento)}}function gt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",58)(1,"div",59),e.\u0275\u0275element(2,"i",67),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",60)(4,"span",61),e.\u0275\u0275text(5,"Origem"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"span",62),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd()()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate(null==t.dadosLeadAtual?null:t.dadosLeadAtual.origemLead)}}function _t(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",58)(1,"div",59),e.\u0275\u0275element(2,"i",4),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",60)(4,"span",61),e.\u0275\u0275text(5,"Porte"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"span",62),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd()()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate(null==t.dadosLeadAtual?null:t.dadosLeadAtual.tamanhoEmpresa)}}function ut(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",58)(1,"div",59),e.\u0275\u0275element(2,"i",68),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",60)(4,"span",61),e.\u0275\u0275text(5,"Localiza\xe7\xe3o"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"span",62),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd()()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate(null==t.dadosLeadAtual?null:t.dadosLeadAtual.localizacao)}}function ft(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",74)(1,"div",75),e.\u0275\u0275element(2,"i",76),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",77)(4,"span",78),e.\u0275\u0275text(5,"Primeiro Contato"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"span",79),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd()()()),2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate(null==t.dadosLeadAtual?null:t.dadosLeadAtual.dataPrimeiroContato)}}function Ct(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",74)(1,"div",75),e.\u0275\u0275element(2,"i",80),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",77)(4,"span",78),e.\u0275\u0275text(5,"\xdaltima Intera\xe7\xe3o"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"span",79),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd()()()),2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate(null==t.dadosLeadAtual?null:t.dadosLeadAtual.ultimaInteracao)}}function ht(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",81)(1,"div",75),e.\u0275\u0275element(2,"i",82),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",77)(4,"span",78),e.\u0275\u0275text(5,"Pr\xf3ximo Follow-up"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"span",83),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd()()()),2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate(null==t.dadosLeadAtual?null:t.dadosLeadAtual.proximoFollowUp)}}function vt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",69)(1,"div",42),e.\u0275\u0275element(2,"i",70),e.\u0275\u0275elementStart(3,"h3"),e.\u0275\u0275text(4,"Timeline"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"div",71),e.\u0275\u0275template(6,ft,8,1,"div",72),e.\u0275\u0275template(7,Ct,8,1,"div",72),e.\u0275\u0275template(8,ht,8,1,"div",73),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(6),e.\u0275\u0275property("ngIf",null==t.dadosLeadAtual?null:t.dadosLeadAtual.dataPrimeiroContato),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==t.dadosLeadAtual?null:t.dadosLeadAtual.ultimaInteracao),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==t.dadosLeadAtual?null:t.dadosLeadAtual.proximoFollowUp)}}function xt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"a",88)(1,"div",89),e.\u0275\u0275element(2,"i",90),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",91)(4,"span",92),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"span",93),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"div",94),e.\u0275\u0275element(9,"i",67),e.\u0275\u0275elementEnd()()),2&o){const t=r.$implicit,n=e.\u0275\u0275nextContext(3);e.\u0275\u0275property("href",n.getLinkUrl(t),e.\u0275\u0275sanitizeUrl)("title",t.descricao||"Abrir "+t.tipo),e.\u0275\u0275advance(1),e.\u0275\u0275styleProp("color",n.getLinkColor(t.tipo)),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",n.getLinkIcon(t.tipo)),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.tipo),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.descricao||t.url)}}function bt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",84)(1,"div",42),e.\u0275\u0275element(2,"i",85),e.\u0275\u0275elementStart(3,"h3"),e.\u0275\u0275text(4,"Links Importantes"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"div",86),e.\u0275\u0275template(6,xt,10,7,"a",87),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(6),e.\u0275\u0275property("ngForOf",null==t.dadosLeadAtual?null:t.dadosLeadAtual.links)}}function Mt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",95)(1,"a",96)(2,"div",97),e.\u0275\u0275element(3,"i",98),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",99)(5,"span",100),e.\u0275\u0275text(6,"Website"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(7,"span",101),e.\u0275\u0275text(8),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(9,"div",102),e.\u0275\u0275element(10,"i",67),e.\u0275\u0275elementEnd()()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275property("href",t.getWebsiteUrl(null==t.dadosLeadAtual?null:t.dadosLeadAtual.site),e.\u0275\u0275sanitizeUrl),e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate(null==t.dadosLeadAtual?null:t.dadosLeadAtual.site)}}function Pt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",107)(1,"div",108),e.\u0275\u0275element(2,"i",109),e.\u0275\u0275elementStart(3,"span"),e.\u0275\u0275text(4,"Observa\xe7\xf5es de Vendas"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"p",110),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate(null==t.dadosLeadAtual?null:t.dadosLeadAtual.observacoes)}}function Ot(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",107)(1,"div",108),e.\u0275\u0275element(2,"i",111),e.\u0275\u0275elementStart(3,"span"),e.\u0275\u0275text(4,"Hist\xf3rico de Propostas"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"p",110),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate(null==t.dadosLeadAtual?null:t.dadosLeadAtual.historicoPropostas)}}function yt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",103)(1,"div",42),e.\u0275\u0275element(2,"i",104),e.\u0275\u0275elementStart(3,"h3"),e.\u0275\u0275text(4,"Observa\xe7\xf5es"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"div",105),e.\u0275\u0275template(6,Pt,7,1,"div",106),e.\u0275\u0275template(7,Ot,7,1,"div",106),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(6),e.\u0275\u0275property("ngIf",null==t.dadosLeadAtual?null:t.dadosLeadAtual.observacoes),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==t.dadosLeadAtual?null:t.dadosLeadAtual.historicoPropostas)}}function St(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"span",115),e.\u0275\u0275element(1,"i",66),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()),2&o){const t=r.$implicit;e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",t," ")}}function Et(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",112)(1,"div",42),e.\u0275\u0275element(2,"i",33),e.\u0275\u0275elementStart(3,"h3"),e.\u0275\u0275text(4,"Interesses"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"div",113),e.\u0275\u0275template(6,St,3,1,"span",114),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(6),e.\u0275\u0275property("ngForOf",null==t.dadosLeadAtual?null:t.dadosLeadAtual.interessesProdutos)}}const wt=function(o){return{"background-color":o}};function kt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",23)(1,"div",24)(2,"div",25)(3,"div",26)(4,"div",27)(5,"h1",28),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(7,"div",29),e.\u0275\u0275element(8,"i",30),e.\u0275\u0275elementStart(9,"span"),e.\u0275\u0275text(10),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(11,"div",31)(12,"div",32),e.\u0275\u0275element(13,"i",33),e.\u0275\u0275elementStart(14,"span"),e.\u0275\u0275text(15),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(16,"div",34),e.\u0275\u0275element(17,"i",35),e.\u0275\u0275elementStart(18,"span"),e.\u0275\u0275text(19),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(20,"div",36),e.\u0275\u0275template(21,rt,4,1,"a",37),e.\u0275\u0275template(22,st,4,1,"a",38),e.\u0275\u0275template(23,ct,4,1,"a",39),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(24,"div",40)(25,"div",41)(26,"div",42),e.\u0275\u0275element(27,"i",43),e.\u0275\u0275elementStart(28,"h3"),e.\u0275\u0275text(29,"Contato"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(30,"div",44),e.\u0275\u0275template(31,lt,8,1,"div",45),e.\u0275\u0275template(32,dt,8,1,"div",45),e.\u0275\u0275template(33,mt,8,2,"div",45),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(34,"div",41)(35,"div",42),e.\u0275\u0275element(36,"i",46),e.\u0275\u0275elementStart(37,"h3"),e.\u0275\u0275text(38,"Neg\xf3cio"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(39,"div",44),e.\u0275\u0275template(40,pt,8,1,"div",45),e.\u0275\u0275template(41,gt,8,1,"div",45),e.\u0275\u0275template(42,_t,8,1,"div",45),e.\u0275\u0275template(43,ut,8,1,"div",45),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275template(44,vt,9,3,"div",47),e.\u0275\u0275template(45,bt,7,1,"div",48),e.\u0275\u0275template(46,Mt,11,2,"div",49),e.\u0275\u0275template(47,yt,8,2,"div",50),e.\u0275\u0275template(48,Et,7,1,"div",51),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate((null==t.dadosLeadAtual?null:t.dadosLeadAtual.empresa)||"Empresa n\xe3o informada"),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate((null==t.dadosLeadAtual?null:t.dadosLeadAtual.nome)||"Contato n\xe3o informado"),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngStyle",e.\u0275\u0275pureFunction1(21,wt,t.getCorDoScore(null==t.dadosLeadAtual?null:t.dadosLeadAtual.scoreLead))),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.formatarScore(null==t.dadosLeadAtual?null:t.dadosLeadAtual.scoreLead)),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass","stage-"+((null==t.dadosLeadAtual||null==t.dadosLeadAtual.etapaFunil?null:t.dadosLeadAtual.etapaFunil.toLowerCase())||"indefinida")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate((null==t.dadosLeadAtual?null:t.dadosLeadAtual.etapaFunil)||"Indefinida"),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",null==t.dadosLeadAtual?null:t.dadosLeadAtual.telefone),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==t.dadosLeadAtual?null:t.dadosLeadAtual.telefone),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==t.dadosLeadAtual?null:t.dadosLeadAtual.email),e.\u0275\u0275advance(8),e.\u0275\u0275property("ngIf",null==t.dadosLeadAtual?null:t.dadosLeadAtual.telefone),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==t.dadosLeadAtual?null:t.dadosLeadAtual.email),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==t.dadosLeadAtual?null:t.dadosLeadAtual.instagram),e.\u0275\u0275advance(7),e.\u0275\u0275property("ngIf",null==t.dadosLeadAtual?null:t.dadosLeadAtual.segmento),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==t.dadosLeadAtual?null:t.dadosLeadAtual.origemLead),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==t.dadosLeadAtual?null:t.dadosLeadAtual.tamanhoEmpresa),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==t.dadosLeadAtual?null:t.dadosLeadAtual.localizacao),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",(null==t.dadosLeadAtual?null:t.dadosLeadAtual.dataPrimeiroContato)||(null==t.dadosLeadAtual?null:t.dadosLeadAtual.ultimaInteracao)||(null==t.dadosLeadAtual?null:t.dadosLeadAtual.proximoFollowUp)),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",(null==t.dadosLeadAtual?null:t.dadosLeadAtual.links)&&(null==t.dadosLeadAtual?null:t.dadosLeadAtual.links.length)>0),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==t.dadosLeadAtual?null:t.dadosLeadAtual.site),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",(null==t.dadosLeadAtual?null:t.dadosLeadAtual.observacoes)||(null==t.dadosLeadAtual?null:t.dadosLeadAtual.historicoPropostas)),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",(null==t.dadosLeadAtual?null:t.dadosLeadAtual.interessesProdutos)&&(null==t.dadosLeadAtual?null:t.dadosLeadAtual.interessesProdutos.length)>0)}}let se=(()=>{class o{constructor(t,n,a,i){this.conversasService=t,this.route=n,this.router=a,this.leadService=i,this.etapaAtual="Conectado",this.username=null,this.carregandoLead=!1,this.leadEncontrado=!1}ngOnInit(){this.route.paramMap.subscribe(t=>{this.username=t.get("username"),console.log("Username capturado da rota:",this.username),this.username?this.buscarLeadPorUsername(this.username):this.inicializarModoDemonstracao(),setTimeout(()=>this.debugEstado(),100)}),this.conversasService.contextoConversa$.subscribe(t=>{t&&t.dadosLead&&(this.dadosLeadAtual=t.dadosLead)})}simularNovasMensagens(){const t=(new Date).toLocaleTimeString(),n=[{mensagens:[{texto:"Ol\xe1, estou interessado no seu sistema de gest\xe3o. Quanto custa?",remetente:"Cliente",horario:t,tipo:"entrada"},{texto:"Ol\xe1! Obrigado pelo interesse. Nossos planos come\xe7am a partir de R$99/m\xeas.",remetente:"Eu",horario:t,tipo:"saida"},{texto:"Isso parece interessante. Voc\xeas oferecem alguma demonstra\xe7\xe3o?",remetente:"Cliente",horario:t,tipo:"entrada"}],contato:"Jo\xe3o Silva",telefone:"+5511987654321",etapa:"Qualifica\xe7\xe3o"},{mensagens:[{texto:"Obrigado pelas informa\xe7\xf5es, mas achei um pouco caro para o meu neg\xf3cio atual.",remetente:"Cliente",horario:t,tipo:"entrada"},{texto:"Entendo sua preocupa\xe7\xe3o com o investimento. Voc\xea j\xe1 avaliou o retorno que pode ter?",remetente:"Eu",horario:t,tipo:"saida"},{texto:"Ainda n\xe3o calculei. Teria como me passar mais detalhes sobre isso?",remetente:"Cliente",horario:t,tipo:"entrada"}],contato:"Maria Oliveira",telefone:"+5511976543210",etapa:"Obje\xe7\xe3o"},{mensagens:[{texto:"Fiquei muito satisfeito com a demonstra\xe7\xe3o. Como fazemos para avan\xe7ar?",remetente:"Cliente",horario:t,tipo:"entrada"},{texto:"\xd3timo! Posso enviar a proposta comercial e os pr\xf3ximos passos por e-mail.",remetente:"Eu",horario:t,tipo:"saida"},{texto:"Perfeito. E quanto tempo leva para implementar o sistema?",remetente:"Cliente",horario:t,tipo:"entrada"}],contato:"Ricardo Mendes",telefone:"+5511955556666",etapa:"Fechamento"}],a=n[Math.floor(Math.random()*n.length)],i=this.gerarDadosLeadSimulados(a.contato,a.etapa);this.conversasService.setContextoConversa({mensagens:a.mensagens,contatoAtual:a.contato,telefoneAtual:a.telefone,etapaFunil:a.etapa,dadosLead:i}),this.dadosLeadAtual=i,this.etapaAtual=a.etapa}gerarDadosLeadSimulados(t,n){return{...{"Jo\xe3o Silva":{email:"<EMAIL>",cargo:"Gerente de Opera\xe7\xf5es",empresa:"Pizzaria Bella Napoli",segmento:"Alimenta\xe7\xe3o",tamanhoEmpresa:"Pequena",localizacao:"S\xe3o Paulo, SP",instagram:"@bellanapoli_pizzaria",site:"www.bellanapoli.com.br",dataPrimeiroContato:"10/05/2023",ultimaInteracao:(new Date).toLocaleDateString(),origemLead:"Site",scoreLead:75,interessesProdutos:["Card\xe1pio Digital","Gest\xe3o de Pedidos"],proximoFollowUp:this.gerarDataFutura(3),historicoPropostas:"Enviada proposta inicial em 15/05/2023",observacoes:"Cliente demonstra interesse em automatizar atendimento",links:[{tipo:"Ifood",url:"https://www.ifood.com.br/delivery/sao-paulo-sp/pizzaria-bella-napoli",descricao:"Card\xe1pio no iFood",ordem:1},{tipo:"Instagram",url:"https://instagram.com/bellanapoli_pizzaria",descricao:"Perfil no Instagram",ordem:2},{tipo:"Localiza\xe7\xe3o",url:"Rua das Flores, 123 - S\xe3o Paulo, SP",descricao:"Endere\xe7o da pizzaria",ordem:3}]},"Maria Oliveira":{email:"<EMAIL>",cargo:"Propriet\xe1ria",empresa:"Confeitaria Doce Sabor",segmento:"Alimenta\xe7\xe3o",tamanhoEmpresa:"Pequena",localizacao:"Rio de Janeiro, RJ",instagram:"@docesabor_confeitaria",linkedin:"linkedin.com/in/mariaoliveira",site:"www.docesabor.com.br",dataPrimeiroContato:"22/04/2023",ultimaInteracao:(new Date).toLocaleDateString(),origemLead:"Instagram",scoreLead:60,interessesProdutos:["Sistema de Delivery","Fideliza\xe7\xe3o"],proximoFollowUp:this.gerarDataFutura(2),observacoes:"Preocupada com custo-benef\xedcio",links:[{tipo:"Site",url:"https://www.docesabor.com.br",descricao:"Website oficial",ordem:1},{tipo:"WhatsApp",url:"21987654321",descricao:"WhatsApp para pedidos",ordem:2},{tipo:"Instagram",url:"https://instagram.com/docesabor_confeitaria",descricao:"Instagram da confeitaria",ordem:3}]},"Ricardo Mendes":{email:"<EMAIL>",cargo:"Diretor",empresa:"Restaurante Fusion",segmento:"Alimenta\xe7\xe3o",tamanhoEmpresa:"M\xe9dia",localizacao:"Belo Horizonte, MG",instagram:"@restaurantefusion",linkedin:"linkedin.com/in/ricardomendes",site:"www.restaurantefusion.com.br",dataPrimeiroContato:"03/06/2023",ultimaInteracao:(new Date).toLocaleDateString(),origemLead:"Indica\xe7\xe3o",scoreLead:90,interessesProdutos:["Sistema Completo","Integra\xe7\xe3o PDV"],proximoFollowUp:this.gerarDataFutura(1),historicoPropostas:"Apresenta\xe7\xe3o realizada em 10/06/2023. Proposta enviada em 12/06/2023",observacoes:"Cliente com alto potencial, j\xe1 testou a solu\xe7\xe3o concorrente",links:[{tipo:"Site",url:"https://www.restaurantefusion.com.br",descricao:"Website do restaurante",ordem:1},{tipo:"Ifood",url:"https://www.ifood.com.br/delivery/belo-horizonte-mg/restaurante-fusion",descricao:"Delivery no iFood",ordem:2},{tipo:"Reservas",url:"https://www.opentable.com.br/restaurante-fusion",descricao:"Sistema de reservas",ordem:3},{tipo:"Concorrente",url:"https://sistema-concorrente.com.br",descricao:"Sistema atual em uso",ordem:4}]}}[t]||{email:`${t.toLowerCase().replace(" ",".")}@email.com`,cargo:"Propriet\xe1rio",empresa:"Empresa Exemplo",segmento:"Alimenta\xe7\xe3o",tamanhoEmpresa:"Pequena",dataPrimeiroContato:(new Date).toLocaleDateString(),ultimaInteracao:(new Date).toLocaleDateString(),origemLead:"WhatsApp",scoreLead:50},nome:t,telefone:this.gerarTelefoneAleatorio(),etapaFunil:n}}gerarDataFutura(t){const n=new Date;return n.setDate(n.getDate()+t),n.toLocaleDateString()}gerarTelefoneAleatorio(){return`+55${Math.floor(89*Math.random())+11}9${Math.floor(9e3*Math.random())+1e3}${Math.floor(9e3*Math.random())+1e3}`}getCorDoScore(t){return t?t>=80?"#2ecc71":t>=50?"#f39c12":"#e74c3c":"#999"}formatarScore(t){return t||0===t?`${t}%`:"N/A"}getWhatsAppUrl(t){return t?`https://wa.me/55${t.replace(/\D/g,"")}`:"#"}getInstagramUrl(t){return t?`https://instagram.com/${t.replace("@","")}`:"#"}getWebsiteUrl(t){return t?t.startsWith("http")?t:`https://${t}`:"#"}getLinkIcon(t){return{Ifood:"fa fa-utensils","Site do Card\xe1pio":"fa fa-list-alt",Concorrente:"fa fa-exclamation-triangle",Reservas:"fa fa-calendar-check",WhatsApp:"fab fa-whatsapp",Localiza\u00e7\u00e3o:"fa fa-map-marker-alt",Site:"fa fa-globe",Instagram:"fa fa-instagram"}[t]||"fa fa-link"}getLinkColor(t){return{Ifood:"#ea1d2c","Site do Card\xe1pio":"#007bff",Concorrente:"#ff6b35",Reservas:"#28a745",WhatsApp:"#25d366",Localiza\u00e7\u00e3o:"#dc3545",Site:"#6c757d",Instagram:"#e4405f"}[t]||"#6c757d"}getLinkUrl(t){if(!t.url)return"#";switch(t.tipo){case"WhatsApp":return this.formatarWhatsAppLink(t.url);case"Localiza\xe7\xe3o":return this.formatarLocalizacaoLink(t.url);default:return t.url.startsWith("http")?t.url:`https://${t.url}`}}getLinkDisplayText(t){if(t.descricao)return t.descricao;switch(t.tipo){case"WhatsApp":return"WhatsApp";case"Localiza\xe7\xe3o":return"Ver no Mapa";case"Ifood":return"Card\xe1pio iFood";case"Site do Card\xe1pio":return"Card\xe1pio Online";case"Concorrente":return"Sistema Concorrente";case"Reservas":return"Fazer Reserva";default:return t.url}}formatarWhatsAppLink(t){if(t.includes("wa.me")||t.includes("whatsapp.com"))return t;const n=t.replace(/\D/g,"");return n.length>=10?`https://wa.me/55${n}`:t}formatarLocalizacaoLink(t){return t.includes("maps.google.com")||t.includes("goo.gl/maps")?t:t.includes(",")||t.includes("rua")||t.includes("av")?`https://maps.google.com/maps?q=${encodeURIComponent(t)}`:t}buscarLeadPorUsername(t){var n=this;return(0,f.Z)(function*(){console.log("Iniciando busca para username:",t),n.carregandoLead=!0;try{const a=yield n.leadService.liste({texto:t});if(console.log("Resposta da API via LeadService:",a),a&&a.data&&a.data.length>0){const i=a.data.find(s=>s.instagramHandle===t||s.instagramHandle===t.replace("@",""));i?(n.dadosLeadAtual=n.converterLeadParaDadosLead(i),n.leadEncontrado=!0,console.log("Lead encontrado e convertido:",n.dadosLeadAtual),n.conversasService.setContextoConversa({mensagens:[],contatoAtual:i.nomeResponsavel,telefoneAtual:i.telefone,etapaFunil:i.etapa,dadosLead:n.dadosLeadAtual})):(console.log("Username espec\xedfico n\xe3o encontrado na lista de leads"),n.leadEncontrado=!1,n.dadosLeadAtual=void 0)}else console.log("Nenhum lead encontrado na resposta da API"),n.leadEncontrado=!1,n.dadosLeadAtual=void 0}catch(a){console.error("Erro ao buscar lead via LeadService:",a),n.leadEncontrado=!1,n.dadosLeadAtual=void 0}finally{n.carregandoLead=!1,console.log("Estado final da busca - leadEncontrado:",n.leadEncontrado,"carregandoLead:",n.carregandoLead),!n.leadEncontrado&&n.username&&(console.log("Lead n\xe3o encontrado, redirecionando para novo lead"),n.router.navigate(["/crm/novo-lead"],{queryParams:{username:n.username}}))}})()}converterLeadParaDadosLead(t){console.log("CRM-HOME: Dados completos do lead recebido:",t),console.log("CRM-HOME: Campo empresa:",t.empresa),console.log("CRM-HOME: Campo crmEmpresa:",t.crmEmpresa),console.log("CRM-HOME: Campos dispon\xedveis:",Object.keys(t));let n=[];t.links&&Array.isArray(t.links)&&(n=t.links.map(s=>({tipo:s.tipo,url:s.url,descricao:s.descricao,ordem:s.ordem})).sort((s,c)=>s.ordem-c.ordem));let a="";t.empresa&&t.empresa.trim()?(a=t.empresa.trim(),console.log("CRM-HOME: Usando lead.empresa:",a)):t.crmEmpresa?.nome&&t.crmEmpresa.nome.trim()?(a=t.crmEmpresa.nome.trim(),console.log("CRM-HOME: Usando lead.crmEmpresa.nome:",a)):t.instagramHandle?(a=t.instagramHandle.replace("@","").replace(/[_.-]/g," ").replace(/\b\w/g,s=>s.toUpperCase()),console.log("CRM-HOME: Usando Instagram handle formatado como empresa:",a)):(a="Empresa n\xe3o informada",console.log("CRM-HOME: Nenhum nome de empresa encontrado, usando fallback"));const i={nome:t.nomeResponsavel,telefone:t.telefone,email:t.crmEmpresa?.email||"",cargo:"Propriet\xe1rio",empresa:a,segmento:t.segmento||"Alimenta\xe7\xe3o",tamanhoEmpresa:"Pequena",localizacao:t.instagramData?.location||"",instagram:t.instagramHandle?`@${t.instagramHandle}`:"",site:t.linkInsta||t.instagramData?.website||"",dataPrimeiroContato:t.dataCriacao?new Date(t.dataCriacao).toLocaleDateString():"",ultimaInteracao:t.dataUltimaInteracao?new Date(t.dataUltimaInteracao).toLocaleDateString():(new Date).toLocaleDateString(),origemLead:t.origem,scoreLead:t.score,etapaFunil:t.etapa,interessesProdutos:[],proximoFollowUp:t.dataProximoFollowup?new Date(t.dataProximoFollowup).toLocaleDateString():"",observacoes:t.observacoes||"",notas:t.notas||"",links:n};return console.log("CRM-HOME: Dados convertidos para exibi\xe7\xe3o:",i),i}inicializarModoDemonstracao(){this.leadEncontrado=!0,this.simularNovasMensagens()}debugEstado(){console.log("=== Estado atual do componente ==="),console.log("username:",this.username),console.log("carregandoLead:",this.carregandoLead),console.log("leadEncontrado:",this.leadEncontrado),console.log("dadosLeadAtual:",this.dadosLeadAtual)}}return o.\u0275fac=function(t){return new(t||o)(e.\u0275\u0275directiveInject(ie),e.\u0275\u0275directiveInject(b.gz),e.\u0275\u0275directiveInject(b.F0),e.\u0275\u0275directiveInject(j))},o.\u0275cmp=e.\u0275\u0275defineComponent({type:o,selectors:[["app-crm-home"]],decls:18,vars:3,consts:[[1,"crm-home-container"],[1,"modern-header"],[1,"header-brand"],[1,"brand-icon"],[1,"fa","fa-users"],[1,"brand-text"],[1,"brand-title"],[1,"brand-subtitle"],[1,"header-actions"],[1,"status-indicator"],[1,"status-dot","active"],[1,"status-text"],["class","loading-container",4,"ngIf"],["class","lead-detail-layout",4,"ngIf"],[1,"loading-container"],[1,"loading-card"],[1,"loading-animation"],[1,"spinner-modern"],[1,"loading-title"],[1,"loading-text"],[1,"loading-card","warning"],[1,"warning-icon"],[1,"fa","fa-exclamation-triangle"],[1,"lead-detail-layout"],[1,"hero-card"],[1,"hero-content"],[1,"hero-main"],[1,"company-info"],[1,"company-name"],[1,"contact-person"],[1,"fa","fa-user"],[1,"status-badges"],[1,"score-badge",3,"ngStyle"],[1,"fa","fa-star"],[1,"stage-badge",3,"ngClass"],[1,"fa","fa-flag"],[1,"hero-actions"],["class","action-btn primary","target","_blank","title","Enviar WhatsApp",3,"href",4,"ngIf"],["class","action-btn secondary","title","Ligar agora",3,"href",4,"ngIf"],["class","action-btn secondary","title","Enviar email",3,"href",4,"ngIf"],[1,"info-grid"],[1,"info-card"],[1,"card-header"],[1,"fa","fa-address-book"],[1,"card-content"],["class","info-item",4,"ngIf"],[1,"fa","fa-building"],["class","timeline-card",4,"ngIf"],["class","links-card",4,"ngIf"],["class","website-card",4,"ngIf"],["class","notes-card",4,"ngIf"],["class","interests-card",4,"ngIf"],["target","_blank","title","Enviar WhatsApp",1,"action-btn","primary",3,"href"],[1,"fab","fa-whatsapp"],["title","Ligar agora",1,"action-btn","secondary",3,"href"],[1,"fa","fa-phone"],["title","Enviar email",1,"action-btn","secondary",3,"href"],[1,"fa","fa-envelope"],[1,"info-item"],[1,"info-icon"],[1,"info-details"],[1,"info-label"],[1,"info-value"],[1,"info-icon","instagram"],[1,"fab","fa-instagram"],["target","_blank",1,"info-value","link",3,"href"],[1,"fa","fa-tag"],[1,"fa","fa-external-link"],[1,"fa","fa-map-marker"],[1,"timeline-card"],[1,"fa","fa-clock-o"],[1,"timeline-content"],["class","timeline-item",4,"ngIf"],["class","timeline-item priority",4,"ngIf"],[1,"timeline-item"],[1,"timeline-icon"],[1,"fa","fa-handshake-o"],[1,"timeline-details"],[1,"timeline-label"],[1,"timeline-value"],[1,"fa","fa-comments"],[1,"timeline-item","priority"],[1,"fa","fa-calendar"],[1,"timeline-value","highlight"],[1,"links-card"],[1,"fa","fa-link"],[1,"links-grid"],["target","_blank","class","link-item",3,"href","title",4,"ngFor","ngForOf"],["target","_blank",1,"link-item",3,"href","title"],[1,"link-icon"],[3,"ngClass"],[1,"link-content"],[1,"link-type"],[1,"link-description"],[1,"link-action"],[1,"website-card"],["target","_blank",1,"website-link",3,"href"],[1,"website-icon"],[1,"fa","fa-globe"],[1,"website-content"],[1,"website-label"],[1,"website-url"],[1,"website-action"],[1,"notes-card"],[1,"fa","fa-sticky-note"],[1,"notes-content"],["class","note-section",4,"ngIf"],[1,"note-section"],[1,"note-header"],[1,"fa","fa-edit"],[1,"note-text"],[1,"fa","fa-file-text"],[1,"interests-card"],[1,"interests-content"],["class","interest-tag",4,"ngFor","ngForOf"],[1,"interest-tag"]],template:function(t,n){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3),e.\u0275\u0275element(4,"i",4),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",5)(6,"h1",6),e.\u0275\u0275text(7,"Assistente de Vendas"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"span",7),e.\u0275\u0275text(9,"Card\xe1pioTech CRM"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(10,"div",8)(11,"div",9),e.\u0275\u0275element(12,"span",10),e.\u0275\u0275elementStart(13,"span",11),e.\u0275\u0275text(14,"Online"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275template(15,at,8,1,"div",12),e.\u0275\u0275template(16,it,8,1,"div",12),e.\u0275\u0275template(17,kt,49,23,"div",13),e.\u0275\u0275elementEnd()),2&t&&(e.\u0275\u0275advance(15),e.\u0275\u0275property("ngIf",n.carregandoLead),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!n.carregandoLead&&!n.leadEncontrado&&n.username),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!n.carregandoLead&&n.leadEncontrado&&n.dadosLeadAtual))},dependencies:[m.NgClass,m.NgForOf,m.NgIf,m.NgStyle],styles:['@charset "UTF-8";.crm-home-container[_ngcontent-%COMP%]{min-height:100vh;background:#f8fafc;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif}.modern-header[_ngcontent-%COMP%]{background:#2563eb;color:#fff;padding:20px 30px;display:flex;justify-content:space-between;align-items:center;box-shadow:0 2px 4px #0000001a}.modern-header[_ngcontent-%COMP%]   .header-brand[_ngcontent-%COMP%]{display:flex;align-items:center;gap:15px}.modern-header[_ngcontent-%COMP%]   .header-brand[_ngcontent-%COMP%]   .brand-icon[_ngcontent-%COMP%]{width:50px;height:50px;background:#1d4ed8;border-radius:8px;display:flex;align-items:center;justify-content:center;font-size:24px}.modern-header[_ngcontent-%COMP%]   .header-brand[_ngcontent-%COMP%]   .brand-text[_ngcontent-%COMP%]   .brand-title[_ngcontent-%COMP%]{margin:0;font-size:24px;font-weight:700;color:#fff}.modern-header[_ngcontent-%COMP%]   .header-brand[_ngcontent-%COMP%]   .brand-text[_ngcontent-%COMP%]   .brand-subtitle[_ngcontent-%COMP%]{font-size:14px;color:#fff;opacity:.9}.modern-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;background:rgba(255,255,255,.1);padding:8px 16px;border-radius:20px}.modern-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%]{width:8px;height:8px;border-radius:50%;background:#22c55e}.modern-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]   .status-dot.active[_ngcontent-%COMP%]{animation:pulse 2s infinite}.modern-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{font-size:14px;font-weight:500}@keyframes pulse{0%{opacity:1}50%{opacity:.5}to{opacity:1}}.loading-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;padding:60px 20px}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%], .loading-container[_ngcontent-%COMP%]   .loading-card.warning[_ngcontent-%COMP%]{background:white;border-radius:16px;padding:40px;text-align:center;box-shadow:0 4px 20px #0000001a;max-width:400px}.loading-container[_ngcontent-%COMP%]   .loading-card.warning[_ngcontent-%COMP%], .loading-container[_ngcontent-%COMP%]   .loading-card.warning.warning[_ngcontent-%COMP%]{border-left:4px solid #f59e0b}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-animation[_ngcontent-%COMP%], .loading-container[_ngcontent-%COMP%]   .loading-card.warning[_ngcontent-%COMP%]   .loading-animation[_ngcontent-%COMP%]{margin-bottom:20px}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-animation[_ngcontent-%COMP%]   .spinner-modern[_ngcontent-%COMP%], .loading-container[_ngcontent-%COMP%]   .loading-card.warning[_ngcontent-%COMP%]   .loading-animation[_ngcontent-%COMP%]   .spinner-modern[_ngcontent-%COMP%]{width:40px;height:40px;border:3px solid #f3f4f6;border-top:3px solid #3b82f6;border-radius:50%;animation:spin 1s linear infinite}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .warning-icon[_ngcontent-%COMP%], .loading-container[_ngcontent-%COMP%]   .loading-card.warning[_ngcontent-%COMP%]   .warning-icon[_ngcontent-%COMP%]{font-size:48px;color:#f59e0b;margin-bottom:20px}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-title[_ngcontent-%COMP%], .loading-container[_ngcontent-%COMP%]   .loading-card.warning[_ngcontent-%COMP%]   .loading-title[_ngcontent-%COMP%]{font-size:20px;font-weight:600;margin-bottom:10px;color:#1f2937}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-text[_ngcontent-%COMP%], .loading-container[_ngcontent-%COMP%]   .loading-card.warning[_ngcontent-%COMP%]   .loading-text[_ngcontent-%COMP%]{color:#6b7280;margin:0}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.lead-detail-layout[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:30px 20px;display:flex;flex-direction:column;gap:24px}.hero-card[_ngcontent-%COMP%]{background:white;border-radius:20px;padding:32px;box-shadow:0 4px 20px #00000014;border:1px solid #e5e7eb}.hero-card[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;gap:32px}@media (max-width: 768px){.hero-card[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]{flex-direction:column;gap:24px}}.hero-card[_ngcontent-%COMP%]   .hero-main[_ngcontent-%COMP%]{flex:1;display:flex;justify-content:space-between;align-items:flex-start;gap:24px}@media (max-width: 768px){.hero-card[_ngcontent-%COMP%]   .hero-main[_ngcontent-%COMP%]{flex-direction:column;width:100%}}.hero-card[_ngcontent-%COMP%]   .company-info[_ngcontent-%COMP%]   .company-name[_ngcontent-%COMP%]{font-size:32px;font-weight:700;color:#1f2937;margin:0 0 12px;line-height:1.2}@media (max-width: 768px){.hero-card[_ngcontent-%COMP%]   .company-info[_ngcontent-%COMP%]   .company-name[_ngcontent-%COMP%]{font-size:28px}}.hero-card[_ngcontent-%COMP%]   .company-info[_ngcontent-%COMP%]   .contact-person[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;color:#6b7280;font-size:16px}.hero-card[_ngcontent-%COMP%]   .company-info[_ngcontent-%COMP%]   .contact-person[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#9ca3af}.hero-card[_ngcontent-%COMP%]   .status-badges[_ngcontent-%COMP%]{display:flex;gap:12px;flex-shrink:0}@media (max-width: 768px){.hero-card[_ngcontent-%COMP%]   .status-badges[_ngcontent-%COMP%]{justify-content:flex-start}}.hero-card[_ngcontent-%COMP%]   .status-badges[_ngcontent-%COMP%]   .score-badge[_ngcontent-%COMP%], .hero-card[_ngcontent-%COMP%]   .status-badges[_ngcontent-%COMP%]   .stage-badge[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;padding:8px 16px;border-radius:20px;font-weight:600;font-size:14px;white-space:nowrap}.hero-card[_ngcontent-%COMP%]   .status-badges[_ngcontent-%COMP%]   .score-badge[_ngcontent-%COMP%]{color:#fff;background:#059669}.hero-card[_ngcontent-%COMP%]   .status-badges[_ngcontent-%COMP%]   .score-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}.hero-card[_ngcontent-%COMP%]   .status-badges[_ngcontent-%COMP%]   .stage-badge[_ngcontent-%COMP%]{background:#f3f4f6;color:#374151}.hero-card[_ngcontent-%COMP%]   .status-badges[_ngcontent-%COMP%]   .stage-badge.stage-prospec\\e7\\e3o[_ngcontent-%COMP%]{background:#dbeafe;color:#1d4ed8}.hero-card[_ngcontent-%COMP%]   .status-badges[_ngcontent-%COMP%]   .stage-badge.stage-qualifica\\e7\\e3o[_ngcontent-%COMP%]{background:#fef3c7;color:#92400e}.hero-card[_ngcontent-%COMP%]   .status-badges[_ngcontent-%COMP%]   .stage-badge.stage-obje\\e7\\e3o[_ngcontent-%COMP%]{background:#fed7d7;color:#c53030}.hero-card[_ngcontent-%COMP%]   .status-badges[_ngcontent-%COMP%]   .stage-badge.stage-fechamento[_ngcontent-%COMP%]{background:#d1fae5;color:#065f46}.hero-card[_ngcontent-%COMP%]   .status-badges[_ngcontent-%COMP%]   .stage-badge.stage-ganho[_ngcontent-%COMP%]{background:#dcfce7;color:#166534}.hero-card[_ngcontent-%COMP%]   .status-badges[_ngcontent-%COMP%]   .stage-badge.stage-perdido[_ngcontent-%COMP%]{background:#fee2e2;color:#dc2626}.hero-card[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]{display:flex;gap:12px;flex-shrink:0}@media (max-width: 768px){.hero-card[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]{width:100%;justify-content:flex-start}}.hero-card[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:12px 20px;border-radius:12px;text-decoration:none;font-weight:600;font-size:14px;transition:all .2s ease;white-space:nowrap}.hero-card[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]   .action-btn.primary[_ngcontent-%COMP%]{background:#25d366;color:#fff;box-shadow:0 2px 8px #25d3664d}.hero-card[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]   .action-btn.primary[_ngcontent-%COMP%]:hover{background:#20ba5a;transform:translateY(-1px);box-shadow:0 4px 12px #25d36666;text-decoration:none;color:#fff}.hero-card[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]{background:#f8fafc;color:#475569;border:1px solid #e2e8f0}.hero-card[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]:hover{background:#f1f5f9;border-color:#cbd5e1;transform:translateY(-1px);text-decoration:none;color:#475569}.hero-card[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}.info-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:24px}@media (max-width: 768px){.info-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}.info-card[_ngcontent-%COMP%], .timeline-card[_ngcontent-%COMP%], .links-card[_ngcontent-%COMP%], .website-card[_ngcontent-%COMP%], .notes-card[_ngcontent-%COMP%], .interests-card[_ngcontent-%COMP%]{background:white;border-radius:16px;border:1px solid #e5e7eb;box-shadow:0 2px 8px #0000000a;transition:all .2s ease}.info-card[_ngcontent-%COMP%]:hover, .timeline-card[_ngcontent-%COMP%]:hover, .links-card[_ngcontent-%COMP%]:hover, .website-card[_ngcontent-%COMP%]:hover, .notes-card[_ngcontent-%COMP%]:hover, .interests-card[_ngcontent-%COMP%]:hover{box-shadow:0 4px 12px #00000014;border-color:#d1d5db}.info-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .timeline-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .links-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .website-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .notes-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .interests-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{padding:20px 24px 16px;border-bottom:1px solid #f3f4f6;display:flex;align-items:center;gap:12px}.info-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .timeline-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .links-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .website-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .notes-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .interests-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:18px;color:#2563eb}.info-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .timeline-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .links-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .website-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .notes-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .interests-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:18px;font-weight:600;color:#1f2937}.info-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%], .timeline-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%], .links-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%], .website-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%], .notes-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%], .interests-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]{padding:20px 24px}.info-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;padding:12px 0}.info-item[_ngcontent-%COMP%]:not(:last-child){border-bottom:1px solid #f9fafb}.info-item[_ngcontent-%COMP%]   .info-icon[_ngcontent-%COMP%]{width:40px;height:40px;background:#f8fafc;border-radius:10px;display:flex;align-items:center;justify-content:center;flex-shrink:0}.info-item[_ngcontent-%COMP%]   .info-icon.instagram[_ngcontent-%COMP%]{background:#f3f4f6;color:#e1306c}.info-item[_ngcontent-%COMP%]   .info-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:18px;color:#6b7280}.info-item[_ngcontent-%COMP%]   .info-details[_ngcontent-%COMP%]{flex:1;min-width:0}.info-item[_ngcontent-%COMP%]   .info-details[_ngcontent-%COMP%]   .info-label[_ngcontent-%COMP%]{display:block;font-size:12px;font-weight:500;color:#9ca3af;text-transform:uppercase;letter-spacing:.5px;margin-bottom:4px}.info-item[_ngcontent-%COMP%]   .info-details[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]{display:block;font-size:15px;font-weight:500;color:#374151;word-break:break-all}.info-item[_ngcontent-%COMP%]   .info-details[_ngcontent-%COMP%]   .info-value.link[_ngcontent-%COMP%]{color:#6366f1;text-decoration:none}.info-item[_ngcontent-%COMP%]   .info-details[_ngcontent-%COMP%]   .info-value.link[_ngcontent-%COMP%]:hover{text-decoration:underline}.timeline-content[_ngcontent-%COMP%]{padding:20px 24px}.timeline-content[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;padding:16px 0}.timeline-content[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]:not(:last-child){border-bottom:1px solid #f9fafb}.timeline-content[_ngcontent-%COMP%]   .timeline-item.priority[_ngcontent-%COMP%]{background:#fef7ee;margin:0 -24px;padding:16px 24px;border-radius:12px}.timeline-content[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-icon[_ngcontent-%COMP%]{width:40px;height:40px;background:#f0f9ff;border-radius:10px;display:flex;align-items:center;justify-content:center;flex-shrink:0}.timeline-content[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px;color:#0ea5e9}.timeline-content[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-details[_ngcontent-%COMP%]{flex:1}.timeline-content[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-details[_ngcontent-%COMP%]   .timeline-label[_ngcontent-%COMP%]{display:block;font-size:12px;font-weight:500;color:#9ca3af;text-transform:uppercase;letter-spacing:.5px;margin-bottom:4px}.timeline-content[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-details[_ngcontent-%COMP%]   .timeline-value[_ngcontent-%COMP%]{display:block;font-size:15px;font-weight:500;color:#374151}.timeline-content[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-details[_ngcontent-%COMP%]   .timeline-value.highlight[_ngcontent-%COMP%]{color:#ea580c;font-weight:600}.links-grid[_ngcontent-%COMP%]{padding:20px 24px;display:flex;flex-direction:column;gap:12px}.links-grid[_ngcontent-%COMP%]   .link-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;padding:16px;background:#f8fafc;border-radius:12px;text-decoration:none;transition:all .2s ease}.links-grid[_ngcontent-%COMP%]   .link-item[_ngcontent-%COMP%]:hover{background:#f1f5f9;transform:translateY(-1px);box-shadow:0 2px 8px #00000014}.links-grid[_ngcontent-%COMP%]   .link-item[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{width:40px;height:40px;background:white;border-radius:10px;display:flex;align-items:center;justify-content:center;flex-shrink:0}.links-grid[_ngcontent-%COMP%]   .link-item[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:18px}.links-grid[_ngcontent-%COMP%]   .link-item[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%]{flex:1;min-width:0}.links-grid[_ngcontent-%COMP%]   .link-item[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%]   .link-type[_ngcontent-%COMP%]{display:block;font-size:14px;font-weight:600;color:#374151;margin-bottom:2px}.links-grid[_ngcontent-%COMP%]   .link-item[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%]   .link-description[_ngcontent-%COMP%]{display:block;font-size:13px;color:#6b7280;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.links-grid[_ngcontent-%COMP%]   .link-item[_ngcontent-%COMP%]   .link-action[_ngcontent-%COMP%]{color:#9ca3af;font-size:14px}.website-card[_ngcontent-%COMP%]   .website-link[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;padding:20px 24px;text-decoration:none;transition:all .2s ease}.website-card[_ngcontent-%COMP%]   .website-link[_ngcontent-%COMP%]:hover{background:#f8fafc}.website-card[_ngcontent-%COMP%]   .website-link[_ngcontent-%COMP%]   .website-icon[_ngcontent-%COMP%]{width:48px;height:48px;background:#2563eb;border-radius:12px;display:flex;align-items:center;justify-content:center;color:#fff}.website-card[_ngcontent-%COMP%]   .website-link[_ngcontent-%COMP%]   .website-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:20px}.website-card[_ngcontent-%COMP%]   .website-link[_ngcontent-%COMP%]   .website-content[_ngcontent-%COMP%]{flex:1;min-width:0}.website-card[_ngcontent-%COMP%]   .website-link[_ngcontent-%COMP%]   .website-content[_ngcontent-%COMP%]   .website-label[_ngcontent-%COMP%]{display:block;font-size:12px;font-weight:500;color:#9ca3af;text-transform:uppercase;letter-spacing:.5px;margin-bottom:4px}.website-card[_ngcontent-%COMP%]   .website-link[_ngcontent-%COMP%]   .website-content[_ngcontent-%COMP%]   .website-url[_ngcontent-%COMP%]{display:block;font-size:15px;font-weight:500;color:#6366f1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.website-card[_ngcontent-%COMP%]   .website-link[_ngcontent-%COMP%]   .website-action[_ngcontent-%COMP%]{color:#9ca3af;font-size:16px}.notes-content[_ngcontent-%COMP%]{padding:20px 24px}.notes-content[_ngcontent-%COMP%]   .note-section[_ngcontent-%COMP%]:not(:last-child){margin-bottom:24px;padding-bottom:24px;border-bottom:1px solid #f3f4f6}.notes-content[_ngcontent-%COMP%]   .note-section[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:12px}.notes-content[_ngcontent-%COMP%]   .note-section[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px;color:#6366f1}.notes-content[_ngcontent-%COMP%]   .note-section[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#374151}.notes-content[_ngcontent-%COMP%]   .note-section[_ngcontent-%COMP%]   .note-text[_ngcontent-%COMP%]{margin:0;font-size:14px;line-height:1.6;color:#4b5563;background:#f8fafc;padding:16px;border-radius:8px;border-left:3px solid #e5e7eb}.interests-content[_ngcontent-%COMP%]{padding:20px 24px;display:flex;flex-wrap:wrap;gap:8px}.interests-content[_ngcontent-%COMP%]   .interest-tag[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:6px;padding:6px 12px;background:#f0f9ff;color:#0369a1;border-radius:16px;font-size:13px;font-weight:500}.interests-content[_ngcontent-%COMP%]   .interest-tag[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}@media (max-width: 768px){.lead-detail-layout[_ngcontent-%COMP%]{padding:20px 16px;gap:20px}.modern-header[_ngcontent-%COMP%]{padding:16px 20px}.modern-header[_ngcontent-%COMP%]   .header-brand[_ngcontent-%COMP%]   .brand-icon[_ngcontent-%COMP%]{width:40px;height:40px;font-size:20px}.modern-header[_ngcontent-%COMP%]   .header-brand[_ngcontent-%COMP%]   .brand-text[_ngcontent-%COMP%]   .brand-title[_ngcontent-%COMP%]{font-size:20px}.modern-header[_ngcontent-%COMP%]   .header-brand[_ngcontent-%COMP%]   .brand-text[_ngcontent-%COMP%]   .brand-subtitle[_ngcontent-%COMP%]{font-size:13px}.hero-card[_ngcontent-%COMP%]{padding:24px 20px}.hero-card[_ngcontent-%COMP%]   .company-info[_ngcontent-%COMP%]   .company-name[_ngcontent-%COMP%]{font-size:24px}.hero-card[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{padding:10px 16px;font-size:13px}.hero-card[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:none}.info-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .timeline-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .links-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .website-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .notes-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .interests-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{padding:16px 20px 12px}.info-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .timeline-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .links-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .website-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .notes-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .interests-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .info-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .timeline-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .links-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .website-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .notes-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .interests-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:16px}.info-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%], .timeline-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%], .links-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%], .website-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%], .notes-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%], .interests-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]{padding:16px 20px}}']}),o})();var It=l(40114);let T=(()=>{class o extends re.N{constructor(t){super(t),this.http=t,this.endpoint="/crm/empresas"}liste(t={}){return this.obtenha(this.endpoint,t)}selecione(t){return this.obtenha(`${this.endpoint}/${t}`,{})}salveEmpresa(t){return this.salve(this.endpoint,t)}removaEmpresa(t){return this.remova(`${this.endpoint}/${t}`,{})}}return o.\u0275fac=function(t){return new(t||o)(e.\u0275\u0275inject(y.eN))},o.\u0275prov=e.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac}),o})();function Lt(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",10),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.novo())}),e.\u0275\u0275element(1,"i",11),e.\u0275\u0275text(2," Novo Lead "),e.\u0275\u0275elementEnd()}}function jt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"option",25),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=r.$implicit;e.\u0275\u0275property("value",t.valor),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(t.texto)}}function Tt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"option",25),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=r.$implicit;e.\u0275\u0275property("value",t.valor),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(t.texto)}}function At(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",12)(1,"div",13)(2,"div",14)(3,"div",15)(4,"label")(5,"strong"),e.\u0275\u0275text(6,"Etapa"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"select",16),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.filtroEtapa=a)})("ngModelChange",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.aplicarFiltros())}),e.\u0275\u0275elementStart(8,"option",17),e.\u0275\u0275text(9,"Todas as Etapas"),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(10,jt,2,2,"option",18),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(11,"div",15)(12,"label")(13,"strong"),e.\u0275\u0275text(14,"Origem"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(15,"select",16),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.filtroOrigem=a)})("ngModelChange",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.aplicarFiltros())}),e.\u0275\u0275elementStart(16,"option",17),e.\u0275\u0275text(17,"Todas as Origens"),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(18,Tt,2,2,"option",18),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(19,"div",19)(20,"label")(21,"strong"),e.\u0275\u0275text(22,"Buscar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(23,"input",20),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.filtroTexto=a)})("ngModelChange",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.aplicarFiltros())}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(24,"div",21)(25,"label")(26,"strong"),e.\u0275\u0275text(27,"Pend\xeancias"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(28,"div",22)(29,"input",23),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.filtroPendencias=a)})("ngModelChange",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.aplicarFiltros())}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(30,"label",24),e.\u0275\u0275text(31,"S\xf3 pendentes"),e.\u0275\u0275elementEnd()()()()()()}if(2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(7),e.\u0275\u0275property("ngModel",t.filtroEtapa),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngForOf",t.etapas),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngModel",t.filtroOrigem),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngForOf",t.origens),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngModel",t.filtroTexto),e.\u0275\u0275advance(6),e.\u0275\u0275property("ngModel",t.filtroPendencias)}}function zt(o,r){1&o&&e.\u0275\u0275element(0,"i",88)}function Nt(o,r){1&o&&e.\u0275\u0275element(0,"i",89)}function Dt(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",76)(1,"div",77)(2,"h6",27),e.\u0275\u0275element(3,"i",78),e.\u0275\u0275text(4," Buscar Dados do Instagram "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"div",79)(6,"div",14)(7,"div",80)(8,"div",81)(9,"label",82)(10,"strong"),e.\u0275\u0275text(11,"Username do Instagram"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(12,"div",54)(13,"div",55)(14,"span",56),e.\u0275\u0275text(15,"@"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(16,"input",83),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.instagramUsername=a)}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(17,"div",19)(18,"label"),e.\u0275\u0275text(19,"\xa0"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(20,"div",32)(21,"button",84),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.buscarDadosInstagram())}),e.\u0275\u0275template(22,zt,1,0,"i",85),e.\u0275\u0275template(23,Nt,1,0,"i",86),e.\u0275\u0275text(24),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(25,"small",44),e.\u0275\u0275element(26,"i",87),e.\u0275\u0275text(27," Digite o username (sem @) para buscar automaticamente os dados do perfil e criar/vincular a empresa. "),e.\u0275\u0275elementEnd()()()}if(2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(16),e.\u0275\u0275property("ngModel",t.instagramUsername)("disabled",t.buscandoInstagram),e.\u0275\u0275advance(5),e.\u0275\u0275property("disabled",t.buscandoInstagram||!t.instagramUsername),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.buscandoInstagram),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.buscandoInstagram),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.buscandoInstagram?"Buscando...":"Buscar Dados"," ")}}function Ft(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"option",42),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=r.$implicit;e.\u0275\u0275property("ngValue",t),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.nome," ")}}function Vt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"option",25),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=r.$implicit;e.\u0275\u0275property("value",t.valor),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.texto," ")}}function Rt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"option",25),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=r.$implicit;e.\u0275\u0275property("value",t.valor),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.texto," ")}}function $t(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"option",25),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=r.$implicit;e.\u0275\u0275property("value",t.valor),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.texto," ")}}function Bt(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",14)(1,"div",62)(2,"div",32)(3,"label",90)(4,"strong"),e.\u0275\u0275text(5,"Bio do Instagram"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(6,"textarea",91),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.leadSelecionado.bioInsta=a)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(7,"small",44),e.\u0275\u0275element(8,"i",87),e.\u0275\u0275text(9," Bio extra\xedda automaticamente do perfil do Instagram "),e.\u0275\u0275elementEnd()()()()}if(2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(6),e.\u0275\u0275property("ngModel",t.leadSelecionado.bioInsta)}}function Ut(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",112),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.descricao," ")}}function Ht(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",100)(1,"div",101)(2,"div",102)(3,"div",103)(4,"div",104),e.\u0275\u0275element(5,"i",105),e.\u0275\u0275elementStart(6,"div")(7,"strong"),e.\u0275\u0275text(8),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(9,"br"),e.\u0275\u0275elementStart(10,"small",44),e.\u0275\u0275text(11),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(12,Ut,2,1,"div",106),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(13,"div",107)(14,"button",108),e.\u0275\u0275listener("click",function(){const i=e.\u0275\u0275restoreView(t).$implicit,s=e.\u0275\u0275nextContext(4);return e.\u0275\u0275resetView(s.abrirLink(i.url,i.tipo))}),e.\u0275\u0275element(15,"i",109),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(16,"button",110),e.\u0275\u0275listener("click",function(){const i=e.\u0275\u0275restoreView(t).index,s=e.\u0275\u0275nextContext(4);return e.\u0275\u0275resetView(s.removerLink(i))}),e.\u0275\u0275element(17,"i",111),e.\u0275\u0275elementEnd()()()()()()}if(2&o){const t=r.$implicit,n=e.\u0275\u0275nextContext(4);e.\u0275\u0275advance(5),e.\u0275\u0275styleProp("color",n.getTipoLinkInfo(t.tipo).cor),e.\u0275\u0275property("ngClass",n.getTipoLinkInfo(t.tipo).icone),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(n.getTipoLinkInfo(t.tipo).texto),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.url),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.descricao)}}function Wt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",98)(1,"h6"),e.\u0275\u0275text(2,"Links Cadastrados:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",14),e.\u0275\u0275template(4,Ht,18,6,"div",99),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(4),e.\u0275\u0275property("ngForOf",t.leadSelecionado.links)}}function Jt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"option",25),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=r.$implicit;e.\u0275\u0275property("value",t.valor),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.texto," ")}}function Gt(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",13),e.\u0275\u0275template(1,Wt,5,1,"div",92),e.\u0275\u0275elementStart(2,"div",93)(3,"h6"),e.\u0275\u0275text(4,"Adicionar Novo Link:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",14)(6,"div",15)(7,"div",32)(8,"label"),e.\u0275\u0275text(9,"Tipo"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"select",94),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.novoLink.tipo=a)}),e.\u0275\u0275elementStart(11,"option",17),e.\u0275\u0275text(12,"Selecione..."),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(13,Jt,2,2,"option",18),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(14,"div",19)(15,"div",32)(16,"label"),e.\u0275\u0275text(17,"URL"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(18,"input",95),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.novoLink.url=a)}),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(19,"div",15)(20,"div",32)(21,"label"),e.\u0275\u0275text(22,"Descri\xe7\xe3o (opcional)"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(23,"input",96),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.novoLink.descricao=a)}),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(24,"div",21)(25,"div",32)(26,"label"),e.\u0275\u0275text(27,"\xa0"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(28,"button",97),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.adicionarLink())}),e.\u0275\u0275element(29,"i",11),e.\u0275\u0275text(30," Adicionar "),e.\u0275\u0275elementEnd()()()()()()}if(2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.leadSelecionado.links&&t.leadSelecionado.links.length>0),e.\u0275\u0275advance(9),e.\u0275\u0275property("ngModel",t.novoLink.tipo),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngForOf",t.tiposLink),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngModel",t.novoLink.url),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngModel",t.novoLink.descricao),e.\u0275\u0275advance(5),e.\u0275\u0275property("disabled",!t.novoLink.tipo||!t.novoLink.url)}}function qt(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",12)(1,"div",26)(2,"h5",27),e.\u0275\u0275element(3,"i",28),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"div",13)(6,"form",29,30),e.\u0275\u0275listener("ngSubmit",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.salvar())}),e.\u0275\u0275template(8,Dt,28,6,"div",31),e.\u0275\u0275elementStart(9,"div",14)(10,"div",19)(11,"div",32)(12,"label",33)(13,"strong"),e.\u0275\u0275text(14,"Nome do Respons\xe1vel *"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(15,"input",34),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.leadSelecionado.nomeResponsavel=a)}),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(16,"div",19)(17,"div",32)(18,"label",35)(19,"strong"),e.\u0275\u0275text(20,"Empresa/Estabelecimento *"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(21,"input",36),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.leadSelecionado.empresa=a)}),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(22,"div",19)(23,"div",32)(24,"label",37)(25,"strong"),e.\u0275\u0275text(26,"Telefone/WhatsApp *"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(27,"input",38),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.leadSelecionado.telefone=a)})("ngModelChange",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.formatarTelefone())}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(28,"div",14)(29,"div",39)(30,"div",32)(31,"label",40)(32,"strong"),e.\u0275\u0275text(33,"Empresa CRM"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(34,"select",41),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.leadSelecionado.crmEmpresa=a)}),e.\u0275\u0275elementStart(35,"option",42),e.\u0275\u0275text(36,"Selecione uma empresa CRM..."),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(37,Ft,2,2,"option",43),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(38,"small",44),e.\u0275\u0275text(39,"Empresa no sistema CRM que este lead ser\xe1 vinculado"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(40,"div",14)(41,"div",15)(42,"div",32)(43,"label",45)(44,"strong"),e.\u0275\u0275text(45,"Etapa do Funil"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(46,"select",46),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.leadSelecionado.etapa=a)}),e.\u0275\u0275template(47,Vt,2,2,"option",18),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(48,"div",15)(49,"div",32)(50,"label",47)(51,"strong"),e.\u0275\u0275text(52,"Origem"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(53,"select",48),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.leadSelecionado.origem=a)}),e.\u0275\u0275template(54,Rt,2,2,"option",18),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(55,"div",15)(56,"div",32)(57,"label",49)(58,"strong"),e.\u0275\u0275text(59,"Segmento"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(60,"select",50),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.leadSelecionado.segmento=a)}),e.\u0275\u0275elementStart(61,"option",17),e.\u0275\u0275text(62,"Selecione..."),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(63,$t,2,2,"option",18),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(64,"div",15)(65,"div",32)(66,"label",51)(67,"strong"),e.\u0275\u0275text(68,"Score (0-100)"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(69,"input",52),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.leadSelecionado.score=a)}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(70,"div",14)(71,"div",19)(72,"div",32)(73,"label",53)(74,"strong"),e.\u0275\u0275text(75,"Instagram"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(76,"div",54)(77,"div",55)(78,"span",56),e.\u0275\u0275text(79,"@"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(80,"input",57),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.leadSelecionado.instagramHandle=a)})("ngModelChange",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.formatarInstagram())}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(81,"div",19)(82,"div",32)(83,"label",58)(84,"strong"),e.\u0275\u0275text(85,"Link da Bio"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(86,"input",59),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.leadSelecionado.linkInsta=a)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(87,"small",44),e.\u0275\u0275text(88,"Link externo da biografia do Instagram"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(89,"div",19)(90,"div",32)(91,"label",60)(92,"strong"),e.\u0275\u0275text(93,"Valor Potencial (R$)"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(94,"input",61),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.leadSelecionado.valorPotencial=a)}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275template(95,Bt,10,1,"div",9),e.\u0275\u0275elementStart(96,"div",14)(97,"div",62)(98,"div",32)(99,"label",63)(100,"strong"),e.\u0275\u0275text(101,"Observa\xe7\xf5es"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(102,"textarea",64),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.leadSelecionado.notas=a)}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(103,"div",14)(104,"div",62)(105,"div",65)(106,"div",66)(107,"h6",27),e.\u0275\u0275element(108,"i",67),e.\u0275\u0275text(109,"Links do Lead "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(110,"button",68),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.toggleSecaoLinks())}),e.\u0275\u0275element(111,"i",69),e.\u0275\u0275text(112),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(113,Gt,31,6,"div",70),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(114,"div",71)(115,"button",72),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.cancelar())}),e.\u0275\u0275element(116,"i",73),e.\u0275\u0275text(117," Cancelar "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(118,"button",74),e.\u0275\u0275element(119,"i",75),e.\u0275\u0275text(120," Salvar "),e.\u0275\u0275elementEnd()()()()()}if(2&o){const t=e.\u0275\u0275reference(7),n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate1(" ",n.leadSelecionado.id?"Editar Lead":"Novo Lead"," "),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngIf",!n.leadSelecionado.id),e.\u0275\u0275advance(7),e.\u0275\u0275property("ngModel",n.leadSelecionado.nomeResponsavel),e.\u0275\u0275advance(6),e.\u0275\u0275property("ngModel",n.leadSelecionado.empresa),e.\u0275\u0275advance(6),e.\u0275\u0275property("ngModel",n.leadSelecionado.telefone),e.\u0275\u0275advance(7),e.\u0275\u0275property("ngModel",n.leadSelecionado.crmEmpresa),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngValue",null),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",n.crmEmpresas),e.\u0275\u0275advance(9),e.\u0275\u0275property("ngModel",n.leadSelecionado.etapa),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",n.etapas),e.\u0275\u0275advance(6),e.\u0275\u0275property("ngModel",n.leadSelecionado.origem),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",n.origens),e.\u0275\u0275advance(6),e.\u0275\u0275property("ngModel",n.leadSelecionado.segmento),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngForOf",n.segmentos),e.\u0275\u0275advance(6),e.\u0275\u0275property("ngModel",n.leadSelecionado.score),e.\u0275\u0275advance(11),e.\u0275\u0275property("ngModel",n.leadSelecionado.instagramHandle),e.\u0275\u0275advance(6),e.\u0275\u0275property("ngModel",n.leadSelecionado.linkInsta),e.\u0275\u0275advance(8),e.\u0275\u0275property("ngModel",n.leadSelecionado.valorPotencial),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.leadSelecionado.bioInsta),e.\u0275\u0275advance(7),e.\u0275\u0275property("ngModel",n.leadSelecionado.notas),e.\u0275\u0275advance(9),e.\u0275\u0275classProp("fa-chevron-down",!n.mostrarSecaoLinks)("fa-chevron-up",n.mostrarSecaoLinks),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",n.mostrarSecaoLinks?"Ocultar":"Mostrar"," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.mostrarSecaoLinks),e.\u0275\u0275advance(5),e.\u0275\u0275property("disabled",t.invalid)}}function Yt(o,r){1&o&&(e.\u0275\u0275elementStart(0,"div",113),e.\u0275\u0275element(1,"i",114),e.\u0275\u0275elementStart(2,"p",115),e.\u0275\u0275text(3,"Carregando leads..."),e.\u0275\u0275elementEnd()())}function Qt(o,r){1&o&&(e.\u0275\u0275elementStart(0,"div",118)(1,"div",65)(2,"div",119),e.\u0275\u0275element(3,"i",120),e.\u0275\u0275elementStart(4,"h5",44),e.\u0275\u0275text(5,"Nenhum lead encontrado"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"p",44),e.\u0275\u0275text(7,"Adicione um novo lead ou ajuste os filtros"),e.\u0275\u0275elementEnd()()()())}function Xt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"span",155),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.segmento," ")}}function Zt(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",156)(1,"div",104),e.\u0275\u0275element(2,"i",157),e.\u0275\u0275elementStart(3,"span",158),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"div",3)(6,"button",159),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext().$implicit,i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.abrirLead(a))}),e.\u0275\u0275element(7,"i",160),e.\u0275\u0275elementStart(8,"span"),e.\u0275\u0275text(9,"Lead"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"a",161),e.\u0275\u0275element(11,"i",162),e.\u0275\u0275elementEnd()()()}if(2&o){const t=e.\u0275\u0275nextContext().$implicit,n=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(t.telefone),e.\u0275\u0275advance(6),e.\u0275\u0275property("href",n.getWhatsappUrl(t.telefone),e.\u0275\u0275sanitizeUrl)}}function Kt(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"a",168),e.\u0275\u0275element(1,"i",109),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext(2).$implicit;e.\u0275\u0275property("href",t.linkInsta,e.\u0275\u0275sanitizeUrl)}}function en(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",156)(1,"div",104),e.\u0275\u0275element(2,"i",163),e.\u0275\u0275elementStart(3,"span",158),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"div",164)(6,"a",165),e.\u0275\u0275element(7,"i",166),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(8,Kt,2,1,"a",167),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext().$implicit,n=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate1("@",t.instagramHandle,""),e.\u0275\u0275advance(2),e.\u0275\u0275property("href",n.getInstagramUrl(t.instagramHandle),e.\u0275\u0275sanitizeUrl),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",t.linkInsta)}}function tn(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",135),e.\u0275\u0275element(1,"i",169),e.\u0275\u0275elementStart(2,"strong",170),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext().$implicit,n=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(n.formatarValor(t.valorPotencial))}}function nn(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"span",171),e.\u0275\u0275element(1,"i",172),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",t.empresa," ")}}function on(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"a",175),e.\u0275\u0275element(1,"i"),e.\u0275\u0275elementStart(2,"span",176),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()()),2&o){const t=r.$implicit,n=e.\u0275\u0275nextContext(4);e.\u0275\u0275styleProp("background-color",n.getTipoLinkInfo(t.tipo).cor)("color","white"),e.\u0275\u0275property("href",n.formatarUrlLink(t.url,t.tipo),e.\u0275\u0275sanitizeUrl)("title",t.descricao||n.getTipoLinkInfo(t.tipo).texto),e.\u0275\u0275advance(1),e.\u0275\u0275classMapInterpolate1("fa ",n.getTipoLinkInfo(t.tipo).icone," mr-1"),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(n.getTipoLinkInfo(t.tipo).texto)}}function an(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",135)(1,"div",173),e.\u0275\u0275template(2,on,4,10,"a",174),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",t.links)}}function rn(o,r){1&o&&(e.\u0275\u0275elementStart(0,"div",177),e.\u0275\u0275element(1,"i",178),e.\u0275\u0275elementStart(2,"strong"),e.\u0275\u0275text(3,"Follow-up atrasado!"),e.\u0275\u0275elementEnd()())}const sn=function(){return["Instagram","WhatsApp Direto","Site/Landing Page","Indica\xe7\xe3o"]};function cn(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",121)(1,"div",122)(2,"div",123)(3,"div",103)(4,"div",104),e.\u0275\u0275element(5,"i"),e.\u0275\u0275elementStart(6,"span",124),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"div",104)(9,"span",125),e.\u0275\u0275text(10),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",126)(12,"button",127),e.\u0275\u0275element(13,"i",128),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(14,"div",129)(15,"a",130),e.\u0275\u0275listener("click",function(){const i=e.\u0275\u0275restoreView(t).$implicit,s=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(s.editar(i))}),e.\u0275\u0275element(16,"i",131),e.\u0275\u0275text(17," Editar "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(18,"a",132),e.\u0275\u0275listener("click",function(){const i=e.\u0275\u0275restoreView(t).$implicit,s=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(s.remover(i.id))}),e.\u0275\u0275element(19,"i",133),e.\u0275\u0275text(20," Remover "),e.\u0275\u0275elementEnd()()()()()(),e.\u0275\u0275elementStart(21,"div",134)(22,"div",135)(23,"h6",136),e.\u0275\u0275element(24,"i",137),e.\u0275\u0275text(25),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(26,"div",138)(27,"span",139),e.\u0275\u0275text(28),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(29,Xt,2,1,"span",140),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(30,"div",141),e.\u0275\u0275template(31,Zt,12,2,"div",142),e.\u0275\u0275template(32,en,9,3,"div",142),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(33,tn,4,1,"div",143),e.\u0275\u0275elementStart(34,"div",135),e.\u0275\u0275template(35,nn,3,1,"span",144),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(36,an,3,1,"div",143),e.\u0275\u0275template(37,rn,4,0,"div",145),e.\u0275\u0275elementStart(38,"div",146),e.\u0275\u0275element(39,"i",147),e.\u0275\u0275text(40),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(41,"div",148)(42,"div",149)(43,"div",150)(44,"button",151),e.\u0275\u0275listener("click",function(){const i=e.\u0275\u0275restoreView(t).$implicit,s=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(s.editar(i))}),e.\u0275\u0275element(45,"i",152),e.\u0275\u0275text(46," Editar "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(47,"div",153)(48,"button",154),e.\u0275\u0275listener("click",function(){const i=e.\u0275\u0275restoreView(t).$implicit,s=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(s.abrirDetalhesLead(i))}),e.\u0275\u0275element(49,"i",87),e.\u0275\u0275text(50," Detalhes "),e.\u0275\u0275elementEnd()()()()()()}if(2&o){const t=r.$implicit,n=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275classProp("border-warning",n.isAtrasado(t))("border-success","Ganho"===t.etapa)("border-danger","Perdido"===t.etapa)("border-info","Fechamento"===t.etapa)("border-primary","Qualifica\xe7\xe3o"===t.etapa),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("bg-gradient-success","Ganho"===t.etapa)("bg-gradient-danger","Perdido"===t.etapa)("bg-gradient-warning",n.isAtrasado(t))("bg-gradient-info","Fechamento"===t.etapa)("bg-gradient-primary","Qualifica\xe7\xe3o"===t.etapa)("bg-gradient-secondary","Prospec\xe7\xe3o"===t.etapa),e.\u0275\u0275advance(3),e.\u0275\u0275classMapInterpolate1("fa ",n.getIconeEtapa(t.etapa)," mr-2"),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.etapa),e.\u0275\u0275advance(2),e.\u0275\u0275styleProp("background-color",n.getCorScore(t.score))("color","white"),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.score||0,"% "),e.\u0275\u0275advance(15),e.\u0275\u0275textInterpolate1(" ",t.nomeResponsavel," "),e.\u0275\u0275advance(2),e.\u0275\u0275classProp("badge-instagram","Instagram"===t.origem)("badge-whatsapp","WhatsApp Direto"===t.origem)("badge-site","Site/Landing Page"===t.origem)("badge-indicacao","Indica\xe7\xe3o"===t.origem)("badge-secondary",!e.\u0275\u0275pureFunction0(51,sn).includes(t.origem)),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.origem," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.segmento),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",t.telefone),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.instagramHandle),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.valorPotencial),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",t.empresa),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.links&&t.links.length>0),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.isAtrasado(t)),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",n.formatarData(t.dataCriacao)," ")}}function ln(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",14),e.\u0275\u0275template(1,Qt,8,0,"div",116),e.\u0275\u0275template(2,cn,51,52,"div",117),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",0===t.leadsFiltrados.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",t.leadsFiltrados)}}let dn=(()=>{class o{constructor(t,n,a){this.leadService=t,this.crmEmpresaService=n,this.router=a,this.leads=[],this.leadsFiltrados=[],this.leadSelecionado={},this.carregando=!1,this.modoEdicao=!1,this.crmEmpresas=[],this.mostrarFiltros=!1,this.filtroEtapa="",this.filtroOrigem="",this.filtroTexto="",this.filtroPendencias=!1,this.buscandoInstagram=!1,this.instagramUsername="",this.mostrarSecaoLinks=!1,this.novoLink={tipo:"",url:"",descricao:""},this.tiposLink=[{valor:"Ifood",texto:"iFood",icone:"fa-utensils",cor:"#ea1d2c"},{valor:"Site do Card\xe1pio",texto:"Site do Card\xe1pio",icone:"fa-list-alt",cor:"#007bff"},{valor:"Concorrente",texto:"Concorrente",icone:"fa-exclamation-triangle",cor:"#ff6b35"},{valor:"Reservas",texto:"Reservas",icone:"fa-calendar-check",cor:"#28a745"},{valor:"WhatsApp",texto:"WhatsApp",icone:"fa-whatsapp",cor:"#25d366"},{valor:"Localiza\xe7\xe3o",texto:"Localiza\xe7\xe3o",icone:"fa-map-marker-alt",cor:"#dc3545"},{valor:"Site",texto:"Site",icone:"fa-globe",cor:"#6c757d"},{valor:"Instagram",texto:"Instagram",icone:"fa-instagram",cor:"#e4405f"}],this.etapas=[{valor:"Prospec\xe7\xe3o",texto:"Prospec\xe7\xe3o"},{valor:"Qualifica\xe7\xe3o",texto:"Qualifica\xe7\xe3o"},{valor:"Obje\xe7\xe3o",texto:"Obje\xe7\xe3o"},{valor:"Fechamento",texto:"Fechamento"},{valor:"Ganho",texto:"Ganho"},{valor:"Perdido",texto:"Perdido"}],this.origens=[{valor:"Instagram",texto:"Instagram"},{valor:"Site/Landing Page",texto:"Site/Landing Page"},{valor:"WhatsApp Direto",texto:"WhatsApp Direto"},{valor:"Indica\xe7\xe3o",texto:"Indica\xe7\xe3o"},{valor:"Evento/Feira",texto:"Evento/Feira"},{valor:"Outros",texto:"Outros"}],this.segmentos=[{valor:"Restaurante",texto:"Restaurante"},{valor:"Pizzaria",texto:"Pizzaria"},{valor:"Lanchonete",texto:"Lanchonete"},{valor:"Hamburgueria",texto:"Hamburgueria"},{valor:"Confeitaria",texto:"Confeitaria/Doceria"},{valor:"Bar",texto:"Bar/Boteco"},{valor:"Food Truck",texto:"Food Truck"},{valor:"Outros",texto:"Outros"}]}ngOnInit(){this.carregarEmpresas().then(()=>{this.listar()})}carregarEmpresas(){return this.crmEmpresaService.liste({ativa:!0}).then(t=>{this.crmEmpresas=t.data?.data||t.data||[],console.log("Empresas CRM carregadas:",this.crmEmpresas)}).catch(t=>{console.error("Erro ao carregar empresas CRM:",t),alert("Erro ao carregar empresas CRM. Verifique se h\xe1 empresas cadastradas em /crm/empresas")})}listar(){this.carregando=!0,this.leadService.liste({inicio:0,total:100}).then(t=>{this.leads=t.data?.data||t.data||[],console.log("Leads carregados:",this.leads),console.log("CRM Empresas dispon\xedveis:",this.crmEmpresas),this.aplicarFiltros(),this.carregando=!1}).catch(()=>this.carregando=!1)}toggleFiltros(){this.mostrarFiltros=!this.mostrarFiltros}aplicarFiltros(){this.leadsFiltrados=this.leads.filter(t=>{if(this.filtroEtapa&&t.etapa!==this.filtroEtapa||this.filtroOrigem&&t.origem!==this.filtroOrigem)return!1;if(this.filtroTexto){const n=this.filtroTexto.toLowerCase();if(!(t.nomeResponsavel?.toLowerCase().includes(n)||t.telefone?.toLowerCase().includes(n)||t.instagramHandle?.toLowerCase().includes(n)))return!1}return!(this.filtroPendencias&&!this.isAtrasado(t))})}novo(){this.leadSelecionado={id:null,crmEmpresa:null,nomeResponsavel:"",empresa:"",telefone:"",instagramHandle:"",linkInsta:"",bioInsta:"",etapa:"Prospec\xe7\xe3o",origem:"Instagram",score:0,valorPotencial:0,segmento:"",notas:"",links:[]},this.instagramUsername="",this.modoEdicao=!0,this.mostrarSecaoLinks=!1,this.novoLink={tipo:"",url:"",descricao:""}}editar(t){console.log("Editando lead:",t),this.leadSelecionado={...t},this.leadSelecionado.score&&(this.leadSelecionado.score=parseInt(this.leadSelecionado.score)),this.leadSelecionado.valorPotencial&&(this.leadSelecionado.valorPotencial=parseFloat(this.leadSelecionado.valorPotencial)),!this.leadSelecionado.crmEmpresa&&this.leadSelecionado.crmEmpresaId&&(this.leadSelecionado.crmEmpresa=this.crmEmpresas.find(n=>n.id==this.leadSelecionado.crmEmpresaId)||null),this.leadSelecionado.links||(this.leadSelecionado.links=[]),this.leadSelecionado.id&&this.leadService.listarLinks(this.leadSelecionado.id).then(n=>{this.leadSelecionado.links=n||[],console.log("Links carregados:",this.leadSelecionado.links)}).catch(n=>{console.error("Erro ao carregar links:",n),this.leadSelecionado.links=[]}),console.log("Lead selecionado para edi\xe7\xe3o:",this.leadSelecionado),this.modoEdicao=!0,this.mostrarSecaoLinks=!1,this.novoLink={tipo:"",url:"",descricao:""}}cancelar(){this.leadSelecionado={},this.modoEdicao=!1}salvar(){this.leadSelecionado.score&&(this.leadSelecionado.score=parseInt(this.leadSelecionado.score)),this.leadSelecionado.valorPotencial&&(this.leadSelecionado.valorPotencial=parseFloat(this.leadSelecionado.valorPotencial));const t={...this.leadSelecionado,crmEmpresaId:this.leadSelecionado.crmEmpresa?.id||null};delete t.crmEmpresa,console.log("Dados sendo enviados:",t),console.log("Modo edi\xe7\xe3o - ID presente?",!!t.id),this.leadService.salveLead(t).then(n=>{console.log("Resposta do servidor:",n),alert("Lead salvo com sucesso!"),this.cancelar(),this.listar()}).catch(n=>{console.error("Erro completo:",n),console.error("Erro detalhado:",JSON.stringify(n,null,2));let a="Erro desconhecido";n.error&&n.error.mensagem?a=n.error.mensagem:n.message?a=n.message:"string"==typeof n&&(a=n),alert("Erro ao salvar lead: "+a)})}remover(t){!confirm("Deseja realmente remover este lead?")||this.leadService.removaLead(t).then(()=>this.listar())}avancarEtapa(t){const n=["Prospec\xe7\xe3o","Qualifica\xe7\xe3o","Obje\xe7\xe3o","Fechamento","Ganho"],a=n.indexOf(t.etapa);a>=0&&a<n.length-1&&(t.etapa=n[a+1],this.leadService.salveLead(t).then(()=>{this.listar()}).catch(i=>{console.error("Erro ao avan\xe7ar etapa:",i),alert("Erro ao avan\xe7ar etapa")}))}formatarTelefone(){if(this.leadSelecionado.telefone){let t=this.leadSelecionado.telefone.replace(/\D/g,"");t.length<=11&&(t=t.replace(/^(\d{2})(\d{5})(\d{4})/,"($1) $2-$3"),t=t.replace(/^(\d{2})(\d{4})(\d{4})/,"($1) $2-$3")),this.leadSelecionado.telefone=t}}formatarInstagram(){if(this.leadSelecionado.instagramHandle){let t=this.leadSelecionado.instagramHandle.replace("@","").trim();this.leadSelecionado.instagramHandle=t}}formatarValor(t){return t?t.toLocaleString("pt-BR",{style:"currency",currency:"BRL"}):"N/A"}formatarData(t){return t?("string"==typeof t?new Date(t):t).toLocaleDateString("pt-BR"):"N/A"}isAtrasado(t){return!!t.dataProximoFollowup&&new Date>new Date(t.dataProximoFollowup)}getCorScore(t){return t?t>=80?"#28a745":t>=50?"#ffc107":"#dc3545":"#6c757d"}getIconeEtapa(t){return{Prospec\u00e7\u00e3o:"fa-eye",Qualifica\u00e7\u00e3o:"fa-search",Obje\u00e7\u00e3o:"fa-exclamation-triangle",Fechamento:"fa-handshake",Ganho:"fa-check-circle",Perdido:"fa-times-circle"}[t]||"fa-circle"}getNomeCrmEmpresa(t){return t.crmEmpresa&&t.crmEmpresa.nome?t.crmEmpresa.nome:t.empresa?t.empresa:"N/A"}getWhatsappUrl(t){return t?`https://wa.me/55${t.replace(/\D/g,"")}`:"#"}getInstagramUrl(t){return t?`https://instagram.com/${t}`:"#"}abrirLead(t){this.editar(t)}abrirDetalhesLead(t){console.log("Abrindo detalhes do lead:",t),this.router.navigate(["/crm/home"],{queryParams:{leadId:t.id,nomeResponsavel:t.nomeResponsavel,empresa:this.getNomeCrmEmpresa(t),telefone:t.telefone,etapa:t.etapa,origem:t.origem}})}buscarDadosInstagram(){if(!this.instagramUsername||!this.instagramUsername.trim())return void alert("Digite um username do Instagram");const t=this.instagramUsername.replace("@","").trim();this.buscandoInstagram=!0,this.leadSelecionado||(this.leadSelecionado={}),console.log("Chamando buscarDadosInstagram com:",{username:t}),this.leadService.buscarDadosInstagram(t).then(n=>{console.log("Resposta completa do servi\xe7o:",n);const a=n;if(console.log("Lead Instagram extra\xeddo:",a),!a)throw new Error("Resposta do servidor n\xe3o cont\xe9m dados v\xe1lidos");if(this.leadSelecionado={...this.leadSelecionado,nomeResponsavel:a.nomeResponsavel||a.nome||"",empresa:a.empresa||"",telefone:a.telefone||"",instagramHandle:a.instagramHandle||t,linkInsta:a.linkInsta||"",bioInsta:a.bioInsta||a.bio||"",notas:a.notas||"",origem:"Instagram"},a.crmEmpresa){const i=this.crmEmpresas.find(s=>s.id===a.crmEmpresa.id);this.leadSelecionado.crmEmpresa=i||a.crmEmpresa}console.log("Lead selecionado ap\xf3s preenchimento:",this.leadSelecionado),this.leadSelecionado.bioInsta&&this.detectarSegmento(this.leadSelecionado.bioInsta),alert("Dados do Instagram carregados com sucesso!"),this.buscandoInstagram=!1}).catch(n=>{console.error("Erro ao buscar dados do Instagram:",n),alert("Erro ao buscar dados do Instagram: "+(n.message||"Erro desconhecido")),this.buscandoInstagram=!1})}detectarSegmento(t){if(!t)return;const n=t.toLowerCase();n.includes("pizza")||n.includes("pizzaria")?this.leadSelecionado.segmento="Pizzaria":n.includes("hambur")||n.includes("burger")?this.leadSelecionado.segmento="Hamburgueria":n.includes("lanche")||n.includes("sandu\xed")?this.leadSelecionado.segmento="Lanchonete":n.includes("doce")||n.includes("bolo")||n.includes("confeit")?this.leadSelecionado.segmento="Confeitaria":n.includes("bar")||n.includes("boteco")||n.includes("cervej")?this.leadSelecionado.segmento="Bar":n.includes("food truck")||n.includes("foodtruck")?this.leadSelecionado.segmento="Food Truck":(n.includes("restaurante")||n.includes("comida")||n.includes("culin\xe1ria"))&&(this.leadSelecionado.segmento="Restaurante")}toggleSecaoLinks(){this.mostrarSecaoLinks=!this.mostrarSecaoLinks}adicionarLink(){if(!this.novoLink.tipo||!this.novoLink.url)return void alert("Tipo e URL s\xe3o obrigat\xf3rios");this.leadSelecionado.links||(this.leadSelecionado.links=[]);const t=this.leadSelecionado.links.find(n=>n.tipo===this.novoLink.tipo);t?confirm("J\xe1 existe um link deste tipo. Deseja substituir?")&&(t.url=this.novoLink.url,t.descricao=this.novoLink.descricao):this.leadSelecionado.links.push({tipo:this.novoLink.tipo,url:this.novoLink.url,descricao:this.novoLink.descricao,ativo:!0,ordem:this.leadSelecionado.links.length}),this.novoLink={tipo:"",url:"",descricao:""}}removerLink(t){confirm("Deseja remover este link?")&&this.leadSelecionado.links.splice(t,1)}getTipoLinkInfo(t){return this.tiposLink.find(n=>n.valor===t)||{texto:t,icone:"fa-link",cor:"#6c757d"}}formatarUrlLink(t,n){if(!t)return"";switch(n){case"WhatsApp":if(t.includes("wa.me")||t.includes("whatsapp.com"))return t;const a=t.replace(/\D/g,"");return a.length>=10?`https://wa.me/55${a}`:t;case"Localiza\xe7\xe3o":return t.includes("maps.google.com")||t.includes("goo.gl/maps")?t:`https://maps.google.com/maps?q=${encodeURIComponent(t)}`;default:return t.startsWith("http")?t:`https://${t}`}}abrirLink(t,n){const a=this.formatarUrlLink(t,n);window.open(a,"_blank")}}return o.\u0275fac=function(t){return new(t||o)(e.\u0275\u0275directiveInject(j),e.\u0275\u0275directiveInject(T),e.\u0275\u0275directiveInject(b.F0))},o.\u0275cmp=e.\u0275\u0275defineComponent({type:o,selectors:[["app-lead-crud"]],decls:14,vars:5,consts:[[1,"container-fluid"],[1,"d-flex","justify-content-between","align-items-center","mb-4"],[1,"fa","fa-users"],[1,"d-flex"],[1,"btn","btn-outline-secondary","mr-2",3,"click"],[1,"fa","fa-filter"],["class","btn btn-primary",3,"click",4,"ngIf"],["class","card mb-4",4,"ngIf"],["class","text-center py-4",4,"ngIf"],["class","row",4,"ngIf"],[1,"btn","btn-primary",3,"click"],[1,"fa","fa-plus"],[1,"card","mb-4"],[1,"card-body"],[1,"row"],[1,"col-md-3"],[1,"form-control",3,"ngModel","ngModelChange"],["value",""],[3,"value",4,"ngFor","ngForOf"],[1,"col-md-4"],["type","text","placeholder","Nome, empresa ou telefone...",1,"form-control",3,"ngModel","ngModelChange"],[1,"col-md-2"],[1,"form-check"],["type","checkbox",1,"form-check-input",3,"ngModel","ngModelChange"],[1,"form-check-label"],[3,"value"],[1,"card-header"],[1,"mb-0"],[1,"fa","fa-edit"],[3,"ngSubmit"],["leadForm","ngForm"],["class","card mb-4 bg-light",4,"ngIf"],[1,"form-group"],["for","nomeResponsavel"],["type","text","id","nomeResponsavel","name","nomeResponsavel","required","","placeholder","Ex: Jo\xe3o Silva",1,"form-control",3,"ngModel","ngModelChange"],["for","empresa"],["type","text","id","empresa","name","empresa","required","","placeholder","Ex: Pizzaria do Jo\xe3o",1,"form-control",3,"ngModel","ngModelChange"],["for","telefone"],["type","text","id","telefone","name","telefone","required","","placeholder","(11) 99999-9999",1,"form-control",3,"ngModel","ngModelChange"],[1,"col-md-12"],["for","crmEmpresa"],["id","crmEmpresa","name","crmEmpresa",1,"form-control",3,"ngModel","ngModelChange"],[3,"ngValue"],[3,"ngValue",4,"ngFor","ngForOf"],[1,"text-muted"],["for","etapa"],["id","etapa","name","etapa",1,"form-control",3,"ngModel","ngModelChange"],["for","origem"],["id","origem","name","origem",1,"form-control",3,"ngModel","ngModelChange"],["for","segmento"],["id","segmento","name","segmento",1,"form-control",3,"ngModel","ngModelChange"],["for","score"],["type","number","id","score","name","score","min","0","max","100","placeholder","0",1,"form-control",3,"ngModel","ngModelChange"],["for","instagram"],[1,"input-group"],[1,"input-group-prepend"],[1,"input-group-text"],["type","text","id","instagram","name","instagramHandle","placeholder","usuario_instagram",1,"form-control",3,"ngModel","ngModelChange"],["for","linkInsta"],["type","url","id","linkInsta","name","linkInsta","placeholder","https://site.com.br",1,"form-control",3,"ngModel","ngModelChange"],["for","valorPotencial"],["type","number","id","valorPotencial","name","valorPotencial","step","0.01","placeholder","0,00",1,"form-control",3,"ngModel","ngModelChange"],[1,"col-12"],["for","notas"],["id","notas","name","notas","rows","3","placeholder","Observa\xe7\xf5es sobre o lead...",1,"form-control",3,"ngModel","ngModelChange"],[1,"card"],[1,"card-header","d-flex","justify-content-between","align-items-center"],[1,"fa","fa-link","mr-2"],["type","button",1,"btn","btn-sm","btn-outline-primary",3,"click"],[1,"fa"],["class","card-body",4,"ngIf"],[1,"d-flex","justify-content-end"],["type","button",1,"btn","btn-secondary","mr-2",3,"click"],[1,"fa","fa-times"],["type","submit",1,"btn","btn-success",3,"disabled"],[1,"fa","fa-save"],[1,"card","mb-4","bg-light"],[1,"card-header","py-2"],[1,"fab","fa-instagram","text-danger","mr-2"],[1,"card-body","py-3"],[1,"col-md-8"],[1,"form-group","mb-2"],["for","instagramUsername"],["type","text","id","instagramUsername","name","instagramUsername","placeholder","usuario_instagram",1,"form-control",3,"ngModel","disabled","ngModelChange"],["type","button",1,"btn","btn-info","btn-block",3,"disabled","click"],["class","fa fa-search mr-2",4,"ngIf"],["class","fa fa-spinner fa-spin mr-2",4,"ngIf"],[1,"fa","fa-info-circle","mr-1"],[1,"fa","fa-search","mr-2"],[1,"fa","fa-spinner","fa-spin","mr-2"],["for","bioInsta"],["id","bioInsta","name","bioInsta","rows","2","readonly","","placeholder","Bio extra\xedda do Instagram...",1,"form-control",3,"ngModel","ngModelChange"],["class","mb-3",4,"ngIf"],[1,"border-top","pt-3"],["name","novoLinkTipo",1,"form-control","form-control-sm",3,"ngModel","ngModelChange"],["type","text","name","novoLinkUrl","placeholder","https://exemplo.com",1,"form-control","form-control-sm",3,"ngModel","ngModelChange"],["type","text","name","novoLinkDescricao","placeholder","Descri\xe7\xe3o do link",1,"form-control","form-control-sm",3,"ngModel","ngModelChange"],["type","button",1,"btn","btn-success","btn-sm","btn-block",3,"disabled","click"],[1,"mb-3"],["class","col-md-6 mb-2",4,"ngFor","ngForOf"],[1,"col-md-6","mb-2"],[1,"card","border-left-primary"],[1,"card-body","p-2"],[1,"d-flex","justify-content-between","align-items-center"],[1,"d-flex","align-items-center"],[1,"fa","mr-2",3,"ngClass"],["class","text-muted","style","font-size: 0.8rem;",4,"ngIf"],[1,"btn-group"],["type","button","title","Abrir link",1,"btn","btn-sm","btn-outline-info",3,"click"],[1,"fa","fa-external-link-alt"],["type","button","title","Remover link",1,"btn","btn-sm","btn-outline-danger",3,"click"],[1,"fa","fa-trash"],[1,"text-muted",2,"font-size","0.8rem"],[1,"text-center","py-4"],[1,"fa","fa-spinner","fa-spin","fa-2x"],[1,"mt-2"],["class","col-12 mb-3",4,"ngIf"],["class","col-lg-6 col-xl-4 mb-3",4,"ngFor","ngForOf"],[1,"col-12","mb-3"],[1,"card-body","text-center","py-5"],[1,"fa","fa-users","fa-3x","text-muted","mb-3"],[1,"col-lg-6","col-xl-4","mb-3"],[1,"card","lead-card","h-100","shadow-sm"],[1,"card-header","py-2","px-3"],[1,"font-weight-light",2,"font-size","0.85rem"],[1,"badge","badge-light","badge-pill","mr-2","px-2",2,"font-size","0.7rem"],[1,"dropdown"],["data-toggle","dropdown",1,"btn","btn-sm","btn-link","p-0"],[1,"fa","fa-ellipsis-v"],[1,"dropdown-menu","dropdown-menu-right"],["href","#",1,"dropdown-item",3,"click"],[1,"fa","fa-edit","mr-2","text-primary"],["href","#",1,"dropdown-item","text-danger",3,"click"],[1,"fa","fa-trash","mr-2"],[1,"card-body","p-3"],[1,"mb-2"],[1,"card-title","mb-2","text-dark","font-weight-light",2,"font-size","1rem"],[1,"fa","fa-user","text-primary","mr-2"],[1,"d-flex","flex-wrap","mb-2"],[1,"badge","mr-1","mb-1","px-2","py-1",2,"font-size","0.7rem"],["class","badge badge-outline-info mb-1 px-2 py-1","style","font-size: 0.7rem;",4,"ngIf"],[1,"contact-info","mb-2"],["class","d-flex align-items-center justify-content-between mb-1",4,"ngIf"],["class","mb-2",4,"ngIf"],["class","badge badge-crm-empresa px-2 py-1","style","font-size: 0.7rem;",4,"ngIf"],["class","alert alert-warning py-1 px-2 mb-2","style","font-size: 0.7rem;",4,"ngIf"],[1,"text-muted","text-right",2,"font-size","0.7rem"],[1,"fa","fa-calendar","mr-1"],[1,"card-footer","p-2","bg-light"],[1,"row","no-gutters"],[1,"col-6","pr-1"],[1,"btn","btn-outline-primary","btn-sm","btn-block","py-1",2,"font-size","0.75rem",3,"click"],[1,"fa","fa-edit","mr-1"],[1,"col-6","pl-1"],[1,"btn","btn-outline-info","btn-sm","btn-block","py-1",2,"font-size","0.75rem",3,"click"],[1,"badge","badge-outline-info","mb-1","px-2","py-1",2,"font-size","0.7rem"],[1,"d-flex","align-items-center","justify-content-between","mb-1"],[1,"fa","fa-phone","text-success","mr-2",2,"font-size","0.8rem"],[2,"font-size","0.8rem"],["title","Abrir Lead",1,"btn","btn-open-lead","btn-xs","mr-1",3,"click"],[1,"fa","fa-external-link-alt","mr-1"],["target","_blank",1,"btn","btn-whatsapp","btn-xs",3,"href"],[1,"fa","fa-whatsapp"],[1,"fab","fa-instagram","text-danger","mr-2",2,"font-size","0.8rem"],[1,"d-flex","gap-1"],["target","_blank","title","Ver perfil",1,"btn","btn-instagram","btn-xs",3,"href"],[1,"fab","fa-instagram"],["target","_blank","class","btn btn-link btn-xs","title","Link da bio",3,"href",4,"ngIf"],["target","_blank","title","Link da bio",1,"btn","btn-link","btn-xs",3,"href"],[1,"fa","fa-dollar-sign","text-success","mr-1"],[1,"text-success",2,"font-size","0.8rem"],[1,"badge","badge-crm-empresa","px-2","py-1",2,"font-size","0.7rem"],[1,"fa","fa-building","mr-1"],[1,"d-flex","flex-wrap"],["target","_blank","class","btn btn-xs mr-1 mb-1",3,"href","background-color","color","title",4,"ngFor","ngForOf"],["target","_blank",1,"btn","btn-xs","mr-1","mb-1",3,"href","title"],[2,"font-size","0.7rem"],[1,"alert","alert-warning","py-1","px-2","mb-2",2,"font-size","0.7rem"],[1,"fa","fa-exclamation-triangle","mr-1"]],template:function(t,n){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"h3"),e.\u0275\u0275element(3,"i",2),e.\u0275\u0275text(4," Gerenciamento de Leads"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",3)(6,"button",4),e.\u0275\u0275listener("click",function(){return n.toggleFiltros()}),e.\u0275\u0275element(7,"i",5),e.\u0275\u0275text(8," Filtros "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(9,Lt,3,0,"button",6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(10,At,32,6,"div",7),e.\u0275\u0275template(11,qt,121,27,"div",7),e.\u0275\u0275template(12,Yt,4,0,"div",8),e.\u0275\u0275template(13,ln,3,2,"div",9),e.\u0275\u0275elementEnd()),2&t&&(e.\u0275\u0275advance(9),e.\u0275\u0275property("ngIf",!n.modoEdicao),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.mostrarFiltros),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.modoEdicao),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.carregando),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!n.modoEdicao&&!n.carregando))},dependencies:[m.NgClass,m.NgForOf,m.NgIf,d._Y,d.YN,d.Kr,d.Fj,d.wV,d.Wl,d.EJ,d.JJ,d.JL,d.Q7,d.qQ,d.Fd,d.On,d.F],styles:['@charset "UTF-8";.container-fluid[_ngcontent-%COMP%]{padding:1.5rem}.card[_ngcontent-%COMP%]{border-radius:8px;box-shadow:0 2px 8px #0000001a;border:1px solid #e9ecef}.card-header[_ngcontent-%COMP%]{background-color:#f8f9fa;border-bottom:1px solid #dee2e6;border-radius:8px 8px 0 0!important}.card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{color:#495057;font-weight:600}.form-group[_ngcontent-%COMP%]{margin-bottom:1.25rem}.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:#495057;font-size:.9rem;margin-bottom:.5rem}.form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{border-radius:4px;border:1px solid #ced4da;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out}.form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{border-color:#007bff;box-shadow:0 0 0 .2rem #007bff40}.form-group[_ngcontent-%COMP%]   .form-control.is-invalid[_ngcontent-%COMP%]{border-color:#dc3545}.form-group[_ngcontent-%COMP%]   select.form-control[_ngcontent-%COMP%]{height:38px}.input-group-text[_ngcontent-%COMP%]{background-color:#e9ecef;border-color:#ced4da;font-weight:500}.btn[_ngcontent-%COMP%]{font-weight:500;border-radius:4px}.btn.btn-primary[_ngcontent-%COMP%]{background-color:#007bff;border-color:#007bff}.btn.btn-success[_ngcontent-%COMP%]{background-color:#28a745;border-color:#28a745}.btn.btn-secondary[_ngcontent-%COMP%]{background-color:#6c757d;border-color:#6c757d}.k-grid[_ngcontent-%COMP%]{border-radius:4px;border:1px solid #dee2e6}.k-grid[_ngcontent-%COMP%]   .k-grid-header[_ngcontent-%COMP%]{background-color:#f8f9fa;border-bottom:2px solid #dee2e6}.k-grid[_ngcontent-%COMP%]   .k-grid-header[_ngcontent-%COMP%]   .k-header[_ngcontent-%COMP%]{font-weight:600;color:#495057;font-size:.9rem}.k-grid[_ngcontent-%COMP%]   .k-grid-content[_ngcontent-%COMP%]   .k-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.badge[_ngcontent-%COMP%]{font-size:.8rem;padding:.375rem .5rem;border-radius:4px}.badge.badge-primary[_ngcontent-%COMP%]{background-color:#007bff}.badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.25rem}@media (max-width: 768px){.container-fluid[_ngcontent-%COMP%]{padding:1rem}.d-flex.justify-content-between[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start!important;gap:1rem}.card-body[_ngcontent-%COMP%]{padding:1rem}.btn-group[_ngcontent-%COMP%]{width:100%}.btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{flex:1}}.k-loading-mask[_ngcontent-%COMP%]{background-color:#fffc}.card[_ngcontent-%COMP%], .btn[_ngcontent-%COMP%], .form-control[_ngcontent-%COMP%]{transition:all .2s ease-in-out}h3[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], h5[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.5rem;color:#007bff}.lead-card[_ngcontent-%COMP%]{transition:all .3s ease;border-radius:8px;box-shadow:0 2px 4px #0000001a}.lead-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 8px #00000026}.lead-card.border-success[_ngcontent-%COMP%]{border-left:4px solid #28a745}.lead-card.border-danger[_ngcontent-%COMP%]{border-left:4px solid #dc3545}.lead-card.border-warning[_ngcontent-%COMP%]{border-left:4px solid #ffc107}.lead-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{border-radius:8px 8px 0 0;font-weight:300;color:#fff}.lead-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .lead-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .lead-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%], .lead-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%], .lead-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]{color:#fff!important}.lead-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover{color:#fffc!important}.lead-card[_ngcontent-%COMP%]   .card-header.bg-success[_ngcontent-%COMP%]{background:linear-gradient(135deg,#28a745,#20c997)!important}.lead-card[_ngcontent-%COMP%]   .card-header.bg-danger[_ngcontent-%COMP%]{background:linear-gradient(135deg,#dc3545,#e83e8c)!important}.lead-card[_ngcontent-%COMP%]   .card-header.bg-warning[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffc107,#fd7e14)!important}.lead-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]{color:#2c3e50;font-weight:300;margin-bottom:.25rem}.lead-card[_ngcontent-%COMP%]   .card-subtitle[_ngcontent-%COMP%]{font-size:.95rem;font-weight:500}.lead-card[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.75rem;font-weight:500}.lead-card[_ngcontent-%COMP%]   .badge.badge-outline-primary[_ngcontent-%COMP%]{color:#007bff;border:1px solid #007bff;background:transparent}.lead-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]{border-top:1px solid #e9ecef;background-color:#f8f9fa!important;border-radius:0 0 8px 8px}.btn-sm[_ngcontent-%COMP%]{font-size:.8rem;padding:.25rem .5rem}.btn-sm.btn-outline-success[_ngcontent-%COMP%]:hover{background-color:#28a745;border-color:#28a745}.btn-sm.btn-outline-primary[_ngcontent-%COMP%]:hover{background-color:#007bff;border-color:#007bff}.fa-phone[_ngcontent-%COMP%]{color:#28a745}.fa-instagram[_ngcontent-%COMP%]{color:#e1306c}.fa-whatsapp[_ngcontent-%COMP%]{color:#25d366}.alert-warning[_ngcontent-%COMP%]{background-color:#fff3cd;border-color:#ffeaa7;color:#856404;border-radius:6px}.dropdown-menu[_ngcontent-%COMP%]{border-radius:6px;box-shadow:0 4px 8px #00000026;border:none}.dropdown-item[_ngcontent-%COMP%]{padding:.5rem 1rem;font-size:.9rem}.dropdown-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.dropdown-item.text-danger[_ngcontent-%COMP%]:hover{background-color:#f5c6cb;color:#721c24}.fa-spinner[_ngcontent-%COMP%]{color:#6c757d}.text-muted[_ngcontent-%COMP%]{color:#6c757d!important}@media (max-width: 768px){.lead-card[_ngcontent-%COMP%]{margin-bottom:1rem}.card-header[_ngcontent-%COMP%]{font-size:.9rem}.card-title[_ngcontent-%COMP%]{font-size:1.1rem}.btn-sm[_ngcontent-%COMP%]{font-size:.75rem;padding:.2rem .4rem}}@keyframes fadeIn{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.lead-card[_ngcontent-%COMP%]{animation:fadeIn .3s ease-out}.badge-pill[_ngcontent-%COMP%]{padding:.35em .65em;font-size:.75em;font-weight:600;border-radius:10rem}a[href*="wa.me"][_ngcontent-%COMP%]{text-decoration:none}a[href*="wa.me"][_ngcontent-%COMP%]:hover{transform:scale(1.05)}a[href*="instagram.com"][_ngcontent-%COMP%]{text-decoration:none}a[href*="instagram.com"][_ngcontent-%COMP%]:hover{transform:scale(1.05)}.badge-instagram[_ngcontent-%COMP%]{background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888)!important;color:#fff}.badge-whatsapp[_ngcontent-%COMP%]{background:linear-gradient(45deg,#25d366,#128c7e)!important;color:#fff}.badge-site[_ngcontent-%COMP%]{background:linear-gradient(45deg,#007bff,#0056b3)!important;color:#fff}.badge-indicacao[_ngcontent-%COMP%]{background:linear-gradient(45deg,#6f42c1,#563d7c)!important;color:#fff}.badge-outline-info[_ngcontent-%COMP%]{color:#17a2b8;border:1px solid #17a2b8;background:rgba(23,162,184,.1)}.btn-xs[_ngcontent-%COMP%]{padding:.15rem .3rem;font-size:.7rem;border-radius:4px;transition:all .2s ease}.btn-xs[_ngcontent-%COMP%]:hover{transform:scale(1.1)}.btn-whatsapp[_ngcontent-%COMP%]{background:linear-gradient(45deg,#25d366,#128c7e);color:#fff;border:none}.btn-whatsapp[_ngcontent-%COMP%]:hover{background:linear-gradient(45deg,#128c7e,#075e54);color:#fff}.btn-instagram[_ngcontent-%COMP%]{background:linear-gradient(45deg,#e1306c,#c13584);color:#fff;border:none}.btn-instagram[_ngcontent-%COMP%]:hover{background:linear-gradient(45deg,#c13584,#833ab4);color:#fff}.contact-info[_ngcontent-%COMP%]{background:rgba(0,0,0,.02);border-radius:6px;padding:.5rem;border:1px solid rgba(0,0,0,.05)}.card-header.bg-gradient-success[_ngcontent-%COMP%]{background:linear-gradient(135deg,#28a745,#20c997)!important}.card-header.bg-gradient-danger[_ngcontent-%COMP%]{background:linear-gradient(135deg,#dc3545,#e83e8c)!important}.card-header.bg-gradient-warning[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffc107,#fd7e14)!important}.card-header.bg-gradient-info[_ngcontent-%COMP%]{background:linear-gradient(135deg,#17a2b8,#6f42c1)!important}.card-header.bg-gradient-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#007bff,#6610f2)!important}.card-header.bg-gradient-secondary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#6c757d,#495057)!important}.lead-card.border-info[_ngcontent-%COMP%]{border-left:5px solid #17a2b8;border-color:#17a2b8}.lead-card.border-primary[_ngcontent-%COMP%]{border-left:5px solid #007bff;border-color:#007bff}.lead-card.border-success[_ngcontent-%COMP%]{border-left:5px solid #28a745;border-color:#28a745}.lead-card.border-danger[_ngcontent-%COMP%]{border-left:5px solid #dc3545;border-color:#dc3545}.lead-card.border-warning[_ngcontent-%COMP%]{border-left:5px solid #ffc107;border-color:#ffc107}.fa-dollar-sign[_ngcontent-%COMP%]{color:#ffc107!important}.fa-calendar[_ngcontent-%COMP%]{color:#6c757d!important}.alert-warning[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff3cd,#ffeaa7)!important;border:1px solid #ffeaa7!important;color:#856404!important;border-radius:6px!important;font-weight:600}.badge[_ngcontent-%COMP%]{transition:all .2s ease}.badge[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.btn-open-lead[_ngcontent-%COMP%]{background:linear-gradient(45deg,#007bff,#0056b3);color:#fff;border:none;font-size:.65rem}.btn-open-lead[_ngcontent-%COMP%]:hover{background:linear-gradient(45deg,#0056b3,#003d82);color:#fff;transform:scale(1.05)}.btn-open-lead[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:600}.badge-empresa[_ngcontent-%COMP%]{background:linear-gradient(45deg,#6c757d,#495057);color:#fff;border-radius:6px;font-weight:500}.badge-empresa[_ngcontent-%COMP%]   .fa-building[_ngcontent-%COMP%]{opacity:.8}.badge-crm-empresa[_ngcontent-%COMP%]{background:linear-gradient(45deg,#007bff,#0056b3);color:#fff;border-radius:6px;font-weight:500}.badge-crm-empresa[_ngcontent-%COMP%]   .fa-users[_ngcontent-%COMP%]{opacity:.9}.card-title[_ngcontent-%COMP%]   .fa-user[_ngcontent-%COMP%]{color:#007bff;font-size:.9rem;opacity:.8}']}),o})();function mn(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",6),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.nova())}),e.\u0275\u0275element(1,"i",7),e.\u0275\u0275text(2," Nova Empresa "),e.\u0275\u0275elementEnd()}}function pn(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",8)(1,"div",9)(2,"h5",10),e.\u0275\u0275element(3,"i",11),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"div",12)(6,"form",13,14),e.\u0275\u0275listener("ngSubmit",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.salvar())}),e.\u0275\u0275elementStart(8,"div",15)(9,"div",16)(10,"div",17)(11,"label",18)(12,"strong"),e.\u0275\u0275text(13,"Nome da Empresa *"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(14,"input",19),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.empresaSelecionada.nome=a)}),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(15,"div",20)(16,"div",17)(17,"label",21)(18,"strong"),e.\u0275\u0275text(19,"CNPJ"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(20,"input",22),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.empresaSelecionada.cnpj=a)})("ngModelChange",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.formatarCnpj())}),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(21,"div",20)(22,"div",17)(23,"label",23)(24,"strong"),e.\u0275\u0275text(25,"Status"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(26,"select",24),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.empresaSelecionada.ativa=a)}),e.\u0275\u0275elementStart(27,"option",25),e.\u0275\u0275text(28,"Ativa"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(29,"option",25),e.\u0275\u0275text(30,"Inativa"),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(31,"div",15)(32,"div",16)(33,"div",17)(34,"label",26)(35,"strong"),e.\u0275\u0275text(36,"Telefone"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(37,"input",27),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.empresaSelecionada.telefone=a)}),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(38,"div",16)(39,"div",17)(40,"label",28)(41,"strong"),e.\u0275\u0275text(42,"E-mail"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(43,"input",29),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.empresaSelecionada.email=a)}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(44,"div",15)(45,"div",30)(46,"div",17)(47,"label",31)(48,"strong"),e.\u0275\u0275text(49,"Endere\xe7o"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(50,"textarea",32),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.empresaSelecionada.endereco=a)}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(51,"div",33)(52,"button",34),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.cancelar())}),e.\u0275\u0275element(53,"i",35),e.\u0275\u0275text(54," Cancelar "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(55,"button",36),e.\u0275\u0275element(56,"i",37),e.\u0275\u0275text(57," Salvar "),e.\u0275\u0275elementEnd()()()()()}if(2&o){const t=e.\u0275\u0275reference(7),n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate1(" ",n.empresaSelecionada.id?"Editar Empresa":"Nova Empresa"," "),e.\u0275\u0275advance(10),e.\u0275\u0275property("ngModel",n.empresaSelecionada.nome),e.\u0275\u0275advance(6),e.\u0275\u0275property("ngModel",n.empresaSelecionada.cnpj),e.\u0275\u0275advance(6),e.\u0275\u0275property("ngModel",n.empresaSelecionada.ativa),e.\u0275\u0275advance(1),e.\u0275\u0275property("value",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("value",!1),e.\u0275\u0275advance(8),e.\u0275\u0275property("ngModel",n.empresaSelecionada.telefone),e.\u0275\u0275advance(6),e.\u0275\u0275property("ngModel",n.empresaSelecionada.email),e.\u0275\u0275advance(7),e.\u0275\u0275property("ngModel",n.empresaSelecionada.endereco),e.\u0275\u0275advance(5),e.\u0275\u0275property("disabled",t.invalid)}}function gn(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"strong"),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=r.$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(t.nome)}}function _n(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"span",48),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=r.$implicit;e.\u0275\u0275classMap(t.ativa?"badge-success":"badge-secondary"),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.ativa?"Ativa":"Inativa"," ")}}function un(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",49),e.\u0275\u0275listener("click",function(){const i=e.\u0275\u0275restoreView(t).$implicit,s=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(s.editar(i))}),e.\u0275\u0275element(1,"i",11),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(2,"button",50),e.\u0275\u0275listener("click",function(){const i=e.\u0275\u0275restoreView(t).$implicit,s=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(s.remover(i.id))}),e.\u0275\u0275element(3,"i",51),e.\u0275\u0275elementEnd()}}function fn(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",38)(1,"div",9)(2,"h5",10),e.\u0275\u0275element(3,"i",39),e.\u0275\u0275text(4," Lista de Empresas"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"div",12)(6,"kendo-grid",40)(7,"kendo-grid-column",41),e.\u0275\u0275template(8,gn,2,1,"ng-template",42),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(9,"kendo-grid-column",43)(10,"kendo-grid-column",44)(11,"kendo-grid-column",45),e.\u0275\u0275elementStart(12,"kendo-grid-column",46),e.\u0275\u0275template(13,_n,2,3,"ng-template",42),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(14,"kendo-grid-column",47),e.\u0275\u0275template(15,un,4,0,"ng-template",42),e.\u0275\u0275elementEnd()()()()),2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(6),e.\u0275\u0275property("data",t.empresas)("loading",t.carregando)("height",500)("pageable",!0)("sortable",!0)("filterable",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("width",250),e.\u0275\u0275advance(2),e.\u0275\u0275property("width",150),e.\u0275\u0275advance(1),e.\u0275\u0275property("width",130),e.\u0275\u0275advance(1),e.\u0275\u0275property("width",200),e.\u0275\u0275advance(1),e.\u0275\u0275property("width",80),e.\u0275\u0275advance(2),e.\u0275\u0275property("width",120)}}let Cn=(()=>{class o{constructor(t){this.crmEmpresaService=t,this.empresas=[],this.empresaSelecionada={},this.carregando=!1,this.modoEdicao=!1}ngOnInit(){this.listar()}listar(){this.carregando=!0,this.crmEmpresaService.liste({inicio:0,total:100}).then(t=>{this.empresas=t.data||t,this.carregando=!1}).catch(()=>this.carregando=!1)}nova(){this.empresaSelecionada={ativa:!0},this.modoEdicao=!0}editar(t){this.empresaSelecionada={...t},this.modoEdicao=!0}cancelar(){this.empresaSelecionada={},this.modoEdicao=!1}salvar(){this.crmEmpresaService.salveEmpresa(this.empresaSelecionada).then(()=>{this.cancelar(),this.listar()}).catch(t=>{alert("Erro ao salvar: "+t)})}remover(t){!confirm("Deseja realmente remover esta empresa?")||this.crmEmpresaService.removaEmpresa(t).then(()=>this.listar())}formatarCnpj(){if(this.empresaSelecionada.cnpj){let t=this.empresaSelecionada.cnpj.replace(/\D/g,"");t=t.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/,"$1.$2.$3/$4-$5"),this.empresaSelecionada.cnpj=t}}}return o.\u0275fac=function(t){return new(t||o)(e.\u0275\u0275directiveInject(T))},o.\u0275cmp=e.\u0275\u0275defineComponent({type:o,selectors:[["app-crm-empresa-crud"]],decls:8,vars:3,consts:[[1,"container-fluid"],[1,"d-flex","justify-content-between","align-items-center","mb-3"],[1,"fa","fa-building"],["class","btn btn-primary",3,"click",4,"ngIf"],["class","card mb-4",4,"ngIf"],["class","card",4,"ngIf"],[1,"btn","btn-primary",3,"click"],[1,"fa","fa-plus"],[1,"card","mb-4"],[1,"card-header"],[1,"mb-0"],[1,"fa","fa-edit"],[1,"card-body"],[3,"ngSubmit"],["empresaForm","ngForm"],[1,"row"],[1,"col-md-6"],[1,"form-group"],["for","nome"],["type","text","id","nome","name","nome","required","","placeholder","Ex: MeuCard\xe1pio LTDA",1,"form-control",3,"ngModel","ngModelChange"],[1,"col-md-3"],["for","cnpj"],["type","text","id","cnpj","name","cnpj","placeholder","00.000.000/0001-00",1,"form-control",3,"ngModel","ngModelChange"],["for","ativa"],["id","ativa","name","ativa",1,"form-control",3,"ngModel","ngModelChange"],[3,"value"],["for","telefone"],["type","text","id","telefone","name","telefone","placeholder","(11) 99999-9999",1,"form-control",3,"ngModel","ngModelChange"],["for","email"],["type","email","id","email","name","email","placeholder","<EMAIL>",1,"form-control",3,"ngModel","ngModelChange"],[1,"col-12"],["for","endereco"],["id","endereco","name","endereco","rows","2","placeholder","Endere\xe7o completo...",1,"form-control",3,"ngModel","ngModelChange"],[1,"d-flex","justify-content-end"],["type","button",1,"btn","btn-secondary","mr-2",3,"click"],[1,"fa","fa-times"],["type","submit",1,"btn","btn-success",3,"disabled"],[1,"fa","fa-save"],[1,"card"],[1,"fa","fa-list"],[3,"data","loading","height","pageable","sortable","filterable"],["field","nome","title","Nome",3,"width"],["kendoGridCellTemplate",""],["field","cnpj","title","CNPJ",3,"width"],["field","telefone","title","Telefone",3,"width"],["field","email","title","E-mail",3,"width"],["field","ativa","title","Status",3,"width"],["title","A\xe7\xf5es",3,"width"],[1,"badge"],["title","Editar",1,"btn","btn-sm","btn-outline-primary","mr-1",3,"click"],["title","Remover",1,"btn","btn-sm","btn-outline-danger",3,"click"],[1,"fa","fa-trash"]],template:function(t,n){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"h3"),e.\u0275\u0275element(3,"i",2),e.\u0275\u0275text(4," Empresas CRM"),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(5,mn,3,0,"button",3),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(6,pn,58,10,"div",4),e.\u0275\u0275template(7,fn,16,12,"div",5),e.\u0275\u0275elementEnd()),2&t&&(e.\u0275\u0275advance(5),e.\u0275\u0275property("ngIf",!n.modoEdicao),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.modoEdicao),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!n.modoEdicao))},dependencies:[m.NgIf,d._Y,d.YN,d.Kr,d.Fj,d.EJ,d.JJ,d.JL,d.Q7,d.On,d.F,L.MwP,L.bg4,L.Ztq],styles:['@charset "UTF-8";.container-fluid[_ngcontent-%COMP%]{padding:1rem}.card[_ngcontent-%COMP%]{border-radius:8px;box-shadow:0 2px 8px #0000001a;border:1px solid #e9ecef}.card-header[_ngcontent-%COMP%]{background-color:#f8f9fa;border-bottom:1px solid #dee2e6;border-radius:8px 8px 0 0!important}.card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{color:#495057;font-weight:600}']}),o})(),ce=(()=>{class o{constructor(){this.dadosInstagram=null,this.username=""}setDados(t,n){this.dadosInstagram=t,this.username=n,console.log("Dados do Instagram salvos no service:",{username:n,dados:t})}getDados(){return this.dadosInstagram?{dados:this.dadosInstagram,username:this.username}:null}clearDados(){this.dadosInstagram=null,this.username="",console.log("Dados do Instagram limpos do service")}hasDados(){return null!==this.dadosInstagram}}return o.\u0275fac=function(t){return new(t||o)},o.\u0275prov=e.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})();function hn(o,r){1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1,"Extrair"),e.\u0275\u0275elementEnd())}function vn(o,r){1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1,"Links"),e.\u0275\u0275elementEnd())}function xn(o,r){1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1,"CNPJ"),e.\u0275\u0275elementEnd())}function bn(o,r){1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1,"S\xf3cios"),e.\u0275\u0275elementEnd())}function Mn(o,r){1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1,"Finalizar"),e.\u0275\u0275elementEnd())}function Pn(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",30),e.\u0275\u0275listener("click",function(){const i=e.\u0275\u0275restoreView(t).$implicit,s=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(s.goToStep(i))}),e.\u0275\u0275elementStart(1,"div",31),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",32),e.\u0275\u0275template(4,hn,2,0,"span",33),e.\u0275\u0275template(5,vn,2,0,"span",33),e.\u0275\u0275template(6,xn,2,0,"span",33),e.\u0275\u0275template(7,bn,2,0,"span",33),e.\u0275\u0275template(8,Mn,2,0,"span",33),e.\u0275\u0275elementEnd()()}if(2&o){const t=r.$implicit,n=e.\u0275\u0275nextContext();e.\u0275\u0275classMap(n.getStepClass(t)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",1===t),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",2===t),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",3===t),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",4===t),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",5===t)}}function On(o,r){1&o&&e.\u0275\u0275element(0,"i",34)}function yn(o,r){1&o&&e.\u0275\u0275element(0,"i",35)}function Sn(o,r){1&o&&e.\u0275\u0275element(0,"i",36)}function En(o,r){1&o&&e.\u0275\u0275element(0,"i",37)}function wn(o,r){1&o&&e.\u0275\u0275element(0,"i",38)}function kn(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",39)(1,"div",2)(2,"div",40),e.\u0275\u0275element(3,"i",41),e.\u0275\u0275elementStart(4,"input",42),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.searchTerm=a)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"button",43),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.buscarEmpresaNoGoogle())}),e.\u0275\u0275text(6," Ver no Google "),e.\u0275\u0275elementEnd()()()()}if(2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(4),e.\u0275\u0275property("ngModel",t.searchTerm),e.\u0275\u0275advance(1),e.\u0275\u0275property("disabled",!(null!=t.lead.empresa&&t.lead.empresa.trim()))}}function In(o,r){1&o&&e.\u0275\u0275element(0,"i",65)}function Ln(o,r){1&o&&e.\u0275\u0275element(0,"i",66)}function jn(o,r){1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1,"Extrair Dados"),e.\u0275\u0275elementEnd())}function Tn(o,r){1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1,"Extraindo..."),e.\u0275\u0275elementEnd())}function An(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"p",78),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(t.dadosInstagram.user.full_name)}}function zn(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",79)(1,"h6",80),e.\u0275\u0275element(2,"i",81),e.\u0275\u0275text(3," Biografia do Perfil "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",82),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate1(" ",t.dadosInstagram.user.biography," ")}}function Nn(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",3)(1,"div",4)(2,"div",67)(3,"div",46)(4,"h6",47),e.\u0275\u0275element(5,"i",68),e.\u0275\u0275text(6," Dados Extra\xeddos do Instagram "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(7,"div",69)(8,"div",70)(9,"div",71)(10,"h6",72),e.\u0275\u0275element(11,"i",73),e.\u0275\u0275text(12),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(13,An,2,1,"p",74),e.\u0275\u0275elementStart(14,"div",75)(15,"span",76),e.\u0275\u0275element(16,"i",37),e.\u0275\u0275elementStart(17,"strong"),e.\u0275\u0275text(18),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(19," seguidores "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(20,"span",76),e.\u0275\u0275element(21,"i",34),e.\u0275\u0275elementStart(22,"strong"),e.\u0275\u0275text(23),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(24," seguindo "),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275template(25,zn,6,1,"div",77),e.\u0275\u0275elementEnd()()()()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(12),e.\u0275\u0275textInterpolate1(" @",t.dadosInstagram.user.username,""),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.dadosInstagram.user.full_name),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate(t.getSeguidoresFormatado()),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate((null==t.dadosInstagram.user.edge_follow?null:t.dadosInstagram.user.edge_follow.count)||0),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",t.dadosInstagram.user.biography)}}function Dn(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",84)(1,"div",50)(2,"label",102),e.\u0275\u0275text(3,"Biografia do Instagram"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"textarea",103),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(i.lead.bioInsta=a)}),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()()}if(2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(4),e.\u0275\u0275property("ngModel",t.lead.bioInsta),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(t.dadosInstagram.user.biography)}}function Fn(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",84)(1,"div",50)(2,"label",104),e.\u0275\u0275text(3,"Observa\xe7\xf5es"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"textarea",105),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(i.lead.observacoes=a)}),e.\u0275\u0275elementEnd()()()}if(2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(4),e.\u0275\u0275property("ngModel",t.lead.observacoes)}}function Vn(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",3)(1,"div",4)(2,"div",83)(3,"div",46)(4,"h6",47),e.\u0275\u0275element(5,"i",63),e.\u0275\u0275text(6," Dados B\xe1sicos do Lead "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(7,"div",3)(8,"div",84)(9,"div",50)(10,"label",85),e.\u0275\u0275text(11,"Nome do Respons\xe1vel *"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"input",86),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.lead.nomeResponsavel=a)}),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(13,"div",84)(14,"div",50)(15,"label",87),e.\u0275\u0275text(16,"Nome da Empresa *"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(17,"input",88),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.lead.empresa=a)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(18,"small",89),e.\u0275\u0275element(19,"i",90),e.\u0275\u0275text(20,' Use o cart\xe3o "Buscar Empresa no Google" abaixo para encontrar mais informa\xe7\xf5es '),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(21,"div",84)(22,"div",50)(23,"label",91),e.\u0275\u0275text(24,"Cidade *"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(25,"input",92),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.lead.cidade=a)}),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(26,"div",84)(27,"div",50)(28,"label",93),e.\u0275\u0275text(29,"Telefone *"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(30,"kendo-maskedtextbox",94),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.telefoneFormatado=a)})("blur",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.onTelefoneBlur())})("valueChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.onTelefoneChange(a))})("paste",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.onTelefonePaste(a))}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(31,"small",95),e.\u0275\u0275element(32,"i",96),e.\u0275\u0275text(33," Campo obrigat\xf3rio para criar contato no Bitrix "),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(34,"div",84)(35,"div",50)(36,"label",97),e.\u0275\u0275text(37,"Username Instagram *"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(38,"div",52)(39,"div",53)(40,"span",54),e.\u0275\u0275text(41,"@"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(42,"input",98),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.lead.instagramHandle=a)}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(43,"div",84)(44,"div",50)(45,"label",99),e.\u0275\u0275text(46,"Website"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(47,"input",100),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.lead.website=a)})("blur",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.onWebsiteBlur())}),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275template(48,Dn,6,2,"div",101),e.\u0275\u0275template(49,Fn,5,1,"div",101),e.\u0275\u0275elementEnd()()()()()}if(2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(12),e.\u0275\u0275property("ngModel",t.lead.nomeResponsavel),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngModel",t.lead.empresa),e.\u0275\u0275advance(8),e.\u0275\u0275property("ngModel",t.lead.cidade),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngModel",t.telefoneFormatado)("mask","(00) 00000-0000"),e.\u0275\u0275advance(12),e.\u0275\u0275property("ngModel",t.lead.instagramHandle),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngModel",t.lead.website),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.dadosInstagram&&t.dadosInstagram.user&&t.dadosInstagram.user.biography),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.lead.observacoes)}}function Rn(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",44)(1,"div",3)(2,"div",4)(3,"div",45)(4,"div",46)(5,"h5",47),e.\u0275\u0275element(6,"i",48),e.\u0275\u0275text(7," Extrair Dados do Instagram "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"p",49),e.\u0275\u0275text(9," Informe o username do Instagram da empresa para extrair dados automaticamente "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"div",50)(11,"label",51),e.\u0275\u0275text(12,"Username do Instagram"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(13,"div",52)(14,"div",53)(15,"span",54),e.\u0275\u0275text(16,"@"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(17,"input",55),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.username=a)})("keyup.enter",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.solicitarDadosInstagram())}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(18,"div",56)(19,"button",57),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.solicitarDadosInstagram())}),e.\u0275\u0275template(20,In,1,0,"i",58),e.\u0275\u0275template(21,Ln,1,0,"i",59),e.\u0275\u0275template(22,jn,2,0,"span",33),e.\u0275\u0275template(23,Tn,2,0,"span",33),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(24,"div",60)(25,"span"),e.\u0275\u0275text(26,"OU"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(27,"div",61)(28,"button",62),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.mostrarFormularioManual())}),e.\u0275\u0275element(29,"i",63),e.\u0275\u0275text(30," Criar Lead Manualmente "),e.\u0275\u0275elementEnd()()()()()(),e.\u0275\u0275template(31,Nn,26,5,"div",64),e.\u0275\u0275template(32,Vn,50,9,"div",64),e.\u0275\u0275elementEnd()}if(2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(17),e.\u0275\u0275property("ngModel",t.username),e.\u0275\u0275advance(2),e.\u0275\u0275property("disabled",t.carregando||!t.username),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.carregando),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.carregando),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.carregando),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.carregando),e.\u0275\u0275advance(8),e.\u0275\u0275property("ngIf",t.dadosInstagram&&t.dadosInstagram.user),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.mostrarFormulario||t.dadosInstagram&&t.dadosInstagram.user)}}function $n(o,r){1&o&&e.\u0275\u0275element(0,"i",117)}function Bn(o,r){1&o&&e.\u0275\u0275element(0,"i",66)}function Un(o,r){1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1,"Analisar Website"),e.\u0275\u0275elementEnd())}function Hn(o,r){1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1,"Analisando..."),e.\u0275\u0275elementEnd())}function Wn(o,r){1&o&&(e.\u0275\u0275elementStart(0,"div",118),e.\u0275\u0275element(1,"i",90),e.\u0275\u0275elementStart(2,"strong"),e.\u0275\u0275text(3,"Etapa Opcional:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(4," Se a empresa n\xe3o possui website, voc\xea pode pular esta etapa. "),e.\u0275\u0275elementEnd())}function Jn(o,r){1&o&&e.\u0275\u0275element(0,"i",148)}function Gn(o,r){1&o&&e.\u0275\u0275element(0,"i",73)}function qn(o,r){1&o&&e.\u0275\u0275element(0,"i",149)}function Yn(o,r){1&o&&e.\u0275\u0275element(0,"i",150)}function Qn(o,r){1&o&&e.\u0275\u0275element(0,"i",151)}function Xn(o,r){1&o&&e.\u0275\u0275element(0,"i",152)}function Zn(o,r){1&o&&e.\u0275\u0275element(0,"i",153)}function Kn(o,r){1&o&&e.\u0275\u0275element(0,"i",35)}const le=function(){return["WhatsApp","Instagram","Ifood","Site do Card\xe1pio","Concorrente","Localiza\xe7\xe3o","Site"]};function eo(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",130)(1,"div",131),e.\u0275\u0275template(2,Jn,1,0,"i",132),e.\u0275\u0275template(3,Gn,1,0,"i",133),e.\u0275\u0275template(4,qn,1,0,"i",134),e.\u0275\u0275template(5,Yn,1,0,"i",135),e.\u0275\u0275template(6,Qn,1,0,"i",136),e.\u0275\u0275template(7,Xn,1,0,"i",137),e.\u0275\u0275template(8,Zn,1,0,"i",138),e.\u0275\u0275template(9,Kn,1,0,"i",11),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"div",139)(11,"div",140),e.\u0275\u0275text(12),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(13,"div",141),e.\u0275\u0275text(14),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(15,"div",142),e.\u0275\u0275text(16),e.\u0275\u0275pipe(17,"slice"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(18,"div",143)(19,"button",144),e.\u0275\u0275listener("click",function(){const i=e.\u0275\u0275restoreView(t).$implicit,s=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(s.abrirLink(i.url))}),e.\u0275\u0275element(20,"i",145),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(21,"button",146),e.\u0275\u0275listener("click",function(){const i=e.\u0275\u0275restoreView(t).$implicit,s=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(s.copiarLink(i.url))}),e.\u0275\u0275element(22,"i",147),e.\u0275\u0275elementEnd()()()}if(2&o){const t=r.$implicit;e.\u0275\u0275classProp("link-whatsapp","WhatsApp"===t.tipo)("link-instagram","Instagram"===t.tipo)("link-ifood","Ifood"===t.tipo)("link-cardapio","Site do Card\xe1pio"===t.tipo)("link-concorrente","Concorrente"===t.tipo)("link-localizacao","Localiza\xe7\xe3o"===t.tipo)("link-site","Site"===t.tipo),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf","WhatsApp"===t.tipo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","Instagram"===t.tipo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","Ifood"===t.tipo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","Site do Card\xe1pio"===t.tipo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","Concorrente"===t.tipo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","Localiza\xe7\xe3o"===t.tipo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","Site"===t.tipo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!e.\u0275\u0275pureFunction0(30,le).includes(t.tipo)),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.tipo),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.descricao),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate2("",e.\u0275\u0275pipeBind3(17,26,t.url,0,40),"",t.url.length>40?"...":"","")}}function to(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"option",154),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=r.$implicit;e.\u0275\u0275property("value",t.valor),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.texto," ")}}function no(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",3)(1,"div",4)(2,"div",119)(3,"div",46)(4,"h6",47),e.\u0275\u0275element(5,"i",68),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(7,"div",120),e.\u0275\u0275template(8,eo,23,31,"div",121),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"div",122)(10,"h6",123),e.\u0275\u0275element(11,"i",124),e.\u0275\u0275text(12," Sistema Concorrente "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(13,"div",50)(14,"label",125),e.\u0275\u0275text(15," Sistema utilizado atualmente "),e.\u0275\u0275elementStart(16,"small",126),e.\u0275\u0275text(17,"(detectado automaticamente dos links ou selecione manualmente)"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(18,"select",127),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.lead.concorrente=a)}),e.\u0275\u0275elementStart(19,"option",128),e.\u0275\u0275text(20,"-- Selecione um concorrente --"),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(21,to,2,2,"option",129),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(22,"small",89),e.\u0275\u0275element(23,"i",90),e.\u0275\u0275text(24," Se voc\xea identificou um sistema concorrente nos links, selecione-o aqui "),e.\u0275\u0275elementEnd()()()()()()()}if(2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate1(" Links Encontrados (",t.linksEncontrados.length,") "),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",t.linksEncontrados),e.\u0275\u0275advance(10),e.\u0275\u0275property("ngModel",t.lead.concorrente),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngForOf",t.concorrentes)}}function oo(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",106)(1,"div",3)(2,"div",4)(3,"div",107)(4,"div",46)(5,"h6",47),e.\u0275\u0275element(6,"i",68),e.\u0275\u0275text(7," Dados B\xe1sicos (Passo 1) "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"div",108)(9,"div")(10,"span",109),e.\u0275\u0275text(11),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"strong"),e.\u0275\u0275text(13),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(14),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(15,"button",110),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.goToStep(1))}),e.\u0275\u0275element(16,"i",63),e.\u0275\u0275text(17," Editar "),e.\u0275\u0275elementEnd()()()()()(),e.\u0275\u0275elementStart(18,"div",3)(19,"div",4)(20,"div",111)(21,"div",46)(22,"h5",47),e.\u0275\u0275element(23,"i",112),e.\u0275\u0275text(24," Analisar Website da Empresa "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(25,"p",49),e.\u0275\u0275text(26," Informe o website da empresa para descobrir links relevantes automaticamente "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(27,"div",50)(28,"label",113),e.\u0275\u0275text(29,"Website da Empresa"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(30,"div",52)(31,"input",114),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.lead.website=a)})("blur",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.onWebsiteBlur())}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(32,"div",56)(33,"button",57),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.carregarDadosDoLink())}),e.\u0275\u0275template(34,$n,1,0,"i",115),e.\u0275\u0275template(35,Bn,1,0,"i",59),e.\u0275\u0275template(36,Un,2,0,"span",33),e.\u0275\u0275template(37,Hn,2,0,"span",33),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(38,"small",89),e.\u0275\u0275text(39," A an\xe1lise ir\xe1 descobrir links para WhatsApp, Instagram, iFood, card\xe1pios e muito mais "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(40,Wn,5,0,"div",116),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275template(41,no,25,4,"div",64),e.\u0275\u0275elementEnd()}if(2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(11),e.\u0275\u0275textInterpolate1("@",t.lead.instagramHandle,""),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.lead.nomeResponsavel),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate2(" - ",t.lead.empresa," - ",t.lead.cidade," "),e.\u0275\u0275advance(17),e.\u0275\u0275property("ngModel",t.lead.website),e.\u0275\u0275advance(2),e.\u0275\u0275property("disabled",t.carregandoWebsite||!t.lead.website),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.carregandoWebsite),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.carregandoWebsite),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.carregandoWebsite),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.carregandoWebsite),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",!t.lead.website),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.linksEncontrados&&t.linksEncontrados.length>0)}}function ao(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",157)(1,"strong"),e.\u0275\u0275text(2,"Links:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",t.linksEncontrados.length," encontrados ")}}function io(o,r){1&o&&e.\u0275\u0275element(0,"i",117)}function ro(o,r){1&o&&e.\u0275\u0275element(0,"i",66)}function so(o,r){1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1,"Buscar CNPJs"),e.\u0275\u0275elementEnd())}function co(o,r){1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1,"Buscando..."),e.\u0275\u0275elementEnd())}function lo(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",180)(1,"button",181),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.abrirGoogleEmpresaCompleta())}),e.\u0275\u0275element(2,"i",41),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()()}if(2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate2(' Buscar "',t.lead.empresa," ",t.lead.cidade,'" no Google ')}}function mo(o,r){1&o&&e.\u0275\u0275element(0,"i",117)}function po(o,r){1&o&&e.\u0275\u0275element(0,"i",66)}function go(o,r){1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1,"Buscar Dados"),e.\u0275\u0275elementEnd())}function _o(o,r){1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1,"Buscando..."),e.\u0275\u0275elementEnd())}function uo(o,r){1&o&&(e.\u0275\u0275elementStart(0,"small"),e.\u0275\u0275element(1,"i",151),e.\u0275\u0275text(2," CNPJ inv\xe1lido. Verifique o n\xfamero digitado. "),e.\u0275\u0275elementEnd())}function fo(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",182),e.\u0275\u0275template(1,uo,3,0,"small",33),e.\u0275\u0275elementEnd()),2&o){e.\u0275\u0275nextContext();const t=e.\u0275\u0275reference(62);e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==t.errors?null:t.errors.cnpjInvalido)}}function Co(o,r){1&o&&(e.\u0275\u0275elementStart(0,"div",118),e.\u0275\u0275element(1,"i",90),e.\u0275\u0275elementStart(2,"strong"),e.\u0275\u0275text(3,"Etapa Opcional:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(4," Se n\xe3o souber o CNPJ da empresa, voc\xea pode pular esta etapa. "),e.\u0275\u0275elementEnd())}function ho(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",187),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(a.removerSelecaoCnpj())}),e.\u0275\u0275element(1,"i",188),e.\u0275\u0275text(2," Limpar Sele\xe7\xe3o "),e.\u0275\u0275elementEnd()}}function vo(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",189),e.\u0275\u0275element(1,"i",190),e.\u0275\u0275elementStart(2,"div",25)(3,"strong"),e.\u0275\u0275text(4,"Confirmado:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5," Nenhum dos CNPJs encontrados corresponde ao lead. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"button",191),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(a.cnpjRecusadoExplicitamente=!1)}),e.\u0275\u0275element(7,"i",192),e.\u0275\u0275text(8," Desfazer "),e.\u0275\u0275elementEnd()()}}function xo(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",213),e.\u0275\u0275element(1,"i",214),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",t.nomeFantasia," ")}}function bo(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",215),e.\u0275\u0275element(1,"i",216),e.\u0275\u0275elementStart(2,"small"),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.razaoSocial)}}function Mo(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",217),e.\u0275\u0275element(1,"i",152),e.\u0275\u0275elementStart(2,"small"),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.endereco)}}function Po(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",218),e.\u0275\u0275element(1,"i",219),e.\u0275\u0275elementStart(2,"small")(3,"strong"),e.\u0275\u0275text(4,"Capital:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate1(" ",t.capitalSocial,"")}}function Oo(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",220),e.\u0275\u0275element(1,"i",221),e.\u0275\u0275elementStart(2,"small")(3,"strong"),e.\u0275\u0275text(4,"Atividade:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate1(" ",t.atividadePrincipal,"")}}function yo(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",222),e.\u0275\u0275element(1,"i",37),e.\u0275\u0275elementStart(2,"small")(3,"strong"),e.\u0275\u0275text(4,"S\xf3cios:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate1(" ",t.socios.join(", "),"")}}function So(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",223)(1,"span",224),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275classProp("active","ATIVA"===t.situacao),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.situacao," ")}}function Eo(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",225)(1,"small",126),e.\u0275\u0275element(2,"i",90),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",t.fonte,"")}}function wo(o,r){1&o&&e.\u0275\u0275element(0,"i",226)}function ko(o,r){1&o&&e.\u0275\u0275element(0,"i",227)}function Io(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",193)(1,"div",194)(2,"span",195),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"titlecase"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",196),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",197),e.\u0275\u0275template(8,xo,3,1,"div",198),e.\u0275\u0275template(9,bo,4,1,"div",199),e.\u0275\u0275template(10,Mo,4,1,"div",200),e.\u0275\u0275template(11,Po,6,1,"div",201),e.\u0275\u0275template(12,Oo,6,1,"div",202),e.\u0275\u0275template(13,yo,6,1,"div",203),e.\u0275\u0275elementStart(14,"div",204),e.\u0275\u0275template(15,So,3,3,"div",205),e.\u0275\u0275template(16,Eo,4,1,"div",206),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(17,"div",207)(18,"button",208),e.\u0275\u0275listener("click",function(){const i=e.\u0275\u0275restoreView(t).$implicit,s=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(s.selecionarCnpj(i))}),e.\u0275\u0275template(19,wo,1,0,"i",209),e.\u0275\u0275template(20,ko,1,0,"i",210),e.\u0275\u0275text(21),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(22,"button",211),e.\u0275\u0275listener("click",function(){const i=e.\u0275\u0275restoreView(t).$implicit,s=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(s.abrirCnpjBiz(i.cnpj))}),e.\u0275\u0275element(23,"i",145),e.\u0275\u0275text(24," Ver Detalhes "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(25,"button",212),e.\u0275\u0275listener("click",function(){const i=e.\u0275\u0275restoreView(t).$implicit,s=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(s.abrirGoogleVerificarCnpj(i))}),e.\u0275\u0275element(26,"i",41),e.\u0275\u0275text(27," Verificar "),e.\u0275\u0275elementEnd()()()}if(2&o){const t=r.$implicit,n=e.\u0275\u0275nextContext(3);e.\u0275\u0275classProp("selected",t.selecionado),e.\u0275\u0275advance(2),e.\u0275\u0275classMap("badge-"+n.getCorConfianca(t.confianca)),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(4,21,t.confianca)," "),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.cnpj),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",t.nomeFantasia),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.razaoSocial),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.endereco),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.capitalSocial),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.atividadePrincipal),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.socios&&t.socios.length>0),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",t.situacao),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.fonte),e.\u0275\u0275advance(2),e.\u0275\u0275classProp("btn-success",t.selecionado)("btn-primary",!t.selecionado),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.selecionado),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.selecionado),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.selecionado?"Selecionado":"Selecionar"," ")}}function Lo(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",3)(1,"div",4)(2,"div",119)(3,"div",46)(4,"h6",47),e.\u0275\u0275element(5,"i",68),e.\u0275\u0275text(6),e.\u0275\u0275template(7,ho,3,0,"button",183),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(8,vo,9,0,"div",184),e.\u0275\u0275elementStart(9,"div",185),e.\u0275\u0275template(10,Io,28,23,"div",186),e.\u0275\u0275elementEnd()()()()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate1(" CNPJs Encontrados (",t.cnpjsEncontrados.length,") "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.lead.cnpj),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.cnpjRecusadoExplicitamente),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",t.cnpjsEncontrados)}}function jo(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",155)(1,"div",3)(2,"div",4)(3,"div",107)(4,"div",46)(5,"h6",47),e.\u0275\u0275element(6,"i",68),e.\u0275\u0275text(7," Progresso Anterior "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"div",156)(9,"div",157)(10,"strong"),e.\u0275\u0275text(11,"Dados:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(12),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(13,"div",158)(14,"div"),e.\u0275\u0275template(15,ao,4,1,"div",159),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(16,"button",110),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.goToStep(1))}),e.\u0275\u0275element(17,"i",63),e.\u0275\u0275text(18," Editar "),e.\u0275\u0275elementEnd()()()()()()(),e.\u0275\u0275elementStart(19,"div",3)(20,"div",4)(21,"div",160)(22,"div",46)(23,"h5",47),e.\u0275\u0275element(24,"i",161),e.\u0275\u0275text(25," Descobrir CNPJ da Empresa "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(26,"p",49),e.\u0275\u0275text(27," Encontre o CNPJ oficial da empresa atrav\xe9s de busca inteligente "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(28,"div",3)(29,"div",162)(30,"div",50)(31,"label",163),e.\u0275\u0275text(32,"Nome da Empresa"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(33,"input",164),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.lead.empresa=a)}),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(34,"div",165)(35,"div",50)(36,"label",166),e.\u0275\u0275text(37,"Cidade"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(38,"input",167),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.lead.cidade=a)}),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(39,"div",168)(40,"div",50)(41,"label"),e.\u0275\u0275text(42,"\xa0"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(43,"button",169),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.descobrirCnpj())}),e.\u0275\u0275template(44,io,1,0,"i",115),e.\u0275\u0275template(45,ro,1,0,"i",59),e.\u0275\u0275template(46,so,2,0,"span",33),e.\u0275\u0275template(47,co,2,0,"span",33),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(48,"div",170)(49,"span"),e.\u0275\u0275text(50,"OU"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(51,"div",171)(52,"h6",172),e.\u0275\u0275element(53,"i",117),e.\u0275\u0275text(54," Buscar CNPJ Manualmente "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(55,lo,4,2,"div",173),e.\u0275\u0275elementStart(56,"div",3)(57,"div",174)(58,"label",175),e.\u0275\u0275text(59,"Encontrou o CNPJ? Informe abaixo:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(60,"div",52)(61,"input",176,177),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.cnpjManual=a)})("input",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.formatarCnpjInput(a))}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(63,"button",178),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.buscarCnpjManual())}),e.\u0275\u0275template(64,mo,1,0,"i",115),e.\u0275\u0275template(65,po,1,0,"i",59),e.\u0275\u0275template(66,go,2,0,"span",33),e.\u0275\u0275template(67,_o,2,0,"span",33),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(68,fo,2,1,"div",179),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275template(69,Co,5,0,"div",116),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275template(70,Lo,11,4,"div",64),e.\u0275\u0275elementEnd()}if(2&o){const t=e.\u0275\u0275reference(62),n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(12),e.\u0275\u0275textInterpolate3(" ",n.lead.empresa," - ",n.lead.cidade," (@",n.lead.instagramHandle,") "),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",n.linksEncontrados.length>0),e.\u0275\u0275advance(18),e.\u0275\u0275property("ngModel",n.lead.empresa),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngModel",n.lead.cidade),e.\u0275\u0275advance(5),e.\u0275\u0275property("disabled",n.carregandoCnpj||!n.lead.empresa||!n.lead.cidade),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!n.carregandoCnpj),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.carregandoCnpj),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!n.carregandoCnpj),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.carregandoCnpj),e.\u0275\u0275advance(8),e.\u0275\u0275property("ngIf",n.lead.empresa&&n.lead.cidade),e.\u0275\u0275advance(6),e.\u0275\u0275property("ngModel",n.cnpjManual),e.\u0275\u0275advance(2),e.\u0275\u0275property("disabled",n.carregandoCnpjManual||t.invalid||!n.cnpjManual),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!n.carregandoCnpjManual),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.carregandoCnpjManual),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!n.carregandoCnpjManual),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.carregandoCnpjManual),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.invalid&&t.touched),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!n.lead.empresa||!n.lead.cidade),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.cnpjsEncontrados&&n.cnpjsEncontrados.length>0)}}function To(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",233)(1,"strong"),e.\u0275\u0275text(2,"Raz\xe3o Social:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",t.razaoSocialSelecionada," ")}}function Ao(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"span",234),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(t.lead.endereco)}}function zo(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",156)(1,"div",158)(2,"div",157)(3,"strong"),e.\u0275\u0275text(4,"CNPJ:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5),e.\u0275\u0275template(6,To,4,1,"div",231),e.\u0275\u0275template(7,Ao,2,1,"span",232),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"button",110),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.goToStep(3))}),e.\u0275\u0275element(9,"i",63),e.\u0275\u0275text(10," Editar "),e.\u0275\u0275elementEnd()()()}if(2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate2(" ",t.lead.cnpj," - ",t.lead.empresa," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.razaoSocialSelecionada),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.lead.endereco)}}function No(o,r){1&o&&(e.\u0275\u0275elementStart(0,"div",235),e.\u0275\u0275element(1,"i",151),e.\u0275\u0275text(2," Nenhum CNPJ foi selecionado. Volte ao passo anterior para selecionar um CNPJ. "),e.\u0275\u0275elementEnd())}function Do(o,r){1&o&&e.\u0275\u0275element(0,"i",117)}function Fo(o,r){1&o&&e.\u0275\u0275element(0,"i",66)}function Vo(o,r){1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1,"Buscar S\xf3cios"),e.\u0275\u0275elementEnd())}function Ro(o,r){1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1,"Buscando..."),e.\u0275\u0275elementEnd())}function $o(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",61)(1,"button",57),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(a.buscarDetalhesSocios())}),e.\u0275\u0275template(2,Do,1,0,"i",115),e.\u0275\u0275template(3,Fo,1,0,"i",59),e.\u0275\u0275template(4,Vo,2,0,"span",33),e.\u0275\u0275template(5,Ro,2,0,"span",33),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"div",240)(7,"small",126),e.\u0275\u0275text(8," A busca ir\xe1 procurar informa\xe7\xf5es p\xfablicas sobre os s\xf3cios da empresa "),e.\u0275\u0275elementEnd()()()}if(2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(1),e.\u0275\u0275property("disabled",t.carregandoSocios||!t.lead.cnpj),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.carregandoSocios),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.carregandoSocios),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.carregandoSocios),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.carregandoSocios)}}function Bo(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",241)(1,"button",242),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(a.buscarDetalhesSocios())}),e.\u0275\u0275element(2,"i",243),e.\u0275\u0275text(3," Buscar Novamente "),e.\u0275\u0275elementEnd()()}if(2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(1),e.\u0275\u0275property("disabled",t.carregandoSocios)}}function Uo(o,r){1&o&&(e.\u0275\u0275elementStart(0,"div",244),e.\u0275\u0275element(1,"i",90),e.\u0275\u0275elementStart(2,"strong"),e.\u0275\u0275text(3,"Etapa Opcional:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(4," Voc\xea pode pular esta etapa se n\xe3o precisar dos dados dos s\xf3cios. "),e.\u0275\u0275elementEnd())}function Ho(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",3)(1,"div",4)(2,"div",111)(3,"div",46)(4,"h5",47),e.\u0275\u0275element(5,"i",236),e.\u0275\u0275text(6," Identificar S\xf3cios e Respons\xe1veis "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(7,"p",49),e.\u0275\u0275text(8," Descubra quem s\xe3o os s\xf3cios e decisores da empresa para melhor qualificar o lead "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(9,$o,9,5,"div",237),e.\u0275\u0275template(10,Bo,4,1,"div",238),e.\u0275\u0275template(11,Uo,5,0,"div",239),e.\u0275\u0275elementEnd()()()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(9),e.\u0275\u0275property("ngIf",!t.sociosBuscados||t.carregandoSocios),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.sociosBuscados&&t.sociosDetalhados.length>0&&!t.carregandoSocios),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.sociosBuscados)}}function Wo(o,r){1&o&&(e.\u0275\u0275elementStart(0,"span",253),e.\u0275\u0275element(1,"i",254),e.\u0275\u0275text(2," Sele\xe7\xe3o Autom\xe1tica "),e.\u0275\u0275elementEnd())}function Jo(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",118),e.\u0275\u0275element(1,"i",255),e.\u0275\u0275elementStart(2,"strong"),e.\u0275\u0275text(3,"Sele\xe7\xe3o Inteligente:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(4," Identificamos automaticamente "),e.\u0275\u0275elementStart(5,"strong"),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(7," como s\xf3cio principal. "),e.\u0275\u0275element(8,"br"),e.\u0275\u0275elementStart(9,"small")(10,"strong"),e.\u0275\u0275text(11,"Motivo:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(12),e.\u0275\u0275elementStart(13,"span",256),e.\u0275\u0275text(14),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(15,"br"),e.\u0275\u0275elementStart(16,"small",126),e.\u0275\u0275text(17," Voc\xea pode alterar esta sele\xe7\xe3o clicando em outro s\xf3cio abaixo. "),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate(t.getSocioPrincipal().nome),e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate1(" ",t.formatarMotivoSelecao(t.getSocioPrincipal().motivoSelecao)," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",t.getScoreClasse(t.getSocioPrincipal().scoreAnalise)),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.getSocioPrincipal().scoreAnalise,"/100 ")}}function Go(o,r){1&o&&(e.\u0275\u0275elementStart(0,"span",271),e.\u0275\u0275element(1,"i",266),e.\u0275\u0275text(2," Principal "),e.\u0275\u0275elementEnd())}function qo(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"span",272),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext().$implicit,n=e.\u0275\u0275nextContext(3);e.\u0275\u0275property("ngClass",n.getScoreClasse(t.scoreAnalise))("title","Score: "+t.scoreAnalise+"/100 - "+n.formatarMotivoSelecao(t.motivoSelecao)),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.scoreAnalise," ")}}function Yo(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",273),e.\u0275\u0275element(1,"i",274),e.\u0275\u0275elementStart(2,"span"),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("CPF: ",t.cpf,"")}}function Qo(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",273),e.\u0275\u0275element(1,"i",275),e.\u0275\u0275elementStart(2,"span"),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("Participa\xe7\xe3o: ",t.participacao,"")}}function Xo(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",273),e.\u0275\u0275element(1,"i",221),e.\u0275\u0275elementStart(2,"span"),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.cargo)}}function Zo(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",273),e.\u0275\u0275element(1,"i",276),e.\u0275\u0275elementStart(2,"span"),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("Desde: ",t.dataEntrada,"")}}function Ko(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",273),e.\u0275\u0275element(1,"i",277),e.\u0275\u0275elementStart(2,"span"),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.qualificacao)}}function ea(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",257),e.\u0275\u0275listener("click",function(){const i=e.\u0275\u0275restoreView(t).$implicit,s=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(s.marcarComoPrincipal(i))}),e.\u0275\u0275elementStart(1,"div",258)(2,"div",259),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",260),e.\u0275\u0275element(5,"i",261),e.\u0275\u0275text(6),e.\u0275\u0275template(7,Go,3,0,"span",262),e.\u0275\u0275template(8,qo,2,3,"span",263),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"div",264)(10,"button",265),e.\u0275\u0275listener("click",function(){const i=e.\u0275\u0275restoreView(t).$implicit,s=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(s.marcarComoPrincipal(i))}),e.\u0275\u0275element(11,"i",266),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(12,"div",267),e.\u0275\u0275template(13,Yo,4,1,"div",268),e.\u0275\u0275template(14,Qo,4,1,"div",268),e.\u0275\u0275template(15,Xo,4,1,"div",268),e.\u0275\u0275template(16,Zo,4,1,"div",268),e.\u0275\u0275template(17,Ko,4,1,"div",268),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(18,"div",269)(19,"label"),e.\u0275\u0275text(20),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(21,"textarea",270),e.\u0275\u0275listener("ngModelChange",function(a){const s=e.\u0275\u0275restoreView(t).$implicit;return e.\u0275\u0275resetView(s.observacoes=a)}),e.\u0275\u0275elementEnd()()()}if(2&o){const t=r.$implicit,n=r.index;e.\u0275\u0275classProp("principal",t.principal),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("#",n+1,""),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",t.nome," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.principal),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.scoreAnalise),e.\u0275\u0275advance(2),e.\u0275\u0275classProp("btn-warning",t.principal)("btn-outline-warning",!t.principal),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",t.cpf),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.participacao),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.cargo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.dataEntrada),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.qualificacao),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("Observa\xe7\xf5es sobre ",t.nome,":"),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngModel",t.observacoes)("name","obs_socio_"+n)}}function ta(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",3)(1,"div",4)(2,"div",119)(3,"div",46)(4,"h6",47),e.\u0275\u0275element(5,"i",68),e.\u0275\u0275text(6),e.\u0275\u0275template(7,Wo,3,0,"span",245),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(8,Jo,18,4,"div",116),e.\u0275\u0275elementStart(9,"div",246),e.\u0275\u0275template(10,ea,22,18,"div",247),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",240)(12,"label",248),e.\u0275\u0275element(13,"i",249),e.\u0275\u0275text(14," Respons\xe1vel pelo Lead: "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(15,"input",250),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.lead.nomeResponsavel=a)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(16,"small",89),e.\u0275\u0275element(17,"i",90),e.\u0275\u0275text(18," Marque um s\xf3cio como principal acima ou digite o nome manualmente "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(19,"div",240)(20,"label",251),e.\u0275\u0275text(21,"Observa\xe7\xf5es Gerais sobre os S\xf3cios:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(22,"textarea",252),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.lead.observacoesSocios=a)}),e.\u0275\u0275elementEnd()()()()()()}if(2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate1(" S\xf3cios Encontrados (",t.sociosDetalhados.length,") "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.getSocioPrincipal()),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.getSocioPrincipal()),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",t.sociosDetalhados),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngModel",t.lead.nomeResponsavel),e.\u0275\u0275advance(7),e.\u0275\u0275property("ngModel",t.lead.observacoesSocios)}}function na(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",3)(1,"div",4)(2,"div",278)(3,"div",279),e.\u0275\u0275element(4,"i",280),e.\u0275\u0275elementStart(5,"h6"),e.\u0275\u0275text(6,"Nenhum s\xf3cio encontrado"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(7,"p",126),e.\u0275\u0275text(8,"N\xe3o foi poss\xedvel encontrar informa\xe7\xf5es p\xfablicas sobre os s\xf3cios desta empresa."),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"div",281)(10,"button",282),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.buscarDetalhesSocios())}),e.\u0275\u0275element(11,"i",243),e.\u0275\u0275text(12," Tentar Novamente "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(13,"button",283),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.abrirConsultaCNPJReceita())}),e.\u0275\u0275element(14,"i",145),e.\u0275\u0275text(15," Consultar na Receita Federal "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(16,"div",60)(17,"span"),e.\u0275\u0275text(18,"OU"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(19,"div",284)(20,"p",175),e.\u0275\u0275text(21,"Informe o nome do s\xf3cio manualmente:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(22,"div",285)(23,"input",286),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.nomeSocioManual=a)})("keyup.enter",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.adicionarSocioManual())}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(24,"button",287),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.adicionarSocioManual())}),e.\u0275\u0275element(25,"i",227),e.\u0275\u0275text(26," Adicionar "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(27,"small",288),e.\u0275\u0275element(28,"i",90),e.\u0275\u0275text(29," Digite o nome do respons\xe1vel principal da empresa "),e.\u0275\u0275elementEnd()()()()()()}if(2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(23),e.\u0275\u0275property("ngModel",t.nomeSocioManual),e.\u0275\u0275advance(1),e.\u0275\u0275property("disabled",!(null!=t.nomeSocioManual&&t.nomeSocioManual.trim()))}}function oa(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",228)(1,"div",3)(2,"div",4)(3,"div",107)(4,"div",46)(5,"h6",47),e.\u0275\u0275element(6,"i",68),e.\u0275\u0275text(7," CNPJ Selecionado "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(8,zo,11,4,"div",229),e.\u0275\u0275template(9,No,3,0,"div",230),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275template(10,Ho,12,3,"div",64),e.\u0275\u0275template(11,ta,23,6,"div",64),e.\u0275\u0275template(12,na,30,2,"div",64),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(8),e.\u0275\u0275property("ngIf",t.lead.cnpj),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.lead.cnpj),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.lead.cnpj),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.sociosDetalhados&&t.sociosDetalhados.length>0),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.sociosBuscados&&0===t.sociosDetalhados.length&&!t.carregandoSocios)}}function aa(o,r){1&o&&e.\u0275\u0275element(0,"i",148)}function ia(o,r){1&o&&e.\u0275\u0275element(0,"i",73)}function ra(o,r){1&o&&e.\u0275\u0275element(0,"i",149)}function sa(o,r){1&o&&e.\u0275\u0275element(0,"i",150)}function ca(o,r){1&o&&e.\u0275\u0275element(0,"i",151)}function la(o,r){1&o&&e.\u0275\u0275element(0,"i",152)}function da(o,r){1&o&&e.\u0275\u0275element(0,"i",153)}function ma(o,r){1&o&&e.\u0275\u0275element(0,"i",35)}function pa(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",141),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(t.descricao)}}function ga(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",312)(1,"div",131),e.\u0275\u0275template(2,aa,1,0,"i",132),e.\u0275\u0275template(3,ia,1,0,"i",133),e.\u0275\u0275template(4,ra,1,0,"i",134),e.\u0275\u0275template(5,sa,1,0,"i",135),e.\u0275\u0275template(6,ca,1,0,"i",136),e.\u0275\u0275template(7,la,1,0,"i",137),e.\u0275\u0275template(8,da,1,0,"i",138),e.\u0275\u0275template(9,ma,1,0,"i",11),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"div",139)(11,"div",140),e.\u0275\u0275text(12),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(13,pa,2,1,"div",313),e.\u0275\u0275elementStart(14,"div",142),e.\u0275\u0275text(15),e.\u0275\u0275pipe(16,"slice"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(17,"div",143)(18,"button",144),e.\u0275\u0275listener("click",function(){const i=e.\u0275\u0275restoreView(t).$implicit,s=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(s.abrirLink(i.url))}),e.\u0275\u0275element(19,"i",145),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(20,"button",146),e.\u0275\u0275listener("click",function(){const i=e.\u0275\u0275restoreView(t).$implicit,s=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(s.copiarLink(i.url))}),e.\u0275\u0275element(21,"i",147),e.\u0275\u0275elementEnd()()()}if(2&o){const t=r.$implicit;e.\u0275\u0275classProp("link-whatsapp","WhatsApp"===t.tipo)("link-instagram","Instagram"===t.tipo)("link-ifood","Ifood"===t.tipo)("link-cardapio","Site do Card\xe1pio"===t.tipo)("link-concorrente","Concorrente"===t.tipo)("link-localizacao","Localiza\xe7\xe3o"===t.tipo)("link-site","Site"===t.tipo),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf","WhatsApp"===t.tipo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","Instagram"===t.tipo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","Ifood"===t.tipo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","Site do Card\xe1pio"===t.tipo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","Concorrente"===t.tipo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","Localiza\xe7\xe3o"===t.tipo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","Site"===t.tipo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!e.\u0275\u0275pureFunction0(30,le).includes(t.tipo)),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.tipo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.descricao),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate2("",e.\u0275\u0275pipeBind3(16,26,t.url,0,35),"",t.url.length>35?"...":"","")}}function _a(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",292)(1,"h6"),e.\u0275\u0275element(2,"i",35),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",310),e.\u0275\u0275template(5,ga,22,31,"div",311),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" Links Encontrados (",t.linksEncontrados.length,")"),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",t.linksEncontrados)}}function ua(o,r){1&o&&(e.\u0275\u0275elementStart(0,"span",322),e.\u0275\u0275text(1,"Principal"),e.\u0275\u0275elementEnd())}function fa(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1("CPF: ",t.cpf,"")}}function Ca(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" \u2022 ",t.cargo,"")}}function ha(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" \u2022 Desde: ",t.dataEntrada,"")}}function va(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" \u2022 ",t.faixaEtaria,"")}}function xa(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",323)(1,"small",126),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.observacoes)}}function ba(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",317)(1,"div",318)(2,"strong"),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(4,ua,2,0,"span",319),e.\u0275\u0275elementStart(5,"div",320),e.\u0275\u0275template(6,fa,2,1,"span",33),e.\u0275\u0275template(7,Ca,2,1,"span",33),e.\u0275\u0275template(8,ha,2,1,"span",33),e.\u0275\u0275template(9,va,2,1,"span",33),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(10,xa,3,1,"div",321),e.\u0275\u0275elementEnd()()),2&o){const t=r.$implicit;e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.nome),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.principal),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",t.cpf),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.cargo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.dataEntrada),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.faixaEtaria),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.observacoes)}}function Ma(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",240)(1,"strong"),e.\u0275\u0275text(2,"Observa\xe7\xf5es Gerais:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"p",324),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(t.lead.observacoesSocios)}}function Pa(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",292)(1,"h6"),e.\u0275\u0275element(2,"i",37),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",314),e.\u0275\u0275template(5,ba,11,7,"div",315),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(6,Ma,5,1,"div",316),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" S\xf3cios Encontrados (",t.sociosDetalhados.length,")"),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",t.sociosDetalhados),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.lead.observacoesSocios)}}function Oa(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div")(1,"strong"),e.\u0275\u0275text(2,"Endere\xe7o:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",t.lead.endereco,"")}}function ya(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",292)(1,"h6"),e.\u0275\u0275element(2,"i",36),e.\u0275\u0275text(3," CNPJ Selecionado"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",325)(5,"div")(6,"strong"),e.\u0275\u0275text(7,"CNPJ:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(8),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"div")(10,"strong"),e.\u0275\u0275text(11,"Empresa:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(12),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(13,Oa,4,1,"div",33),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(8),e.\u0275\u0275textInterpolate1(" ",t.lead.cnpj,""),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate1(" ",t.lead.empresa,""),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.lead.endereco)}}function Sa(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"option",154),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=r.$implicit;e.\u0275\u0275property("value",t.valor),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.texto," ")}}function Ea(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"option",154),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=r.$implicit;e.\u0275\u0275property("value",t.valor),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.texto," ")}}function wa(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"option",154),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=r.$implicit;e.\u0275\u0275property("value",t.valor),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.texto," ")}}function ka(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",289)(1,"div",3)(2,"div",290)(3,"div",107)(4,"div",46)(5,"h6",47),e.\u0275\u0275element(6,"i",291),e.\u0275\u0275text(7," Resumo do Lead "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"div",292)(9,"h6"),e.\u0275\u0275element(10,"i",293),e.\u0275\u0275text(11," Dados B\xe1sicos"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"div",294)(13,"div")(14,"strong"),e.\u0275\u0275text(15,"Respons\xe1vel:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(17,"div")(18,"strong"),e.\u0275\u0275text(19,"Empresa:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(20),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(21,"div")(22,"strong"),e.\u0275\u0275text(23,"Cidade:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(24),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(25,"div")(26,"strong"),e.\u0275\u0275text(27,"Telefone:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(28),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(29,"div")(30,"strong"),e.\u0275\u0275text(31,"Instagram:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(32),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(33,"div")(34,"strong"),e.\u0275\u0275text(35,"Website:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(36),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275template(37,_a,6,2,"div",295),e.\u0275\u0275template(38,Pa,7,3,"div",295),e.\u0275\u0275template(39,ya,14,3,"div",295),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(40,"div",165)(41,"div",83)(42,"div",46)(43,"h6",47),e.\u0275\u0275element(44,"i",296),e.\u0275\u0275text(45," Configura\xe7\xf5es do Lead "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(46,"div",50)(47,"label",297),e.\u0275\u0275element(48,"i",249),e.\u0275\u0275text(49," Respons\xe1vel "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(50,"input",298),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.lead.nomeResponsavel=a)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(51,"small",89),e.\u0275\u0275text(52," Contato principal da empresa "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(53,"div",50)(54,"label",299),e.\u0275\u0275text(55,"Etapa do Funil"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(56,"select",300),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.lead.etapa=a)}),e.\u0275\u0275template(57,Sa,2,2,"option",129),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(58,"div",50)(59,"label",301),e.\u0275\u0275text(60,"Origem do Lead"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(61,"select",302),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.lead.origem=a)}),e.\u0275\u0275template(62,Ea,2,2,"option",129),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(63,"div",50)(64,"label",303),e.\u0275\u0275text(65,"Segmento"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(66,"select",304),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.lead.segmento=a)}),e.\u0275\u0275template(67,wa,2,2,"option",129),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(68,"div",50)(69,"label",305),e.\u0275\u0275text(70,"Observa\xe7\xf5es"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(71,"textarea",306),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.lead.observacoes=a)}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(72,"div",307)(73,"input",308),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.sincronizarBitrix=a)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(74,"label",309),e.\u0275\u0275text(75," Sincronizar com Bitrix24 "),e.\u0275\u0275elementEnd()()()()()()()}if(2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(16),e.\u0275\u0275textInterpolate1(" ",t.lead.nomeResponsavel,""),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate1(" ",t.lead.empresa,""),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate1(" ",t.lead.cidade,""),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate1(" ",t.lead.telefone||"N\xe3o informado",""),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate1(" @",t.lead.instagramHandle,""),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate1(" ",t.lead.website||"N\xe3o informado",""),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.linksEncontrados.length>0),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.sociosDetalhados&&t.sociosDetalhados.length>0),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.lead.cnpj),e.\u0275\u0275advance(11),e.\u0275\u0275property("ngModel",t.lead.nomeResponsavel),e.\u0275\u0275advance(6),e.\u0275\u0275property("ngModel",t.lead.etapa),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",t.etapas),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngModel",t.lead.origem),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",t.origens),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngModel",t.lead.segmento),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",t.segmentos),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngModel",t.lead.observacoes),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngModel",t.sincronizarBitrix)}}function Ia(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",62),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.prevStep())}),e.\u0275\u0275element(1,"i",326),e.\u0275\u0275text(2," Voltar "),e.\u0275\u0275elementEnd()}}function La(o,r){if(1&o&&(e.\u0275\u0275elementStart(0,"div",327),e.\u0275\u0275element(1,"i",151),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",t.erro," ")}}function ja(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",328),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.skipStep())}),e.\u0275\u0275element(1,"i",329),e.\u0275\u0275text(2," Pular Etapa "),e.\u0275\u0275elementEnd()}}function Ta(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",330),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.nextStep())}),e.\u0275\u0275text(1," Pr\xf3ximo "),e.\u0275\u0275element(2,"i",331),e.\u0275\u0275elementEnd()}}function Aa(o,r){1&o&&e.\u0275\u0275element(0,"i",333)}function za(o,r){1&o&&e.\u0275\u0275element(0,"i",66)}function Na(o,r){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",287),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.salvarLead())}),e.\u0275\u0275template(1,Aa,1,0,"i",332),e.\u0275\u0275template(2,za,1,0,"i",59),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()}if(2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275property("disabled",t.carregando),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.carregando),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.carregando),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.carregando?"Salvando...":"Salvar Lead"," ")}}const Da=function(){return[1,2,3,4,5]},Fa=[{path:"home",component:se},{path:"home/:username",component:se},{path:"novo-lead",component:(()=>{class o{constructor(t,n,a,i,s,c){this.route=t,this.router=n,this.instagramDataService=a,this.leadService=i,this.crmEmpresaService=s,this.cdr=c,this.dadosInstagram=null,this.username="",this.leadProcessadoAPI=null,this.carregando=!1,this.carregandoWebsite=!1,this.carregandoCnpj=!1,this.erro="",this.mostrarFormulario=!1,this.sincronizarBitrix=!1,this.linksEncontrados=[],this.telefonesEncontrados=[],this.cnpjsEncontrados=[],this.sociosDetalhados=[],this.carregandoSocios=!1,this.sociosBuscados=!1,this.cnpjManual="",this.carregandoCnpjManual=!1,this.currentStep=1,this.totalSteps=5,this.wizardData={dadosBasicos:{},linksEncontrados:[],cnpjSelecionado:null,sociosEncontrados:[],configuracoes:{}},this.websiteAnalisado=!1,this.cnpjBuscado=!1,this.etapaFoiPulada=!1,this.cnpjRecusadoExplicitamente=!1,this.parceirSelecionado=!1,this.lead={nomeResponsavel:"",empresa:"",cidade:"",endereco:"",cnpj:"",telefone:"",instagramHandle:"",website:"",linkCardapio:"",bioInsta:"",biografia:"",observacoes:"",observacoesSocios:"",origem:"Instagram",etapa:"Prospec\xe7\xe3o",segmento:"Alimenta\xe7\xe3o",concorrente:"",crmEmpresaId:null},this.crmEmpresas=[],this.telefoneFormatado="",this.razaoSocialSelecionada="",this.nomeSocioManual="",this.etapas=[{valor:"Prospec\xe7\xe3o",texto:"Prospec\xe7\xe3o"},{valor:"Qualifica\xe7\xe3o",texto:"Qualifica\xe7\xe3o"},{valor:"Obje\xe7\xe3o",texto:"Obje\xe7\xe3o"},{valor:"Fechamento",texto:"Fechamento"},{valor:"Ganho",texto:"Ganho"},{valor:"Perdido",texto:"Perdido"}],this.origens=[{valor:"Instagram",texto:"Instagram"},{valor:"Site/Landing Page",texto:"Site/Landing Page"},{valor:"WhatsApp Direto",texto:"WhatsApp Direto"},{valor:"Indica\xe7\xe3o",texto:"Indica\xe7\xe3o"},{valor:"Evento/Feira",texto:"Evento/Feira"},{valor:"Outros",texto:"Outros"}],this.segmentos=[{valor:"Alimenta\xe7\xe3o",texto:"Alimenta\xe7\xe3o"},{valor:"Varejo",texto:"Varejo"},{valor:"Servi\xe7os",texto:"Servi\xe7os"},{valor:"Sa\xfade",texto:"Sa\xfade"},{valor:"Educa\xe7\xe3o",texto:"Educa\xe7\xe3o"},{valor:"Outros",texto:"Outros"}],this.tiposTelefone=[{valor:"WhatsApp",texto:"WhatsApp",icone:"fa-whatsapp",cor:"#25d366"},{valor:"Telefone Fixo",texto:"Telefone Fixo",icone:"fa-phone",cor:"#6c757d"},{valor:"Celular",texto:"Celular",icone:"fa-mobile-alt",cor:"#007bff"},{valor:"Comercial",texto:"Comercial",icone:"fa-briefcase",cor:"#28a745"},{valor:"Emerg\xeancia",texto:"Emerg\xeancia",icone:"fa-exclamation-triangle",cor:"#dc3545"}],this.concorrentes=[{valor:"N\xe3o descobri",texto:"N\xe3o descobri"},{valor:"N\xe3o tem sistema",texto:"N\xe3o tem sistema"},{valor:"Accon",texto:"Accon"},{valor:"Amo Delivery",texto:"Amo Delivery"},{valor:"Anota Ai",texto:"Anota Ai"},{valor:"App Para Delivery",texto:"App Para Delivery"},{valor:"Beetech",texto:"Beetech"},{valor:"Bigdim",texto:"Bigdim"},{valor:"By App Food",texto:"By App Food"},{valor:"By Food",texto:"By Food"},{valor:"Cardapio.co",texto:"Cardapio.co"},{valor:"Card\xe1pio F\xe1cil",texto:"Card\xe1pio F\xe1cil"},{valor:"Card\xe1pio Pronto",texto:"Card\xe1pio Pronto"},{valor:"Cardapioweb",texto:"Cardapioweb"},{valor:"Cardapius",texto:"Cardapius"},{valor:"CCMPedidoOnline",texto:"CCMPedidoOnline"},{valor:"Cinndi",texto:"Cinndi"},{valor:"Delivery Direto",texto:"Delivery Direto"},{valor:"Delivery Seguro",texto:"Delivery Seguro"},{valor:"Delyver",texto:"Delyver"},{valor:"Ecta",texto:"Ecta"},{valor:"Eita.delivery",texto:"Eita.delivery"},{valor:"Expresso Delivery",texto:"Expresso Delivery"},{valor:"Expresso Menu",texto:"Expresso Menu"},{valor:"Glow Delivery",texto:"Glow Delivery"},{valor:"Go2Go Solutions",texto:"Go2Go Solutions"},{valor:"GoEntrega",texto:"GoEntrega"},{valor:"Goomer Gratuito",texto:"Goomer Gratuito"},{valor:"Goomer Pago",texto:"Goomer Pago"},{valor:"Grand Chef",texto:"Grand Chef"},{valor:"Instabuy",texto:"Instabuy"},{valor:"Instadelivery",texto:"Instadelivery"},{valor:"Jotaja",texto:"Jotaja"},{valor:"Kyte",texto:"Kyte"},{valor:"Kuppi",texto:"Kuppi"},{valor:"LadDelivery",texto:"LadDelivery"},{valor:"Magnata App",texto:"Magnata App"},{valor:"MenuIntegrado",texto:"MenuIntegrado"},{valor:"Menuvem",texto:"Menuvem"},{valor:"Meu Card\xe1pio Digital",texto:"Meu Card\xe1pio Digital"},{valor:"MeuPedido",texto:"MeuPedido"},{valor:"Menap",texto:"Menap"},{valor:"Menu Dino",texto:"Menu Dino"},{valor:"Menu Ifood",texto:"Menu Ifood"},{valor:"Miller Delivery",texto:"Miller Delivery"},{valor:"Neemo",texto:"Neemo"},{valor:"Ola Click",texto:"Ola Click"},{valor:"Pedefacil UOL",texto:"Pedefacil UOL"},{valor:"Pediaki",texto:"Pediaki"},{valor:"Pedir Delivery",texto:"Pedir Delivery"},{valor:"Pedir Online",texto:"Pedir Online"},{valor:"Pedyun",texto:"Pedyun"},{valor:"PedZap",texto:"PedZap"},{valor:"PodePedir",texto:"PodePedir"},{valor:"PopSales",texto:"PopSales"},{valor:"Prefiro Delivery",texto:"Prefiro Delivery"},{valor:"Rvpedidos",texto:"Rvpedidos"},{valor:"Saipos Card\xe1pio",texto:"Saipos Card\xe1pio"},{valor:"StayApp",texto:"StayApp"},{valor:"StarFood",texto:"StarFood"},{valor:"Vtto",texto:"Vtto"},{valor:"Zappedis",texto:"Zappedis"},{valor:"Wabiz",texto:"Wabiz"},{valor:"Webcardapio",texto:"Webcardapio"},{valor:"Whats Menu",texto:"Whats Menu"},{valor:"Alloy",texto:"Alloy"},{valor:"Card\xe1pio Digital Totvs",texto:"Card\xe1pio Digital Totvs"},{valor:"Pedir Agora",texto:"Pedir Agora"},{valor:"StiloWeb Delivery",texto:"StiloWeb Delivery"},{valor:"Tuigo Eats",texto:"Tuigo Eats"},{valor:"Hubt",texto:"Hubt"}]}get searchTerm(){const t=this.lead?.empresa?.trim()||"",n=this.lead?.cidade?.trim()||"";return t&&n?`${t} - ${n}`:t||""}set searchTerm(t){if(!t)return this.lead.empresa="",void(this.lead.cidade="");if(t.includes(" - ")){const n=t.split(" - ");this.lead.empresa=n[0].trim(),this.lead.cidade=n.slice(1).join(" - ").trim()}else this.lead.empresa=t.trim()}ngOnInit(){console.log("\u{1f41b} [ngOnInit] Starting - lead.empresa:",this.lead.empresa,"Type:",typeof this.lead.empresa),this.route.queryParams.subscribe(n=>{this.username=n.username||"",console.log("Username da URL:",this.username)});const t=this.instagramDataService.getDados();t?(this.dadosInstagram=t.dados,this.username=t.username,this.preencherFormularioComDadosInstagram()):(console.warn("Nenhum dado do Instagram encontrado no service"),this.username?(console.log("Username presente, aguardando a\xe7\xe3o do usu\xe1rio para buscar dados"),this.setupInstagramDataListener()):this.router.navigate(["/crm/home"])),this.carregarCrmEmpresas(),this.lead.telefone&&(this.telefoneFormatado=this.formatarTelefone(this.lead.telefone)),console.log("\u{1f41b} [ngOnInit] Finished - lead.empresa:",this.lead.empresa,"Type:",typeof this.lead.empresa)}preencherFormularioComDadosInstagram(){if(!this.dadosInstagram||!this.dadosInstagram.user)return void console.warn("Dados do Instagram n\xe3o encontrados ou estrutura incorreta:",this.dadosInstagram);const t=this.dadosInstagram.user;this.lead.nomeResponsavel=t.full_name||t.username||"",this.lead.empresa=t.full_name||t.username||"",this.lead.instagramHandle=t.username||"",this.lead.website=t.external_url||"",this.lead.bioInsta=t.biography||"",this.lead.biografia=t.biography||"",this.lead.telefone=t.business_phone_number||"",this.lead.telefone&&(this.telefoneFormatado=this.formatarTelefone(this.lead.telefone)),console.log("Formul\xe1rio preenchido com dados do Instagram:",this.lead),console.log("\u{1f41b} [preencherFormularioComDadosInstagram] lead.empresa set to:",this.lead.empresa,"Type:",typeof this.lead.empresa)}preencherFormularioComLeadProcessado(t){t?(console.log("Preenchendo formul\xe1rio com lead processado (objeto Lead completo):",t),this.leadProcessadoAPI=t,this.lead.nomeResponsavel=t.nomeResponsavel||"",this.lead.empresa=t.empresa||"",this.lead.cidade=t.cidade||"",this.lead.endereco=t.endereco||"",this.lead.telefone=t.telefone||"",this.lead.telefone&&(this.telefoneFormatado=this.formatarTelefone(this.lead.telefone)),console.log("\u{1f41b} [preencherFormularioComLeadProcessado] lead.empresa set to:",this.lead.empresa,"Type:",typeof this.lead.empresa),t.endereco&&console.log("Endere\xe7o extra\xeddo e preenchido no formul\xe1rio:",t.endereco),this.lead.instagramHandle=t.instagramHandle||"",this.lead.website=t.website||t.instagramData?.website||"",this.lead.bioInsta=t.bioInsta||"",this.lead.biografia=t.bioInsta||"",this.lead.origem=t.origem||"Instagram",this.lead.etapa=t.etapa||"Prospec\xe7\xe3o",this.lead.score=t.score||0,this.lead.segmento=t.instagramData?.businessCategory?this.mapearSegmento(t.instagramData.businessCategory):"Alimenta\xe7\xe3o",t.crmEmpresaId&&(this.lead.crmEmpresaId=t.crmEmpresaId),t.telefones&&Array.isArray(t.telefones)&&(this.telefonesEncontrados=t.telefones.map((n,a)=>({id:`temp_${a}`,tipo:n.tipo,numero:n.numero,descricao:n.descricao||"",numeroFormatado:this.formatarTelefone(n.numero),icone:this.getIconeTelefone(n.tipo),cor:this.getCorTelefone(n.tipo)})),console.log("Telefones processados no frontend:",this.telefonesEncontrados)),t.links&&Array.isArray(t.links)&&(this.linksEncontrados=t.links.map((n,a)=>({id:n.id||`temp_${a}`,tipo:n.tipo,url:n.url,descricao:n.descricao||"",ordem:n.ordem||a+1,ativo:!1!==n.ativo})),console.log("Links processados no frontend:",this.linksEncontrados),this.wizardData.linksEncontrados=[...this.linksEncontrados],this.websiteAnalisado=!0,console.log("Total de links carregados do backend:",this.linksEncontrados.length)),t.notas&&(this.lead.observacoes=t.notas),t.instagramData&&(this.dadosInstagram={user:{username:t.instagramHandle,full_name:t.empresa,biography:t.bioInsta,business_phone_number:t.telefone,edge_followed_by:{count:t.instagramData.followers},edge_follow:{count:t.instagramData.following},is_business_account:"Business"===t.instagramData.accountType,business_category_name:t.instagramData.businessCategory,external_url:t.instagramData.website,profile_pic_url:t.avatarUrl}}),console.log("Formul\xe1rio preenchido com lead processado:",this.lead),console.log("Dados do Instagram simulados para template:",this.dadosInstagram)):console.warn("Lead processado n\xe3o encontrado:",t)}mapearSegmento(t){if(!t)return"Outros";const n=t.toLowerCase();return n.includes("restaurante")||n.includes("comida")||n.includes("food")||n.includes("pizza")||n.includes("lanche")||n.includes("caf\xe9")||n.includes("bar")||n.includes("japon\xeas")||n.includes("delivery")?"Alimenta\xe7\xe3o":n.includes("loja")||n.includes("varejo")||n.includes("shop")?"Varejo":n.includes("servi\xe7o")||n.includes("service")?"Servi\xe7os":n.includes("sa\xfade")||n.includes("health")||n.includes("m\xe9dico")||n.includes("cl\xednica")?"Sa\xfade":n.includes("educa\xe7\xe3o")||n.includes("education")||n.includes("escola")||n.includes("curso")?"Educa\xe7\xe3o":"Outros"}carregarCrmEmpresas(){var t=this;return(0,f.Z)(function*(){try{const n=yield t.crmEmpresaService.liste();n.sucesso&&(t.crmEmpresas=n.dados||[])}catch(n){console.error("Erro ao carregar empresas CRM:",n)}})()}salvarLead(){var t=this;return(0,f.Z)(function*(){if(t.validarFormulario()){t.carregando=!0,t.erro="",t.telefoneFormatado&&(t.lead.telefone=t.limparTelefone(t.telefoneFormatado));try{let n;if(t.leadProcessadoAPI)n={...t.leadProcessadoAPI},n.nomeResponsavel=t.lead.nomeResponsavel,n.empresa=t.lead.empresa,n.cidade=t.lead.cidade,n.endereco=t.lead.endereco,n.telefone=t.lead.telefone,n.instagramHandle=t.lead.instagramHandle,n.website=t.lead.website,n.linkCardapio=t.lead.linkCardapio,n.bioInsta=t.lead.bioInsta,n.origem=t.lead.origem,n.etapa=t.lead.etapa,n.segmento=t.lead.segmento,n.crmEmpresaId=t.lead.crmEmpresaId,n.observacoes=t.lead.observacoes,n.observacoesSocios=t.lead.observacoesSocios,n.concorrente=t.lead.concorrente,delete n.id,delete n.dataCriacao,delete n.createdAt,delete n.updatedAt,t.linksEncontrados&&t.linksEncontrados.length>0&&(n.links=t.linksEncontrados,console.log("Adicionando links categorizados ao lead processado:",t.linksEncontrados)),t.telefonesEncontrados&&t.telefonesEncontrados.length>0&&(n.telefones=t.telefonesEncontrados.map(i=>({tipo:i.tipo,numero:i.numero,descricao:i.descricao})),console.log("Adicionando telefones ao lead processado:",n.telefones)),t.sociosDetalhados&&t.sociosDetalhados.length>0&&(n.sociosDetalhados=t.sociosDetalhados,console.log("Adicionando s\xf3cios detalhados ao lead:",t.sociosDetalhados.length));else{if(n={...t.lead},t.dadosInstagram&&t.dadosInstagram.user){const i=t.dadosInstagram.user;n.avatarUrl=i.profile_pic_url,n.instagramData={bio:i.biography,followers:i.edge_followed_by?.count,following:i.edge_follow?.count,accountType:i.is_business_account?"Business":"Pessoal",businessCategory:i.business_category_name||i.category_name,location:i.business_address_json?JSON.stringify(i.business_address_json):void 0,website:i.external_url}}t.linksEncontrados&&t.linksEncontrados.length>0&&(n.links=t.linksEncontrados,console.log("Adicionando links categorizados ao lead manual:",t.linksEncontrados)),t.telefonesEncontrados&&t.telefonesEncontrados.length>0&&(n.telefones=t.telefonesEncontrados.map(i=>({tipo:i.tipo,numero:i.numero,descricao:i.descricao})),console.log("Adicionando telefones ao lead manual:",n.telefones)),t.sociosDetalhados&&t.sociosDetalhados.length>0&&(n.sociosDetalhados=t.sociosDetalhados,console.log("Adicionando s\xf3cios detalhados ao lead manual:",t.sociosDetalhados.length))}console.log("Salvando lead:",n),console.log("Sincronizar com Bitrix:",t.sincronizarBitrix),console.log("Total de links categorizados a serem enviados:",n.links?.length||0),n.links&&n.links.length>0&&(console.log("Links categorizados detalhados:"),n.links.forEach((i,s)=>{console.log(`  ${s+1}. ${i.tipo}: ${i.url} (${i.descricao})`)})),t.sincronizarBitrix&&(n.sincronizarBitrix=!0);const a=yield t.leadService.salveLead(n);console.log("Lead criado com sucesso:",a),t.instagramDataService.clearDados(),t.router.navigate(["/crm/home",t.username])}catch(n){console.error("Erro ao salvar lead:",n),t.erro="Erro ao criar lead. Tente novamente."}finally{t.carregando=!1}}})()}validarFormulario(){return this.lead.nomeResponsavel?this.lead.empresa?this.lead.cidade?!!this.lead.instagramHandle||(this.erro="Username do Instagram \xe9 obrigat\xf3rio",!1):(this.erro="Cidade da empresa \xe9 obrigat\xf3ria",!1):(this.erro="Nome da empresa \xe9 obrigat\xf3rio",!1):(this.erro="Nome do respons\xe1vel \xe9 obrigat\xf3rio",!1)}cancelar(){this.instagramDataService.clearDados(),this.router.navigate(["/crm/home",this.username])}getFotoPerfilInstagram(){return this.dadosInstagram?.user?.profile_pic_url?this.dadosInstagram.user.profile_pic_url:"/assets/images/default-avatar.png"}getSeguidoresFormatado(){const t=this.dadosInstagram?.user?.edge_followed_by?.count;return t?t>=1e6?`${(t/1e6).toFixed(1)}M`:t>=1e3?`${(t/1e3).toFixed(1)}K`:t.toString():"0"}solicitarDadosInstagram(){const t={tipo:"REQUEST_INSTAGRAM_DATA",username:this.username};window.parent&&window.parent!==window&&window.parent.postMessage({tipo:"NOVA_MENSAGEM",text:t},"*"),console.log("Solicita\xe7\xe3o de dados enviada para content script:",t)}setupInstagramDataListener(){window.addEventListener("message",t=>{t.data&&"INSTAGRAM_DATA_RESPONSE"===t.data.tipo&&(console.log("Dados do Instagram recebidos:",t.data),this.processarDadosInstagram(t.data)),t.data&&"INSTAGRAM_DATA_ERROR"===t.data.tipo&&(console.error("Erro ao buscar dados do Instagram:",t.data),this.processarErroInstagram(t.data))})}processarDadosInstagram(t){var n=this;return(0,f.Z)(function*(){console.log("Processando dados do Instagram para username:",t.username);try{console.log("Enviando texto do Instagram para API dadosig2..."),n.carregando=!0;const a=yield n.leadService.enviarDadosInstagram(t.textoInsta,null,t.username);console.log("Lead processado pela API dadosig2:",a),a?(console.log("Objeto Lead processado com sucesso:",a),n.username=t.username,n.preencherFormularioComLeadProcessado(a),n.instagramDataService.setDados(n.dadosInstagram,n.username),console.log("Lead processado e formul\xe1rio preenchido:",n.lead),console.log("Dados simulados para template:",n.dadosInstagram)):(console.error("Erro: resposta vazia da API dadosig2"),n.erro="Erro ao processar dados do Instagram na API.")}catch(a){console.error("Erro ao enviar dados para API dadosig2:",a),n.erro="Erro ao processar dados do Instagram. Tente novamente."}finally{n.carregando=!1}})()}enviarDadosInstagramParaAPI(t){var n=this;return(0,f.Z)(function*(){try{console.log("Enviando dados do Instagram para API:",t.data);const a=yield n.leadService.enviarDadosInstagram(t.data,null,t.username);a.sucesso?console.log("Lead criado/atualizado com sucesso:",a.dados):console.error("Erro na resposta da API:",a.erro)}catch(a){console.error("Erro ao enviar dados do Instagram para API:",a)}})()}processarErroInstagram(t){console.error("Erro ao buscar dados do Instagram:",t.error),this.erro="Erro ao buscar dados do Instagram. Tente novamente."}mostrarFormularioManual(){this.mostrarFormulario=!0,console.log("Formul\xe1rio manual ativado")}onWebsiteBlur(){if(this.lead.website){const t=this.lead.website;this.lead.website=this.normalizarUrl(this.lead.website),t!==this.lead.website&&console.log("URL normalizada:",t,"->",this.lead.website)}}onTelefoneBlur(){this.lead.telefone=this.limparTelefone(this.telefoneFormatado),console.log("Telefone formatado:",this.telefoneFormatado,"-> Limpo:",this.lead.telefone)}onTelefoneChange(t){this.lead.telefone=this.limparTelefone(t||"")}onTelefonePaste(t){t.preventDefault();const n=t.clipboardData?.getData("text")||"";console.log("Telefone colado:",n);const a=this.processarTelefoneColado(n);this.lead.telefone=a,this.telefoneFormatado=this.formatarTelefone(a),console.log("Telefone processado:",this.telefoneFormatado),this.cdr.detectChanges()}limparTelefone(t){return t?t.replace(/\D/g,""):""}processarTelefoneColado(t){let n=t.replace(/\D/g,"");return n.startsWith("55")&&n.length>11&&(n=n.substring(2),console.log("Removido c\xf3digo do pa\xeds 55, telefone:",n)),n.length>11&&(n=n.substring(n.length-11),console.log("Telefone tinha mais de 11 d\xedgitos, pegando \xfaltimos 11:",n)),n}carregarDadosDoLink(){var t=this;return(0,f.Z)(function*(){if(t.lead.website){t.lead.website=t.normalizarUrl(t.lead.website);try{new URL(t.lead.website)}catch{return void(t.erro="Informe uma URL v\xe1lida (ex: https://exemplo.com.br)")}t.carregandoWebsite=!0,t.erro="",t.marcarWebsiteAnalisado();try{console.log("Carregando dados do website:",t.lead.website);const n=yield t.leadService.analisarWebsite(t.lead.website);if(n){console.log("Dados do website recebidos:",n),n.linksCategorized&&Array.isArray(n.linksCategorized)?(t.linksEncontrados=n.linksCategorized.sort((c,g)=>c.ordem-g.ordem),console.log("Links categorizados recebidos da API:",t.linksEncontrados),console.log("Total de links categorizados:",t.linksEncontrados.length),t.linksEncontrados.forEach((c,g)=>{console.log(`Link ${g+1}: ${c.tipo} = ${c.url} (${c.descricao})`)})):(console.warn("Nenhum link categorizado recebido da API ou formato inv\xe1lido:",n.linksCategorized),t.linksEncontrados=[]);const a=t.linksEncontrados.find(c=>"WhatsApp"===c.tipo);if(a&&!t.lead.telefone){const c=a.url.match(/(\d{10,15})/);c&&(t.lead.telefone=c[1],t.telefoneFormatado=t.formatarTelefone(t.lead.telefone))}const i=t.linksEncontrados.find(c=>"Instagram"===c.tipo);if(i&&!t.lead.instagramHandle){const c=i.url.match(/instagram\.com\/([^\/\?]+)/);c&&(t.lead.instagramHandle=c[1])}const s=t.linksEncontrados.find(c=>"Site do Card\xe1pio"===c.tipo||"Concorrente"===c.tipo);s&&(t.lead.linkCardapio=s.url,console.log(`Link de card\xe1pio/concorrente encontrado: ${s.tipo} = ${s.url}`)),console.log("Formul\xe1rio atualizado com dados dos links:",t.lead)}else t.erro="N\xe3o foi poss\xedvel extrair dados do website informado"}catch(n){console.error("Erro ao carregar dados do website:",n),t.erro="Erro ao analisar o website. Verifique a URL e tente novamente."}finally{t.carregandoWebsite=!1}}else t.erro="Informe o website para carregar os dados"})()}normalizarUrl(t){return!t||(t=t.trim()).match(/^https?:\/\//i)?t:`https://${t}`}abrirLink(t){const n=this.normalizarUrl(t);window.open(n,"_blank"),console.log("Abrindo link:",t,"-> normalizado:",n)}descobrirCnpj(){var t=this;return(0,f.Z)(function*(){if(t.lead.empresa)if(t.lead.cidade){t.carregandoCnpj=!0,t.erro="",t.cnpjsEncontrados=[],t.cnpjRecusadoExplicitamente=!1,t.marcarCnpjBuscado();try{console.log("Descobrindo CNPJs para empresa:",t.lead.empresa,"em",t.lead.cidade);const n=yield t.leadService.descobrirCnpj(t.lead.empresa,t.lead.cidade);console.log("Resposta completa do servidor:",n),console.log("Tipo da resposta:",typeof n),console.log("Estrutura da resposta:",Object.keys(n||{}));const a=n;console.log("Dados extra\xeddos:",a),console.log("CNPJs encontrados:",a?.cnpjsEncontrados),console.log("Total encontrados:",a?.totalEncontrados),console.log("dados existe?",!!a),console.log("dados.cnpjsEncontrados existe?",!!a?.cnpjsEncontrados),console.log("dados.cnpjsEncontrados \xe9 array?",Array.isArray(a?.cnpjsEncontrados)),console.log("length do array:",a?.cnpjsEncontrados?.length),a&&a.cnpjsEncontrados&&a.cnpjsEncontrados.length>0?(t.cnpjsEncontrados=a.cnpjsEncontrados.map((i,s)=>({...i,id:`cnpj_${s}`,selecionado:!1})),console.log("CNPJs encontrados:",a.totalEncontrados),console.log("Lista de CNPJs processada:",t.cnpjsEncontrados),console.log("Propriedade cnpjsEncontrados.length:",t.cnpjsEncontrados.length),console.log("Primeiro CNPJ:",t.cnpjsEncontrados[0]),t.cdr.detectChanges(),setTimeout(()=>{console.log("Ap\xf3s timeout - cnpjsEncontrados.length:",t.cnpjsEncontrados.length),t.cdr.detectChanges()},100),1===a.totalEncontrados&&"alta"===a.cnpjsEncontrados[0].confianca&&(t.selecionarCnpj(t.cnpjsEncontrados[0]),console.log("CNPJ \xfanico com alta confian\xe7a selecionado automaticamente"))):(console.warn("Condi\xe7\xe3o de CNPJs falhou:",{dados:!!a,cnpjsEncontrados:!!a?.cnpjsEncontrados,isArray:Array.isArray(a?.cnpjsEncontrados),length:a?.cnpjsEncontrados?.length,dadosCompletos:a}),t.erro=`Nenhum CNPJ encontrado para "${t.lead.empresa}" em ${t.lead.cidade}. Tente com um nome mais espec\xedfico ou verifique se a empresa possui CNPJ.`,console.warn("Nenhum CNPJ encontrado para a busca realizada"))}catch(n){console.error("Erro ao descobrir CNPJs:",n),t.erro=`Erro ao buscar CNPJs: ${n}`}finally{t.carregandoCnpj=!1}}else t.erro="Informe a cidade da empresa para descobrir o CNPJ";else t.erro="Informe o nome da empresa para descobrir o CNPJ"})()}selecionarCnpj(t){if(this.cnpjsEncontrados.forEach(n=>{n.selecionado=n.id===t.id}),this.lead.cnpj=t.cnpj,this.razaoSocialSelecionada=t.razaoSocial||"",this.cnpjRecusadoExplicitamente=!1,this.erro="",console.log("Mantendo nome da empresa do Instagram:",this.lead.empresa),t.nomeFantasia&&t.nomeFantasia!==this.lead.empresa&&(console.log("Nome fantasia do CNPJ \xe9 diferente:",t.nomeFantasia),console.log("Mas mantemos o nome original:",this.lead.empresa)),t.razaoSocial){const n=`Raz\xe3o Social: ${t.razaoSocial}`;this.lead.observacoes?this.lead.observacoes.includes("Raz\xe3o Social:")||(this.lead.observacoes=`${n}\n\n${this.lead.observacoes}`):this.lead.observacoes=n}t.endereco&&!this.lead.endereco&&(this.lead.endereco=t.endereco,console.log("Endere\xe7o preenchido automaticamente:",t.endereco)),console.log("CNPJ selecionado:",t.cnpj),console.log("Dados atualizados no formul\xe1rio:",{cnpj:this.lead.cnpj,empresa:this.lead.empresa+" (mantido do Instagram)",endereco:this.lead.endereco})}removerSelecaoCnpj(){this.cnpjsEncontrados.forEach(t=>{t.selecionado=!1}),this.lead.cnpj="",this.razaoSocialSelecionada="",this.cnpjRecusadoExplicitamente=!1,console.log("Sele\xe7\xe3o de CNPJ removida")}formatarCnpjInput(t){let n=t.target.value.replace(/\D/g,"");n.length<=14&&(n=n.replace(/(\d{2})(\d)/,"$1.$2"),n=n.replace(/(\d{2})\.(\d{3})(\d)/,"$1.$2.$3"),n=n.replace(/\.(\d{3})(\d)/,".$1/$2"),n=n.replace(/(\d{4})(\d)/,"$1-$2")),t.target.value=n,this.cnpjManual=n}buscarCnpjManual(){var t=this;return(0,f.Z)(function*(){if(!t.cnpjManual)return void(t.erro="Informe o CNPJ para buscar os dados da empresa");const n=t.cnpjManual.replace(/\D/g,"");if(14===n.length){t.carregandoCnpjManual=!0,t.erro="";try{console.log("Buscando dados para CNPJ manual:",t.cnpjManual);const a=yield t.leadService.buscarDetalhesSocios(n);if(a&&a.empresa){const i={id:"cnpj_manual",cnpj:n,cnpjFormatado:t.cnpjManual,nomeFantasia:a.empresa.nomeFantasia||a.empresa.razaoSocial||"Nome n\xe3o informado",razaoSocial:a.empresa.razaoSocial||"",endereco:a.empresa.endereco||"",capitalSocial:a.empresa.capitalSocial||"",porte:a.empresa.porte||"",situacao:a.empresa.situacao||"ATIVA",atividadePrincipal:a.empresa.naturezaJuridica||a.empresa.atividadePrincipal||"",socios:a.socios?a.socios.map(s=>s.nome).slice(0,3):[],confianca:"manual",fonte:"Informado manualmente",selecionado:!1};t.cnpjsEncontrados=[i],t.selecionarCnpj(i),console.log("Dados do CNPJ manual encontrados:",i),t.cnpjManual="",t.cdr.detectChanges()}else t.erro="CNPJ n\xe3o encontrado ou dados indispon\xedveis. Verifique o n\xfamero informado.",console.warn("Nenhum dado encontrado para o CNPJ manual:",n)}catch(a){console.error("Erro ao buscar CNPJ manual:",a),t.erro="Erro ao buscar dados do CNPJ. Verifique o n\xfamero e tente novamente."}finally{t.carregandoCnpjManual=!1}}else t.erro="CNPJ deve ter 14 d\xedgitos"})()}abrirGoogleCnpj(){if(!this.cnpjManual)return void console.warn("CNPJ manual n\xe3o informado para busca no Google");this.cnpjManual.replace(/\D/g,"");const a=`https://www.google.com/search?q=${encodeURIComponent(`CNPJ ${this.cnpjManual}`)}`;console.log("Abrindo busca Google para CNPJ:",this.cnpjManual),window.open(a,"_blank")}abrirGoogleEmpresaCnpj(){if(!this.lead.empresa)return void console.warn("Nome da empresa n\xe3o informado para busca no Google");const t=`${this.lead.empresa} CNPJ`,n=`https://www.google.com/search?q=${encodeURIComponent(t)}`;console.log("Abrindo busca Google para empresa:",t),window.open(n,"_blank")}abrirGoogleEmpresaCompleta(){if(!this.lead.empresa)return void console.warn("Nome da empresa n\xe3o informado para busca no Google");let t=`${this.lead.empresa}`;this.lead.cidade&&(t+=` ${this.lead.cidade}`),t+=" CNPJ";const n=`https://www.google.com/search?q=${encodeURIComponent(t)}`;console.log("Abrindo busca Google completa:",t),window.open(n,"_blank")}abrirGoogleReceitaFederal(){console.log("Abrindo consulta Receita Federal"),window.open("https://solucoes.receita.fazenda.gov.br/servicos/cnpjreva/cnpjreva_solicitacao.asp","_blank")}abrirConsultaCNPJReceita(){this.lead.cnpj&&navigator.clipboard.writeText(this.lead.cnpj).then(()=>{console.log("CNPJ copiado para \xe1rea de transfer\xeancia:",this.lead.cnpj)}).catch(n=>{console.error("Erro ao copiar CNPJ:",n)}),console.log("Abrindo consulta Receita Federal para buscar s\xf3cios"),window.open("https://solucoes.receita.fazenda.gov.br/servicos/cnpjreva/cnpjreva_solicitacao.asp","_blank")}adicionarSocioManual(){if(!this.nomeSocioManual?.trim())return;const t={nome:this.nomeSocioManual.trim(),cpf:null,participacao:null,cargo:"S\xf3cio",dataEntrada:null,qualificacao:null,principal:!0,observacoes:"Informado manualmente",scoreAnalise:50,motivoSelecao:"Informado manualmente pelo usu\xe1rio",manual:!0};this.sociosDetalhados=[t],this.lead.nomeResponsavel=t.nome,this.parceirSelecionado=!0,this.nomeSocioManual="",this.cdr.detectChanges(),console.log("S\xf3cio adicionado manualmente:",t.nome),console.log("Nome respons\xe1vel atualizado para:",this.lead.nomeResponsavel)}abrirGoogleVerificarCnpj(t){const a=`${t.nomeFantasia||t.razaoSocial||"empresa"} CNPJ ${t.cnpj}`,i=`https://www.google.com/search?q=${encodeURIComponent(a)}`;console.log("Abrindo busca Google para verificar CNPJ:",a),window.open(i,"_blank")}confirmarNenhumCnpj(){this.cnpjRecusadoExplicitamente=!0,this.removerSelecaoCnpj(),this.erro="",console.log("Usu\xe1rio confirmou que n\xe3o quer escolher nenhum CNPJ")}copiarCnpj(t){return(0,f.Z)(function*(){try{yield navigator.clipboard.writeText(t),console.log("CNPJ copiado:",t)}catch(n){console.error("Erro ao copiar CNPJ:",n)}})()}abrirCnpjBiz(t){const a=`https://cnpj.biz/${t.replace(/[^\d]/g,"")}`;window.open(a,"_blank"),console.log("Abrindo CNPJ.biz para:",t,"-> URL:",a)}getCorConfianca(t){switch(t){case"alta":return"success";case"media":return"warning";case"baixa":return"danger";default:return"secondary"}}getIconeConfianca(t){switch(t){case"alta":return"fa-check-circle";case"media":return"fa-exclamation-circle";case"baixa":return"fa-times-circle";default:return"fa-question-circle"}}copiarLink(t){var n=this;return(0,f.Z)(function*(){try{const a=n.normalizarUrl(t);yield navigator.clipboard.writeText(a),console.log("Link copiado:",t,"-> normalizado:",a)}catch(a){console.error("Erro ao copiar link:",a);const i=n.normalizarUrl(t),s=document.createElement("textarea");s.value=i,document.body.appendChild(s),s.select(),document.execCommand("copy"),document.body.removeChild(s)}})()}formatarTelefone(t){if(!t)return"";const n=t.replace(/\D/g,"");return 11===n.length?n.replace(/^(\d{2})(\d{5})(\d{4})/,"($1) $2-$3"):10===n.length?n.replace(/^(\d{2})(\d{4})(\d{4})/,"($1) $2-$3"):t}getIconeTelefone(t){const n=this.tiposTelefone.find(a=>a.valor===t);return n?n.icone:"fa-phone"}getCorTelefone(t){const n=this.tiposTelefone.find(a=>a.valor===t);return n?n.cor:"#6c757d"}abrirTelefone(t){if("WhatsApp"===t.tipo||"Celular"===t.tipo){const a=`https://wa.me/55${t.numero.replace(/\D/g,"")}`;window.open(a,"_blank")}else this.copiarTelefone(t.numero)}copiarTelefone(t){var n=this;return(0,f.Z)(function*(){try{const a=n.formatarTelefone(t);yield navigator.clipboard.writeText(a),console.log("Telefone copiado:",a)}catch(a){console.error("Erro ao copiar telefone:",a)}})()}buscarDetalhesSocios(){var t=this;return(0,f.Z)(function*(){if(t.lead.cnpj){t.carregandoSocios=!0,t.erro="",t.sociosBuscados=!0,t.parceirSelecionado=!1;try{console.log("Buscando s\xf3cios para CNPJ:",t.lead.cnpj);const n=yield t.leadService.buscarDetalhesSocios(t.lead.cnpj);if(console.log("Resposta do leadService (j\xe1 extra\xedda):",n),n&&n.socios){if(console.log("Dados recebidos:",n),console.log("Array de s\xf3cios:",n.socios),Array.isArray(n.socios)?t.sociosDetalhados=n.socios:(console.error("response.socios n\xe3o \xe9 um array:",n.socios),t.sociosDetalhados=[]),console.log("S\xf3cios atribu\xeddos a this.sociosDetalhados:",t.sociosDetalhados),console.log("Quantidade de s\xf3cios:",t.sociosDetalhados.length),console.log("sociosBuscados:",t.sociosBuscados),t.sociosDetalhados.forEach((a,i)=>{console.log(`S\xf3cio ${i}:`,a)}),t.sociosDetalhados.length>0){const a=t.sociosDetalhados.find(i=>i.principal);a?(t.lead.nomeResponsavel=a.nome,t.parceirSelecionado=!0,console.log("\u{1f3af} S\xf3cio principal identificado automaticamente:",a.nome),console.log(`   Score: ${a.scoreAnalise}/100`),console.log(`   Motivo: ${a.motivoSelecao}`),console.log("   Nome respons\xe1vel atualizado para:",t.lead.nomeResponsavel)):(t.sociosDetalhados[0].principal=!0,t.lead.nomeResponsavel=t.sociosDetalhados[0].nome,t.parceirSelecionado=!0,console.log("\u26a0\ufe0f Fallback: primeiro s\xf3cio definido como principal:",t.sociosDetalhados[0].nome)),t.cdr.detectChanges()}if(t.cdr.detectChanges(),setTimeout(()=>{console.log("Ap\xf3s timeout - sociosDetalhados:",t.sociosDetalhados),console.log("Ap\xf3s timeout - length:",t.sociosDetalhados.length),console.log("Ap\xf3s timeout - lead.nomeResponsavel:",t.lead.nomeResponsavel),t.cdr.detectChanges()},100),n.empresa){console.log("Informa\xe7\xf5es extras da empresa:",n.empresa);const a=n.empresa;let i="";a.razaoSocial&&a.razaoSocial!==t.lead.empresa&&(i+=`Raz\xe3o Social: ${a.razaoSocial}\n`),a.capitalSocial&&(i+=`Capital Social: ${a.capitalSocial}\n`),a.porte&&(i+=`Porte: ${a.porte}\n`),a.naturezaJuridica&&(i+=`Natureza Jur\xeddica: ${a.naturezaJuridica}\n`),"Sim"===a.mei&&(i+="MEI: Sim\n"),i&&!t.lead.observacoes?.includes("Raz\xe3o Social:")&&(t.lead.observacoes=i+(t.lead.observacoes||""))}if(0===t.sociosDetalhados.length){const a=t.cnpjsEncontrados.find(i=>i.cnpj===t.lead.cnpj);if(a&&a.socios&&a.socios.length>0){const i=a.socios;t.sociosDetalhados=i.map((s,c)=>({nome:s,cpf:null,participacao:null,cargo:"S\xf3cio",dataEntrada:null,qualificacao:null,principal:0===c,observacoes:"",scoreAnalise:0===c?60:30,motivoSelecao:0===c?1===i.length?"S\xf3cio \xfanico da empresa":"Primeiro na lista":"S\xf3cio secund\xe1rio"})),t.sociosDetalhados.length>0&&(t.lead.nomeResponsavel=t.sociosDetalhados[0].nome,t.parceirSelecionado=!0,1===t.sociosDetalhados.length?(console.log("\u{1f3af} S\xf3cio \xfanico (do CNPJ) definido automaticamente como respons\xe1vel:",t.sociosDetalhados[0].nome),console.log("   Score: 60/100 (s\xf3cio \xfanico)")):(console.log("\u{1f3af} Primeiro s\xf3cio (do CNPJ) definido como respons\xe1vel:",t.sociosDetalhados[0].nome),console.log("   Score: 60/100 (primeiro na lista)")),console.log("   Nome respons\xe1vel atualizado para:",t.lead.nomeResponsavel),t.cdr.detectChanges()),console.log("Usando s\xf3cios b\xe1sicos do CNPJ selecionado:",t.sociosDetalhados)}}}else console.warn("Nenhum s\xf3cio encontrado na busca detalhada. Response:",n),t.sociosDetalhados=[],t.parceirSelecionado=!1,"string"==typeof n&&(t.erro=n)}catch(n){console.error("Erro ao buscar s\xf3cios:",n),t.erro="Erro ao buscar informa\xe7\xf5es dos s\xf3cios. Tente novamente.",t.sociosDetalhados=[],t.parceirSelecionado=!1}finally{t.carregandoSocios=!1}}else t.erro="CNPJ n\xe3o selecionado"})()}extrairPorcentagem(t){if(!t)return 0;const n=t.match(/(\d+(?:\.\d+)?)/);return n?parseFloat(n[1]):0}identificarSocioPrincipalAutomaticamente(){if(!this.sociosDetalhados||0===this.sociosDetalhados.length)return void console.log("Nenhum s\xf3cio encontrado para identifica\xe7\xe3o autom\xe1tica");let t=null,n="";const a=["presidente","diretor presidente","ceo","diretor","administrador","s\xf3cio administrador","gerente","representante legal"];for(const i of a)if(t=this.sociosDetalhados.find(s=>s.cargo?.toLowerCase().includes(i)),t){n=`cargo: ${t.cargo}`;break}if(!t){const i=this.sociosDetalhados.reduce((c,g)=>{const u=this.extrairPorcentagem(c.participacao)||0;return(this.extrairPorcentagem(g.participacao)||0)>u?g:c});this.extrairPorcentagem(i.participacao)>0&&(t=i,n=`maior participa\xe7\xe3o: ${i.participacao}`)}if(!t){const i=["administrador","diretor","representante"];for(const s of i)if(t=this.sociosDetalhados.find(c=>c.qualificacao?.toLowerCase().includes(s)),t){n=`qualifica\xe7\xe3o: ${t.qualificacao}`;break}}t||(t=this.sociosDetalhados[0],n="primeiro da lista"),t&&(console.log(`S\xf3cio principal identificado automaticamente: ${t.nome} (crit\xe9rio: ${n})`),this.marcarComoPrincipal(t),t.selecionadoAutomaticamente=!0,t.criterioSelecao=n)}marcarComoPrincipal(t){this.sociosDetalhados.forEach(n=>{n.principal=!1,n!==t&&(n.selecionadoAutomaticamente=!1,n.criterioSelecao=null)}),t.principal=!0,t.selecionadoAutomaticamente||(t.selecionadoManualmente=!0),this.lead.nomeResponsavel=t.nome,this.parceirSelecionado=!0,this.erro="",console.log("S\xf3cio marcado como principal:",t.nome),console.log("Nome respons\xe1vel atualizado para:",this.lead.nomeResponsavel),t.scoreAnalise&&(console.log(`Score do s\xf3cio selecionado: ${t.scoreAnalise}/100`),console.log(`Motivo da sele\xe7\xe3o: ${t.motivoSelecao}`))}getSocioPrincipal(){return this.sociosDetalhados?.find(t=>t.principal)}getScoreClasse(t){return t>=80?"badge-success":t>=60?"badge-warning":t>=40?"badge-info":"badge-secondary"}formatarMotivoSelecao(t){return t?t.charAt(0).toUpperCase()+t.slice(1):""}buscarEmpresaNoGoogle(){if(console.log("\u{1f50d} buscarEmpresaNoGoogle called - lead.empresa:",this.lead.empresa),!this.lead.empresa?.trim())return void console.warn("Nome da empresa n\xe3o informado para busca no Google");let t=this.lead.empresa.trim();this.lead.cidade?.trim()&&(t+=` ${this.lead.cidade.trim()}`);const a=`https://www.google.com/search?q=${encodeURIComponent(t)}`;console.log("Abrindo busca Google para:",t),console.log("URL:",a),window.open(a,"_blank")}nextStep(){this.canAdvance()?(this.saveCurrentStepData(),this.currentStep++,this.etapaFoiPulada=!1,this.erro="",console.log("Avan\xe7ando para passo:",this.currentStep)):this.mostrarMensagemValidacao()}prevStep(){this.currentStep>1&&(this.saveCurrentStepData(),this.currentStep--,console.log("Voltando para passo:",this.currentStep))}goToStep(t){t>=1&&t<=this.totalSteps&&(this.saveCurrentStepData(),this.currentStep=t,console.log("Indo para passo:",this.currentStep))}getCamposFaltantes(){const t=[];return this.lead.nomeResponsavel?.trim()||t.push("Nome do Respons\xe1vel"),this.lead.empresa?.trim()||t.push("Nome da Empresa"),this.lead.cidade?.trim()||t.push("Cidade"),this.lead.instagramHandle?.trim()||t.push("Instagram"),this.telefoneFormatado&&(this.lead.telefone=this.limparTelefone(this.telefoneFormatado)),this.lead.telefone?.trim()||t.push("Telefone"),t}canAdvance(){switch(this.currentStep){case 1:return this.telefoneFormatado&&(this.lead.telefone=this.limparTelefone(this.telefoneFormatado)),!!(this.lead.nomeResponsavel&&this.lead.empresa&&this.lead.cidade&&this.lead.instagramHandle&&this.lead.telefone?.trim());case 2:return this.websiteAnalisado||!this.lead.website||this.etapaFoiPulada;case 3:return this.cnpjsEncontrados.length>0?!!this.lead.cnpj||this.cnpjRecusadoExplicitamente:this.cnpjBuscado||!this.lead.empresa||!this.lead.cidade||this.etapaFoiPulada;case 4:return this.parceirSelecionado||!this.lead.cnpj||this.etapaFoiPulada;case 5:return!0;default:return!1}}isStepCompleted(t){switch(t){case 1:return!!(this.lead.nomeResponsavel&&this.lead.empresa&&this.lead.cidade);case 2:return this.linksEncontrados.length>0;case 3:return!!this.lead.cnpj;case 4:return this.parceirSelecionado;default:return!1}}getStepClass(t){return t===this.currentStep?"active":t<this.currentStep||this.isStepCompleted(t)?"completed":"pending"}saveCurrentStepData(){switch(this.currentStep){case 1:this.wizardData.dadosBasicos={nomeResponsavel:this.lead.nomeResponsavel,empresa:this.lead.empresa,cidade:this.lead.cidade,telefone:this.lead.telefone,instagramHandle:this.lead.instagramHandle,website:this.lead.website,bioInsta:this.lead.bioInsta};break;case 2:this.wizardData.linksEncontrados=[...this.linksEncontrados];break;case 3:this.wizardData.cnpjSelecionado=this.lead.cnpj?{cnpj:this.lead.cnpj,empresa:this.lead.empresa,endereco:this.lead.endereco}:null;break;case 4:this.wizardData.sociosEncontrados=[...this.sociosDetalhados];break;case 5:this.wizardData.configuracoes={etapa:this.lead.etapa,origem:this.lead.origem,segmento:this.lead.segmento,observacoes:this.lead.observacoes,observacoesSocios:this.lead.observacoesSocios,sincronizarBitrix:this.sincronizarBitrix}}console.log("Dados salvos do passo",this.currentStep,":",this.wizardData)}skipStep(){this.currentStep<this.totalSteps&&this.canSkipCurrentStep()&&(console.log("Pulando passo:",this.currentStep),this.etapaFoiPulada=!0,this.nextStep())}getCurrentStepTitle(){switch(this.currentStep){case 1:return"Extrair Dados do Instagram";case 2:return"Buscar Links do Website";case 3:return"Descobrir CNPJ da Empresa";case 4:return"Buscar S\xf3cios da Empresa";case 5:return"Finalizar Lead";default:return"Passo Desconhecido"}}canSkipCurrentStep(){return 2===this.currentStep||3===this.currentStep||4===this.currentStep}mostrarMensagemValidacao(){switch(this.currentStep){case 1:const t=this.getCamposFaltantes();t.length>0&&(this.erro=`Preencha os campos obrigat\xf3rios: ${t.join(", ")}`);break;case 2:this.lead.website&&!this.websiteAnalisado&&(this.erro="Voc\xea deve analisar o website antes de continuar ou pular esta etapa.");break;case 3:this.cnpjsEncontrados.length>0&&!this.lead.cnpj&&!this.cnpjRecusadoExplicitamente?this.erro="Foram encontrados CNPJs para esta empresa. Selecione um ou confirme que nenhum corresponde ao lead.":this.lead.empresa&&this.lead.cidade&&!this.cnpjBuscado&&(this.erro="Voc\xea deve buscar o CNPJ da empresa antes de continuar ou pular esta etapa.");break;case 4:this.lead.cnpj&&this.sociosBuscados&&!this.parceirSelecionado?this.erro="Voc\xea deve selecionar um s\xf3cio como contato principal antes de continuar ou pular esta etapa.":this.lead.cnpj&&!this.sociosBuscados&&(this.erro="Voc\xea deve buscar os s\xf3cios da empresa antes de continuar ou pular esta etapa.");break;default:this.erro="Complete os dados obrigat\xf3rios para continuar."}}marcarWebsiteAnalisado(){this.websiteAnalisado=!0,this.erro=""}marcarCnpjBuscado(){this.cnpjBuscado=!0,this.erro=""}}return o.\u0275fac=function(t){return new(t||o)(e.\u0275\u0275directiveInject(b.gz),e.\u0275\u0275directiveInject(b.F0),e.\u0275\u0275directiveInject(ce),e.\u0275\u0275directiveInject(j),e.\u0275\u0275directiveInject(T),e.\u0275\u0275directiveInject(e.ChangeDetectorRef))},o.\u0275cmp=e.\u0275\u0275defineComponent({type:o,selectors:[["app-novo-lead"]],decls:38,vars:19,consts:[[1,"wizard-container"],[1,"wizard-header"],[1,"container-fluid"],[1,"row"],[1,"col-12"],[1,"wizard-progress"],[1,"step-progress"],["class","step",3,"class","click",4,"ngFor","ngForOf"],[1,"wizard-title"],[1,"step-title"],["class","fas fa-user-plus",4,"ngIf"],["class","fas fa-link",4,"ngIf"],["class","fas fa-id-card",4,"ngIf"],["class","fas fa-users",4,"ngIf"],["class","fas fa-check-circle",4,"ngIf"],["class","google-search-bar",4,"ngIf"],[1,"wizard-content"],["class","step-content step-1",4,"ngIf"],["class","step-content step-2",4,"ngIf"],["class","step-content step-3",4,"ngIf"],["class","step-content step-4",4,"ngIf"],["class","step-content step-5",4,"ngIf"],[1,"wizard-footer"],[1,"navigation-buttons"],["type","button","class","btn btn-outline-secondary",3,"click",4,"ngIf"],[1,"flex-grow-1"],["class","alert alert-danger mb-0 mr-3",4,"ngIf"],["type","button","class","btn btn-outline-warning mr-3",3,"click",4,"ngIf"],["type","button","class","btn btn-primary",3,"click",4,"ngIf"],["type","button","class","btn btn-success",3,"disabled","click",4,"ngIf"],[1,"step",3,"click"],[1,"step-number"],[1,"step-label"],[4,"ngIf"],[1,"fas","fa-user-plus"],[1,"fas","fa-link"],[1,"fas","fa-id-card"],[1,"fas","fa-users"],[1,"fas","fa-check-circle"],[1,"google-search-bar"],[1,"search-compact"],[1,"fab","fa-google"],["type","text","name","globalSearchTerm","placeholder","Empresa - Cidade",1,"form-control",3,"ngModel","ngModelChange"],["type","button","title","Ver empresa no Google",1,"btn",3,"disabled","click"],[1,"step-content","step-1"],[1,"card","extraction-main-card"],[1,"card-body"],[1,"card-title"],[1,"fab","fa-instagram","text-primary"],[1,"card-text","text-muted"],[1,"form-group"],["for","instagramUsername"],[1,"input-group"],[1,"input-group-prepend"],[1,"input-group-text"],["type","text","id","instagramUsername","name","instagramUsername","placeholder","restaurante_exemplo",1,"form-control",3,"ngModel","ngModelChange","keyup.enter"],[1,"input-group-append"],["type","button",1,"btn","btn-primary",3,"disabled","click"],["class","fas fa-download",4,"ngIf"],["class","fas fa-spinner fa-spin",4,"ngIf"],[1,"divider-or"],[1,"text-center"],["type","button",1,"btn","btn-outline-secondary",3,"click"],[1,"fas","fa-edit"],["class","row",4,"ngIf"],[1,"fas","fa-download"],[1,"fas","fa-spinner","fa-spin"],[1,"card","data-card"],[1,"fas","fa-check","text-success"],[1,"instagram-profile"],[1,"profile-header-simple"],[1,"profile-details"],[1,"username"],[1,"fab","fa-instagram"],["class","full-name",4,"ngIf"],[1,"stats"],[1,"stat"],["class","bio-section",4,"ngIf"],[1,"full-name"],[1,"bio-section"],[1,"bio-title"],[1,"fas","fa-quote-left"],[1,"bio-content"],[1,"card","form-card"],[1,"col-md-6"],["for","nomeResponsavel"],["type","text","id","nomeResponsavel","name","nomeResponsavel","placeholder","Jo\xe3o Silva","required","",1,"form-control",3,"ngModel","ngModelChange"],["for","empresa"],["type","text","id","empresa","name","empresa","placeholder","Restaurante Exemplo","required","",1,"form-control",3,"ngModel","ngModelChange"],[1,"form-text","text-muted"],[1,"fas","fa-info-circle"],["for","cidade"],["type","text","id","cidade","name","cidade","placeholder","S\xe3o Paulo","required","",1,"form-control",3,"ngModel","ngModelChange"],["for","telefone"],["id","telefone","name","telefone","placeholder","(62) 99999-9999","required","","campoTelefone","",1,"form-control",3,"ngModel","mask","ngModelChange","blur","valueChange","paste"],[1,"form-text","text-muted","text-danger"],[1,"fas","fa-exclamation-circle"],["for","instagramHandle"],["type","text","id","instagramHandle","name","instagramHandle","placeholder","restaurante_exemplo","required","",1,"form-control",3,"ngModel","ngModelChange"],["for","website"],["type","url","id","website","name","website","placeholder","https://exemplo.com.br",1,"form-control",3,"ngModel","ngModelChange","blur"],["class","col-md-6",4,"ngIf"],["for","bioInsta"],["id","bioInsta","name","bioInsta","rows","3","placeholder","Biografia extra\xedda do Instagram...",1,"form-control",3,"ngModel","ngModelChange"],["for","observacoesStep1"],["id","observacoesStep1","name","observacoesStep1","rows","3","placeholder","Observa\xe7\xf5es sobre o lead...",1,"form-control",3,"ngModel","ngModelChange"],[1,"step-content","step-2"],[1,"card","summary-card"],[1,"summary-content","d-flex","justify-content-between","align-items-center"],[1,"badge","badge-primary"],[1,"btn","btn-sm","btn-outline-secondary","ml-3",3,"click"],[1,"card","main-card"],[1,"fas","fa-link","text-primary"],["for","websiteAnalysis"],["type","url","id","websiteAnalysis","name","websiteAnalysis","placeholder","https://restaurante-exemplo.com.br",1,"form-control",3,"ngModel","ngModelChange","blur"],["class","fas fa-search",4,"ngIf"],["class","alert alert-info",4,"ngIf"],[1,"fas","fa-search"],[1,"alert","alert-info"],[1,"card","results-card"],[1,"links-grid"],["class","link-item",3,"link-whatsapp","link-instagram","link-ifood","link-cardapio","link-concorrente","link-localizacao","link-site",4,"ngFor","ngForOf"],[1,"mt-4"],[1,"text-muted","mb-3"],[1,"fas","fa-exclamation-triangle","text-warning"],["for","concorrente"],[1,"text-muted"],["id","concorrente","name","concorrente",1,"form-control",3,"ngModel","ngModelChange"],["value",""],[3,"value",4,"ngFor","ngForOf"],[1,"link-item"],[1,"link-icon"],["class","fab fa-whatsapp",4,"ngIf"],["class","fab fa-instagram",4,"ngIf"],["class","fas fa-utensils",4,"ngIf"],["class","fas fa-list-alt",4,"ngIf"],["class","fas fa-exclamation-triangle",4,"ngIf"],["class","fas fa-map-marker-alt",4,"ngIf"],["class","fas fa-globe",4,"ngIf"],[1,"link-content"],[1,"link-tipo"],[1,"link-descricao"],[1,"link-url"],[1,"link-actions"],["type","button","title","Abrir link",1,"btn","btn-sm","btn-outline-primary",3,"click"],[1,"fas","fa-external-link-alt"],["type","button","title","Copiar link",1,"btn","btn-sm","btn-outline-secondary",3,"click"],[1,"fas","fa-copy"],[1,"fab","fa-whatsapp"],[1,"fas","fa-utensils"],[1,"fas","fa-list-alt"],[1,"fas","fa-exclamation-triangle"],[1,"fas","fa-map-marker-alt"],[1,"fas","fa-globe"],[3,"value"],[1,"step-content","step-3"],[1,"summary-content"],[1,"summary-item"],[1,"d-flex","justify-content-between","align-items-center"],["class","summary-item",4,"ngIf"],[1,"card","main-card","cnpj-discovery-card"],[1,"fas","fa-id-card","text-info"],[1,"col-md-5"],["for","empresaBusca"],["type","text","id","empresaBusca","name","empresaBusca","placeholder","Nome exato da empresa",1,"form-control",3,"ngModel","ngModelChange"],[1,"col-md-4"],["for","cidadeBusca"],["type","text","id","cidadeBusca","name","cidadeBusca","placeholder","Cidade da empresa",1,"form-control",3,"ngModel","ngModelChange"],[1,"col-md-3"],["type","button",1,"btn","btn-primary","btn-block",3,"disabled","click"],[1,"divider-or","mt-3"],[1,"manual-search-section","mt-3"],[1,"text-center","mb-2","text-muted"],["class","text-center mb-2",4,"ngIf"],[1,"col-md-8","mx-auto"],[1,"text-muted","mb-2"],["type","text","name","cnpjManual","placeholder","00.000.000/0000-00","cnpjValido","","maxlength","18",1,"form-control",3,"ngModel","ngModelChange","input"],["cnpjManualField","ngModel"],["type","button",1,"btn","btn-warning",3,"disabled","click"],["class","invalid-feedback",4,"ngIf"],[1,"text-center","mb-2"],["type","button",1,"btn","btn-outline-info",3,"click"],[1,"invalid-feedback"],["type","button","class","btn btn-sm btn-outline-secondary float-right ml-1",3,"click",4,"ngIf"],["class","alert alert-info d-flex align-items-center",4,"ngIf"],[1,"cnpjs-grid"],["class","cnpj-card",3,"selected",4,"ngFor","ngForOf"],["type","button",1,"btn","btn-sm","btn-outline-secondary","float-right","ml-1",3,"click"],[1,"fas","fa-times"],[1,"alert","alert-info","d-flex","align-items-center"],[1,"fas","fa-info-circle","mr-2"],["type","button",1,"btn","btn-sm","btn-outline-info","ml-2",3,"click"],[1,"fas","fa-undo"],[1,"cnpj-card"],[1,"cnpj-header"],[1,"cnpj-badge"],[1,"cnpj-number"],[1,"cnpj-info"],["class","cnpj-name",4,"ngIf"],["class","cnpj-razao-social",4,"ngIf"],["class","cnpj-endereco",4,"ngIf"],["class","cnpj-capital",4,"ngIf"],["class","cnpj-atividade",4,"ngIf"],["class","cnpj-socios",4,"ngIf"],[1,"cnpj-footer-info"],["class","cnpj-status",4,"ngIf"],["class","cnpj-fonte",4,"ngIf"],[1,"cnpj-actions"],["type","button",1,"btn","btn-select",3,"click"],["class","fas fa-check",4,"ngIf"],["class","fas fa-plus",4,"ngIf"],["type","button","title","Ver detalhes",1,"btn","btn-outline-info","btn-details",3,"click"],["type","button","title","Verificar empresa no Google",1,"btn","btn-outline-secondary","btn-google",3,"click"],[1,"cnpj-name"],[1,"fas","fa-store"],[1,"cnpj-razao-social"],[1,"fas","fa-building"],[1,"cnpj-endereco"],[1,"cnpj-capital"],[1,"fas","fa-dollar-sign"],[1,"cnpj-atividade"],[1,"fas","fa-briefcase"],[1,"cnpj-socios"],[1,"cnpj-status"],[1,"status-badge"],[1,"cnpj-fonte"],[1,"fas","fa-check"],[1,"fas","fa-plus"],[1,"step-content","step-4"],["class","summary-content",4,"ngIf"],["class","alert alert-warning",4,"ngIf"],["class","text-muted small mt-1",4,"ngIf"],["class","text-muted ml-2",4,"ngIf"],[1,"text-muted","small","mt-1"],[1,"text-muted","ml-2"],[1,"alert","alert-warning"],[1,"fas","fa-users","text-primary"],["class","text-center",4,"ngIf"],["class","text-center mt-3",4,"ngIf"],["class","alert alert-info mt-3",4,"ngIf"],[1,"mt-3"],[1,"text-center","mt-3"],["type","button",1,"btn","btn-outline-primary","btn-sm",3,"disabled","click"],[1,"fas","fa-redo"],[1,"alert","alert-info","mt-3"],["class","badge badge-info ml-2",4,"ngIf"],[1,"socios-grid"],["class","socio-card","style","cursor: pointer",3,"principal","click",4,"ngFor","ngForOf"],["for","responsavelLead"],[1,"fas","fa-user-check","text-primary"],["type","text","id","responsavelLead","name","responsavelLead","placeholder","Nome do respons\xe1vel principal da empresa","title","Este ser\xe1 o contato principal do lead",1,"form-control",3,"ngModel","ngModelChange"],["for","observacoesSocios"],["id","observacoesSocios","name","observacoesSocios","rows","3","placeholder","Informa\xe7\xf5es adicionais sobre a estrutura societ\xe1ria ou decisores...",1,"form-control",3,"ngModel","ngModelChange"],[1,"badge","badge-info","ml-2"],[1,"fas","fa-robot"],[1,"fas","fa-magic"],[1,"badge","ml-1",3,"ngClass"],[1,"socio-card",2,"cursor","pointer",3,"click"],[1,"socio-header"],[1,"socio-number"],[1,"socio-nome"],[1,"fas","fa-user-tie"],["class","badge badge-warning ml-1",4,"ngIf"],["class","badge ml-1",3,"ngClass","title",4,"ngIf"],[1,"socio-actions"],["type","button","title","Marcar como contato principal",1,"btn","btn-sm",3,"click"],[1,"fas","fa-star"],[1,"socio-info"],["class","info-item",4,"ngIf"],[1,"socio-observacoes"],["rows","2","placeholder","Ex: Respons\xe1vel pelas decis\xf5es de marketing...",1,"form-control","form-control-sm",3,"ngModel","name","ngModelChange"],[1,"badge","badge-warning","ml-1"],[1,"badge","ml-1",3,"ngClass","title"],[1,"info-item"],[1,"fas","fa-id-badge"],[1,"fas","fa-percentage"],[1,"fas","fa-calendar-alt"],[1,"fas","fa-award"],[1,"card"],[1,"card-body","text-center"],[1,"fas","fa-user-slash","fa-3x","text-muted","mb-3"],[1,"d-flex","justify-content-center","mb-4"],["type","button",1,"btn","btn-outline-primary","mx-2",3,"click"],["type","button","title","Consultar CNPJ no site da Receita Federal",1,"btn","btn-outline-info","mx-2",3,"click"],[1,"manual-socio-input","mt-3"],[1,"input-group","mx-auto",2,"max-width","400px"],["type","text","name","nomeSocioManual","placeholder","Nome do s\xf3cio principal",1,"form-control",3,"ngModel","ngModelChange","keyup.enter"],["type","button",1,"btn","btn-success",3,"disabled","click"],[1,"form-text","text-muted","mt-1"],[1,"step-content","step-5"],[1,"col-md-8"],[1,"fas","fa-clipboard-list"],[1,"summary-section"],[1,"fas","fa-user"],[1,"summary-grid"],["class","summary-section",4,"ngIf"],[1,"fas","fa-cog"],["for","nomeResponsavelFinal"],["type","text","id","nomeResponsavelFinal","name","nomeResponsavelFinal","placeholder","Nome do respons\xe1vel",1,"form-control",3,"ngModel","ngModelChange"],["for","etapa"],["id","etapa","name","etapa",1,"form-control",3,"ngModel","ngModelChange"],["for","origem"],["id","origem","name","origem",1,"form-control",3,"ngModel","ngModelChange"],["for","segmento"],["id","segmento","name","segmento",1,"form-control",3,"ngModel","ngModelChange"],["for","observacoes"],["id","observacoes","name","observacoes","rows","3","placeholder","Observa\xe7\xf5es sobre o lead...",1,"form-control",3,"ngModel","ngModelChange"],[1,"form-check"],["type","checkbox","id","sincronizarBitrix","name","sincronizarBitrix",1,"form-check-input",3,"ngModel","ngModelChange"],["for","sincronizarBitrix",1,"form-check-label"],[1,"links-summary-grid"],["class","link-summary-item",3,"link-whatsapp","link-instagram","link-ifood","link-cardapio","link-concorrente","link-localizacao","link-site",4,"ngFor","ngForOf"],[1,"link-summary-item"],["class","link-descricao",4,"ngIf"],[1,"socios-summary-grid"],["class","socio-summary-item",4,"ngFor","ngForOf"],["class","mt-3",4,"ngIf"],[1,"socio-summary-item"],[1,"socio-info-summary"],["class","badge badge-warning ml-2",4,"ngIf"],[1,"socio-details"],["class","socio-obs",4,"ngIf"],[1,"badge","badge-warning","ml-2"],[1,"socio-obs"],[1,"text-muted","mb-0"],[1,"cnpj-summary"],[1,"fas","fa-arrow-left"],[1,"alert","alert-danger","mb-0","mr-3"],["type","button",1,"btn","btn-outline-warning","mr-3",3,"click"],[1,"fas","fa-forward"],["type","button",1,"btn","btn-primary",3,"click"],[1,"fas","fa-arrow-right"],["class","fas fa-save",4,"ngIf"],[1,"fas","fa-save"]],template:function(t,n){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"div",5)(6,"div",6),e.\u0275\u0275template(7,Pn,9,8,"div",7),e.\u0275\u0275elementEnd()()()()()(),e.\u0275\u0275elementStart(8,"div",8)(9,"div",2)(10,"div",3)(11,"div",4)(12,"h3",9),e.\u0275\u0275template(13,On,1,0,"i",10),e.\u0275\u0275template(14,yn,1,0,"i",11),e.\u0275\u0275template(15,Sn,1,0,"i",12),e.\u0275\u0275template(16,En,1,0,"i",13),e.\u0275\u0275template(17,wn,1,0,"i",14),e.\u0275\u0275text(18),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275template(19,kn,7,2,"div",15),e.\u0275\u0275elementStart(20,"div",16)(21,"div",2),e.\u0275\u0275template(22,Rn,33,8,"div",17),e.\u0275\u0275template(23,oo,42,12,"div",18),e.\u0275\u0275template(24,jo,71,21,"div",19),e.\u0275\u0275template(25,oa,13,5,"div",20),e.\u0275\u0275template(26,ka,76,18,"div",21),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(27,"div",22)(28,"div",2)(29,"div",3)(30,"div",4)(31,"div",23),e.\u0275\u0275template(32,Ia,3,0,"button",24),e.\u0275\u0275element(33,"div",25),e.\u0275\u0275template(34,La,3,1,"div",26),e.\u0275\u0275template(35,ja,3,0,"button",27),e.\u0275\u0275template(36,Ta,3,0,"button",28),e.\u0275\u0275template(37,Na,4,4,"button",29),e.\u0275\u0275elementEnd()()()()()()),2&t&&(e.\u0275\u0275advance(7),e.\u0275\u0275property("ngForOf",e.\u0275\u0275pureFunction0(18,Da)),e.\u0275\u0275advance(6),e.\u0275\u0275property("ngIf",1===n.currentStep),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",2===n.currentStep),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",3===n.currentStep),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",4===n.currentStep),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",5===n.currentStep),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",n.getCurrentStepTitle()," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==n.lead.empresa?null:n.lead.empresa.trim()),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",1===n.currentStep),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",2===n.currentStep),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",3===n.currentStep),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",4===n.currentStep),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",5===n.currentStep),e.\u0275\u0275advance(6),e.\u0275\u0275property("ngIf",n.currentStep>1),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",n.erro),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.canSkipCurrentStep()),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.currentStep<n.totalSteps),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.currentStep===n.totalSteps))},dependencies:[m.NgClass,m.NgForOf,m.NgIf,d.YN,d.Kr,d.Fj,d.Wl,d.EJ,d.JJ,d.Q7,d.nD,d.On,ae.Kb,m.SlicePipe,m.TitleCasePipe],styles:['.wizard-container[_ngcontent-%COMP%]{min-height:100vh;background:#f8f9fa;display:flex;flex-direction:column}kendo-maskedtextbox[_ngcontent-%COMP%]{display:block;width:100%}kendo-maskedtextbox[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%]{width:100%;padding:.375rem .75rem;font-size:1rem;font-weight:400;line-height:1.5;color:#495057;background-color:#fff;background-clip:padding-box;border:1px solid #ced4da;border-radius:.25rem;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out}kendo-maskedtextbox[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%]:focus{color:#495057;background-color:#fff;border-color:#80bdff;outline:0;box-shadow:0 0 0 .2rem #007bff40}.wizard-actions-bar[_ngcontent-%COMP%]{background:#f8f9fa;border-bottom:1px solid #e9ecef;padding:12px 0;margin-bottom:20px}.wizard-actions-bar[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.wizard-actions-bar[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .company-info[_ngcontent-%COMP%]{font-size:14px}.wizard-actions-bar[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .company-info[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#495057}@media (max-width: 768px){.wizard-actions-bar[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]{flex-direction:column;gap:10px;text-align:center}}.google-search-bar[_ngcontent-%COMP%]{background:#f8f9fa;border-bottom:1px solid #e9ecef;padding:8px 0}.google-search-bar[_ngcontent-%COMP%]   .search-compact[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;max-width:400px;margin:0 auto}.google-search-bar[_ngcontent-%COMP%]   .search-compact[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#4285f4;font-size:14px;flex-shrink:0}.google-search-bar[_ngcontent-%COMP%]   .search-compact[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{flex:1;padding:4px 8px;font-size:13px;border:1px solid #ced4da;border-radius:4px;min-height:28px}.google-search-bar[_ngcontent-%COMP%]   .search-compact[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{border-color:#4285f4;box-shadow:0 0 0 .1rem #4285f426}.google-search-bar[_ngcontent-%COMP%]   .search-compact[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder{color:#adb5bd;font-style:italic}.google-search-bar[_ngcontent-%COMP%]   .search-compact[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{background:#4285f4;border:1px solid #4285f4;color:#fff;font-weight:500;padding:4px 12px;border-radius:4px;font-size:12px;white-space:nowrap;min-height:28px;flex-shrink:0}.google-search-bar[_ngcontent-%COMP%]   .search-compact[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#3367d6;border-color:#3367d6}.google-search-bar[_ngcontent-%COMP%]   .search-compact[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled{background:#e9ecef;border-color:#e9ecef;color:#6c757d;cursor:not-allowed}@media (max-width: 768px){.google-search-bar[_ngcontent-%COMP%]   .search-compact[_ngcontent-%COMP%]{max-width:100%;margin:0 10px}}@media (max-width: 480px){.google-search-bar[_ngcontent-%COMP%]   .search-compact[_ngcontent-%COMP%]{gap:6px;margin:0 5px}.google-search-bar[_ngcontent-%COMP%]   .search-compact[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-size:11px;padding:4px 8px}}.wizard-header[_ngcontent-%COMP%]{background:white;border-bottom:1px solid #dee2e6;padding:20px 0;position:sticky;top:0;z-index:100;box-shadow:0 2px 4px #0000001a}.wizard-progress[_ngcontent-%COMP%]   .step-progress[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.wizard-progress[_ngcontent-%COMP%]   .step-progress[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;margin:0 30px;cursor:pointer;transition:all .3s ease}.wizard-progress[_ngcontent-%COMP%]   .step-progress[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%]{width:50px;height:50px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-weight:600;font-size:18px;margin-bottom:8px;border:3px solid #dee2e6;background:white;color:#6c757d;transition:all .3s ease}.wizard-progress[_ngcontent-%COMP%]   .step-progress[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%]{font-size:12px;font-weight:500;text-transform:uppercase;letter-spacing:.5px;color:#6c757d;transition:all .3s ease}.wizard-progress[_ngcontent-%COMP%]   .step-progress[_ngcontent-%COMP%]   .step.completed[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%]{background:#28a745;border-color:#28a745;color:#fff}.wizard-progress[_ngcontent-%COMP%]   .step-progress[_ngcontent-%COMP%]   .step.completed[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%]{color:#28a745}.wizard-progress[_ngcontent-%COMP%]   .step-progress[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%]{background:#007bff;border-color:#007bff;color:#fff;box-shadow:0 0 0 3px #007bff40}.wizard-progress[_ngcontent-%COMP%]   .step-progress[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%]{color:#007bff;font-weight:600}.wizard-progress[_ngcontent-%COMP%]   .step-progress[_ngcontent-%COMP%]   .step.pending[_ngcontent-%COMP%]{opacity:.6}.wizard-progress[_ngcontent-%COMP%]   .step-progress[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]:hover:not(.pending){transform:translateY(-2px)}.wizard-title[_ngcontent-%COMP%]{background:white;padding:20px 0;border-bottom:1px solid #dee2e6}.wizard-title[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]{margin:0;color:#2c3e50;font-size:28px;font-weight:600;display:flex;align-items:center}.wizard-title[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:12px;font-size:32px}.wizard-content[_ngcontent-%COMP%]{flex:1;padding:30px 0}.step-content[_ngcontent-%COMP%]{animation:fadeInUp .5s ease}@keyframes fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.card[_ngcontent-%COMP%]{border:none;border-radius:12px;box-shadow:0 4px 6px #0000001a;margin-bottom:20px;transition:all .3s ease}.card[_ngcontent-%COMP%]:hover{box-shadow:0 8px 15px #00000026}.card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]{font-weight:600;color:#2c3e50;margin-bottom:16px;display:flex;align-items:center}.card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:8px;font-size:20px}.main-card[_ngcontent-%COMP%]{border-left:4px solid #007bff}.extraction-main-card[_ngcontent-%COMP%]{border:2px solid #007bff;box-shadow:0 4px 12px #007bff1a}.instagram-profile[_ngcontent-%COMP%]   .profile-header-simple[_ngcontent-%COMP%]{margin-bottom:20px}.instagram-profile[_ngcontent-%COMP%]   .profile-header-simple[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%]{color:#007bff;font-weight:600;margin-bottom:8px;display:flex;align-items:center}.instagram-profile[_ngcontent-%COMP%]   .profile-header-simple[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:8px;color:#e1306c}.instagram-profile[_ngcontent-%COMP%]   .profile-header-simple[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .full-name[_ngcontent-%COMP%]{color:#6c757d;font-weight:500;margin-bottom:12px}.instagram-profile[_ngcontent-%COMP%]   .profile-header-simple[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%]{display:flex;gap:20px;flex-wrap:wrap}.instagram-profile[_ngcontent-%COMP%]   .profile-header-simple[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]{display:flex;align-items:center;color:#495057;font-size:14px}.instagram-profile[_ngcontent-%COMP%]   .profile-header-simple[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:6px;color:#007bff;font-size:12px}.instagram-profile[_ngcontent-%COMP%]   .profile-header-simple[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{margin-right:4px}.instagram-profile[_ngcontent-%COMP%]   .bio-section[_ngcontent-%COMP%]{background:rgba(0,123,255,.05);border:2px solid rgba(0,123,255,.3);padding:15px;border-radius:8px;margin-top:15px;box-shadow:0 2px 8px #007bff1a}.instagram-profile[_ngcontent-%COMP%]   .bio-section[_ngcontent-%COMP%]   .bio-title[_ngcontent-%COMP%]{color:#007bff;font-weight:600;margin-bottom:10px;font-size:14px;display:flex;align-items:center}.instagram-profile[_ngcontent-%COMP%]   .bio-section[_ngcontent-%COMP%]   .bio-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:8px;font-size:12px}.instagram-profile[_ngcontent-%COMP%]   .bio-section[_ngcontent-%COMP%]   .bio-content[_ngcontent-%COMP%]{color:#495057;line-height:1.6;font-style:italic;font-size:14px;background:white;padding:12px;border-radius:6px;border:1px solid rgba(0,123,255,.1)}.summary-card[_ngcontent-%COMP%]{background:#f8f9fa;border:2px solid #28a745;box-shadow:0 4px 12px #28a7451a}.summary-card[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]{display:flex;align-items:center;flex-wrap:wrap}.summary-card[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{margin-right:10px}.summary-card[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]{margin-bottom:5px}.data-card[_ngcontent-%COMP%]{border:2px solid #28a745;box-shadow:0 4px 12px #28a7451a}.form-card[_ngcontent-%COMP%]{border:2px solid #17a2b8;box-shadow:0 4px 12px #17a2b81a}.results-card[_ngcontent-%COMP%]{border:2px solid #ffc107;box-shadow:0 4px 12px #ffc1071a}.form-group[_ngcontent-%COMP%]{margin-bottom:15px}.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-weight:600;color:#2c3e50;margin-bottom:8px;display:block}.form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{border-radius:8px;border:2px solid #e9ecef;padding:12px 16px;font-size:14px;line-height:1.4;min-height:48px;transition:all .3s ease}.form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{border-color:#007bff;box-shadow:0 0 0 3px #007bff1a}.form-group[_ngcontent-%COMP%]   select.form-control[_ngcontent-%COMP%]{min-height:52px;padding:12px 40px 12px 12px;line-height:1.6;font-size:15px;appearance:none;background-image:url(\'data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" width="4" height="5" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4z"/><path fill="%23666" d="M0 3l2 2 2-2z"/></svg>\');background-repeat:no-repeat;background-position:right 12px center;background-size:12px}.form-group[_ngcontent-%COMP%]   .input-group-text[_ngcontent-%COMP%]{background:#f8f9fa;border:2px solid #e9ecef;border-right:none;color:#6c757d;font-weight:600}.form-group[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-prepend[_ngcontent-%COMP%]   .input-group-text[_ngcontent-%COMP%], .form-group[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-prepend[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-radius:8px 0 0 8px;border-right:none}.form-group[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .input-group-text[_ngcontent-%COMP%], .form-group[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-radius:0 8px 8px 0;border-left:none}.form-group[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:not(:first-child):not(:last-child){border-radius:0;border-left:none;border-right:none}.form-group[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:not(:first-child):last-child{border-radius:0 8px 8px 0;border-left:none}.form-group[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:first-child:not(:last-child){border-radius:8px 0 0 8px;border-right:none}.form-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{min-height:48px;padding:12px 16px;font-size:14px;line-height:1.4;font-weight:600;border-radius:0 8px 8px 0;border-left:none;border-width:2px}.form-group[_ngcontent-%COMP%]   .input-group-prepend[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{min-height:48px;padding:14px 16px;font-size:14px;line-height:1.4;font-weight:600;border-radius:8px 0 0 8px;border-right:none;border-width:2px}.divider-or[_ngcontent-%COMP%]{text-align:center;margin:20px 0;position:relative}.divider-or[_ngcontent-%COMP%]:before{content:"";position:absolute;top:50%;left:0;right:0;height:1px;background:#dee2e6}.divider-or[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{background:white;padding:0 20px;color:#6c757d;font-weight:500;position:relative}.card[_ngcontent-%COMP%]   .divider-or[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{background:#fff}.cnpj-discovery-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{padding:1.25rem}.cnpj-discovery-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]{margin-bottom:.5rem}.cnpj-discovery-card[_ngcontent-%COMP%]   .card-text[_ngcontent-%COMP%]{margin-bottom:1rem}.cnpj-discovery-card[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{margin-bottom:12px}.cnpj-discovery-card[_ngcontent-%COMP%]   .manual-search-section[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{margin-bottom:1rem!important}.cnpj-discovery-card[_ngcontent-%COMP%]   .divider-or[_ngcontent-%COMP%]{margin:15px 0}.instagram-profile[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:15px}.instagram-profile[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]   .profile-pic[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;margin-right:20px;border:3px solid #e1306c}.instagram-profile[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{margin:0;color:#2c3e50;font-size:18px;font-weight:600}.instagram-profile[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .full-name[_ngcontent-%COMP%]{margin:5px 0 10px;color:#6c757d;font-weight:500}.instagram-profile[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%]{display:flex;gap:20px}.instagram-profile[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]{text-align:center}.instagram-profile[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .count[_ngcontent-%COMP%]{display:block;font-weight:600;color:#2c3e50;font-size:16px}.instagram-profile[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{font-size:12px;color:#6c757d;text-transform:uppercase}.instagram-profile[_ngcontent-%COMP%]   .bio[_ngcontent-%COMP%]{color:#495057;line-height:1.5;font-style:italic}.links-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:15px}.links-grid[_ngcontent-%COMP%]   .link-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:15px;background:white;border:2px solid #e9ecef;border-radius:8px;transition:all .3s ease}.links-grid[_ngcontent-%COMP%]   .link-item[_ngcontent-%COMP%]:hover{border-color:#007bff;transform:translateY(-2px);box-shadow:0 4px 8px #0000001a}.links-grid[_ngcontent-%COMP%]   .link-item[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{margin-right:15px;font-size:24px}.links-grid[_ngcontent-%COMP%]   .link-item[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:30px;text-align:center}.links-grid[_ngcontent-%COMP%]   .link-item[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%]{flex:1}.links-grid[_ngcontent-%COMP%]   .link-item[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%]   .link-tipo[_ngcontent-%COMP%]{font-weight:600;color:#2c3e50;font-size:14px}.links-grid[_ngcontent-%COMP%]   .link-item[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%]   .link-descricao[_ngcontent-%COMP%]{color:#6c757d;font-size:12px;margin:2px 0}.links-grid[_ngcontent-%COMP%]   .link-item[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%]   .link-url[_ngcontent-%COMP%]{color:#007bff;font-size:11px;font-family:monospace}.links-grid[_ngcontent-%COMP%]   .link-item[_ngcontent-%COMP%]   .link-actions[_ngcontent-%COMP%]{display:flex;gap:5px}.links-grid[_ngcontent-%COMP%]   .link-item[_ngcontent-%COMP%]   .link-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:8px 12px;font-size:12px;min-height:34px;line-height:1.3;border-radius:6px}.links-grid[_ngcontent-%COMP%]   .link-item.link-whatsapp[_ngcontent-%COMP%]{border-left:4px solid #25d366}.links-grid[_ngcontent-%COMP%]   .link-item.link-whatsapp[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{color:#25d366}.links-grid[_ngcontent-%COMP%]   .link-item.link-instagram[_ngcontent-%COMP%]{border-left:4px solid #e1306c}.links-grid[_ngcontent-%COMP%]   .link-item.link-instagram[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{color:#e1306c}.links-grid[_ngcontent-%COMP%]   .link-item.link-ifood[_ngcontent-%COMP%]{border-left:4px solid #ea1d2c}.links-grid[_ngcontent-%COMP%]   .link-item.link-ifood[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{color:#ea1d2c}.links-grid[_ngcontent-%COMP%]   .link-item.link-cardapio[_ngcontent-%COMP%]{border-left:4px solid #28a745}.links-grid[_ngcontent-%COMP%]   .link-item.link-cardapio[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{color:#28a745}.links-grid[_ngcontent-%COMP%]   .link-item.link-concorrente[_ngcontent-%COMP%]{border-left:4px solid #dc3545}.links-grid[_ngcontent-%COMP%]   .link-item.link-concorrente[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{color:#dc3545}.links-grid[_ngcontent-%COMP%]   .link-item.link-localizacao[_ngcontent-%COMP%]{border-left:4px solid #17a2b8}.links-grid[_ngcontent-%COMP%]   .link-item.link-localizacao[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{color:#17a2b8}.links-grid[_ngcontent-%COMP%]   .link-item.link-site[_ngcontent-%COMP%]{border-left:4px solid #6f42c1}.links-grid[_ngcontent-%COMP%]   .link-item.link-site[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{color:#6f42c1}.cnpjs-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:15px;overflow-x:auto;max-width:100%}@media (max-width: 1200px){.cnpjs-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(320px,1fr));gap:12px}}@media (max-width: 1024px){.cnpjs-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:12px}}@media (max-width: 768px){.cnpjs-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:10px;padding:0 5px}}@media (max-width: 480px){.cnpjs-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:8px;padding:0 3px}}.cnpjs-grid[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{border-radius:8px;border-width:2px;margin-bottom:16px}.cnpjs-grid[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{min-height:32px;padding:6px 12px;font-size:12px;font-weight:600;border-radius:6px}.cnpjs-grid[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:4px}.cnpjs-grid[_ngcontent-%COMP%]   .alert.alert-warning[_ngcontent-%COMP%]{background-color:#fff3cd;border-color:#ffc107;color:#856404}.cnpjs-grid[_ngcontent-%COMP%]   .alert.alert-warning[_ngcontent-%COMP%]   .btn-outline-warning[_ngcontent-%COMP%]{color:#856404;border-color:#ffc107}.cnpjs-grid[_ngcontent-%COMP%]   .alert.alert-warning[_ngcontent-%COMP%]   .btn-outline-warning[_ngcontent-%COMP%]:hover{background-color:#ffc107;color:#212529}.cnpjs-grid[_ngcontent-%COMP%]   .alert.alert-info[_ngcontent-%COMP%]{background-color:#d1ecf1;border-color:#17a2b8;color:#0c5460}.cnpjs-grid[_ngcontent-%COMP%]   .alert.alert-info[_ngcontent-%COMP%]   .btn-outline-info[_ngcontent-%COMP%]{color:#0c5460;border-color:#17a2b8}.cnpjs-grid[_ngcontent-%COMP%]   .alert.alert-info[_ngcontent-%COMP%]   .btn-outline-info[_ngcontent-%COMP%]:hover{background-color:#17a2b8;color:#fff}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]{background:white;border:2px solid #e9ecef;border-radius:12px;padding:12px;transition:all .3s ease}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]:hover{border-color:#007bff;transform:translateY(-2px);box-shadow:0 6px 12px #00000026}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card.selected[_ngcontent-%COMP%]{border-color:#28a745;background:#f8fff9;box-shadow:0 6px 12px #28a74533}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:8px}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-header[_ngcontent-%COMP%]   .cnpj-badge[_ngcontent-%COMP%]{padding:4px 12px;border-radius:20px;font-size:11px;font-weight:600;text-transform:uppercase}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-header[_ngcontent-%COMP%]   .cnpj-badge.badge-success[_ngcontent-%COMP%]{background:#d4edda;color:#155724}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-header[_ngcontent-%COMP%]   .cnpj-badge.badge-warning[_ngcontent-%COMP%]{background:#fff3cd;color:#856404}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-header[_ngcontent-%COMP%]   .cnpj-badge.badge-danger[_ngcontent-%COMP%]{background:#f8d7da;color:#721c24}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-header[_ngcontent-%COMP%]   .cnpj-number[_ngcontent-%COMP%]{font-family:monospace;font-weight:600;color:#2c3e50;font-size:16px}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]{margin-bottom:10px}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-name[_ngcontent-%COMP%]{font-weight:600;color:#007bff;margin-bottom:6px;display:flex;align-items:center;word-break:break-word;line-height:1.3}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-name[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:6px;font-size:14px;flex-shrink:0}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-razao-social[_ngcontent-%COMP%]{margin-bottom:4px;display:flex;align-items:flex-start;color:#6c757d}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-razao-social[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:6px;margin-top:2px;font-size:12px;flex-shrink:0}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-razao-social[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{line-height:1.3;font-weight:500;word-break:break-word;overflow-wrap:break-word}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-endereco[_ngcontent-%COMP%]{margin-bottom:4px;display:flex;align-items:flex-start;color:#6c757d}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-endereco[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:6px;margin-top:2px;font-size:12px;color:#dc3545;flex-shrink:0}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-endereco[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{line-height:1.3;word-break:break-word;overflow-wrap:break-word}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-capital[_ngcontent-%COMP%]{margin-bottom:4px;display:flex;align-items:center;color:#28a745}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-capital[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:6px;font-size:12px}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-capital[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:11px}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-atividade[_ngcontent-%COMP%]{margin-bottom:4px;display:flex;align-items:flex-start;color:#6c757d}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-atividade[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:6px;margin-top:2px;font-size:12px;color:#6f42c1;flex-shrink:0}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-atividade[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{line-height:1.3;font-size:11px;word-break:break-word;overflow-wrap:break-word}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-socios[_ngcontent-%COMP%]{margin-bottom:6px;display:flex;align-items:flex-start;color:#fd7e14}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-socios[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:6px;margin-top:2px;font-size:12px;flex-shrink:0}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-socios[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{line-height:1.3;font-size:11px;font-weight:500;word-break:break-word;overflow-wrap:break-word}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-footer-info[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-top:6px;padding-top:6px;border-top:1px solid #f1f3f4}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-footer-info[_ngcontent-%COMP%]   .cnpj-status[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]{padding:2px 8px;border-radius:12px;font-size:10px;font-weight:600;text-transform:uppercase;background:#f8d7da;color:#721c24}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-footer-info[_ngcontent-%COMP%]   .cnpj-status[_ngcontent-%COMP%]   .status-badge.active[_ngcontent-%COMP%]{background:#d4edda;color:#155724}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-footer-info[_ngcontent-%COMP%]   .cnpj-fonte[_ngcontent-%COMP%]{flex:1;text-align:right}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-footer-info[_ngcontent-%COMP%]   .cnpj-fonte[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:10px}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-footer-info[_ngcontent-%COMP%]   .cnpj-fonte[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:4px}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-actions[_ngcontent-%COMP%]{display:flex;gap:6px;margin-top:12px}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-actions[_ngcontent-%COMP%]   .btn-select[_ngcontent-%COMP%]{flex:1;padding:8px 12px;font-size:13px;line-height:1.3;font-weight:600;min-height:36px;border-radius:6px;margin-bottom:0}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-actions[_ngcontent-%COMP%]   .btn-details[_ngcontent-%COMP%]{flex:1;padding:8px 12px;font-size:12px;line-height:1.3;font-weight:500;min-height:36px;border-radius:6px}@media (max-width: 768px){.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]{padding:10px}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:6px}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-header[_ngcontent-%COMP%]   .cnpj-number[_ngcontent-%COMP%]{font-size:14px}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-actions[_ngcontent-%COMP%]{flex-direction:column;gap:8px}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-actions[_ngcontent-%COMP%]   .btn-select[_ngcontent-%COMP%], .cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-actions[_ngcontent-%COMP%]   .btn-details[_ngcontent-%COMP%]{flex:none;width:100%;min-height:40px;font-size:14px}}@media (max-width: 480px){.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]{padding:8px}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-header[_ngcontent-%COMP%]   .cnpj-badge[_ngcontent-%COMP%]{margin-bottom:4px;align-self:flex-start}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-header[_ngcontent-%COMP%]   .cnpj-number[_ngcontent-%COMP%]{font-size:13px}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-name[_ngcontent-%COMP%]{font-size:14px}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-razao-social[_ngcontent-%COMP%]   small[_ngcontent-%COMP%], .cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-endereco[_ngcontent-%COMP%]   small[_ngcontent-%COMP%], .cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-atividade[_ngcontent-%COMP%]   small[_ngcontent-%COMP%], .cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-socios[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:11px}.cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-actions[_ngcontent-%COMP%]   .btn-select[_ngcontent-%COMP%], .cnpjs-grid[_ngcontent-%COMP%]   .cnpj-card[_ngcontent-%COMP%]   .cnpj-actions[_ngcontent-%COMP%]   .btn-details[_ngcontent-%COMP%]{padding:10px 16px;font-size:13px;min-height:44px}}.socios-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:12px}@media (max-width: 768px){.socios-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:10px}}@media (max-width: 480px){.socios-grid[_ngcontent-%COMP%]{gap:8px}}.socios-grid[_ngcontent-%COMP%]   .socio-card[_ngcontent-%COMP%]{background:white;border:2px solid #e9ecef;border-radius:12px;padding:12px;transition:all .3s ease;cursor:pointer}.socios-grid[_ngcontent-%COMP%]   .socio-card[_ngcontent-%COMP%]:hover{border-color:#007bff;box-shadow:0 4px 8px #00000026;background:rgba(0,123,255,.02)}.socios-grid[_ngcontent-%COMP%]   .socio-card[_ngcontent-%COMP%]:active{box-shadow:0 2px 4px #00000026}.socios-grid[_ngcontent-%COMP%]   .socio-card.principal[_ngcontent-%COMP%]{border-color:#ffc107;background:rgba(255,193,7,.05);box-shadow:0 4px 12px #ffc10733}.socios-grid[_ngcontent-%COMP%]   .socio-card.principal[_ngcontent-%COMP%]   .socio-number[_ngcontent-%COMP%]{background:#ffc107;color:#212529}.socios-grid[_ngcontent-%COMP%]   .socio-card.principal[_ngcontent-%COMP%]:hover{border-color:#e0a800;background:rgba(255,193,7,.08)}.socios-grid[_ngcontent-%COMP%]   .socio-card[_ngcontent-%COMP%]   .socio-header[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:8px}.socios-grid[_ngcontent-%COMP%]   .socio-card[_ngcontent-%COMP%]   .socio-header[_ngcontent-%COMP%]   .socio-number[_ngcontent-%COMP%]{background:#e9ecef;color:#6c757d;padding:3px 7px;border-radius:20px;font-size:11px;font-weight:600;margin-right:10px;min-width:24px;text-align:center}.socios-grid[_ngcontent-%COMP%]   .socio-card[_ngcontent-%COMP%]   .socio-header[_ngcontent-%COMP%]   .socio-nome[_ngcontent-%COMP%]{flex:1;font-weight:600;color:#2c3e50;font-size:15px;display:flex;align-items:center;line-height:1.3}.socios-grid[_ngcontent-%COMP%]   .socio-card[_ngcontent-%COMP%]   .socio-header[_ngcontent-%COMP%]   .socio-nome[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:6px;color:#007bff;font-size:14px}.socios-grid[_ngcontent-%COMP%]   .socio-card[_ngcontent-%COMP%]   .socio-header[_ngcontent-%COMP%]   .socio-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:4px 8px;font-size:12px;min-height:28px}.socios-grid[_ngcontent-%COMP%]   .socio-card[_ngcontent-%COMP%]   .socio-header[_ngcontent-%COMP%]   .socio-actions[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%]{background-color:#ffc107;border-color:#ffc107;color:#212529}.socios-grid[_ngcontent-%COMP%]   .socio-card[_ngcontent-%COMP%]   .socio-info[_ngcontent-%COMP%]{margin-bottom:8px}.socios-grid[_ngcontent-%COMP%]   .socio-card[_ngcontent-%COMP%]   .socio-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:4px;font-size:12px;color:#495057;line-height:1.3}.socios-grid[_ngcontent-%COMP%]   .socio-card[_ngcontent-%COMP%]   .socio-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:6px;width:14px;text-align:center;color:#6c757d;font-size:11px}.socios-grid[_ngcontent-%COMP%]   .socio-card[_ngcontent-%COMP%]   .socio-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{flex:1}.socios-grid[_ngcontent-%COMP%]   .socio-card[_ngcontent-%COMP%]   .socio-observacoes[_ngcontent-%COMP%]{margin-top:8px;padding-top:8px;border-top:1px solid #f1f3f4}.socios-grid[_ngcontent-%COMP%]   .socio-card[_ngcontent-%COMP%]   .socio-observacoes[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-size:11px;font-weight:600;color:#6c757d;margin-bottom:4px}.socios-grid[_ngcontent-%COMP%]   .socio-card[_ngcontent-%COMP%]   .socio-observacoes[_ngcontent-%COMP%]   .form-control-sm[_ngcontent-%COMP%]{font-size:11px;padding:5px 8px;border-radius:6px;min-height:32px}.summary-section[_ngcontent-%COMP%]{margin-bottom:25px}.summary-section[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#2c3e50;font-weight:600;margin-bottom:10px;display:flex;align-items:center}.summary-section[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:8px;color:#007bff}.summary-section[_ngcontent-%COMP%]   .summary-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:8px}.summary-section[_ngcontent-%COMP%]   .summary-grid[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{padding:5px 0;border-bottom:1px solid #f8f9fa}.summary-section[_ngcontent-%COMP%]   .links-summary[_ngcontent-%COMP%]   .link-tag[_ngcontent-%COMP%]{display:inline-block;background:#e9ecef;padding:4px 8px;border-radius:12px;font-size:12px;margin:2px 4px 2px 0;color:#495057}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:10px;margin-top:10px}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:10px;background:#f8f9fa;border:1px solid #e9ecef;border-radius:6px;transition:all .2s ease;font-size:12px}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item[_ngcontent-%COMP%]:hover{border-color:#007bff;background:white;box-shadow:0 2px 4px #0000001a}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{margin-right:10px;font-size:18px;min-width:24px;text-align:center}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:24px}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%]{flex:1;min-width:0}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%]   .link-tipo[_ngcontent-%COMP%]{font-weight:600;color:#2c3e50;font-size:12px;margin-bottom:2px}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%]   .link-descricao[_ngcontent-%COMP%]{color:#6c757d;font-size:11px;margin-bottom:2px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%]   .link-url[_ngcontent-%COMP%]{color:#007bff;font-size:10px;font-family:monospace;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item[_ngcontent-%COMP%]   .link-actions[_ngcontent-%COMP%]{display:flex;gap:4px;margin-left:8px}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item[_ngcontent-%COMP%]   .link-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:4px 8px;font-size:10px;min-height:28px;line-height:1.2;border-radius:4px}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item[_ngcontent-%COMP%]   .link-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item.link-whatsapp[_ngcontent-%COMP%]{border-left:3px solid #25d366}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item.link-whatsapp[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{color:#25d366}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item.link-instagram[_ngcontent-%COMP%]{border-left:3px solid #e1306c}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item.link-instagram[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{color:#e1306c}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item.link-ifood[_ngcontent-%COMP%]{border-left:3px solid #ea1d2c}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item.link-ifood[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{color:#ea1d2c}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item.link-cardapio[_ngcontent-%COMP%]{border-left:3px solid #28a745}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item.link-cardapio[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{color:#28a745}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item.link-concorrente[_ngcontent-%COMP%]{border-left:3px solid #dc3545}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item.link-concorrente[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{color:#dc3545}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item.link-localizacao[_ngcontent-%COMP%]{border-left:3px solid #17a2b8}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item.link-localizacao[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{color:#17a2b8}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item.link-site[_ngcontent-%COMP%]{border-left:3px solid #6f42c1}.summary-section[_ngcontent-%COMP%]   .links-summary-grid[_ngcontent-%COMP%]   .link-summary-item.link-site[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{color:#6f42c1}.summary-section[_ngcontent-%COMP%]   .cnpj-summary[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{margin-bottom:8px;padding:8px 0;border-bottom:1px solid #f8f9fa}.summary-section[_ngcontent-%COMP%]   .cnpj-summary[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:last-child{border-bottom:none;margin-bottom:0}.summary-section[_ngcontent-%COMP%]   .socios-summary-grid[_ngcontent-%COMP%]   .socio-summary-item[_ngcontent-%COMP%]{padding:12px;background:#f8f9fa;border-radius:6px;margin-bottom:8px;border:1px solid #e9ecef}.summary-section[_ngcontent-%COMP%]   .socios-summary-grid[_ngcontent-%COMP%]   .socio-summary-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.summary-section[_ngcontent-%COMP%]   .socios-summary-grid[_ngcontent-%COMP%]   .socio-summary-item[_ngcontent-%COMP%]   .socio-info-summary[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:11px;padding:2px 8px}.summary-section[_ngcontent-%COMP%]   .socios-summary-grid[_ngcontent-%COMP%]   .socio-summary-item[_ngcontent-%COMP%]   .socio-info-summary[_ngcontent-%COMP%]   .socio-details[_ngcontent-%COMP%]{font-size:13px;color:#6c757d;margin-top:4px}.summary-section[_ngcontent-%COMP%]   .socios-summary-grid[_ngcontent-%COMP%]   .socio-summary-item[_ngcontent-%COMP%]   .socio-info-summary[_ngcontent-%COMP%]   .socio-details[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:inline-block;margin-right:8px}.summary-section[_ngcontent-%COMP%]   .socios-summary-grid[_ngcontent-%COMP%]   .socio-summary-item[_ngcontent-%COMP%]   .socio-info-summary[_ngcontent-%COMP%]   .socio-details[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:last-child{margin-right:0}.summary-section[_ngcontent-%COMP%]   .socios-summary-grid[_ngcontent-%COMP%]   .socio-summary-item[_ngcontent-%COMP%]   .socio-info-summary[_ngcontent-%COMP%]   .socio-obs[_ngcontent-%COMP%]{margin-top:6px;padding-top:6px;border-top:1px solid #e9ecef}.summary-section[_ngcontent-%COMP%]   .socios-summary-grid[_ngcontent-%COMP%]   .socio-summary-item[_ngcontent-%COMP%]   .socio-info-summary[_ngcontent-%COMP%]   .socio-obs[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:12px;line-height:1.4}.wizard-footer[_ngcontent-%COMP%]{background:white;border-top:1px solid #dee2e6;padding:20px 0;position:sticky;bottom:0;z-index:100;box-shadow:0 -2px 4px #0000001a}.wizard-footer[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%]{display:flex;align-items:center}.wizard-footer[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%]   .flex-grow-1[_ngcontent-%COMP%]{flex:1}.wizard-footer[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{margin-left:10px;font-weight:600;padding:14px 20px;border-radius:8px;min-height:48px}.wizard-footer[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:first-child{margin-left:0}.wizard-footer[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{max-width:300px}@media (max-width: 1200px) and (min-width: 1025px){.cnpjs-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(320px,1fr));gap:12px}}@media (max-width: 1024px) and (min-width: 769px){.cnpjs-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:12px}.cnpj-card[_ngcontent-%COMP%]{padding:10px}.cnpj-card[_ngcontent-%COMP%]   .cnpj-header[_ngcontent-%COMP%]   .cnpj-number[_ngcontent-%COMP%]{font-size:14px}.cnpj-card[_ngcontent-%COMP%]   .cnpj-actions[_ngcontent-%COMP%]   .btn-select[_ngcontent-%COMP%], .cnpj-card[_ngcontent-%COMP%]   .cnpj-actions[_ngcontent-%COMP%]   .btn-details[_ngcontent-%COMP%]{padding:6px 10px;font-size:12px;min-height:32px}}@media (max-width: 768px) and (min-width: 481px){.cnpjs-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:10px}.cnpj-card[_ngcontent-%COMP%]{padding:12px;margin-bottom:10px}}@media (max-width: 768px){.wizard-progress[_ngcontent-%COMP%]   .step-progress[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]{margin:0 15px}.wizard-progress[_ngcontent-%COMP%]   .step-progress[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%]{width:40px;height:40px;font-size:16px}.wizard-progress[_ngcontent-%COMP%]   .step-progress[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%]{font-size:10px}.wizard-title[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]{font-size:24px}.wizard-title[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:28px}.links-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.cnpjs-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:10px;padding:0 5px}.summary-grid[_ngcontent-%COMP%]{grid-template-columns:1fr!important}.links-summary-grid[_ngcontent-%COMP%], .socios-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.navigation-buttons[_ngcontent-%COMP%]{flex-wrap:wrap}.navigation-buttons[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{order:-1;width:100%;margin:0 0 15px!important}.instagram-profile[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]{flex-direction:column;text-align:center}.instagram-profile[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]   .profile-pic[_ngcontent-%COMP%]{margin:0 0 15px}}@media (max-width: 480px){.cnpjs-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:8px;padding:0 8px}.cnpj-card[_ngcontent-%COMP%]{padding:10px;margin-bottom:8px}.cnpj-card[_ngcontent-%COMP%]   .cnpj-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:6px}.cnpj-card[_ngcontent-%COMP%]   .cnpj-header[_ngcontent-%COMP%]   .cnpj-badge[_ngcontent-%COMP%]{order:-1;align-self:flex-start}.cnpj-card[_ngcontent-%COMP%]   .cnpj-header[_ngcontent-%COMP%]   .cnpj-number[_ngcontent-%COMP%]{font-size:13px;font-family:monospace}.cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-name[_ngcontent-%COMP%]{font-size:14px;margin-bottom:4px}.cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-razao-social[_ngcontent-%COMP%], .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-endereco[_ngcontent-%COMP%], .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-atividade[_ngcontent-%COMP%], .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-socios[_ngcontent-%COMP%]{margin-bottom:3px}.cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-razao-social[_ngcontent-%COMP%]   small[_ngcontent-%COMP%], .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-endereco[_ngcontent-%COMP%]   small[_ngcontent-%COMP%], .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-atividade[_ngcontent-%COMP%]   small[_ngcontent-%COMP%], .cnpj-card[_ngcontent-%COMP%]   .cnpj-info[_ngcontent-%COMP%]   .cnpj-socios[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:10px;line-height:1.2}.cnpj-card[_ngcontent-%COMP%]   .cnpj-actions[_ngcontent-%COMP%]{flex-direction:column;gap:4px}.cnpj-card[_ngcontent-%COMP%]   .cnpj-actions[_ngcontent-%COMP%]   .btn-select[_ngcontent-%COMP%], .cnpj-card[_ngcontent-%COMP%]   .cnpj-actions[_ngcontent-%COMP%]   .btn-details[_ngcontent-%COMP%]{padding:8px 12px;font-size:11px;min-height:32px;width:100%}}.manual-toggle-section[_ngcontent-%COMP%]{margin-top:20px;padding-top:15px;border-top:1px solid #e9ecef}.manual-toggle-section[_ngcontent-%COMP%]   .separator-or[_ngcontent-%COMP%]{text-align:center;margin-bottom:15px;position:relative}.manual-toggle-section[_ngcontent-%COMP%]   .separator-or[_ngcontent-%COMP%]:before{content:"";position:absolute;top:50%;left:0;right:0;height:1px;background:#e9ecef;z-index:1}.manual-toggle-section[_ngcontent-%COMP%]   .separator-or[_ngcontent-%COMP%]   .separator-text[_ngcontent-%COMP%]{background:white;padding:0 15px;color:#6c757d;font-weight:600;position:relative;z-index:2;font-size:14px}.manual-toggle-section[_ngcontent-%COMP%]   .btn-outline-warning[_ngcontent-%COMP%]{font-weight:500;border-width:2px}.manual-toggle-section[_ngcontent-%COMP%]   .btn-outline-warning[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:8px}.manual-toggle-section[_ngcontent-%COMP%]   .btn-outline-warning[_ngcontent-%COMP%]:hover{background-color:#ffc107;border-color:#ffc107;color:#212529}.manual-cnpj-card[_ngcontent-%COMP%]{border:2px solid #ffc107;box-shadow:0 4px 12px #ffc10726;margin-bottom:20px}.manual-cnpj-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]{color:#856404}.manual-cnpj-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ffc107}.manual-cnpj-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]   .btn-sm[_ngcontent-%COMP%]{padding:4px 8px;font-size:12px;line-height:1.2}.manual-cnpj-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]   .btn-sm[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;border-color:#6c757d}.manual-cnpj-card[_ngcontent-%COMP%]   .google-search-primary[_ngcontent-%COMP%]{background:rgba(66,133,244,.05);border:2px solid rgba(66,133,244,.2);border-radius:8px;padding:20px;margin-bottom:20px}.manual-cnpj-card[_ngcontent-%COMP%]   .google-search-primary[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{color:#1a73e8;font-weight:600;margin-bottom:8px;font-size:16px}.manual-cnpj-card[_ngcontent-%COMP%]   .google-search-primary[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:8px;font-size:18px}.manual-cnpj-card[_ngcontent-%COMP%]   .google-search-primary[_ngcontent-%COMP%]   .section-description[_ngcontent-%COMP%]{color:#5f6368;font-size:14px;margin-bottom:15px}.manual-cnpj-card[_ngcontent-%COMP%]   .google-search-primary[_ngcontent-%COMP%]   .btn-google-option[_ngcontent-%COMP%]{font-weight:500;padding:10px 15px;border-width:2px}.manual-cnpj-card[_ngcontent-%COMP%]   .google-search-primary[_ngcontent-%COMP%]   .btn-google-option[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:8px}.manual-cnpj-card[_ngcontent-%COMP%]   .google-search-primary[_ngcontent-%COMP%]   .btn-google-option.btn-outline-primary[_ngcontent-%COMP%]:hover{background-color:#1a73e8;border-color:#1a73e8}.manual-cnpj-card[_ngcontent-%COMP%]   .google-search-primary[_ngcontent-%COMP%]   .btn-google-option.btn-outline-info[_ngcontent-%COMP%]:hover{background-color:#17a2b8;border-color:#17a2b8}.manual-cnpj-card[_ngcontent-%COMP%]   .manual-separator[_ngcontent-%COMP%]{text-align:center;margin:25px 0;position:relative}.manual-cnpj-card[_ngcontent-%COMP%]   .manual-separator[_ngcontent-%COMP%]:before{content:"";position:absolute;top:50%;left:0;right:0;height:1px;background:#dee2e6;z-index:1}.manual-cnpj-card[_ngcontent-%COMP%]   .manual-separator[_ngcontent-%COMP%]   .separator-text[_ngcontent-%COMP%]{background:white;padding:0 15px;color:#6c757d;font-weight:600;font-size:12px;position:relative;z-index:2;letter-spacing:.5px}.manual-cnpj-card[_ngcontent-%COMP%]   .cnpj-input-secondary[_ngcontent-%COMP%]{background:rgba(255,193,7,.05);border:2px solid rgba(255,193,7,.2);border-radius:8px;padding:20px}.manual-cnpj-card[_ngcontent-%COMP%]   .cnpj-input-secondary[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{color:#856404;font-weight:600;margin-bottom:8px;font-size:16px}.manual-cnpj-card[_ngcontent-%COMP%]   .cnpj-input-secondary[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:8px;font-size:18px}.manual-cnpj-card[_ngcontent-%COMP%]   .cnpj-input-secondary[_ngcontent-%COMP%]   .section-description[_ngcontent-%COMP%]{color:#6c757d;font-size:14px;margin-bottom:15px}.manual-cnpj-card[_ngcontent-%COMP%]   .google-search-section[_ngcontent-%COMP%]{text-align:center;padding:15px 0;border-top:1px solid #e9ecef;margin-top:15px}.manual-cnpj-card[_ngcontent-%COMP%]   .google-search-section[_ngcontent-%COMP%]   .btn-google-search[_ngcontent-%COMP%]{min-width:200px;font-weight:500}.manual-cnpj-card[_ngcontent-%COMP%]   .google-search-section[_ngcontent-%COMP%]   .btn-google-search[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:8px;color:#4285f4}.manual-cnpj-card[_ngcontent-%COMP%]   .google-search-section[_ngcontent-%COMP%]   .btn-google-search[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;border-color:#4285f4;color:#4285f4}.manual-cnpj-card[_ngcontent-%COMP%]   .form-control.ng-invalid.ng-touched[_ngcontent-%COMP%]{border-color:#dc3545}.manual-cnpj-card[_ngcontent-%COMP%]   .form-control.ng-invalid.ng-touched[_ngcontent-%COMP%]:focus{border-color:#dc3545;box-shadow:0 0 0 .2rem #dc354540}.manual-cnpj-card[_ngcontent-%COMP%]   .form-control.ng-valid.ng-touched[_ngcontent-%COMP%]{border-color:#28a745}.manual-cnpj-card[_ngcontent-%COMP%]   .form-control.ng-valid.ng-touched[_ngcontent-%COMP%]:focus{border-color:#28a745;box-shadow:0 0 0 .2rem #28a74540}.manual-cnpj-card[_ngcontent-%COMP%]   .invalid-feedback[_ngcontent-%COMP%]{display:block;color:#dc3545;font-size:12px;margin-top:5px}.manual-cnpj-card[_ngcontent-%COMP%]   .invalid-feedback[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:4px}.cnpj-actions[_ngcontent-%COMP%]   .btn-google[_ngcontent-%COMP%]{font-size:11px;padding:6px 10px;min-height:32px}.cnpj-actions[_ngcontent-%COMP%]   .btn-google[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#4285f4;margin-right:4px}.cnpj-actions[_ngcontent-%COMP%]   .btn-google[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;border-color:#4285f4;color:#4285f4}@media (max-width: 768px){.manual-toggle-section[_ngcontent-%COMP%]   .btn-outline-warning[_ngcontent-%COMP%]{width:100%;max-width:250px}.manual-cnpj-card[_ngcontent-%COMP%]   .google-search-primary[_ngcontent-%COMP%]   .btn-google-option[_ngcontent-%COMP%]{margin-bottom:8px}.manual-cnpj-card[_ngcontent-%COMP%]   .google-search-primary[_ngcontent-%COMP%]   .btn-google-option[_ngcontent-%COMP%]:last-child{margin-bottom:0}.manual-cnpj-card[_ngcontent-%COMP%]   .google-search-section[_ngcontent-%COMP%]   .btn-google-search[_ngcontent-%COMP%]{min-width:auto;width:100%}.cnpj-actions[_ngcontent-%COMP%]{flex-wrap:wrap}.cnpj-actions[_ngcontent-%COMP%]   .btn-google[_ngcontent-%COMP%]{flex:1;min-width:80px}}@media (max-width: 480px){.manual-cnpj-card[_ngcontent-%COMP%]   .google-search-primary[_ngcontent-%COMP%]{padding:15px}.manual-cnpj-card[_ngcontent-%COMP%]   .google-search-primary[_ngcontent-%COMP%]   .btn-google-option[_ngcontent-%COMP%]{margin-bottom:8px;padding:8px 12px;font-size:13px}.manual-cnpj-card[_ngcontent-%COMP%]   .cnpj-input-secondary[_ngcontent-%COMP%]{padding:15px}.manual-cnpj-card[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]{margin:0}.manual-cnpj-card[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-8[_ngcontent-%COMP%], .manual-cnpj-card[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-4[_ngcontent-%COMP%], .manual-cnpj-card[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%], .manual-cnpj-card[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%]{padding:0 0 15px}.manual-cnpj-card[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-8[_ngcontent-%COMP%]:last-child, .manual-cnpj-card[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-4[_ngcontent-%COMP%]:last-child, .manual-cnpj-card[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%]:last-child, .manual-cnpj-card[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%]:last-child{padding-bottom:0}.cnpj-actions[_ngcontent-%COMP%]{flex-direction:column}.cnpj-actions[_ngcontent-%COMP%]   .btn-select[_ngcontent-%COMP%], .cnpj-actions[_ngcontent-%COMP%]   .btn-details[_ngcontent-%COMP%], .cnpj-actions[_ngcontent-%COMP%]   .btn-google[_ngcontent-%COMP%]{width:100%;margin-bottom:4px}.cnpj-actions[_ngcontent-%COMP%]   .btn-select[_ngcontent-%COMP%]:last-child, .cnpj-actions[_ngcontent-%COMP%]   .btn-details[_ngcontent-%COMP%]:last-child, .cnpj-actions[_ngcontent-%COMP%]   .btn-google[_ngcontent-%COMP%]:last-child{margin-bottom:0}}@media (max-width: 576px){.wizard-header[_ngcontent-%COMP%], .wizard-title[_ngcontent-%COMP%], .wizard-content[_ngcontent-%COMP%], .wizard-footer[_ngcontent-%COMP%]{padding-left:15px;padding-right:15px}.wizard-progress[_ngcontent-%COMP%]   .step-progress[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]{margin:0 8px}.wizard-progress[_ngcontent-%COMP%]   .step-progress[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%]{width:35px;height:35px;font-size:14px}.wizard-progress[_ngcontent-%COMP%]   .step-progress[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%]{font-size:9px}.card[_ngcontent-%COMP%]{margin-bottom:15px}.links-summary-grid[_ngcontent-%COMP%]   .link-summary-item[_ngcontent-%COMP%]{padding:8px}.links-summary-grid[_ngcontent-%COMP%]   .link-summary-item[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{font-size:16px;margin-right:8px}.links-summary-grid[_ngcontent-%COMP%]   .link-summary-item[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%]   .link-tipo[_ngcontent-%COMP%]{font-size:11px}.links-summary-grid[_ngcontent-%COMP%]   .link-summary-item[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%]   .link-descricao[_ngcontent-%COMP%]{font-size:10px}.links-summary-grid[_ngcontent-%COMP%]   .link-summary-item[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%]   .link-url[_ngcontent-%COMP%]{font-size:9px}.links-summary-grid[_ngcontent-%COMP%]   .link-summary-item[_ngcontent-%COMP%]   .link-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:3px 6px;font-size:9px;min-height:24px}.links-summary-grid[_ngcontent-%COMP%]   .link-summary-item[_ngcontent-%COMP%]   .link-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}.form-control[_ngcontent-%COMP%]{min-height:46px;padding:12px 14px}.btn[_ngcontent-%COMP%]{min-height:46px;padding:12px 14px;font-size:14px}.btn-sm[_ngcontent-%COMP%]{min-height:38px;padding:9px 12px;font-size:12px}.wizard-footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{min-height:46px;padding:12px 16px;font-size:14px}.input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{min-height:46px;padding:12px 14px;border-radius:0 8px 8px 0}.input-group-prepend[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{min-height:46px;padding:12px 14px;border-radius:8px 0 0 8px}}.btn[_ngcontent-%COMP%]{border-radius:8px;font-weight:600;padding:14px 16px;font-size:14px;min-height:48px;line-height:1.4;transition:all .2s ease;border-width:2px}.btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] + span[_ngcontent-%COMP%], .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] + *[_ngcontent-%COMP%]{margin-left:6px}.btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] + i[_ngcontent-%COMP%], .btn[_ngcontent-%COMP%]   *[_ngcontent-%COMP%] + i[_ngcontent-%COMP%]{margin-left:6px}.btn[_ngcontent-%COMP%]:hover{transform:none;box-shadow:0 4px 8px #00000026}.btn[_ngcontent-%COMP%]:active{transform:translateY(1px);box-shadow:0 2px 4px #0000001a}.btn[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 3px #007bff40}.btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:not(:last-child){margin-right:6px}.btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:not(:first-child){margin-left:6px}.btn[_ngcontent-%COMP%]   i.fas[_ngcontent-%COMP%]:not(:only-child):first-child, .btn[_ngcontent-%COMP%]   i.fab[_ngcontent-%COMP%]:not(:only-child):first-child, .btn[_ngcontent-%COMP%]   i.far[_ngcontent-%COMP%]:not(:only-child):first-child{margin-right:6px}.btn[_ngcontent-%COMP%]   i.fas[_ngcontent-%COMP%]:not(:only-child):last-child, .btn[_ngcontent-%COMP%]   i.fab[_ngcontent-%COMP%]:not(:only-child):last-child, .btn[_ngcontent-%COMP%]   i.far[_ngcontent-%COMP%]:not(:only-child):last-child{margin-left:6px}.btn-primary[_ngcontent-%COMP%]{background-color:#007bff;border-color:#007bff}.btn-primary[_ngcontent-%COMP%]:hover{background-color:#0056b3;border-color:#0056b3}.btn-success[_ngcontent-%COMP%]{background-color:#28a745;border-color:#28a745}.btn-outline-primary[_ngcontent-%COMP%]{border-color:#007bff;color:#007bff}.btn-outline-primary[_ngcontent-%COMP%]:hover{background-color:#007bff;border-color:#007bff;color:#fff}.btn-outline-secondary[_ngcontent-%COMP%]{border-color:#6c757d;color:#6c757d}.btn-outline-info[_ngcontent-%COMP%]{border-color:#17a2b8;color:#17a2b8}.btn-outline-secondary[_ngcontent-%COMP%]{background-color:#fff;border-color:#6c757d;color:#6c757d;box-shadow:0 4px 12px #6c757d26}.btn-outline-secondary[_ngcontent-%COMP%]:hover{background-color:#6c757d;border-color:#6c757d;color:#fff}.btn-outline-info[_ngcontent-%COMP%]{background-color:#fff;border-color:#17a2b8;color:#17a2b8;box-shadow:0 4px 12px #17a2b826}.btn-outline-info[_ngcontent-%COMP%]:hover{background-color:#17a2b8;border-color:#17a2b8;color:#fff}.btn-success[_ngcontent-%COMP%]{background-color:#28a745;border-color:#28a745;color:#fff;box-shadow:0 4px 12px #28a7454d}.btn-success[_ngcontent-%COMP%]:hover{background-color:#1e7e34;border-color:#1e7e34}.btn-sm[_ngcontent-%COMP%]{min-height:40px;padding:10px 14px;font-size:13px;line-height:1.4;font-weight:600;border-radius:6px}.wizard-footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{min-height:48px;padding:14px 20px;font-size:14px;line-height:1.4;font-weight:600;border-radius:8px;box-shadow:0 4px 12px #00000026}.wizard-footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 18px #0003}.form-control[_ngcontent-%COMP%]:focus{animation:focusPulse .3s ease}@keyframes focusPulse{0%{box-shadow:0 0 #007bff66}70%{box-shadow:0 0 0 6px #007bff00}to{box-shadow:0 0 #007bff00}}.btn-xs[_ngcontent-%COMP%]{padding:4px 8px;font-size:11px;line-height:1.2;border-radius:3px;font-weight:500}.btn-xs[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px;margin-right:3px}'],data:{animation:[(0,p.X$)("slideDown",[(0,p.SB)("closed",(0,p.oB)({height:"0px",opacity:0,overflow:"hidden"})),(0,p.SB)("open",(0,p.oB)({height:"*",opacity:1,overflow:"visible"})),(0,p.eR)("closed => open",[(0,p.jt)("300ms ease-in-out")]),(0,p.eR)("open => closed",[(0,p.jt)("300ms ease-in-out")])])]}}),o})()},{path:"index",component:dn},{path:"empresas",component:Cn},{path:"leads",component:It.N},{path:"",redirectTo:"leads",pathMatch:"full"}];let Va=(()=>{class o{}return o.\u0275fac=function(t){return new(t||o)},o.\u0275mod=e.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=e.\u0275\u0275defineInjector({imports:[b.Bz.forChild(Fa),b.Bz]}),o})(),Ra=(()=>{class o{constructor(){this.mapaPalavrasChave={pre\u00e7o:["Nossos planos come\xe7am a partir de R$99/m\xeas. Posso explicar os detalhes de cada um?","Temos condi\xe7\xf5es especiais neste m\xeas. Posso enviar uma proposta personalizada?"],demo:["\xd3timo! Posso agendar uma demonstra\xe7\xe3o para amanh\xe3 \xe0s 14h?","Podemos fazer uma demonstra\xe7\xe3o hoje mesmo. Qual hor\xe1rio seria melhor para voc\xea?"],d\u00favida:["Entendo sua d\xfavida. Nosso sistema resolve isso de forma simples, permitindo que voc\xea...","\xc9 uma d\xfavida comum. Vou te explicar como funciona:"],concorrente:["Diferente da concorr\xeancia, nosso sistema oferece integra\xe7\xe3o completa com WhatsApp e mais recursos de automa\xe7\xe3o.","Nosso diferencial \xe9 a facilidade de uso e suporte 24/7, algo que a concorr\xeancia n\xe3o oferece."],interessado:["Que \xf3timo! Posso lhe mostrar uma demonstra\xe7\xe3o agora mesmo?","Excelente! Vamos agendar uma reuni\xe3o para mostrar como podemos atender suas necessidades?"],valor:["O investimento \xe9 bem acess\xedvel comparado ao retorno que voc\xea ter\xe1. Posso detalhar melhor?","Nosso sistema se paga em poucos meses pelo aumento de convers\xe3o que proporciona."],problema:["Entendo o desafio que voc\xea est\xe1 enfrentando. Nossa solu\xe7\xe3o foi desenvolvida especificamente para resolver isso.","Muitos clientes tinham esse mesmo problema e conseguiram resultados incr\xedveis com nossa plataforma."],prazo:["A implementa\xe7\xe3o \xe9 r\xe1pida, em m\xe9dia 3 dias \xfateis para ter tudo funcionando.","Podemos iniciar imediatamente ap\xf3s a contrata\xe7\xe3o. Todo o processo leva menos de uma semana."]},this.sugestoesPorEtapa={Prospec\u00e7\u00e3o:{Consultivo:["Oi Ricardo! Que legal conhecer voc\xea! Vi que tem uma pizzaria. Quantas mesas voc\xeas atendem por dia? Nosso card\xe1pio digital pode multiplicar isso!","Ol\xe1! Adoro pizzarias familiares. Voc\xeas j\xe1 pensaram em como seria se cada mesa pudesse fazer pedidos sem chamar o gar\xe7om? Parece fic\xe7\xe3o cient\xedfica, n\xe9?","Ricardo, parab\xe9ns pela pizzaria! Uma pergunta: seus clientes reclamam da demora no atendimento nos fins de semana?"],Emp\u00e1tico:["Ol\xe1 Ricardo! Como est\xe1 o movimento na pizzaria? Sei que gerenciar o fluxo de clientes pode ser desafiador, especialmente em hor\xe1rios de pico!","Oi! Adoro pizza! Imagino que o maior desafio seja manter a qualidade do atendimento quando a casa est\xe1 cheia, n\xe9?","Ol\xe1! Sou apaixonado por neg\xf3cios familiares como pizzarias. Conte um pouco sobre como come\xe7ou sua jornada!"]},Qualifica\u00e7\u00e3o:{Consultivo:["Entendi suas necessidades! Nossa solu\xe7\xe3o permite que os clientes fa\xe7am pedidos diretamente pelo celular escaneando um QR code na mesa. Isso reduz erros em 70% e aumenta a rotatividade das mesas.","Baseado no seu volume de 30 mesas, nossa plataforma pode aumentar sua efici\xeancia em at\xe9 40%. O melhor \xe9 que voc\xea paga apenas uma pequena taxa mensal, sem investimento inicial.","Nossos clientes relatam um aumento m\xe9dio de 22% no valor do ticket quando usam nosso card\xe1pio digital. Os adicionais s\xe3o muito mais f\xe1ceis de vender!"],T\u00e9cnico:["Nossa plataforma integra com 95% dos sistemas de PDV do mercado, incluindo o que voc\xea usa. A implementa\xe7\xe3o leva apenas 24 horas e n\xe3o precisa de nenhum hardware adicional.","A sincroniza\xe7\xe3o com seu estoque \xe9 em tempo real. Quando um item acaba, ele \xe9 automaticamente removido do card\xe1pio, evitando frustra\xe7\xe3o dos clientes.","Nossas APIs s\xe3o abertas e documentadas, permitindo integra\xe7\xe3o com qualquer sistema de gest\xe3o ou contabilidade que voc\xea j\xe1 use."]},Obje\u00e7\u00e3o:{Consultivo:["Entendo sua preocupa\xe7\xe3o com o custo. A maioria dos nossos clientes recupera o investimento em menos de 45 dias. Uma pizzaria do seu porte economiza em m\xe9dia R$3.200/m\xeas s\xf3 com redu\xe7\xe3o de erros e otimiza\xe7\xe3o da equipe.","O plano mais b\xe1sico custa menos que um delivery por dia. E voc\xea pode fazer um teste gratuito por 30 dias para comprovar o retorno antes de decidir.","Comparado ao custo de contratar mais um gar\xe7om (sal\xe1rio + encargos + benef\xedcios), nossa solu\xe7\xe3o \xe9 80% mais econ\xf4mica e funciona 24/7 sem reclamar!"],T\u00e9cnico:["Nossa plataforma n\xe3o exige compra de tablets ou hardware. Os clientes usam o pr\xf3prio celular, e oferecemos op\xe7\xf5es flex\xedveis de pagamento, incluindo mensalidade sem contrato de fidelidade.","Voc\xea sabia que restaurantes com sistemas de autoatendimento reduzem o custo operacional em at\xe9 30%? Nossos relat\xf3rios detalhados permitem identificar todas as economias geradas.","Nossa API processa mais de 500 mil pedidos por dia com 99.99% de uptime garantido em contrato. O suporte t\xe9cnico est\xe1 dispon\xedvel 24/7 sem custo adicional."]},Fechamento:{Consultivo:["Excelente! Vamos formalizar? Posso enviar o contrato hoje mesmo e agendar a implementa\xe7\xe3o para a pr\xf3xima semana. Assim voc\xea j\xe1 come\xe7a a ver resultados antes do fim do m\xeas.","Com base na nossa conversa, recomendo o plano Profissional. Ele inclui todas as funcionalidades que voc\xea precisa agora, e voc\xea pode fazer upgrade quando sua opera\xe7\xe3o crescer.","Para come\xe7armos, preciso apenas que voc\xea preencha este formul\xe1rio com os dados b\xe1sicos do restaurante. Nossa equipe far\xe1 todo o resto!"],Urgente:["Temos uma promo\xe7\xe3o especial que termina hoje: 30% de desconto nos 3 primeiros meses. Posso garantir esse valor se fecharmos agora.","Consigo antecipar sua implementa\xe7\xe3o para esta semana se confirmarmos hoje. Assim voc\xea j\xe1 aproveita o movimento do final de semana com o sistema rodando!","Acabei de confirmar com nosso time de implementa\xe7\xe3o que podemos fazer a configura\xe7\xe3o expressa em 24h se assinarmos hoje. O que acha?"]}},this.sugestoesGenericas=["Ol\xe1! Como posso ajudar voc\xea hoje?","Temos v\xe1rias solu\xe7\xf5es que podem atender suas necessidades. Podemos conversar mais sobre seu neg\xf3cio?","Gostaria de agendar uma demonstra\xe7\xe3o para conhecer melhor nossa plataforma?","Estou \xe0 disposi\xe7\xe3o para esclarecer qualquer d\xfavida sobre nossos servi\xe7os.","Nossos clientes t\xeam obtido resultados excelentes com nossa solu\xe7\xe3o. Posso compartilhar alguns casos de sucesso?"]}gerarSugestoes(t){console.log("SugestoesService - gerarSugestoes, contexto:",t);try{const n=[],a=t.etapaFunil||"Prospec\xe7\xe3o",i=t.tomConversa||"Consultivo";if(this.sugestoesPorEtapa[a]&&this.sugestoesPorEtapa[a][i]&&this.sugestoesPorEtapa[a][i].forEach(c=>{n.push({texto:c})}),0===n.length&&this.sugestoesPorEtapa[a]){const c=Object.keys(this.sugestoesPorEtapa[a]);c.length>0&&this.sugestoesPorEtapa[a][c[0]].forEach(u=>{n.push({texto:u})})}if(0===n.length&&t.mensagens&&t.mensagens.length>0){const c=t.mensagens.slice(-3).map(g=>g.texto.toLowerCase());for(const[g,u]of Object.entries(this.mapaPalavrasChave))for(const h of c)if(h.includes(g)){u.forEach(V=>{n.push({texto:V,palavraChave:g})});break}}0===n.length&&this.sugestoesGenericas.forEach(c=>{n.push({texto:c})});const s=this.removerDuplicatas(n).slice(0,3);return console.log("Sugest\xf5es geradas:",s),(0,v.of)(s)}catch(n){return console.error("Erro ao gerar sugest\xf5es:",n),(0,v.of)([{texto:"Ol\xe1! Como posso ajudar?"},{texto:"Gostaria de saber mais sobre nossos produtos?"},{texto:"Podemos agendar uma demonstra\xe7\xe3o se preferir."}])}}removerDuplicatas(t){const n=new Set;return t.filter(a=>!n.has(a.texto)&&(n.add(a.texto),!0))}gerarSugestoesComIA(t){return this.gerarSugestoes(t)}}return o.\u0275fac=function(t){return new(t||o)},o.\u0275prov=e.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})(),$a=(()=>{class o{}return o.\u0275fac=function(t){return new(t||o)},o.\u0275mod=e.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=e.\u0275\u0275defineInjector({providers:[ie,Ra,j,T,ce],imports:[m.CommonModule,d.u5,d.UX,ue,fe.QW,Ae,He,nt,_.si,Va,L.zE6,ae.I1]}),o})()}}]);
//# sourceMappingURL=630.76845b496d001349.js.map