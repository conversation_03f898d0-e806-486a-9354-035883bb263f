# Requisitos - Assistente de Vendas WhatsApp

## 1. Visão Geral

Sistema interno de sugestão de respostas para vendedores no WhatsApp, integrado à extensão Chrome PromoKit. O objetivo é ajudar o vendedor a responder clientes de forma mais eficaz usando técnicas de SPIN Selling e inteligência artificial.

## 2. Funcionalidades Principais

### 2.1 Captura de Contexto
- **Identificação do Cliente**: Nome e telefone do contato atual
- **Histórico da Conversa**: Últimas mensagens trocadas (mínimo 5, máximo 20)
- **Contexto do Produto**: Qual produto/serviço está sendo discutido
- **Fase da Venda**: Identificação automática da fase SPIN atual

### 2.2 Análise Inteligente
- **Detecção de Intenção**: Identificar se cliente está interessado, com dúvidas, ou resistente
- **Identificação de Fase SPIN**: 
  - S (Situação): Cliente explicando contexto atual
  - P (Problema): Cliente mencionando dificuldades
  - I (Implicação): Discussão sobre impactos dos problemas
  - N (Necessidade): Cliente demonstrando interesse em solução
- **Extração de Informações-Chave**: Orçamento, prazo, decisores mencionados

### 2.3 Geração de Sugestões
- **Resposta Contextualizada**: Baseada na conversa atual e fase SPIN
- **Tom Adequado**: Profissional, informal ou técnico conforme contexto
- **Perguntas Estratégicas**: Sugerir próximas perguntas para avançar na venda
- **Objeções**: Identificar e sugerir como lidar com resistências

## 3. Interface do Sistema

### 3.1 Painel Principal
```
┌─────────────────────────────────────────┐
│ 👤 Cliente: João Silva                   │
│ 📱 (11) 98765-4321                      │
├─────────────────────────────────────────┤
│ 🎯 Fase SPIN: [Problema]                │
│ 📊 Produto: Sistema de Gestão           │
├─────────────────────────────────────────┤
│ 💬 Contexto da Conversa:                │
│ "Estamos tendo dificuldades com..."     │
├─────────────────────────────────────────┤
│ [🤖 Sugerir Resposta]                   │
│                                         │
│ 📝 Sugestão:                            │
│ "Entendo João, isso deve estar..."      │
│                                         │
│ [✏️ Editar] [🔄 Regenerar] [✅ Usar]    │
└─────────────────────────────────────────┘
```

## 4. Funcionalidades Específicas

### 4.1 Botão "Sugerir Resposta"
- Analisa o contexto atual da conversa
- Gera resposta apropriada para a fase SPIN
- Considera o histórico de mensagens
- Tempo de resposta: máximo 3 segundos

### 4.2 Opções de Ação
- **Usar**: Copia sugestão para área de transferência
- **Editar**: Permite modificar a sugestão antes de usar
- **Regenerar**: Gera nova sugestão com contexto ligeiramente diferente

### 4.3 Personalização
- Configuração de prompt base por empresa
- Ajuste de tom padrão (formal/informal)
- Templates para produtos específicos

## 5. Dados Necessários

### 5.1 Entrada Automática
- Nome do contato ativo no WhatsApp
- Mensagens da conversa atual (via content script)
- Timestamp das mensagens

### 5.2 Configuração Manual
- Produto/serviço sendo oferecido
- Contexto adicional específico do cliente
- Objetivo da conversa (venda, suporte, follow-up

## 6. Requisitos Técnicos

### 6.1 Integração
- **WhatsApp Web**: Content script para capturar contexto
- **ChatGPT API**: Para geração de respostas
- **Base de Dados**: Armazenar histórico e configurações
- **Chrome Extension**: Interface no side panel

### 6.2 Performance
- Tempo de resposta da IA: máximo 3 segundos
- Interface responsiva sem travamentos
- Funcionar offline com mensagens em cache

### 6.3 Segurança
- Não armazenar mensagens sensíveis
- Criptografia para dados de clientes
- Conformidade com LGPD

## 7. Fluxo de Uso

### 7.1 Fluxo Básico
1. Vendedor abre WhatsApp Web
2. Seleciona conversa com cliente
3. Sistema automaticamente captura contexto
4. Vendedor clica "Sugerir Resposta"
5. IA analisa e gera sugestão
6. Vendedor revisa, edita se necessário
7. Copia resposta para WhatsApp

### 7.2 Fluxo Avançado
1. Sistema identifica fase SPIN automaticamente
2. Sugere próximos passos estratégicos
3. Alerta sobre oportunidades perdidas
4. Indica quando fazer follow-up

## 8. Métricas e Melhorias

### 8.1 Coleta de Dados
- Taxa de uso das sugestões
- Tempo médio de resposta do vendedor
- Feedback sobre qualidade das sugestões

### 8.2 Otimização Contínua
- Ajuste de prompts baseado em feedback
- Melhoria da detecção de fase SPIN
- Personalização por vendedor/empresa

## 9. Limitações e Considerações

### 9.1 Limitações Técnicas
- Dependente da qualidade da API do ChatGPT
- Requer conexão com internet estável
- Limitado pela estrutura do WhatsApp Web

### 9.2 Considerações Éticas
- Transparência sobre uso de IA (se necessário)
- Responsabilidade final do vendedor
- Privacidade das conversas dos clientes

## 10. Roadmap de Implementação

### Fase 1 - MVP
- Captura básica de contexto
- Geração simples de respostas
- Interface minimalista

### Fase 2 - Inteligência
- Detecção de fase SPIN
- Análise de sentimento
- Sugestões estratégicas

### Fase 3 - Personalização
- Templates por empresa
- Configurações avançadas
- Métricas e relatórios
