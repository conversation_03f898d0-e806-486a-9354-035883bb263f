document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('iframeSettingsForm');
    const urlInput = document.getElementById('iframeUrl');
    const notification = document.getElementById('notification');
    const findTabsBtn = document.getElementById('findTabsBtn');
    const tabsContainer = document.getElementById('tabsContainer');
    const tabsList = document.getElementById('tabsList');
    const noTabsMessage = document.getElementById('noTabsMessage');
    const tabCountBadge = document.getElementById('tabCountBadge');
    const urlHelpText = document.getElementById('urlHelpText');
    const manualEntryTrigger = document.getElementById('manualEntryTrigger');
    const tabSearchInput = document.getElementById('tabSearchInput');
    const backButton = document.getElementById('backButton');

    const storageKey = 'iframeCustomUrl';

    // Estado do iframe
    let isIframeActive = false;

    // Mostrar URL completa ao passar o mouse
    document.addEventListener('mouseover', function(e) {
        if (e.target.classList.contains('tab-url')) {
            const tooltip = document.createElement('div');
            tooltip.className = 'tab-url-tooltip';
            tooltip.textContent = e.target.textContent;

            // Posicionar o tooltip próximo ao elemento
            const rect = e.target.getBoundingClientRect();
            tooltip.style.left = rect.left + 'px';
            tooltip.style.top = (rect.bottom + 5) + 'px';

            document.body.appendChild(tooltip);

            // Usar uma função que verifica se o tooltip ainda existe antes de remover
            const removeTooltip = function() {
                if (tooltip && tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
                e.target.removeEventListener('mouseleave', removeTooltip);
            };

            e.target.addEventListener('mouseleave', removeTooltip);
        }
    });

    // Wrapper para atualizar o badge de contagem de abas encontradas
    const originalFindPromokitTabs = window.findPromokitTabs;
    if (typeof originalFindPromokitTabs === 'function') {
        window.findPromokitTabs = function() {
            originalFindPromokitTabs();

            // Adicione um pequeno atraso para garantir que os tabs já foram processados
            setTimeout(() => {
                const tabCount = document.querySelectorAll('.tab-item').length;
                if (tabCount > 0) {
                    tabCountBadge.textContent = tabCount;
                    tabCountBadge.style.display = 'inline-flex';
                } else {
                    tabCountBadge.style.display = 'none';
                }
            }, 100);
        };
    }

    function showNotification(message, type = 'success') {
        const icon = type === 'success' ? '<i class="fas fa-check-circle"></i>' : '<i class="fas fa-exclamation-circle"></i>';
        notification.innerHTML = `${icon} ${message}`;
        notification.className = `notification ${type}`;
        notification.style.display = 'flex';
        setTimeout(() => {
            notification.style.display = 'none';
        }, 5000);
    }

    // Função para enviar mensagens via chrome runtime
    function sendRuntimeMessage(url) {
        try {
            chrome.runtime.sendMessage({
                type: 'IFRAME_URL_UPDATED',
                url: url
            });
            console.log('Mensagem enviada via chrome.runtime');
        } catch (e) {
            console.warn('Erro ao enviar via chrome.runtime:', e);
        }
    }

    // Função para filtrar abas conforme o usuário digita
    function filterTabs(searchTerm) {
        const tabItems = document.querySelectorAll('.tab-item');
        let visibleCount = 0;

        tabItems.forEach(item => {
            const title = item.querySelector('.tab-title').textContent.toLowerCase();
            const url = item.querySelector('.tab-url').textContent.toLowerCase();

            if (title.includes(searchTerm) || url.includes(searchTerm)) {
                item.style.display = 'flex';
                visibleCount++;
            } else {
                item.style.display = 'none';
            }
        });

        // Se não houver resultados, mostrar mensagem
        if (visibleCount === 0 && tabItems.length > 0) {
            noTabsMessage.style.display = 'flex';
            noTabsMessage.innerHTML = `<i class="fas fa-search"></i> Nenhuma página encontrada para "${searchTerm}"`;

            // Manter o status de seleção visível mesmo quando não há resultados
            const selectionStatus = document.getElementById('selectionStatus');
            if (selectionStatus) {
                selectionStatus.style.display = 'flex';
            }
        } else {
            noTabsMessage.style.display = 'none';
        }

        // Garantir que a instrução permaneça visível
        const instructionBox = document.querySelector('.instruction-box');
        if (instructionBox) {
            instructionBox.style.display = 'flex';
        }

        return visibleCount;
    }

    // Adicionar evento de input ao campo de busca
    if (tabSearchInput) {
        tabSearchInput.addEventListener('input', function() {
            filterTabs(this.value.toLowerCase());
        });
    }

    // Permitir inserção manual da URL
    if (manualEntryTrigger) {
        manualEntryTrigger.addEventListener('click', function() {
            // Exibir o formulário
            document.getElementById('iframeSettingsForm').style.display = 'block';
            urlInput.disabled = false;
            urlInput.focus();
            urlHelpText.textContent = 'Insira manualmente a URL completa incluindo https://';
        });
    }

    // Função para formatar a URL para o padrão correto
    function formatPromokitUrl(url) {
        try {
            const urlObj = new URL(url);
            if (urlObj.hostname.endsWith('promokit.com.br')) {
                // Garantir que a URL termine com /admin/index
                return `https://${urlObj.hostname}/admin/index`;
            }
            return url;
        } catch (e) {
            return url;
        }
    }

    // Função para formatar URL para exibição
    function formatDisplayUrl(url) {
        try {
            const urlObj = new URL(url);
            // Extrair apenas o domínio para exibição mais limpa
            return urlObj.hostname;
        } catch (e) {
            return url;
        }
    }

    // Função para buscar abas abertas do domínio promokit.com.br
    window.findPromokitTabs = function() {
        findTabsBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Buscando...';
        findTabsBtn.disabled = true;

        // Limpar a lista de abas visualmente e mostrar mensagem de carregamento
        tabsList.innerHTML = '';
        tabsContainer.style.display = 'block';
        noTabsMessage.style.display = 'flex';
        noTabsMessage.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Buscando páginas abertas...';

        // Ocultar resultados anteriores
        if (tabCountBadge) {
            tabCountBadge.style.display = 'none';
        }

        // Ocultar o campo de busca inicialmente
        const searchContainer = document.querySelector('.search-container');
        if (searchContainer) {
            searchContainer.style.display = 'none';
        }

        // Remover instrução e status de seleção anteriores se existirem
        const existingInstruction = document.querySelector('.instruction-box');
        if (existingInstruction) {
            existingInstruction.remove();
        }

        const existingStatus = document.getElementById('selectionStatus');
        if (existingStatus) {
            existingStatus.remove();
        }

        chrome.tabs.query({}, function(tabs) {
            // Filtrar apenas as abas que correspondem ao padrão https://*.promokit.com.br
            const promokitTabs = tabs.filter(tab => {
                try {
                    const url = new URL(tab.url);
                    return url.protocol === 'https:' && url.hostname.endsWith('promokit.com.br');
                } catch (e) {
                    return false;
                }
            });

            // Pequeno atraso para tornar visível o processo de busca
            setTimeout(() => {
                // Atualizar o botão
                findTabsBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Buscar páginas do Promokit';
                findTabsBtn.disabled = false;

                if (promokitTabs.length > 0) {
                    // Esconder a mensagem de nenhuma aba encontrada
                    noTabsMessage.style.display = 'none';

                    // Desabilitar o campo de entrada manual por padrão
                    urlInput.disabled = true;
                    urlInput.placeholder = 'Selecione uma página da lista acima';
                    urlHelpText.textContent = 'Clique em uma página acima ou insira manualmente';

                    // Atualizar o badge de contagem
                    tabCountBadge.textContent = promokitTabs.length;
                    tabCountBadge.style.display = 'inline-flex';

                    // Mostrar o campo de busca apenas se houver mais de 1 página
                    const searchContainer = document.querySelector('.search-container');
                    if (searchContainer) {
                        if (promokitTabs.length > 1) {
                            searchContainer.style.display = 'block';
                        } else {
                            searchContainer.style.display = 'none';
                        }
                    }

                    // Limpar campo de busca
                    if (tabSearchInput) {
                        tabSearchInput.value = '';
                    }

                    // Adicionar instrução explícita no topo da lista (Opção 4)
                    const instructionBox = document.createElement('div');
                    instructionBox.className = 'instruction-box';
                    instructionBox.innerHTML = '<i class="fas fa-info-circle"></i><span>A primeira página será selecionada automaticamente. Você pode escolher outra clicando nela.</span>';
                    tabsContainer.insertBefore(instructionBox, tabsList);

                    // Criar uma referência para o primeiro item para selecioná-lo depois
                    let firstTabItem = null;
                    let firstTab = null;

                    // Adicionar cada aba à lista
                    promokitTabs.forEach((tab, index) => {
                        const tabItem = document.createElement('div');
                        tabItem.className = 'tab-item';

                        // Destacar o primeiro item automaticamente (Opção 8)
                        if (index === 0) {
                            tabItem.classList.add('highlighted');
                            firstTabItem = tabItem;
                            firstTab = tab;
                        }

                        // Truncar a URL para exibição
                        const displayUrl = tab.url.length > 60 ?
                            tab.url.substring(0, 57) + '...' :
                            tab.url;

                        // Novo layout com botão de seleção explícito (Opção 1)
                        tabItem.innerHTML = `
                            <div class="tab-content">
                                <div class="tab-title"><span class="tab-index">${index + 1}.</span> ${tab.title}</div>
                                <div class="tab-url" title="${tab.url}">${displayUrl}</div>
                            </div>
                            <button class="btn-select">Selecionar</button>
                        `;

                        // Função para selecionar a aba
                        const selectTab = function() {
                            // Exibir o formulário se estiver oculto
                            document.getElementById('iframeSettingsForm').style.display = 'block';

                            // Formatar a URL para garantir que termine com /admin/index
                            const formattedUrl = formatPromokitUrl(tab.url);

                            // Habilitar o campo de entrada e definir o valor
                            urlInput.disabled = false;
                            urlInput.value = formattedUrl;
                            urlHelpText.textContent = 'URL formatada para /admin/index. Clique em Salvar para confirmar.';
                            showNotification('URL formatada para o padrão correto', 'success');

                            // Destacar a aba selecionada e atualizar texto dos botões
                            document.querySelectorAll('.tab-item').forEach(item => {
                                item.classList.remove('selected');
                                const btn = item.querySelector('.btn-select');
                                if (btn) {
                                    btn.textContent = 'Selecionar';
                                    btn.classList.remove('selected');
                                }
                            });
                            tabItem.classList.add('selected');

                            // Atualizar o texto do botão da página selecionada
                            const selectedBtn = tabItem.querySelector('.btn-select');
                            if (selectedBtn) {
                                selectedBtn.textContent = 'Selecionada';
                                selectedBtn.classList.add('selected');
                            }

                            // Atualizar o status de seleção (Opção 15)
                            const selectionStatus = document.getElementById('selectionStatus');
                            if (selectionStatus) {
                                selectionStatus.className = 'selection-status selected';
                                selectionStatus.innerHTML = '<i class="fas fa-check-circle"></i><span>Página selecionada: ' + tab.title + '</span>';
                            }
                        };

                        // Adicionar evento de clique para o item inteiro
                        tabItem.addEventListener('click', selectTab);

                        // Adicionar evento de clique para o botão de seleção
                        const selectButton = tabItem.querySelector('.btn-select');
                        if (selectButton) {
                            selectButton.addEventListener('click', function(e) {
                                e.stopPropagation(); // Evitar duplo disparo do evento
                                selectTab();
                            });
                        }

                        tabsList.appendChild(tabItem);
                    });

                    // Adicionar texto de status abaixo da lista (Opção 15)
                    const selectionStatus = document.createElement('div');
                    selectionStatus.id = 'selectionStatus';
                    selectionStatus.className = 'selection-status';
                    selectionStatus.innerHTML = '<i class="fas fa-info-circle"></i><span>Nenhuma página selecionada. Clique em uma página acima para selecioná-la.</span>';
                    tabsContainer.appendChild(selectionStatus);

                    // Selecionar automaticamente o primeiro item após adicionar todos à lista
                    if (firstTabItem && firstTab) {
                        // Pequeno atraso para garantir que a UI esteja pronta
                        setTimeout(() => {
                            // Exibir o formulário
                            document.getElementById('iframeSettingsForm').style.display = 'block';

                            // Formatar a URL para garantir que termine com /admin/index
                            const formattedUrl = formatPromokitUrl(firstTab.url);

                            // Habilitar o campo de entrada e definir o valor
                            urlInput.disabled = false;
                            urlInput.value = formattedUrl;
                            urlHelpText.textContent = 'URL formatada para /admin/index. Clique em Salvar para confirmar.';

                            // Destacar a aba selecionada e atualizar texto dos botões
                            document.querySelectorAll('.tab-item').forEach(item => {
                                item.classList.remove('selected');
                                const btn = item.querySelector('.btn-select');
                                if (btn) {
                                    btn.textContent = 'Selecionar';
                                    btn.classList.remove('selected');
                                }
                            });
                            firstTabItem.classList.add('selected');

                            // Atualizar o texto do botão da página selecionada
                            const selectedBtn = firstTabItem.querySelector('.btn-select');
                            if (selectedBtn) {
                                selectedBtn.textContent = 'Selecionada';
                                selectedBtn.classList.add('selected');
                            }

                            // Atualizar o status de seleção
                            selectionStatus.className = 'selection-status selected';
                            selectionStatus.innerHTML = '<i class="fas fa-check-circle"></i><span>Página selecionada: ' + firstTab.title + '</span>';

                            // Atualizar a instrução
                            const instructionBox = document.querySelector('.instruction-box');
                            if (instructionBox) {
                                instructionBox.innerHTML = '<i class="fas fa-info-circle"></i><span>Página selecionada automaticamente. Você pode escolher outra ou clicar em Salvar.</span>';
                            }
                        }, 100);
                    }
                } else {
                    // Mostrar a mensagem de nenhuma aba encontrada
                    noTabsMessage.style.display = 'flex';
                    noTabsMessage.innerHTML = '<i class="fas fa-exclamation-circle"></i> Nenhuma página do promokit.com.br encontrada.';

                    // Atualizar o badge de contagem
                    tabCountBadge.style.display = 'none';

                    // Habilitar o campo de entrada manual
                    urlInput.disabled = false;
                    urlInput.placeholder = 'https://exemplo.promokit.com.br';
                    urlInput.focus();
                    urlHelpText.textContent = 'Insira a URL completa incluindo https://';

                    // Adicionar instrução explícita para inserção manual
                    const instructionBox = document.createElement('div');
                    instructionBox.className = 'instruction-box';
                    instructionBox.innerHTML = '<i class="fas fa-info-circle"></i><span>Nenhuma página encontrada. Insira a URL manualmente abaixo.</span>';
                    tabsContainer.appendChild(instructionBox);

                    // Exibir o formulário
                    document.getElementById('iframeSettingsForm').style.display = 'block';
                }
            }, 800); // Delay para dar sensação de busca
        });
    };

    // Adicionar evento de clique ao botão de buscar abas
    findTabsBtn.addEventListener('click', window.findPromokitTabs);

    // Inicializar o campo de entrada como habilitado
    urlInput.disabled = false;
    urlInput.placeholder = 'https://exemplo.promokit.com.br';

    // Carregar a URL salva ao iniciar
    chrome.storage.local.get([storageKey], function(result) {
        if (result[storageKey]) {
            // Manter o formulário oculto inicialmente mesmo com URL salva
            urlInput.value = result[storageKey];
            sendRuntimeMessage(result[storageKey]);

            // Formatar o domínio para o botão de voltar
            const domainText = formatDisplayUrl(result[storageKey]);

            // Mostrar botão de voltar quando há URL salva
            backButton.style.display = 'flex';

            // Atualizar apenas o texto de destino
            const destinationElement = backButton.querySelector('.back-destination');
            if (destinationElement) {
                destinationElement.textContent = domainText;
            }
        }
    });

    // Adicionar evento de cancelamento
    document.getElementById('cancelBtn').addEventListener('click', function() {
        // Ocultar o formulário
        document.getElementById('iframeSettingsForm').style.display = 'none';

        // Restaurar estado anterior
        chrome.storage.local.get([storageKey], function(result) {
            if (result[storageKey]) {
                urlInput.value = result[storageKey];
            } else {
                urlInput.value = '';
            }
        });

        // Remover seleção das abas e restaurar texto dos botões
        document.querySelectorAll('.tab-item').forEach(item => {
            item.classList.remove('selected');
            const btn = item.querySelector('.btn-select');
            if (btn) {
                btn.textContent = 'Selecionar';
                btn.classList.remove('selected');
            }
        });

        // Restaurar dica de uso
        urlHelpText.textContent = 'Insira a URL completa incluindo https://';

        // Atualizar o status de seleção para indicar que nenhuma página está selecionada
        const selectionStatus = document.getElementById('selectionStatus');
        if (selectionStatus) {
            selectionStatus.className = 'selection-status';
            selectionStatus.innerHTML = '<i class="fas fa-info-circle"></i><span>Nenhuma página selecionada. Clique em uma página acima para selecioná-la.</span>';
        }
    });

    // Salvar a nova URL
    form.addEventListener('submit', function(event) {
        event.preventDefault();
        let newUrl = urlInput.value.trim();

        if (newUrl) {
            try {
                // Garantir que a URL esteja no formato correto antes de salvar
                newUrl = formatPromokitUrl(newUrl);
                urlInput.value = newUrl;

                new URL(newUrl);
                chrome.storage.local.set({ [storageKey]: newUrl }, function() {
                    if (chrome.runtime.lastError) {
                        console.error('Erro ao salvar URL:', chrome.runtime.lastError);
                        showNotification(`Erro ao salvar: ${chrome.runtime.lastError.message}`, 'error');
                    } else {
                        console.log('URL salva:', newUrl);
                        showNotification('URL salva com sucesso!', 'success');

                        // Notifica o content script para mostrar o alerta de sucesso
                        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                            chrome.tabs.sendMessage(tabs[0].id, {
                                type: 'IFRAME_URL_UPDATED',
                                url: newUrl
                            });
                        });

                        sendRuntimeMessage(newUrl);

                        // Fechar a janela após salvar
                        setTimeout(() => {
                            window.close();
                        }, 1500);
                    }
                });
            } catch (error) {
                showNotification('URL inválida', 'error');
            }
        } else {
            showNotification('Por favor, insira uma URL válida', 'error');
        }
    });

    // Controle de z-index do iframe
    let isIframeOnTop = false;

    window.addEventListener('message', function(event) {
        if (event.data.type === 'TOGGLE_IFRAME_Z_INDEX') {
            isIframeOnTop = !isIframeOnTop;
            isIframeActive = isIframeOnTop;
            const iframe = document.getElementById('whatsapp-support-iframe');
            if (iframe) {
                iframe.style.zIndex = isIframeOnTop ? '999999999' : '0';
                // Atualizar classes visuais
                if (isIframeActive) {
                    iframe.classList.remove('iframe-inactive');
                    iframe.classList.add('iframe-active');
                } else {
                    iframe.classList.remove('iframe-active');
                    iframe.classList.add('iframe-inactive');
                }

                // Notificar sobre a mudança de estado
                chrome.runtime.sendMessage({
                    type: 'IFRAME_STATE_CHANGED',
                    isActive: isIframeActive
                });
            }
        }
    });

    // Função para aplicar o estilo inicial do iframe
    function setInitialIframeStyle() {
        const iframe = document.getElementById('whatsapp-support-iframe');
        if (iframe) {
            iframe.classList.add('iframe-inactive');
        }
    }

    // Aplicar o estilo inicial quando a página carregar
    setTimeout(setInitialIframeStyle, 500);

    // Iniciar o processo de busca automaticamente quando a página carregar
    setTimeout(window.findPromokitTabs, 200);

    // Adicionar evento de clique ao botão de voltar
    backButton.addEventListener('click', function() {
        // Enviar mensagem para a aba atual antes de fechar
        chrome.storage.local.get([storageKey], function(result) {
            if (result[storageKey]) {
                const savedUrl = result[storageKey];
                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    chrome.tabs.sendMessage(tabs[0].id, {
                        type: 'IFRAME_URL_UPDATED',
                        url: savedUrl
                    });
                });

                // Também envia através do runtime
                sendRuntimeMessage(savedUrl);

                // Fechar a janela após enviar a mensagem
                setTimeout(() => {
                    window.close();
                }, 100);
            } else {
                window.close();
            }
        });
    });
});
