/* Estilos para o Side Panel da extensão WhatsApp Suporte */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f7fa;
  color: #333;
  line-height: 1.6;
  overflow-x: hidden;
}

.sidepanel-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 100%;
}

/* Header */
.header {
  background: linear-gradient(135deg, #128C7E 0%, #25D366 100%);
  color: white;
  padding: 16px 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  width: 32px;
  height: 32px;
  border-radius: 6px;
}

.title-section {
  flex: 1;
}

.title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 4px;
}

.status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  opacity: 0.9;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.status-active {
  background-color: #4CAF50;
  box-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
}

.status-inactive {
  background-color: #FF5252;
  box-shadow: 0 0 8px rgba(255, 82, 82, 0.6);
}

/* Main Content */
.main-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title:before {
  content: '';
  width: 3px;
  height: 16px;
  background: linear-gradient(135deg, #128C7E, #25D366);
  border-radius: 2px;
}

/* Stats Section */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #128C7E, #25D366);
  color: white;
  font-size: 18px;
}

.stat-info {
  flex: 1;
}

.stat-count {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Actions Section */
.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 12px 16px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  min-height: 48px;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-btn.primary {
  background: linear-gradient(135deg, #128C7E, #25D366);
  color: white;
  box-shadow: 0 2px 8px rgba(37, 211, 102, 0.3);
}

.action-btn.primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(37, 211, 102, 0.4);
}

.action-btn.secondary {
  background: white;
  color: #128C7E;
  border: 2px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.action-btn.secondary:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #128C7E;
  transform: translateY(-1px);
}

.action-btn i {
  font-size: 16px;
}

/* PromoKit Tabs Section */
.tabs-list {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e9ecef;
}

.tab-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s ease;
}

.tab-item:last-child {
  border-bottom: none;
}

.tab-item:hover {
  background-color: #f8f9fa;
}

.tab-info {
  flex: 1;
}

.tab-title {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tab-url {
  font-size: 12px;
  color: #6c757d;
}

.tab-actions {
  margin-left: 12px;
}

.tab-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: #128C7E;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.tab-btn:hover {
  background: #0E7369;
  transform: scale(1.05);
}

/* Messages Section */
.messages-list {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e9ecef;
}

.message-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s ease;
}

.message-item:last-child {
  border-bottom: none;
}

.message-item:hover {
  background-color: #f8f9fa;
}

.message-info {
  flex: 1;
}

.message-contact {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.message-preview {
  font-size: 14px;
  color: #6c757d;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.message-time {
  font-size: 12px;
  color: #adb5bd;
  margin-left: 12px;
  white-space: nowrap;
}

/* Empty States */
.no-tabs-message,
.no-messages {
  padding: 32px 20px;
  text-align: center;
  color: #6c757d;
  font-style: italic;
}

/* Footer */
.footer {
  background: white;
  padding: 16px 20px;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #6c757d;
}

.footer-info p {
  margin: 0;
}

.last-sync {
  font-size: 11px;
  opacity: 0.8;
}

/* Scrollbar Styling */
.main-content::-webkit-scrollbar {
  width: 6px;
}

.main-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.main-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive Design */
@media (max-width: 300px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* Loading Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.fa-spin {
  animation: spin 1s linear infinite;
}

/* Focus States for Accessibility */
.action-btn:focus,
.tab-btn:focus {
  outline: 2px solid #128C7E;
  outline-offset: 2px;
}

/* Animation for status indicator */
.status-active {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(76, 175, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

/* Estilos do IFrame Settings */
.iframe-settings-section {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e9ecef;
}

.card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  background-color: #f9fafb;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 500;
  color: #4f46e5;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-body {
  padding: 16px;
}

.step {
  display: flex;
  margin-bottom: 20px;
  align-items: flex-start;
}

.step-number {
  background-color: #4f46e5;
  color: white;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  margin-right: 12px;
  flex-shrink: 0;
}

.step-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.step-heading {
  font-weight: 600;
  font-size: 1.125rem;
  color: #4f46e5;
  margin-left: 8px;
}

.step-content {
  flex-grow: 1;
}

.step-title {
  font-weight: 500;
  margin-bottom: 8px;
  color: #374151;
}

.step-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 16px;
}

.form-group {
  margin-bottom: 20px;
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-group i {
  position: absolute;
  left: 12px;
  color: #9ca3af;
}

input[type="url"] {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  color: #1f2937;
  transition: all 0.2s;
}

input[type="url"]:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

input[type="url"]:disabled {
  background-color: #f3f4f6;
  cursor: not-allowed;
}

.help-text {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 4px;
}

.actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background-color: #4f46e5;
  color: white;
}

.btn-primary:hover {
  background-color: #4338ca;
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background-color: #e5e7eb;
  transform: translateY(-1px);
}

.btn-search {
  background-color: #0ea5e9;
  color: white;
}

.btn-search:hover {
  background-color: #0284c7;
  transform: translateY(-1px);
}

.notification {
  display: none;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
  font-size: 0.875rem;
  align-items: center;
  gap: 8px;
}

.notification.success {
  background-color: #ecfdf5;
  color: #047857;
  border: 1px solid #a7f3d0;
}

.notification.error {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.search-container {
  position: relative;
  margin-bottom: 16px;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.tabs-list {
  max-height: 260px;
  overflow-y: auto;
  margin-bottom: 12px;
  border-radius: 8px;
}

.tab-item {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.tab-content {
  flex: 1;
}

.tab-item:hover {
  background-color: #f5f5ff;
  border-color: #4f46e5;
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.1);
}

.tab-item.selected {
  background-color: #dbeafe;
  border-left: 3px solid #3b82f6;
}

.btn-select {
  background-color: #4f46e5;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.btn-select:hover {
  background-color: #4338ca;
}

.btn-select.selected {
  background-color: #10b981;
}

.btn-select.selected:hover {
  background-color: #059669;
}

.instruction-box {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  padding: 10px 12px;
  margin-bottom: 12px;
  font-size: 0.875rem;
  color: #0369a1;
}

.instruction-box i {
  color: #0ea5e9;
  font-size: 1rem;
}

.tab-item.highlighted {
  border-left: 3px solid #4f46e5;
  background-color: #f5f3ff;
  position: relative;
}

.selection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px;
  background-color: #f9fafb;
  border-radius: 6px;
  margin-top: 12px;
  font-size: 0.875rem;
  color: #6b7280;
  border: 1px dashed #d1d5db;
}

.selection-status.selected {
  background-color: #f0fdf4;
  border: 1px solid #86efac;
  color: #166534;
}

.selection-status i {
  font-size: 1rem;
}

.tab-title {
  font-weight: 500;
  font-size: 0.875rem;
  margin-bottom: 4px;
  color: #1f2937;
}

.tab-index {
  display: inline-block;
  min-width: 20px;
  color: #4f46e5;
  font-weight: 600;
}

.tab-url {
  font-size: 0.75rem;
  color: #6b7280;
  word-break: break-all;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  margin-left: 8px;
}

.badge-count {
  background-color: #e0f2fe;
  color: #0284c7;
}

.manual-entry-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  color: #4f46e5;
  margin-top: 8px;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: all 0.2s;
}

.manual-entry-trigger:hover {
  background-color: #f5f3ff;
  text-decoration: underline;
}

.manual-entry-trigger i {
  margin-right: 6px;
}

#noTabsMessage {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 0.875rem;
  background-color: #f9fafb;
  padding: 12px;
  border-radius: 6px;
  border: 1px dashed #d1d5db;
}