import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:data_connection_checker/data_connection_checker.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/platform_interface.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:http/http.dart' as http;
import 'seminternet.dart';

String url_inicial = '';
String urlServidor = 'https://kruke.promokit.com.br';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host,
          int port) => true;
  }
}

MyApp myApp;
final navigatorKey = GlobalKey<NavigatorState>();

/// Initalize the [FlutterLocalNotificationsPlugin] package.
final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
/// Create a [AndroidNotificationChannel] for heads up notifications
final AndroidNotificationChannel channel = AndroidNotificationChannel(
  'high_importance_channel', // id
  'High Importance Notifications', // title
  'This channel is used for important notifications.', // description
  importance: Importance.high,
  enableVibration: true,
  playSound: true,
);

void main() async {
  //HttpOverrides.global = new MyHttpOverrides();

  url_inicial = 'https://lojaskruke.meucardapio.ai';

  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp();

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  firebaseCloudMessaging_Listeners();

  myApp = MyApp();
  runApp(myApp);
}

void firebaseCloudMessaging_Listeners() async {
  FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  /// Create an Android Notification Channel.
  ///
  /// We use this channel in the `AndroidManifest.xml` file to override the
  /// default FCM channel to enable heads up notifications.
  await flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<
      AndroidFlutterLocalNotificationsPlugin>()
      ?.createNotificationChannel(channel);

  /// Update the iOS foreground notification presentation options to allow
  /// heads up notifications.
  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    alert: true,
    badge: true,
    sound: true,
  );

  NotificationSettings settings = await _firebaseMessaging.requestPermission(
    alert: true,
    announcement: false,
    badge: true,
    carPlay: false,
    criticalAlert: false,
    provisional: false,
    sound: true,
  );
}

showAlertDialog(BuildContext context, String mensagem) {
  /*
  if( context == null ) {
    return;
  }

  // set up the button
  Widget okButton = FlatButton(
    child: Text("OK"),
    onPressed: () { },
  );

  // set up the AlertDialog
  AlertDialog alert = AlertDialog(
    title: Text(""),
    content: SelectableText(mensagem),
    actions: [
      okButton,
    ],
  );

  // show the dialog
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return alert;
    },
  );
   */
  print('Alert Message: ' + mensagem);
}



Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  // If you're going to use other Firebase services in the background, such as Firestore,
  // make sure you call `initializeApp` before using other Firebase services.
  String url = message.data["LINK"];

  url_inicial = url;

  print("Alert Message: Handling a background message: ${message.messageId}: " + url_inicial);
}

class MyApp extends StatelessWidget {
  MyApp() {

  }
  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    if (Platform.isAndroid) WebView.platform = SurfaceAndroidWebView();

    /*
    FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

    _firebaseMessaging.getToken().then((token) {
      showAlertDialog(context, "token: $token");
    });
*/

    const MaterialColor white = const MaterialColor(
        0xFFFFFFFF,
        const <int, Color>{
          50: const Color(0xFFFFFFFF),
          100: const Color(0xFFFFFFFF),
          200: const Color(0xFFFFFFFF),
          300: const Color(0xFFFFFFFF),
          400: const Color(0xFFFFFFFF),
          500: const Color(0xFFFFFFFF),
          600: const Color(0xFFFFFFFF),
          700: const Color(0xFFFFFFFF),
          800: const Color(0xFFFFFFFF),
          900: const Color(0xFFFFFFFF),
        });


    SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          //color set to transperent or set your own color
          //set brightness for icons, like dark background light icons
        )
    );
    return MaterialApp(
      title: 'Flutter Demo',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        // This is the theme of your application.
        //
        // Try running your application with "flutter run". You'll see the
        // application has a blue toolbar. Then, without quitting the app, try
        // changing the primarySwatch below to Colors.green and then invoke
        // "hot reload" (press "r" in the console where you ran "flutter run",
        // or simply save your changes to "hot reload" in a Flutter IDE).
        // Notice that the counter didn't reset back to zero; the application
        // is not restarted.
        primarySwatch: white,
        // This makes the visual density adapt to the platform that you run
        // the app on. For desktop platforms, the controls will be smaller and
        // closer together (more dense) than on mobile platforms.
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: MyHomePage(title: 'Flutter Demo Home Page'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  MyHomePage({Key key, this.title}) : super(key: key);

  // This widget is the home page of your application. It is stafiteful, meaning
  // that it has a State object (defined below) that contains fields that affect
  // how it looks.

  // This class is the configuration for the state. It holds the values (in this
  // case the title) provided by the parent (in this case the App widget) and
  // used by the build method of the State. Fields in a Widget subclass are
  // always marked "final".

  final String title;

  @override
  _MyHomePageState createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _counter = 0;
  WebViewController _controller;
  final Completer<WebViewController> _controllerCompleter = Completer<WebViewController>();
  FirebaseMessaging _firebaseMessaging;
  WebViewController webViewController;

  StreamSubscription<DataConnectionStatus> listener;

  WebView webview;

  bool carregando = true;

  @override
  void initState() {
    super.initState();

    final InAppReview inAppReview = InAppReview.instance;

    inAppReview.isAvailable().then((disponivel) {
      if( disponivel ) {
        inAppReview.requestReview();
      }
    });

    // Get any messages which caused the application to open from
    // a terminated state.
    FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage initialMessage) {
      if( initialMessage != null ) {
        url_inicial = initialMessage.data['LINK'];

        _controllerCompleter.future.then((value) => {
          value.loadUrl(url_inicial)
        });
      }
    });

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      showAlertDialog(context, "abriu mensagem app fechado");

      RemoteNotification notification = message.notification;
      AndroidNotification android = message.notification?.android;

      if (notification != null && android != null) {
        flutterLocalNotificationsPlugin.show(
            notification.hashCode,
            notification.title,
            notification.body,
            NotificationDetails(
              android: AndroidNotificationDetails(
                channel.id,
                channel.name,
                channel.description,
                // TODO add a proper drawable resource to android, for now using
                //      one that already exists in example app.
                icon: 'launch_background',
              ),
            ));
      }
    });

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) async {
      await Firebase.initializeApp();
      // If you're going to use other Firebase services in the background, such as Firestore,
      // make sure you call `initializeApp` before using other Firebase services.
      String url = message.data["LINK"];

      url_inicial = url;

      print("Alert Message: Apertou na notificação");
    });
  }
  /*
  @override
  void initState() {
    super.initState();




  }
*/

  @override
  void dispose() {
    listener.cancel();
    super.dispose();
  }

  checkInternet() async {
    // Simple check to see if we have internet
    print("The statement 'this machine is connected to the Internet' is: ");
    print(await DataConnectionChecker().hasConnection);
    // returns a bool

    // We can also get an enum value instead of a bool
    print("Current status: ${await DataConnectionChecker().connectionStatus}");
    // prints either DataConnectionStatus.connected
    // or DataConnectionStatus.disconnected

    // This returns the last results from the last call
    // to either hasConnection or connectionStatus
    print("Last results: ${DataConnectionChecker().lastTryResults}");

    // actively listen for status updates
    // this will cause DataConnectionChecker to check periodically
    // with the interval specified in DataConnectionChecker().checkInterval
    // until listener.cancel() is called
    listener = DataConnectionChecker().onStatusChange.listen((status) {
      switch (status) {
        case DataConnectionStatus.connected:
          print('Data connection is available.');
          break;
        case DataConnectionStatus.disconnected:
          print('You are disconnected from the internet.');
          break;
      }
    });

    return await DataConnectionChecker().connectionStatus;
  }

  _MyHomePageState() {
  }

  Widget crie() {
    return carregando ?  Center(
        child: Container(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          color: Colors.white,
          child: Center(
            child: Container(
              child: CircularProgressIndicator(
                strokeWidth: 14,
              ),
              height: 105,
              width: 105,
            )

          ),
        )
    )
        : Center();
  }

  salveToken(String token, Map dadosUsuario) async {
    var url = urlServidor + '/api/insiraToken';

    var dados = <String, dynamic>{
      'token': token
    };

    if( dadosUsuario != null ) {
      dados['contato'] = {
        'id': dadosUsuario['id']
      };
    }

    var response = await http.post(Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode(dados)
      );
    print('Response status: ${response.statusCode}');
    print('Response body: ${response.body}');
  }

  @override
  Widget build(BuildContext context) {
    // This method is rerun every time setState is called, for instance as done
    // by the _incrementCounter method above.
    //
    // The Flutter framework has been optimized to make rerunning build methods
    // fast, so that you can just rebuild anything that needs updating rather
    // than having to individually change instances of widgets.
    //showAlertDialog(context, "criando webview: " + url_inicial);

    webview = WebView(
      initialUrl: url_inicial,
      javascriptMode: JavascriptMode.unrestricted,
      debuggingEnabled: true,
      javascriptChannels: <JavascriptChannel>[
        JavascriptChannel(name: 'Flutter', onMessageReceived: (JavascriptMessage msg) {
          setState(() {
            carregando = false;
            var objMensagem = jsonDecode(msg.message);

            var tipoDeMensagem = objMensagem['tipo'];
            var usuario = null;

            if( tipoDeMensagem == 'usuario' ) {
              usuario = objMensagem['usuario'];
            }

            FirebaseMessaging.instance.getToken().then((value) {
              salveToken(value, usuario);
            });
          });
        })
      ].toSet(),
      navigationDelegate: (NavigationRequest request) {
        if (request.url.startsWith('https://instagram.com') ||
            request.url.startsWith('https://facebook.com') ||
            request.url.startsWith('https://api.whatsapp.com/') )
        {
          print('blocking navigation to $request}');
          _launchURL(request.url);
          return NavigationDecision.prevent;
        }

        return NavigationDecision.navigate;
      },
      onWebViewCreated: (WebViewController webViewController) async {
        var status = await this.checkInternet();

        FirebaseMessaging.instance.getToken().then((value) {
          FirebaseMessaging.instance.subscribeToTopic("lojaskruke.meucardapio.ai").then( (value) => {
          });
        });
        if( status != DataConnectionStatus.connected ) {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => SemInternetPage()),
          ).then((value) {
            webViewController.reload();
            carregando = true;
          });
        }

        _controllerCompleter.future.then((value) => _controller = value);
        _controllerCompleter.complete(webViewController);
        this.webViewController = webViewController;
      },
      onWebResourceError: (WebResourceError error) {
        print("Handle your Error Page here");
      },
      onPageFinished: (finish) {

      },
    );

    return WillPopScope(
      child: Scaffold(
        appBar: AppBar(
          toolbarHeight: 0,
          elevation: 0,
        ),
        body: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          child: Stack(
            children: [
              webview,
              crie()
            ],
          )
        ),
      ),
      onWillPop: () async {
        return _onWillPop(context);
      },
    );
  }

  _launchURL(String url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  Future<bool> _onWillPop(BuildContext context) async {
    if (await _controller.canGoBack()) {
      _controller.goBack();
      return Future.value(false);
    } else {
      return Future.value(true);
    }
  }
}
